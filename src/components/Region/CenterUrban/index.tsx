import { useAppDispatch, useAppSelector } from 'store';
import {
  fetchUrbanCenters,
  selectUrbanCenters
} from 'store/slices/urban_center.slices';
import { OptionCard } from 'components/Common';
import ButtonAdd from 'components/Common/ButtonAdd';
import CenterUrbanForm from './CenterUrbanForm';
import { useState, useEffect } from 'react';

const CenterUrban = ({ regionId }: { regionId: number }) => {
  const dispatch = useAppDispatch();
  const [isShowCreateForm, setIsShowCreateForm] = useState<boolean>(false);
  const urbanCenterList = useAppSelector(selectUrbanCenters);

  const handleShowCreateForm = (value: boolean) => {
    setIsShowCreateForm(value);
  };

  const getListUrbanCenters = async () => {
    await dispatch(fetchUrbanCenters(regionId)).unwrap();
  };

  useEffect(() => {
    getListUrbanCenters();
  }, []);

  return (
    <OptionCard
      title='Centre Urbain'
      otherStyles={{ marginTop: '30px', marginBottom: '16px' }}>
      {urbanCenterList?.map((urbanCenter) => (
        <div key={urbanCenter.id}>
          <CenterUrbanForm
            urbanCenter={urbanCenter}
            action='update'
            regionId={regionId}
            handleShowCreateForm={handleShowCreateForm}
            fetchUrbanCenterList={getListUrbanCenters}
          />
          <hr className='product-regul__end-line' />
        </div>
      ))}
      {isShowCreateForm && (
        <div>
          <CenterUrbanForm
            action='create'
            regionId={regionId}
            handleShowCreateForm={handleShowCreateForm}
            fetchUrbanCenterList={getListUrbanCenters}
          />
          <hr
            className='product-regul__end-line'
            style={{
              marginBottom: isShowCreateForm ? '72px' : '0px'
            }}
          />
        </div>
      )}
      {!isShowCreateForm && (
        <ButtonAdd
          otherStyles={{
            height: '32px',
            borderRadius: '6px',
            marginTop: '43px'
          }}
          handleClick={() => handleShowCreateForm(true)}>
          Ajouter un Centre Urbain
        </ButtonAdd>
      )}
    </OptionCard>
  );
};
export default CenterUrban;
