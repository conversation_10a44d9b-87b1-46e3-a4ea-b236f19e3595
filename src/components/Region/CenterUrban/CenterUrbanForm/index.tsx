import { useEffect, useState } from 'react';
import {
  CloseOutlined,
  EditOutlined,
  PlusCircleOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import { Space, Input, Typography, Button } from 'antd';
import Autocomplete from 'react-google-autocomplete';
import { CheckButton, TrashButton } from 'components/Common';
import BulletPointIcon from 'components/Icons/BulletPointIcon';
import { UrbanCenter, Zone } from 'models';
import { toast } from 'react-toastify';
import { urbanCenterService } from 'services';
import { useMergeState } from 'hooks';
import ZoneItem from '../../../Common/ZoneItem';
import { validateZones } from 'utils';
const { Text } = Typography;

const CenterUrbanForm = ({
  urbanCenter = { Zones: [] },
  action,
  regionId,
  handleShowCreateForm,
  fetchUrbanCenterList,
}: {
  urbanCenter?: UrbanCenter;
  action: 'create' | 'update';
  regionId: number;
  handleShowCreateForm: (value: boolean) => void;
  fetchUrbanCenterList: () => void;
}) => {
  const [isEditing, setIsEditing] = useState<boolean>(
    action === 'create' ? true : false
  );
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [isSubmited, setIsSubmited] = useState<boolean>(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [selectedUrbanCenter, setSelectedUrbanCenter] =
    useMergeState<UrbanCenter>(urbanCenter);

  const handleEditingMode = (value: boolean) => {
    setIsEditing(value);
  };

  const handleAddZoneItemInList = () => {
    const updateZones = [...(selectedUrbanCenter?.Zones as Zone[])];
    updateZones.push({ maxDistance: null });
    setSelectedUrbanCenter({ Zones: updateZones });
    setIsEditing(true);
  };

  const handleChangeZoneList = (
    maxDistance: number | null,
    zoneIndex: number
  ) => {
    const updateZone = selectedUrbanCenter?.Zones?.map((zone) => ({
      ...zone,
    })) as Zone[];
    updateZone[zoneIndex].maxDistance = maxDistance;
    setSelectedUrbanCenter({ Zones: updateZone });
  };

  const handleSubmitUrbanCenter = async () => {
    setIsSubmited(true);
    try {
      if (!selectedUrbanCenter?.name || !selectedUrbanCenter?.formattedAddress)
        return;
      if (!validateZones(selectedUrbanCenter?.Zones)) return;
      setIsSubmiting(true);
      const submitData = {
        ...selectedUrbanCenter,
        regionId,
        Zones: selectedUrbanCenter?.Zones?.map((item) => ({
          maxDistance: item.maxDistance,
        })),
      };
      if (action === 'create') {
        await urbanCenterService.createUrbanCenter(regionId, submitData);
      } else {
        if(!selectedUrbanCenter?.id) return
        await urbanCenterService.updateUrbanCenter(
          regionId,
          selectedUrbanCenter?.id,
          submitData
        );
      }
      await fetchUrbanCenterList();
      setIsSubmiting(false);
      handleShowCreateForm(false);
      setIsEditing(false);
      toast.success('Succès');
      setIsSubmited(false);
    } catch (error) {
      setIsSubmiting(false);
      toast.error('Erreur');
    }
  };

  const handleCancelEdit = () => {
    handleEditingMode(false);
    setSelectedUrbanCenter(urbanCenter);
  };

  const handleDeactivateUrbanCenter = async () => {
    if(!selectedUrbanCenter?.id) return
    try {
      setDeleteLoading(true);
      await urbanCenterService.deactivateUrbanCenter(
        regionId,
        selectedUrbanCenter?.id
      );
      await fetchUrbanCenterList();
      setDeleteLoading(false);
      toast.success('Succès');
    } catch (error) {
      setDeleteLoading(false);
      toast.error('Erreur');
    }
  };

  const handleSelectAddress = (place: any) => {
    if (
      !place?.address_components ||
      !place?.formatted_address ||
      !place?.place_id
    ) {
      setSelectedUrbanCenter({
        formattedAddress: undefined,
        address: undefined,
        city: undefined,
        postalcode: undefined,
        country: undefined,
        longitude: undefined,
        latitude: undefined,
      });
      return;
    }
    const { address_components, formatted_address } = place;
    // Extract the required information from the address component
    let address = '';
    let city = '';
    let postalcode = '';
    let country = '';
    for (const component of address_components) {
      if (component.types.includes('postal_town')) {
        city = component.long_name;
      } else if (component.types.includes('locality')) {
        city = component.long_name;
      } else if (component.types.includes('administrative_area_level_1')) {
        city = component.long_name;
      } else if (component.types.includes('postal_code')) {
        postalcode = component.long_name;
      } else if (component.types.includes('country')) {
        country = component.long_name;
      } else if (component.types.includes('route')) {
        address = component.long_name;
      } else if (component.types.includes('street_number')) {
        address += ' ' + component.long_name;
      }
    }
    // Extract the latitude and longitude values
    const { lat, lng } = place.geometry.location;
    setSelectedUrbanCenter({
      formattedAddress: formatted_address,
      address,
      city,
      postalcode,
      country,
      longitude: lng(),
      latitude: lat(),
    });
  };

  return (
    <div>
      <Space className='center-urban__form'>
        <Space>
          <Text className='center-urban__title-label'>
            Titre du centre urbain:
          </Text>
          {action === 'update' && !isEditing ? (
            <Text className='center-urban__title-text'>
              {urbanCenter?.name}
            </Text>
          ) : (
            <Input
              placeholder='Input'
              className='center-urban__title-input'
              value={selectedUrbanCenter?.name}
              style={{
                borderColor:
                  isSubmited && !selectedUrbanCenter?.name ? 'red' : '',
              }}
              onChange={(e) => setSelectedUrbanCenter({ name: e.target.value })}
              onPressEnter={handleSubmitUrbanCenter}
            />
          )}
        </Space>
        <Space>
          <Text className='center-urban__title-label'>
            Adresse du centre urbain:
          </Text>
          {action === 'update' && !isEditing ? (
            <Text className='center-urban__address-text'>
              {urbanCenter?.formattedAddress}
            </Text>
          ) : (
            <Autocomplete
              className={`center-urban__address-input ${
                isSubmited && !selectedUrbanCenter?.formattedAddress
                  ? 'center-urban__address-input-error'
                  : ''
              }`}
              defaultValue={urbanCenter?.formattedAddress}
              apiKey={`${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}`}
              onChange={handleSelectAddress}
              onPlaceSelected={handleSelectAddress}
              options={{
                types: ['address'],
                fields: [
                  'address_components',
                  'geometry.location',
                  'place_id',
                  'formatted_address',
                ],
              }}
              placeholder='Address'
            />
          )}
        </Space>
        <Space>
          {action === 'update' &&
            (isEditing ? (
              <>
                <CheckButton
                  loading={isSubmiting}
                  className='check-button'
                  onClick={handleSubmitUrbanCenter}
                />
                <CloseOutlined
                  className='center-urban__update-cancel-icon cancel-button'
                  onClick={handleCancelEdit}
                />
              </>
            ) : (
              <>
                <EditOutlined
                  className='center-urban__edit-icon edit-icon'
                  onClick={() => handleEditingMode(true)}
                />
                <TrashButton
                  className='center-urban__trash-icon trash-button'
                  loading={deleteLoading}
                  onClick={handleDeactivateUrbanCenter}
                />
              </>
            ))}
          {action === 'create' && (
            <>
              <Button
                type='text'
                size='small'
                className='center-urban__ajouter-btn'
                loading={isSubmiting}
                onClick={handleSubmitUrbanCenter}
              >
                <PlusCircleOutlined className='center-urban__add-icon' />
                Ajouter
              </Button>
              <CloseOutlined
                className='center-urban__cancel-icon cancel-button'
                onClick={() => handleShowCreateForm(false)}
              />
            </>
          )}
        </Space>
      </Space>
      <Space className='center-urban__zones'>
        <Space className='center-urban__zone-list'>
          {selectedUrbanCenter?.Zones?.map((zone, index) => (
            <Space
              className='center-urban__zone-item'
              key={`${zone.id}-${index}`}
            >
              <BulletPointIcon className='center-urban__bullet-point-icon' />
              <Text className='center-urban__zone-label'>
                Zone {index + 1}:
              </Text>
              {action === 'update' && !isEditing ? (
                <Text className='center-urban__zone-text'>
                  {zone.maxDistance}
                </Text>
              ) : (
                <ZoneItem
                  zone={zone}
                  zoneIndex={index}
                  zoneList={selectedUrbanCenter?.Zones as Zone[]}
                  handleChangeZoneList={handleChangeZoneList}
                />
              )}
              <Text className='center-urban__zone-unit'>Km</Text>
            </Space>
          ))}
        </Space>
        <Space>
          <Button
            type='text'
            size='small'
            className='center-urban__add-parameter-btn'
            onClick={handleAddZoneItemInList}
          >
            <PlusOutlined />
            Ajouter une zone
          </Button>
        </Space>
      </Space>
    </div>
  );
};

export default CenterUrbanForm;
