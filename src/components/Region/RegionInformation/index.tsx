import { useState } from 'react';
import { toast } from 'react-toastify';
import { Input, Typography, Select } from 'antd';
import { CloseOutlined, EditOutlined } from '@ant-design/icons';
import { Region } from 'models';
import { useAppSelector } from 'store';
import { useMergeState } from 'hooks';
import { regionService } from 'services';
import { CheckButton, OptionCard } from 'components/Common';
import { selectRegion } from 'store/slices/region.slices';
import { selectPlatforms } from 'store/slices/platform.slices';
const { Text } = Typography;
const { Option } = Select;

const RegionInformation = ({
  regionId,
  setRegionName,
  onRefreshRegion,
}: {
  regionId: number;
  setRegionName: Function;
  onRefreshRegion: Function;
}) => {
  const platforms = useAppSelector(selectPlatforms);
  const region = useAppSelector(selectRegion);
  const [isSubmitTitle, setIsSubmitTitle] = useState<boolean>(false);
  const [isSubmitPlatform, setIsSubmitPlatform] = useState<boolean>(false);
  const [editingRegion, setEditingRegion] = useMergeState<Region | null>(
    region
  );

  const [isTitleEditing, setIsTitleEditing] = useState<boolean>(false);

  const handleTitleEdit = () => {
    setIsTitleEditing(true);
  };

  const handleCancelTitle = () => {
    if (!isSubmitTitle) {
      setIsTitleEditing(false);
      setEditingRegion({ name: region?.name });
    }
  };

  const onChangePlatform = (platformId: number) => {
    setEditingRegion({ platformId });
    onSubmit({ platformId });
  };

  const onSubmit = async (submitData: {
    name?: string;
    platformId?: number;
  }) => {
    try {
      if (!submitData?.name && !submitData?.platformId) {
        return;
      }
      submitData?.name ? setIsSubmitTitle(true) : setIsSubmitPlatform(true);
      await regionService.updateRegion(regionId, submitData);
      toast.success('Succès');
      if (submitData?.name) {
        setRegionName(submitData?.name);
        setIsSubmitTitle(false);
        setIsTitleEditing(false);
      } else {
        setIsSubmitPlatform(false);
      }
      onRefreshRegion();
    } catch (error) {
      submitData?.name ? setIsSubmitTitle(false) : setIsSubmitPlatform(false);
      toast.error('Erreur');
    }
  };

  return (
    <OptionCard title='Informations Région' otherStyles={{ marginTop: '30px' }}>
      <div className='region-information__title-of-region'>
        <Text className='region-information__title-of-region-label'>
          Titre de la Région:
        </Text>
        {!isTitleEditing ? (
          <>
            <Text className='region-information__title-of-region-content'>
              {editingRegion?.name}
            </Text>
            <EditOutlined className='edit-icon' onClick={handleTitleEdit} />
          </>
        ) : (
          <>
            <Input
              placeholder='Input'
              value={editingRegion?.name}
              className='region-information__title-of-region-content-input'
              onChange={(e) => setEditingRegion({ name: e.target.value })}
              onPressEnter={() => onSubmit({ name: editingRegion?.name })}
            />
            <CheckButton
              loading={isSubmitTitle}
              onClick={() => onSubmit({ name: editingRegion?.name })}
              className='check-button'
            />
            <CloseOutlined
              className='region-information__title-of-region-cancel-icon cancel-button'
              onClick={handleCancelTitle}
            />
          </>
        )}
      </div>
      <div className='region-information__platform'>
        <Text className='region-information__platform-label'>
          Type de Région:
        </Text>
        <Select
          placeholder='Sélectionner'
          value={editingRegion?.platformId}
          loading={isSubmitPlatform}
          className='region-information__platform-selection'
          onChange={onChangePlatform}
        >
          {platforms.map((platform) => (
            <Option key={platform?.id} value={platform?.id}>
              {platform?.name}
            </Option>
          ))}
        </Select>
      </div>
    </OptionCard>
  );
};
export default RegionInformation;
