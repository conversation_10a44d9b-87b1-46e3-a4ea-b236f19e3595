import { useState, useEffect, useCallback } from 'react';
import { Button, Table, Pagination, Space, Spin, Select } from 'antd';
import {
  CatalogPrice,
  Product,
  ProductCatalogPrice,
  RegionProduct
} from 'models';
import { BlockOutlined, DeleteOutlined } from '@ant-design/icons';
import RegionProductCell, { RegionProductRow } from './RegionProductCell';
import { GridLoader, ScaleLoader } from 'react-spinners';
import { Loading } from 'types';
import { useAppDispatch, useAppSelector } from 'store';
import {
  fetchProductsByZoneIdForProductCatalogPrices,
  selectProductsByZoneIdForProductCatalogPrices,
  selectProductsByZoneIdForProductCatalogPricesTotal
} from 'store/slices/product.slices';
import { toast } from 'react-toastify';
import { fetchPriceTypes, selectPriceTypes } from 'store/slices/price.slices';
import { PRICE_TYPES } from 'utils/constant';
import { productCatalogPriceService } from 'services';
const { Option } = Select;

type EditableTableProps = Parameters<typeof Table>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

const RegionProductPriceList = ({
  catalogPricesList,
  catalogPricesListLoading,
  query,
  onQueryChange
}: {
  catalogPricesList: CatalogPrice[] | null;
  catalogPricesListLoading: Loading;
  query: any;
  onQueryChange: Function;
}) => {
  const dispatch = useAppDispatch();
  const [priceColumns, setPriceColumns] = useState<any[]>([]);
  const [dataSource, setDataSource] = useState<RegionProduct[]>([]);
  const [numberTotal, setNumberTotal] = useState<number>(0);
  const [isLoadingList, setIsLoadingList] = useState<boolean>(true);
  const [deletingObjs, setDeletingObjs] = useState<
    ({ id: number; groupId: number } | undefined)[]
  >([]);
  const [duplicatingObjs, setDuplicatingObjs] = useState<
    ({ id: number; groupId: number } | undefined)[]
  >([]);
  const productsByZoneId = useAppSelector(
    selectProductsByZoneIdForProductCatalogPrices
  );
  const totalItems = useAppSelector(
    selectProductsByZoneIdForProductCatalogPricesTotal
  );
  const priceTypes = useAppSelector(selectPriceTypes);
  const selectionPriceType = priceTypes.find(
    (priceType) => priceType.key === PRICE_TYPES.selection
  );

  const getProductsByZoneIdForProductCatalogPrices = useCallback(
    async (query: any) => {
      await dispatch(
        fetchProductsByZoneIdForProductCatalogPrices({
          zoneId: query.zone,
          query: {
            productTypeId: !query.productType ? 'null' : query.productType,
            include: true,
            page: query.page,
            limit: query.limit,
            search: query.keyword
          }
        })
      )
        .unwrap()
        .catch((error) => {
          toast.error('Erreur');
        });
      setTimeout(() => {
        setIsLoadingList(false);
      });
    },
    [dispatch]
  );

  const getPriceTypes = useCallback(async () => {
    await dispatch(fetchPriceTypes({}))
      .unwrap()
      .catch((error) => {
        toast.error('Erreur');
      });
  }, [dispatch]);

  useEffect(() => {
    getPriceTypes();
  }, []);

  useEffect(() => {
    const newProducts: Product[] = productsByZoneId.map((item) => {
      const product: any = {
        id: item.id,
        productTypeId: item.productTypeId,
        name: item.name,
        groupId: item.groupId,
        ProductCatalogPrices: item.ProductCatalogPrices
      };

      item?.ProductCatalogPrices?.forEach((price: ProductCatalogPrice) => {
        product[price.catalogPriceId] = {
          ...(price?.price ? { price: price.price } : { price: null }),
          ...(price.priceSubOptionId
            ? { priceSubOptionId: price.priceSubOptionId }
            : { priceSubOptionId: null }),
          productCatalogPriceId: price.id
        };
      });

      return product;
    });
    setDataSource(newProducts);
    setNumberTotal(totalItems);
  }, [productsByZoneId]);

  useEffect(() => {
    const newPriceColumns = !catalogPricesList
      ? []
      : catalogPricesList?.map((catalogPrice) => {
          if (
            !!catalogPrice?.PriceFamily &&
            !catalogPrice?.Price &&
            !catalogPrice?.PriceOption
          ) {
            return {
              title: catalogPrice?.PriceFamily?.name || '',
              width: 180,
              dataIndex: catalogPrice?.id,
              key: catalogPrice?.id,
              editable: true,
              required: true,
              inputType: 'currency',
              render: (value: any, record: any) => {
                if (query.zone) {
                  if (!value) {
                    return (
                      <div className='region-product-list__prix-forfait-tonne'>
                        {(Math.round(0 * 100) / 100).toFixed(2)}
                        <span className='region-product-list__prix-ht-unit'>
                          €
                        </span>
                      </div>
                    );
                  }
                  return (
                    <div className='region-product-list__prix-forfait-tonne'>
                      {(Math.round(value.price * 100) / 100).toFixed(2)}
                      <span className='region-product-list__prix-ht-unit'>
                        €
                      </span>
                    </div>
                  );
                }
              }
            };
          } else if (
            !!catalogPrice?.PriceFamily &&
            !!catalogPrice?.Price &&
            !catalogPrice?.PriceOption
          ) {
            return {
              title: catalogPrice?.Price?.name || '',
              width: 180,
              dataIndex: catalogPrice?.id,
              key: catalogPrice?.id,
              editable: true,
              required: true,
              inputType: 'currency',
              render: (value: any, record: any) => {
                if (query.zone) {
                  if (!value) {
                    return (
                      <div className='region-product-list__prix-forfait-tonne'>
                        {(Math.round(0 * 100) / 100).toFixed(2)}
                        <span className='region-product-list__prix-ht-unit'>
                          €
                        </span>
                      </div>
                    );
                  }
                  return (
                    <div className='region-product-list__prix-forfait-tonne'>
                      {(Math.round(value.price * 100) / 100).toFixed(2)}
                      <span className='region-product-list__prix-ht-unit'>
                        €
                      </span>
                    </div>
                  );
                }
              }
            };
          } else if (
            !!catalogPrice?.PriceFamily &&
            !!catalogPrice?.Price &&
            !!catalogPrice?.PriceOption
          ) {
            return {
              title: catalogPrice?.PriceOption?.name || '',
              width: 200,
              dataIndex: catalogPrice?.id,
              key: catalogPrice?.id,
              editable: true,
              inputType: 'select',
              selectOptions: catalogPrice.PriceOption?.PriceSubOptions,
              render: (value: any, record: any) => {
                if (query.zone) {
                  if (
                    catalogPrice.PriceOption?.priceTypeId ===
                    selectionPriceType?.id
                  ) {
                    return (
                      <Select
                        placeholder='Sélectionner'
                        value={value?.priceSubOptionId}
                        className='region-information__platform-selection'
                        style={{ width: '140px' }}
                        open={false}>
                        {catalogPrice.PriceOption?.PriceSubOptions?.map(
                          (platform) => (
                            <Option key={platform?.id} value={platform?.id}>
                              {platform?.name}
                            </Option>
                          )
                        )}
                      </Select>
                    );
                  }
                }
              }
            };
          }
        });
    setPriceColumns(newPriceColumns);
  }, [catalogPricesList, catalogPricesListLoading, query]);

  useEffect(() => {
    if (query.zone) {
      setIsLoadingList(true);
      getProductsByZoneIdForProductCatalogPrices(query);
    }
    if (!query.zone) {
      setDataSource([]);
      setNumberTotal(0);
      setIsLoadingList(false);
    }
  }, [query]);

  const deleteProductCatalogPrice = async (
    zoneId: number,
    productId: number,
    groupId: number
  ) => {
    try {
      setDeletingObjs((prevState) => [
        ...prevState,
        { id: productId, groupId }
      ]);
      const result = await productCatalogPriceService.deleteProductCatalogPrice(
        {
          zoneId,
          productId,
          groupId
        }
      );
      await getProductsByZoneIdForProductCatalogPrices(query);
      setDeletingObjs(
        duplicatingObjs.filter(
          (obj) => obj?.id !== productId && obj?.groupId !== groupId
        )
      );
      toast.success('Succès');
    } catch (error) {
      setDeletingObjs(
        duplicatingObjs.filter(
          (obj) => obj?.id !== productId && obj?.groupId !== groupId
        )
      );
      toast.error('Erreur');
    }
  };

  const duplicateProductCatalogPrice = async (item: any) => {
    try {
      setDuplicatingObjs((prevState) => [
        ...prevState,
        { id: item.id, groupId: item.groupId ?? 1 }
      ]);
      const duplicateData = {
        productId: item.id,
        groupId: item.groupId ?? 1,
        zoneId: query.zone
      };
      await productCatalogPriceService.duplicateProductCatalogPrice(
        duplicateData
      );
      setDuplicatingObjs(
        duplicatingObjs.filter(
          (obj) => obj?.id !== item.id && obj?.groupId !== item.groupId
        )
      );
      await getProductsByZoneIdForProductCatalogPrices(query);
      toast.success('Succès');
    } catch (error) {
      setDuplicatingObjs(
        duplicatingObjs.filter(
          (obj) => obj?.id !== item.id && obj?.groupId !== item.groupId
        )
      );
      toast.error('Erreur');
    }
  };

  const defaultColumns: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
    inputType?: string;
    required?: boolean;
    selectOptions?: any[];
  })[] = query.productType
    ? [
        {
          title: 'Produit',
          dataIndex: 'name',
          key: 'name',
          width: 250,
          fixed: 'left',
          editable: false
        },
        ...priceColumns,
        {
          title: 'Action',
          dataIndex: 'action',
          fixed: 'right',
          width: 120,
          render: (_, record: any) => {
            const result = record.ProductCatalogPrices.some(
              (productCatalogPrice: ProductCatalogPrice) =>
                (Number(productCatalogPrice.price) > 0 &&
                  productCatalogPrice.priceSubOptionId === null) ||
                (productCatalogPrice.price === null &&
                  productCatalogPrice.priceSubOptionId !== null)
            );
            if (!result) return <></>;
            return (
              result && (
                <Space size='middle'>
                  <Button
                    type='link'
                    icon={<DeleteOutlined />}
                    loading={
                      !!deletingObjs.find(
                        (item) =>
                          JSON.stringify(item) ===
                          JSON.stringify({
                            id: record.id,
                            groupId: record.groupId
                          })
                      )
                    }
                    className='datatable__action-destroy-button'
                    onClick={() =>
                      deleteProductCatalogPrice(
                        query.zone,
                        record.id,
                        record.groupId
                      )
                    }
                  />
                  <Button
                    type='link'
                    icon={<BlockOutlined />}
                    loading={
                      !!duplicatingObjs.find(
                        (item) =>
                          JSON.stringify(item) ===
                          JSON.stringify({
                            id: record.id,
                            groupId: record.groupId
                          })
                      )
                    }
                    className='datatable__action-copy-button'
                    onClick={() => duplicateProductCatalogPrice(record)}
                  />
                </Space>
              )
            );
          }
        }
      ]
    : [
        {
          title: 'Produit',
          dataIndex: 'name',
          key: 'name',
          width: 750,
          editable: false
        },
        {
          title: 'Action',
          dataIndex: 'action',
          width: 250
        }
      ];

  const updateProductCatalogPrice = async (
    productCatalogPriceId: number,
    data: any
  ) => {
    try {
      const updatedProductCatalogPrice =
        await productCatalogPriceService.updateProductCatalogPrice(
          productCatalogPriceId,
          data
        );
      return updatedProductCatalogPrice;
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const createProductCatalogPrice = async (data: any) => {
    try {
      const newProductCatalogPrice =
        await productCatalogPriceService.createProductCatalogPrice(data);
      return newProductCatalogPrice;
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const handleSave = async (row: RegionProduct | any) => {
    const cell = row[row.dataIndex];
    if (cell.productCatalogPriceId) {
      // update product catalog price
      const updatedProductCatalogPrice = await updateProductCatalogPrice(
        cell.productCatalogPriceId,
        {
          ...(cell.price ? { price: cell.price } : { price: null }),
          ...(cell.priceSubOptionId
            ? { priceSubOptionId: cell.priceSubOptionId }
            : { priceSubOptionId: null })
        }
      );
    } else {
      // create product catalog price
      const newProductCatalogPrice = await createProductCatalogPrice({
        catalogPriceId: row.dataIndex,
        zoneId: query.zone,
        productId: row.id,
        groupId: row.groupId,
        ...(cell.price ? { price: cell.price } : { price: null }),
        ...(cell.priceSubOptionId
          ? { priceSubOptionId: cell.priceSubOptionId }
          : { priceSubOptionId: null })
      });
    }
    await getProductsByZoneIdForProductCatalogPrices(query);
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: RegionProduct) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        inputType: col.inputType,
        selectOptions: col.selectOptions,
        required: col.required,
        handleSave
      })
    };
  });

  if (catalogPricesListLoading === 'pending') {
    return (
      <Spin
        spinning={true}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '100px'
        }}
        indicator={
          <GridLoader
            size={15}
            color='#A6C84D'
            cssOverride={{
              display: 'inline-block !important',
              margin: '0 auto',
              height: '100%',
              width: '100%',
              maxHeight: '100%'
            }}
            aria-label='Loading Spinner'
            data-testid='loader'
          />
        }></Spin>
    );
  }

  return (
    <>
      <Table
        components={{
          body: {
            row: RegionProductRow,
            cell: RegionProductCell
          }
        }}
        loading={{
          indicator: (
            <ScaleLoader
              color='#A6C84D'
              cssOverride={{
                display: 'inline-block !important',
                margin: '0 auto',
                left: 0,
                height: '100%',
                width: '100%'
              }}
              aria-label='Loading Spinner'
              data-testid='loader'
            />
          ),
          spinning: isLoadingList
        }}
        rowClassName={() => 'editable-row'}
        bordered
        dataSource={dataSource}
        columns={columns as ColumnTypes}
        pagination={false}
        className='region-product-list__datatable'
        scroll={{ x: 'calc(500px + 50%)' }}
      />
      <div className='pagination__pagination-items'>
        <span className='pagination__number-total'>
          Total {numberTotal} items
        </span>
        <Pagination
          showSizeChanger
          current={query.page}
          total={numberTotal}
          pageSize={query.limit}
          onChange={(page, limit) => onQueryChange({ page, limit })}
          className='pagination'
        />
      </div>
    </>
  );
};

export default RegionProductPriceList;
