import {
  createContext,
  FC,
  useContext,
  useEffect,
  useRef,
  useState,
} from 'react';
import { Form, FormInstance, Input, InputRef, Select } from 'antd';
import { RegionProduct } from 'models';
import { BaseSelectRef } from 'types';
import { ClockLoader } from 'react-spinners';

interface EditableCellProps {
  title: React.ReactNode;
  editable: boolean;
  children: React.ReactNode;
  inputType: string;
  required: boolean;
  selectOptions: any[];
  dataIndex: keyof RegionProduct;
  record: RegionProduct;
  handleSave: (record: RegionProduct) => void;
}

const EditableContext = createContext<FormInstance<any> | null>(null);

export const RegionProductRow: FC<{ index: number }> = ({
  index,
  ...props
}) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

const RegionProductCell: FC<EditableCellProps> = ({
  title,
  editable,
  required,
  children,
  dataIndex,
  record,
  inputType,
  selectOptions = [],
  handleSave,
  ...restProps
}) => {
  const [editing, setEditing] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [isOpenSelect, setIsOpenSelect] = useState<boolean>(true);
  const inputRef = useRef<InputRef>(null);
  const numberRef = useRef<HTMLInputElement>(null);
  const selectRef = useRef<BaseSelectRef>(null);
  const form = useContext(EditableContext)!;

  const handleDropdownVisibleChange = (open: boolean) => {
    if (isOpenSelect && open) {
      setIsOpenSelect(false);
    } else {
      setIsOpenSelect(open);
    }
  };

  useEffect(() => {
    if (editing) {
      if (inputRef.current) inputRef.current!.focus();
      if (numberRef.current) numberRef.current!.focus();
      if (selectRef.current) selectRef.current!.focus();
    }
  }, [editing]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({
      ...(record[dataIndex]?.price || inputType === 'currency'
        ? {
            [dataIndex]: record[dataIndex]?.price || '',
          }
        : {
            [dataIndex]: record[dataIndex]?.priceSubOptionId || null,
          }),
      productCatalogPriceId: record[dataIndex]?.productCatalogPriceId || null,
    });
    setIsOpenSelect(true);
  };

  const save = async () => {
    try {
      const values = await form.validateFields([
        dataIndex,
        'productCatalogPriceId',
      ]);
      if (inputType === 'select') {
        values[dataIndex] = {
          priceSubOptionId: parseInt(values[dataIndex]),
          productCatalogPriceId: values['productCatalogPriceId'],
        };
      }
      if (inputType === 'currency') {
        values[dataIndex] = {
          price: parseFloat(values[dataIndex]).toFixed(2),
          productCatalogPriceId: values['productCatalogPriceId'],
        };
      }
      toggleEdit();
      if (form.isFieldTouched(dataIndex)) {
        setLoading(true);
        await handleSave({ ...record, ...values, dataIndex });
        setLoading(false);
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };
  let inputNode = <Input onPressEnter={save} onBlur={save} />;
  switch (inputType) {
    case 'currency':
      inputNode = (
        <Input ref={inputRef} onPressEnter={save} onBlur={save} suffix={'€'} />
      );
      break;
    case 'select':
      inputNode = (
        <Select
          onChange={save}
          ref={selectRef}
          value={record[dataIndex]?.priceSubOptionId}
          placeholder='Sélectionner'
          style={{ width: '140px' }}
          open={isOpenSelect}
          onDropdownVisibleChange={handleDropdownVisibleChange}
          onBlur={save}
        >
          {selectOptions.map((option) => (
            <Select.Option key={option.id} value={option.id}>
              {option.name}
            </Select.Option>
          ))}
        </Select>
      );
      break;
    default:
      inputNode = <Input ref={inputRef} onPressEnter={save} onBlur={save} />;
  }

  let childNode = children;

  if (editable) {
    childNode = editing ? (
      <Form.Item
        style={{ margin: 0 }}
        help={null}
        name={dataIndex}
        rules={
          record[dataIndex] && [
            { required },
            ...(inputType === 'currency'
              ? [
                  () => ({
                    validator(_: any, value: any) {
                      if (!value) {
                        return Promise.reject();
                      }
                      if (isNaN(value)) {
                        return Promise.reject();
                      }
                      return Promise.resolve();
                    },
                  }),
                ]
              : []),
          ]
        }
      >
        {inputNode}
      </Form.Item>
    ) : (
      <div
        className='editable-cell-value-wrap'
        style={{
          display: 'flex',
          justifyContent: 'left',
          alignItems: 'center',
        }}
        onClick={toggleEdit}
      >
        <div style={{ marginRight: '15px' }}>{children}</div>
        <div>
          <ClockLoader size={25} color='#A6C84D' loading={loading} />
        </div>
      </div>
    );
  }

  return <td {...restProps}>{childNode}</td>;
};

export default RegionProductCell;
