import { Table, Pagination, Spin } from 'antd';
import {
  Option,
  Product,
  ProductCatalogOptionPrice,
  RegionProduct
} from 'models';
import { useEffect, useState, useCallback } from 'react';
import RegionRegulProductCell, {
  RegionRegulProductRow
} from './RegionRegulProductCell';
import { useAppDispatch, useAppSelector } from 'store';
import { Loading } from 'types';
import { GridLoader, ScaleLoader } from 'react-spinners';
import {
  fetchProductsByZoneIdForProductCatalogOptionPrices,
  selectProductsByZoneIdForProductCatalogOptionPrices,
  selectProductsByZoneIdForProductCatalogOptionPricesTotal
} from 'store/slices/product.slices';
import { toast } from 'react-toastify';
import { productCatalogOptionPriceService } from 'services';

type EditableTableProps = Parameters<typeof Table>[0];
type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

const RegionRegulProductPriceList = ({
  regulOptionsList,
  regulOptionsListLoading,
  query,
  onQueryChange
}: {
  regulOptionsList: Option[] | null;
  regulOptionsListLoading: Loading;
  query: any;
  onQueryChange: Function;
}) => {
  const dispatch = useAppDispatch();
  const [optionColumns, setOptionColumns] = useState<any[]>([]);
  const [isLoadingList, setIsLoadingList] = useState<boolean>(true);
  const [numberTotal, setNumberTotal] = useState<number>(0);
  const [dataSource, setDataSource] = useState<RegionProduct[]>([]);
  const productsByZoneId = useAppSelector(
    selectProductsByZoneIdForProductCatalogOptionPrices
  );
  const totalItems = useAppSelector(
    selectProductsByZoneIdForProductCatalogOptionPricesTotal
  );

  const getProductsByZoneIdForProductCatalogOptionPrices = useCallback(
    async (query: any) => {
      await dispatch(
        fetchProductsByZoneIdForProductCatalogOptionPrices({
          zoneId: query.zone,
          query: {
            productTypeId: !query.productType ? 'null' : query.productType,
            include: true,
            page: query.page,
            limit: query.limit,
            search: query.keyword
          }
        })
      )
        .unwrap()
        .catch((error) => {
          toast.error('Erreur');
        });
      setTimeout(() => {
        setIsLoadingList(false);
      });
    },
    [dispatch]
  );

  useEffect(() => {
    const newProducts: Product[] = productsByZoneId.map((item) => {
      const product: any = {
        id: item.id,
        productTypeId: item.productTypeId,
        name: item.name,
        ProductCatalogOptionPrices: item.ProductCatalogOptionPrices
      };

      item?.ProductCatalogOptionPrices?.forEach(
        (price: ProductCatalogOptionPrice) => {
          product[price.optionId] = {
            ...(price?.price ? { price: price.price } : { price: null }),
            productCatalogOptionPriceId: price.id
          };
        }
      );

      return product;
    });
    setDataSource(newProducts);
    setNumberTotal(totalItems);
  }, [productsByZoneId]);

  useEffect(() => {
    const newOptionColumns = !regulOptionsList
      ? []
      : regulOptionsList.map((regulOption) => ({
          title: regulOption.name,
          dataIndex: regulOption.id,
          key: regulOption.id,
          width: 220,
          editable: true,
          required: true,
          inputType: 'currency',
          render: (value: any, record: any) => {
            if (query.zone) {
              if (!value) {
                return (
                  <div className='region-product-list__prix-forfait-tonne'>
                    {(Math.round(0 * 100) / 100).toFixed(2)}
                    <span className='region-product-list__prix-ht-unit'>€</span>
                  </div>
                );
              }
              return (
                <div className='region-product-list__prix-forfait-tonne'>
                  {(Math.round(value.price * 100) / 100).toFixed(2)}
                  <span className='region-product-list__prix-ht-unit'>€</span>
                </div>
              );
            }
          }
        }));
    setOptionColumns(newOptionColumns);
  }, [regulOptionsList, regulOptionsListLoading, query]);

  useEffect(() => {
    if (query.zone) {
      setIsLoadingList(true);
      getProductsByZoneIdForProductCatalogOptionPrices(query);
    }
    if (!query.zone) {
      setDataSource([]);
      setNumberTotal(0);
      setIsLoadingList(false);
    }
  }, [query]);

  const defaultColumns: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
    inputType?: string;
    required?: boolean;
    selectOptions?: any[];
  })[] = query.productType
    ? [
        {
          title: 'Produit',
          dataIndex: 'name',
          key: 'name',
          width: 250,
          fixed: 'left',
          editable: false
        },
        ...optionColumns
      ]
    : [
        {
          title: 'Produit',
          dataIndex: 'name',
          key: 'name',
          width: 400,
          editable: false
        }
      ];

  const updateProductCatalogOptionPrice = async (
    productCatalogOptionPriceId: number,
    data: any
  ) => {
    try {
      const updatedProductCatalogOptionPrice =
        await productCatalogOptionPriceService.updateProductCatalogOptionPrice(
          productCatalogOptionPriceId,
          data
        );
      return updatedProductCatalogOptionPrice;
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const createProductCatalogOptionPrice = async (data: any) => {
    try {
      const newProductCatalogOptionPrice =
        await productCatalogOptionPriceService.createProductCatalogOptionPrice(
          data
        );
      return newProductCatalogOptionPrice;
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const handleSave = async (row: RegionProduct | any) => {
    const cell = row[row.dataIndex];
    if (cell.productCatalogOptionPriceId) {
      // update product catalog price
      const updatedProductCatalogOptionPrice =
        await updateProductCatalogOptionPrice(
          cell.productCatalogOptionPriceId,
          {
            ...(cell.price ? { price: cell.price } : { price: null })
          }
        );
    } else {
      // create product catalog price
      const newProductCatalogOptionPrice =
        await createProductCatalogOptionPrice({
          optionId: row.dataIndex,
          zoneId: query.zone,
          productId: row.id,
          ...(cell.price ? { price: cell.price } : { price: null })
        });
    }
    await getProductsByZoneIdForProductCatalogOptionPrices(query);
  };

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: RegionProduct) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        inputType: col.inputType,
        selectOptions: col.selectOptions,
        required: col.required,
        handleSave
      })
    };
  });

  if (regulOptionsListLoading === 'pending') {
    return (
      <Spin
        spinning={true}
        style={{
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          marginTop: '100px'
        }}
        indicator={
          <GridLoader
            size={15}
            color='#A6C84D'
            cssOverride={{
              display: 'inline-block !important',
              margin: '0 auto',
              height: '100%',
              width: '100%',
              maxHeight: '100%'
            }}
            aria-label='Loading Spinner'
            data-testid='loader'
          />
        }></Spin>
    );
  }

  return (
    <>
      <Table
        components={{
          body: {
            row: RegionRegulProductRow,
            cell: RegionRegulProductCell
          }
        }}
        loading={{
          indicator: (
            <ScaleLoader
              color='#A6C84D'
              cssOverride={{
                display: 'inline-block !important',
                margin: '0 auto',
                left: 0,
                height: '100%',
                width: '100%'
              }}
              aria-label='Loading Spinner'
              data-testid='loader'
            />
          ),
          spinning: isLoadingList
        }}
        rowClassName={() => 'editable-row'}
        bordered
        dataSource={dataSource}
        columns={columns as ColumnTypes}
        pagination={false}
        className='region-product-list__datatable'
        scroll={{ x: 'calc(500px + 50%)' }}
      />
      <div className='pagination__pagination-items'>
        <span className='pagination__number-total'>
          Total {numberTotal} items
        </span>
        <Pagination
          showSizeChanger
          current={query.page}
          total={numberTotal}
          pageSize={query.limit}
          onChange={(page, limit) => onQueryChange({ page, limit })}
          className='pagination'
        />
      </div>
    </>
  );
};
export default RegionRegulProductPriceList;
