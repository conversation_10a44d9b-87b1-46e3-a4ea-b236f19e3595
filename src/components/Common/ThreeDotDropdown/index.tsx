import { MoreOutlined } from '@ant-design/icons';
import { Dropdown, Space } from 'antd';
import type { MenuProps } from 'antd';

const ThreeDotDropdown = ({
  items,
  selectedKeys,
}: {
  items: MenuProps['items'];
  selectedKeys: string[];
}) => {
  return (
    <Dropdown
      menu={{ items, selectedKeys }}
      trigger={['click']}
      className='three-dot-dropdown'
    >
      <Space>
        <MoreOutlined className='three-dot-dropdown__more-icon' />
      </Space>
    </Dropdown>
  );
};
export default ThreeDotDropdown;
