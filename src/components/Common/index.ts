import Spinner from './Spinner';
import PageTitle from './PageTitle';
import MainTitle from './MainTitle';
import OptionCard from './OptionCard';
import CheckButton from './CheckButton';
import TrashButton from './TrashButton';
import CustomTextArea from './CustomTextArea';
import ButtonAdd from './ButtonAdd';
import AddressInput from './AddressInput';
import ColorPickerInput from './ColorPickerInput';
import { ButtonEdit, ButtonEditSave } from './ButtonEdit';
import { ZoneItem } from 'components/Region';
import ThreeDotDropdown from './ThreeDotDropdown/index';
import AddProduitsModal from './AddProduitsModal';
import UrbanCenterModal from './UrbanCenterModal';
import YearPriceCopyModal from './YearPriceCopyModal';
import CopyConfirmationModal from './YearPriceCopyModal/CopyConfirmationModal';
import { EditableCatalogPriceCell, EditableCatalogPriceRow } from './CatalogPrice/CatalogPriceCellTable';
import CatalogPriceActions from './CatalogPrice/CatalogPriceActions';
import CatalogPriceProductList from './CatalogPrice/CatalogPriceProductList';
import CatalogPriceProduitPopup from './CatalogPrice/CatalogPriceProduitPopup';
import CatalogPriceTable from './CatalogPrice/CatalogPriceTable';
import CatalogPriceTabItem from './CatalogPrice/CatalogPriceTabItem';
import CatalogPriceTab from './CatalogPrice/CatalogPriceTab';
import CloseIconItem from './CatalogPrice/CloseIconItem';
import ProduitsDuplicateModal from './ProduitsDuplicateModal';
import ValidateAndCreateOrderConfirmModal from './ValidateAndCreateOrderConfirmModal';
import ButtonSync from './ButtonSync';
import { ParameterTarifItem } from './ParameterTarifItem/ParameterTarifItem';

export {
  Spinner,
  PageTitle,
  MainTitle,
  OptionCard,
  ButtonAdd,
  ButtonEdit,
  CheckButton,
  TrashButton,
  AddressInput,
  CustomTextArea,
  ColorPickerInput,
  ZoneItem,
  ThreeDotDropdown,
  AddProduitsModal,
  UrbanCenterModal,
  YearPriceCopyModal,
  CopyConfirmationModal,
  EditableCatalogPriceCell,
  EditableCatalogPriceRow,
  CatalogPriceActions,
  CatalogPriceProductList,
  CatalogPriceProduitPopup,
  CatalogPriceTable,
  CatalogPriceTabItem,
  CatalogPriceTab,
  CloseIconItem,
  ProduitsDuplicateModal,
  ButtonEditSave,
  ValidateAndCreateOrderConfirmModal,
  ButtonSync,
  ParameterTarifItem,
};
