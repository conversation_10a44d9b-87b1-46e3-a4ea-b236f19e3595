import { Fragment } from 'react';
import { Typography } from 'antd';
import { ParameterTarifLine } from 'components/Estimate/ProductCell/PrestationCell';
import { PRICE_TYPE_LOGICS, PRICE_TYPES } from 'utils/constant';
const { Text } = Typography;
export const ParameterTarifItem = ({ parameterTarif }: { parameterTarif: ParameterTarifLine }) => {
  const priceLogicKey = parameterTarif?.price?.PriceTypeLogic?.key;
  const isIndicative = priceLogicKey === PRICE_TYPE_LOGICS.indicative_purpose;
  // Check if there's at least one number input with a non-zero default value
  const hasValidNumberInput = parameterTarif?.priceOptions?.some(
    (option) => option?.PriceType?.key === PRICE_TYPES.numberInput && option?.defaultValue !== '0',
  );
  // We only display this tarif if:
  // - It's not indicative_purpose
  // - OR it is indicative_purpose but has at least one valid number input
  const shouldDisplay = !isIndicative || (isIndicative && hasValidNumberInput);
  if (!shouldDisplay) return null;
  return (
    <Fragment key={parameterTarif?.price?.id}>
      {/* Display the main price name */}
      <div className='mb-2'>
        <Text strong>{parameterTarif?.price?.name}</Text>
      </div>
      {/* Loop through each price option */}
      {parameterTarif?.priceOptions?.map((priceOption) => {
        // Skip option if it's a number input with value '0' under indicative_purpose
        const isZeroIndicativeNumberInput =
          isIndicative && priceOption?.PriceType?.key === PRICE_TYPES.numberInput && priceOption?.defaultValue === '0';
        if (isZeroIndicativeNumberInput) return null;
        // Find the selected sub-option based on defaultValue
        const selectedSubOption = priceOption?.PriceSubOptions?.find(
          (sub) => sub?.id === Number(priceOption?.defaultValue),
        );
        return (
          <div key={priceOption?.id} className='ml-4'>
            <div className='mb-2'>
              {/* Display option name and its selected value */}
              <Text strong>{priceOption?.name}: </Text>
              <Text>
                {selectedSubOption?.name ?? priceOption?.defaultValue} {priceOption?.unit}
              </Text>
            </div>
          </div>
        );
      })}
      {/* Display the final label and tarif value */}
      <div className='ml-4'>
        <div className='mb-2'>
          <Text strong>{parameterTarif?.price?.labelName}: </Text>
          <Text>
            {parameterTarif?.value} €/{' '}
            {(() => {
              const firstOption = parameterTarif?.priceOptions?.[0];
              // Check if the first option has sub-options to show the selected one
              if (firstOption?.PriceSubOptions && firstOption?.PriceSubOptions?.length > 0) {
                const selected = firstOption?.PriceSubOptions?.find(
                  (sub) => sub?.id === Number(firstOption?.defaultValue),
                );
                return selected?.name ?? '';
              }
              // Fallback to unit if no sub-options are available
              return firstOption?.unit ?? '';
            })()}
          </Text>
        </div>
      </div>
    </Fragment>
  );
};
