import { But<PERSON>, Col, Flex, Modal, Row, Form, Input, Select, DatePicker, Radio, FormInstance } from 'antd';
import locale from 'antd/es/date-picker/locale/fr_FR';
import { OrderPrestataireLine } from '../../../Logistique/LogistiqueOrderDetail/OrderPrestataireCellTable';
import {
  useBenneWasteManagers,
  useBenneWasteManagerQuery,
  usePeriodTimeBenne,
  usePeriodTimeBenneQuery,
  useBennePointPProducts,
  useBennePointPProductQuery,
  // useGlobalGarbagePrices,
  // useGlobalGarbagePriceQuery,
  // useGarbageUnitsPriceQuery,
  // useGarbageUnitsPrices,
} from 'hooks';
import { BIGBAG_ACTIONS, BO_TIME_TYPES, LIST_TIME_FROM_00_TO_23_30 } from 'utils/constant';
import { Documents } from 'models';
import { useState } from 'react';
import { TimeHour } from '../../../../models/bo';
import dayjs from 'dayjs';

const { TextArea } = Input;
const BigbagNouvelleCommamdeModal = ({
  catalogPriceLine,
  data,
  isOpenModal,
  onCancel,
  onConfirmation,
  form,
}: {
  data: Documents & { serviceProviderId: number; service: string };
  catalogPriceLine: OrderPrestataireLine | null | undefined;
  isOpenModal: boolean;
  onCancel: () => void;
  onConfirmation: () => void;
  form: FormInstance;
}) => {
  const [benneWasteManagerQuery] = useBenneWasteManagerQuery({
    supplier_id: data.serviceProviderId,
  });
  const [benneWasteManagers] = useBenneWasteManagers(benneWasteManagerQuery);
  console.log(benneWasteManagers);

  const [periodTimeBenneQuery] = usePeriodTimeBenneQuery({
    service: data.service,
  });
  const [{ periodsDepositTimeBenne }] = usePeriodTimeBenne(periodTimeBenneQuery);

  const [bennePointPProductsQuery] = useBennePointPProductQuery({
    type: 'BIG_BAG',
    service: data.service,
  });
  const [bennePointPProducts] = useBennePointPProducts(bennePointPProductsQuery);

  // const [globalGarbagePriceQuery] = useGlobalGarbagePriceQuery({
  //   type: 'BIG_BAG',
  //   service: data.service,
  // });
  // const [{ globalGarbagePricing }] = useGlobalGarbagePrices(globalGarbagePriceQuery);

  // const [garbageUnitsPriceQuery] = useGarbageUnitsPriceQuery();
  // const [garbageUnits] = useGarbageUnitsPrices(garbageUnitsPriceQuery);
  const [pickupTimeType, setPickupTimeType] = useState<string>(BO_TIME_TYPES.HOUR);
  const timeHours: TimeHour[] = LIST_TIME_FROM_00_TO_23_30;

  const handleCancel = () => {
    onCancel();
  };

  const handleTimeTypeChange = (value: string) => {
    setPickupTimeType(value);
  };

  return (
    <>
      <Modal
        title={`NOUVELLE COMMANDE`}
        open={isOpenModal}
        maskClosable={false}
        onCancel={handleCancel}
        style={{ left: 264, margin: 0 }}
        width={1200}
        footer={[
          <>
            <Flex justify='center' align='center' style={{ width: '100%' }}>
              <Button key='annuler' onClick={handleCancel} className='ant-modal-content__btn-color-dark'>
                Annuler
              </Button>
              <Button key='ajouter' type='primary' className='ant-modal-content__add-btn' onClick={onConfirmation}>
                Confirmer
              </Button>
            </Flex>
          </>,
        ]}
      >
        <Form
          form={form}
          layout='vertical'
          autoComplete='off'
          style={{ width: '100%' }}
          initialValues={{
            pickupTimeHourKey: timeHours[0].key,
            pickupTimePeriod: periodsDepositTimeBenne[0]?.key_value,
            pickupDate: dayjs(),
            quantity: 1,
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Prestataire' name='truckDriverName'>
                <Input placeholder='Prestataire' defaultValue={catalogPriceLine?.serviceProviderName} disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Déchèterie' name='pupId' rules={[{ required: true, message: 'Ce champ est requis!' }]}>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={benneWasteManagers.map((item) => ({
                    label: item.name,
                    value: item.id,
                  }))}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Date d’enlèvement' name='pickupDate'>
                <DatePicker
                  placeholder='Sélectionner une date'
                  locale={locale}
                  style={{ width: '100%' }}
                  format='DD/MM/YYYY'
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item
                label={pickupTimeType === BO_TIME_TYPES.HOUR ? 'Heure' : 'Période'}
                name={pickupTimeType === BO_TIME_TYPES.HOUR ? 'pickupTimeHourKey' : 'pickupTimePeriod'}
              >
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={
                    pickupTimeType === BO_TIME_TYPES.HOUR
                      ? timeHours.map((item) => ({
                          value: item.key,
                          label: item.name,
                        }))
                      : periodsDepositTimeBenne.map((item) => ({
                          label: item.name,
                          value: item.key_value,
                        }))
                  }
                ></Select>
              </Form.Item>
            </Col>
            <Col span={3} style={{ alignContent: 'center' }}>
              <Form.Item label='' name='pickupTimeType' className='mb-0' initialValue={pickupTimeType}>
                <Radio.Group
                  // value={pickupTimeType}
                  onChange={(e) => handleTimeTypeChange(e.target.value)}
                  options={[
                    { value: BO_TIME_TYPES.HOUR, label: 'Heure' },
                    { value: BO_TIME_TYPES.PERIOD, label: 'Période' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Action' name='pickupAction' initialValue={BIGBAG_ACTIONS[0].key}>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={BIGBAG_ACTIONS.map((item) => ({
                    label: item.name,
                    value: item.key,
                  }))}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Client' initialValue={data?.Contact?.name} name='clientName'>
                <Input placeholder='Client' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Tél. Contact' initialValue={data?.contactSurPlace} name='phone'>
                <Input placeholder='Tel. contact' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='CDE Zoho' initialValue={data?.cdeZoho} name='cdeZoho'>
                <Input placeholder='CDE Zoho' disabled />
              </Form.Item>
            </Col>
            {/* <Col span={6}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0'>
                  <Checkbox>Chargement Express</Checkbox>
                </Form.Item>
              </Flex>
            </Col> */}
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item label='Départ - Adressse Chantier' initialValue={data?.siteAddressFull} name='siteAddress'>
                <Input placeholder='Départ - Adressse Chantier' disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='Type Produit'
                name='productTypeId'
                rules={[{ required: true, message: 'Vous devez sélectionner une taille camio!' }]}
              >
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={bennePointPProducts.map((item) => ({
                    value: item.id,
                    label: item.name,
                  }))}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='Quantité'
                name='quantity'
                rules={[
                  { required: true, message: 'Ce champ est requis!' },
                  {
                    validator: (_, value) =>
                      value > 0 ? Promise.resolve() : Promise.reject(new Error('La quantité doit être supérieure à 0')),
                  },
                ]}
              >
                <Input placeholder='Quantité' type='number' />
              </Form.Item>
            </Col>
          </Row>
          {/*<Row gutter={[16, 16]}>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Type de déchets à récupérer' name='globalGarbagePricingId'>*/}
          {/*      <Select*/}
          {/*        placeholder='Sélectionner'*/}
          {/*        onChange={(value) => {*/}
          {/*          console.log(value);*/}
          {/*        }}*/}
          {/*        showSearch*/}
          {/*        filterOption={(input, option) =>*/}
          {/*          ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())*/}
          {/*        }*/}
          {/*        options={globalGarbagePricing?.map((item) => ({*/}
          {/*          value: item.id,*/}
          {/*          label: item.garbage_type.name,*/}
          {/*        }))}*/}
          {/*        // loading={loading}*/}
          {/*      ></Select>*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Quantité' name='garbageQuantity'>*/}
          {/*      <Input placeholder='Quantité' type='number' />*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Unité' name='garbageUnit'>*/}
          {/*      <Select*/}
          {/*        placeholder='Unité'*/}
          {/*        onChange={(value) => {*/}
          {/*          console.log(value);*/}
          {/*        }}*/}
          {/*        showSearch*/}
          {/*        filterOption={(input, option) =>*/}
          {/*          ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())*/}
          {/*        }*/}
          {/*        options={garbageUnits.map((item) => ({*/}
          {/*          value: item.name,*/}
          {/*          label: item.name,*/}
          {/*        }))}*/}
          {/*        // loading={loading}*/}
          {/*      ></Select>*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}

          {/*  <Col span={3}>*/}
          {/*    <Flex style={{ height: '100%', alignItems: 'center' }}>*/}
          {/*      <Form.Item label='' className='mb-0'>*/}
          {/*        <Checkbox>Multipesées</Checkbox>*/}
          {/*      </Form.Item>*/}
          {/*    </Flex>*/}
          {/*  </Col>*/}
          {/*</Row>*/}
          {/* <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item label=''>
                <Button type='text' className='btn-add__ajouter-btn'>
                  <PlusCircleOutlined />
                  Ajouter un type de déchets à récupérer
                </Button>
              </Form.Item>
            </Col>
          </Row> */}
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item label='Commentaire' name='comment'>
                <TextArea rows={3} placeholder='Commentaire' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Commentaire Benneur' name='commentBenneur'>
                <TextArea rows={3} placeholder='Commentaire Benneur' />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default BigbagNouvelleCommamdeModal;
