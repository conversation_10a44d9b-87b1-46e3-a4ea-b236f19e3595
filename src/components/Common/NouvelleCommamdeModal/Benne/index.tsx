import { <PERSON>ton, Col, Flex, Modal, Row, Form, Input, Select, Checkbox, DatePicker, FormInstance, Radio } from 'antd';
import locale from 'antd/es/date-picker/locale/fr_FR';
import { OrderPrestataireLine } from '../../../Logistique/LogistiqueOrderDetail/OrderPrestataireCellTable';
import { Contact } from '../../../../models';
import { useEffect, useState } from 'react';
import { <PERSON>nePointp, BenneWasteManager, TimeHour, TimePeriod } from '../../../../models/bo';
import { boService } from '../../../../services';
import { LIST_ACTION_BO_BENNE_ORDER, BO_TIME_TYPES, LIST_TIME_FROM_00_TO_23_30 } from '../../../../utils/constant';
import dayjs from 'dayjs';

const { TextArea } = Input;
const BenneNouvelleCommamdeModal = ({
  catalogPriceLine,
  isOpenModal,
  onCancel,
  onConfirmation,
  form,
  client,
  phone,
  orderCode,
  siteAddress,
}: {
  catalogPriceLine: OrderPrestataireLine | null | undefined;
  isOpenModal: boolean;
  onCancel: () => void;
  onConfirmation: () => void;
  form: FormInstance;
  client: Contact | null;
  phone: string;
  orderCode: string;
  siteAddress: string;
}) => {
  const [pups, setPups] = useState<BenneWasteManager[]>([]);
  const [timePeriods, setTimePeriods] = useState<TimePeriod[]>([]);
  const [typeBenneProducts, setTypeBenneProducts] = useState<BennePointp[]>([]);
  // const [typesDechets, setTypesDechets] = useState<GarbagePricing[]>([]);
  // const [garbageUnits, setGarbageUnits] = useState<GarbageUnit[]>([]);
  const [loading, setLoading] = useState(false);
  const [depositTimeType, setDepositTimeType] = useState<string>(BO_TIME_TYPES.HOUR);
  const [pickupTimeType, setPickupTimeType] = useState<string>(BO_TIME_TYPES.HOUR);
  const timeHours: TimeHour[] = LIST_TIME_FROM_00_TO_23_30;
  const [isChargementExpress, setIsChargementExpress] = useState<boolean>(false);

  console.log(client);

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Call APIs parallel
        const [
          benneSupplierRes,
          // globalGarbagePricingsRes,
          productBennesRes,
          // garbageUnitsRes,
          timePeriodsRes,
        ] = await Promise.all([
          boService.getBenneSupplier(catalogPriceLine?.serviceProviderId),
          // boService.getGlobalGarbagePricings(),
          boService.getProductBenneTypes(),
          // boService.getGarbageUnits(),
          boService.getTimePeriods(),
        ]);

        // const globalGarbagePricingg = globalGarbagePricingsRes.globalGarbagePricing;
        console.log(timePeriodsRes);
        setPups(benneSupplierRes?.benneSupplier?.benne_waste_managers);
        setTimePeriods(timePeriodsRes.list_periods_time);
        setTypeBenneProducts(productBennesRes.listProductEcodrop);
        // setTypesDechets(globalGarbagePricingg);
        // setGarbageUnits(garbageUnitsRes.unit);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (isOpenModal) {
      fetchData();
    }
  }, [isOpenModal]);

  const handleCancel = () => {
    onCancel();
  };

  const handleDecheterieChange = (value: number) => {
    const decheterie = pups.find((pup) => pup.id === value);
    form.setFieldsValue({
      pupFullAddress: decheterie ? `${decheterie.address}, ${decheterie.zip_code} ${decheterie.city}` : '',
    });
  };

  const handleDepositTimeTypeChange = (value: string) => {
    setDepositTimeType(value);
  };

  const handlePickupTimeTypeChange = (value: string) => {
    setPickupTimeType(value);
  };

  const handleCheckChargementExpress = (value: boolean) => {
    setIsChargementExpress(value);
    form.setFieldsValue({
      pickupDate: undefined,
      pickupTimeHourKey: undefined,
      pickupTimeType: undefined,
      pickupAction: undefined,
    });
  };

  return (
    <>
      <Modal
        title={`NOUVELLE COMMANDE`}
        open={isOpenModal}
        maskClosable={false}
        onCancel={handleCancel}
        style={{ left: 264, margin: 0 }}
        width={1200}
        footer={[
          <>
            <Flex justify='center' align='center' style={{ width: '100%' }}>
              <Button key='annuler' onClick={handleCancel} className='ant-modal-content__btn-color-dark'>
                Annuler
              </Button>
              <Button key='ajouter' type='primary' className='ant-modal-content__add-btn' onClick={onConfirmation}>
                Confirmer
              </Button>
            </Flex>
          </>,
        ]}
      >
        <Form
          form={form}
          layout='vertical'
          autoComplete='off'
          style={{ width: '100%' }}
          initialValues={{
            client: client?.name,
            phone: phone,
            cde: orderCode,
            siteAddress: siteAddress,
            depositTimeHourKey: timeHours[0]?.key,
            depositTimePeriod: timePeriods[0]?.key_value,
            action: LIST_ACTION_BO_BENNE_ORDER[0].key,
            pickupAction: LIST_ACTION_BO_BENNE_ORDER[0].key,
            depositDate: dayjs(),
            isChargementExpress: false,
            quantity: 1,
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Prestataire' name='truckDriverName'>
                <Input placeholder='Prestataire' defaultValue={catalogPriceLine?.serviceProviderName} disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Déchèterie' name='pupId' rules={[{ required: true, message: 'Ce champ est requis!' }]}>
                <Select
                  placeholder='Sélectionner'
                  onChange={handleDecheterieChange}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={pups.map((item) => ({
                    value: item.id,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={7}>
              <Form.Item label='Date de dépôt sur Chantier' name='depositDate'>
                <DatePicker
                  placeholder='Sélectionner une date'
                  locale={locale}
                  style={{ width: '100%' }}
                  disabled={loading}
                  format='DD/MM/YYYY'
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item
                label={depositTimeType === BO_TIME_TYPES.HOUR ? 'Heure' : 'Période'}
                name={depositTimeType === BO_TIME_TYPES.HOUR ? 'depositTimeHourKey' : 'depositTimePeriod'}
              >
                <Select
                  placeholder='Sélectionner'
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={
                    depositTimeType === BO_TIME_TYPES.HOUR
                      ? timeHours.map((item) => ({
                          value: item.key,
                          label: item.name,
                        }))
                      : timePeriods.map((item) => ({
                          value: item.key_value,
                          label: item.name,
                        }))
                  }
                  loading={loading}
                />
              </Form.Item>
            </Col>
            <Col span={3} style={{ alignContent: 'center' }}>
              <Form.Item label='' className='mb-0' name='depositTimeType' initialValue={depositTimeType}>
                <Radio.Group
                  value={depositTimeType}
                  onChange={(e) => handleDepositTimeTypeChange(e.target.value)}
                  options={[
                    { value: BO_TIME_TYPES.HOUR, label: 'Heure' },
                    { value: BO_TIME_TYPES.PERIOD, label: 'Période' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Action' name='action'>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_ACTION_BO_BENNE_ORDER.map((item: { key: string; name: string }) => ({
                    value: item.key,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={3}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0' name='isChargementExpress' valuePropName='checked'>
                  <Checkbox onChange={(e) => handleCheckChargementExpress(e.target.checked)}>
                    Chargement Express
                  </Checkbox>
                </Form.Item>
              </Flex>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={7}>
              <Form.Item label='Date d’enlèvement' name='pickupDate'>
                <DatePicker
                  placeholder='Sélectionner une date'
                  locale={locale}
                  style={{ width: '100%' }}
                  disabled={loading || isChargementExpress}
                  format='DD/MM/YYYY'
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item
                label={pickupTimeType === BO_TIME_TYPES.HOUR ? 'Heure' : 'Période'}
                name={pickupTimeType === BO_TIME_TYPES.HOUR ? 'pickupTimeHourKey' : 'pickupTimePeriod'}
              >
                <Select
                  placeholder='Sélectionner'
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={
                    pickupTimeType === BO_TIME_TYPES.HOUR
                      ? timeHours.map((item) => ({
                          value: item.key,
                          label: item.name,
                        }))
                      : timePeriods.map((item) => ({
                          value: item.key_value,
                          label: item.name,
                        }))
                  }
                  loading={loading}
                  disabled={isChargementExpress}
                />
              </Form.Item>
            </Col>
            <Col span={3} style={{ alignContent: 'center' }}>
              <Form.Item label='' className='mb-0' name='pickupTimeType' initialValue={pickupTimeType}>
                <Radio.Group
                  value={pickupTimeType}
                  onChange={(e) => handlePickupTimeTypeChange(e.target.value)}
                  options={[
                    { value: BO_TIME_TYPES.HOUR, label: 'Heure' },
                    { value: BO_TIME_TYPES.PERIOD, label: 'Période' },
                  ]}
                  disabled={isChargementExpress}
                />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Action' name='pickupAction'>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_ACTION_BO_BENNE_ORDER.map((item: { key: string; name: string }) => ({
                    value: item.key,
                    label: item.name,
                  }))}
                  loading={loading}
                  disabled={isChargementExpress}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item initialValue={client?.name ?? ''} label='Client' name='clientName'>
                <Input value={client?.name ?? ''} placeholder='Client' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item initialValue={phone} label='Tél. Contact' name='phone'>
                <Input value={phone} placeholder='Tel. contact' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item initialValue={orderCode} label='CDE Zoho' name='cdeZoho'>
                <Input value={orderCode} placeholder='CDE Zoho' disabled />
              </Form.Item>
            </Col>
            {/* <Col span={6}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0'>
                  <Checkbox>Chargement Express</Checkbox>
                </Form.Item>
              </Flex>
            </Col> */}
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item label='Départ - Adressse Chantier' name='siteAddress'>
                <Input placeholder='Départ - Adressse Chantier' disabled />
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item
                label='Type Produit'
                name='productTypeId'
                rules={[{ required: true, message: 'Vous devez sélectionner une taille camio!' }]}
              >
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={typeBenneProducts.map((item) => ({
                    value: item.id,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={6}>
              <Form.Item label='Quantité' name='quantity'>
                <Input placeholder='Quantité' type='number' />
              </Form.Item>
            </Col>
          </Row>
          {/*<Row gutter={[16, 16]}>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Type de déchets à récupérer' name='globalGarbagePricingId'>*/}
          {/*      <Select*/}
          {/*        placeholder='Sélectionner'*/}
          {/*        onChange={(value) => {*/}
          {/*          console.log(value);*/}
          {/*        }}*/}
          {/*        showSearch*/}
          {/*        filterOption={(input, option) =>*/}
          {/*          ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())*/}
          {/*        }*/}
          {/*        options={typesDechets.map((item) => ({*/}
          {/*          value: item.id,*/}
          {/*          label: item.garbage_type.name,*/}
          {/*        }))}*/}
          {/*        loading={loading}*/}
          {/*      ></Select>*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Quantité' name='garbageQuantity'>*/}
          {/*      <Input placeholder='Quantité' type='number' />*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}
          {/*  <Col span={7}>*/}
          {/*    <Form.Item label='Unité' name='garbageUnit'>*/}
          {/*      <Select*/}
          {/*        placeholder='Unité'*/}
          {/*        onChange={(value) => {*/}
          {/*          console.log(value);*/}
          {/*        }}*/}
          {/*        showSearch*/}
          {/*        filterOption={(input, option) =>*/}
          {/*          ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())*/}
          {/*        }*/}
          {/*        options={garbageUnits.map((item) => ({*/}
          {/*          value: item.name,*/}
          {/*          label: item.name,*/}
          {/*        }))}*/}
          {/*        loading={loading}*/}
          {/*      ></Select>*/}
          {/*    </Form.Item>*/}
          {/*  </Col>*/}

          {/*  <Col span={3}>*/}
          {/*    <Flex style={{ height: '100%', alignItems: 'center' }}>*/}
          {/*      <Form.Item label='' className='mb-0'>*/}
          {/*        <Checkbox>Multipesées</Checkbox>*/}
          {/*      </Form.Item>*/}
          {/*    </Flex>*/}
          {/*  </Col>*/}
          {/*</Row>*/}
          {/* <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item label=''>
                <Button type='text' className='btn-add__ajouter-btn'>
                  <PlusCircleOutlined />
                  Ajouter un type de déchets à récupérer
                </Button>
              </Form.Item>
            </Col>
          </Row> */}
          <Row gutter={[16, 16]}>
            <Col span={12}>
              <Form.Item label='Commentaire' name='commentaire'>
                <TextArea rows={3} placeholder='Commentaire' />
              </Form.Item>
            </Col>
            <Col span={12}>
              <Form.Item label='Commentaire Benneur' name='commentaireBenneur'>
                <TextArea rows={3} placeholder='Commentaire Benneur' />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};
export default BenneNouvelleCommamdeModal;
