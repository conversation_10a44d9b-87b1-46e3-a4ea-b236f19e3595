import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, Flex, Modal, Row, Form, Input, Select, Checkbox, DatePicker, FormInstance, Radio } from 'antd';
import locale from 'antd/es/date-picker/locale/fr_FR';
import { OrderPrestataireLine } from '../../../Logistique/LogistiqueOrderDetail/OrderPrestataireCellTable';
import { useState, useEffect } from 'react';
import { boService } from 'services';
import { BennePointp, GarbagePricing, GarbageUnit, PUP, TimeHour, TimePeriod } from '../../../../models/bo';
import { Contact } from '../../../../models';
import dayjs from 'dayjs';
import {
  BO_TIME_TYPES,
  LIST_ACTION_BO_ORDER,
  LIST_FLOORS,
  LIST_LIFTS,
  LIST_PRISE_EN_CHARGE,
  LIST_TIME_FROM_00_TO_23_30,
} from '../../../../utils/constant';

const { TextArea } = Input;

const CamionNouvelleCommamdeModal = ({
  catalogPriceLine,
  isOpenModal,
  onCancel,
  onConfirmation,
  form,
  client,
  phone,
  orderCode,
  siteAddress,
}: {
  catalogPriceLine: OrderPrestataireLine | null | undefined;
  isOpenModal: boolean;
  onCancel: () => void;
  onConfirmation: () => void;
  form: FormInstance;
  client: Contact | null;
  phone: string;
  orderCode: string;
  siteAddress: string;
}) => {
  const [pups, setPups] = useState<PUP[]>([]);
  const [selectedDecheterie, setSelectedDecheterie] = useState<PUP>();
  const [timePeriods, setTimePeriods] = useState<TimePeriod[]>([]);
  const [typeCamionProducts, setTypeCamionProducts] = useState<BennePointp[]>([]);
  const [typesDechets, setTypesDechets] = useState<GarbagePricing[]>([]);
  // const [craftmens, setCraftmens] = useState<Craftmen[]>([]);
  const [garbageUnits, setGarbageUnits] = useState<GarbageUnit[]>([]);
  // const [prestataires, setPrestataires] = useState<TruckDriver[]>([]);
  const [priseEnCharge, setPriseEnCharge] = useState<string>('');
  const [loading, setLoading] = useState(false);
  const [timeType, setTimeType] = useState<string>(BO_TIME_TYPES.HOUR);
  const timeHours: TimeHour[] = LIST_TIME_FROM_00_TO_23_30;

  useEffect(() => {
    const fetchData = async () => {
      setLoading(true);
      try {
        // Call APIs parallel
        const [
          decheteriesRes,
          globalGarbagePricingsRes,
          // clientsRes,
          productCamionsRes,
          garbageUnitsRes,
          timePeriodsRes,
          // prestatairesRes,
        ] = await Promise.all([
          boService.getDecheteries(),
          boService.getGlobalGarbagePricings(),
          // boService.getClients(),
          // boService.getProductCamions(zoneId?.toString() ?? ""),
          boService.getProductTypes(),
          boService.getGarbageUnits(),
          boService.getTimePeriods(),
          // boService.getPrestataire(),
        ]);

        const globalGarbagePricingg = globalGarbagePricingsRes.globalGarbagePricing;
        setPups(decheteriesRes.pups);
        setTimePeriods(timePeriodsRes.list_periods_time);
        setTypeCamionProducts(productCamionsRes.listProduct);
        setTypesDechets(globalGarbagePricingg);
        // setCraftmens(clientsRes.listUserEcodropCM);
        setGarbageUnits(garbageUnitsRes.unit);
        // setPrestataires(prestatairesRes.listTruckDriver);
      } catch (error) {
        console.error('Error fetching data:', error);
      } finally {
        setLoading(false);
      }
    };

    if (isOpenModal) {
      fetchData();
    }
  }, [isOpenModal]);

  const handleCancel = () => {
    onCancel();
  };

  const handleDecheterieChange = (value: number) => {
    const decheterie = pups.find((pup) => pup.id === value);
    setSelectedDecheterie(decheterie);

    form.setFieldsValue({
      pupFullAddress: decheterie ? `${decheterie.address1}, ${decheterie.zip_code} ${decheterie.city}` : '',
      pupAddress: decheterie?.address1,
      pupZipCode: decheterie?.zip_code,
      pupCity: decheterie?.city,
    });
  };

  const handleTimeTypeChange = (value: string) => {
    setTimeType(value);
  };

  return (
    <>
      <Modal
        title={`NOUVELLE COMMANDE`}
        open={isOpenModal}
        maskClosable={false}
        onCancel={handleCancel}
        style={{ left: 264, margin: 0 }}
        width={1200}
        confirmLoading={loading}
        footer={[
          <>
            <Flex justify='center' align='center' style={{ width: '100%' }}>
              <Button
                key='annuler'
                onClick={handleCancel}
                className='ant-modal-content__btn-color-dark'
                disabled={loading}
              >
                Annuler
              </Button>
              <Button
                key='ajouter'
                type='primary'
                className='ant-modal-content__add-btn'
                onClick={onConfirmation}
                disabled={loading}
              >
                Confirmer
              </Button>
            </Flex>
          </>,
        ]}
      >
        <Form
          form={form}
          layout='vertical'
          autoComplete='off'
          style={{ width: '100%' }}
          initialValues={{
            client: client?.name,
            phone: phone,
            cde: orderCode,
            siteAddress: siteAddress,
            timeHourKey: timeHours[0].key,
            timePeriod: timePeriods[0]?.key_value,
            priseEnCharge: LIST_PRISE_EN_CHARGE[0].value,
            action: LIST_ACTION_BO_ORDER[0].key,
            collectDate: dayjs(),
          }}
        >
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Prestataire' name='truckDriverName'>
                <Input placeholder='Prestataire' defaultValue={catalogPriceLine?.serviceProviderName} disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Déchèterie' name='pupId' rules={[{ required: true, message: 'Ce champ est requis!' }]}>
                <Select
                  placeholder='Sélectionner'
                  onChange={handleDecheterieChange}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={pups.map((item) => ({
                    value: item.id,
                    label: `${item.ref_client}-${item.company_name}`,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                initialValue={
                  selectedDecheterie
                    ? `${selectedDecheterie?.address1}, ${selectedDecheterie?.zip_code} ${selectedDecheterie?.city}`
                    : ''
                }
                label='Adressse de la déchèterie'
                name='pupFullAddress'
              >
                <Input placeholder='Adressse de la déchèterie' readOnly />
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item label='Date de collecte' name='collectDate'>
                <DatePicker
                  placeholder='Sélectionner une date'
                  locale={locale}
                  style={{ width: '100%' }}
                  disabled={loading}
                  format='DD/MM/YYYY'
                />
              </Form.Item>
            </Col>
            <Col span={5}>
              <Form.Item
                label={timeType === BO_TIME_TYPES.HOUR ? 'Heure' : 'Période'}
                name={timeType === BO_TIME_TYPES.HOUR ? 'timeHourKey' : 'timePeriod'}
              >
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={
                    timeType === BO_TIME_TYPES.HOUR
                      ? timeHours.map((item) => ({
                          value: item.key,
                          label: item.name,
                        }))
                      : timePeriods.map((item) => ({
                          value: item.key_value,
                          label: item.name,
                        }))
                  }
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={3} style={{ alignContent: 'center' }}>
              <Form.Item label='' name='timeType' className='mb-0' initialValue={timeType}>
                <Radio.Group
                  // value={timeType}
                  onChange={(e) => handleTimeTypeChange(e.target.value)}
                  options={[
                    { value: BO_TIME_TYPES.HOUR, label: 'Heure' },
                    { value: BO_TIME_TYPES.PERIOD, label: 'Période' },
                  ]}
                />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item label='Action' name='action'>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_ACTION_BO_ORDER.map((item: { key: string; name: string }) => ({
                    value: item.key,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            {/* <Col span={7}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0'>
                  <Checkbox>Chargement Express</Checkbox>
                </Form.Item>
              </Flex>
            </Col> */}
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Form.Item initialValue={client?.name ?? ''} label='Client' name='clientName'>
                <Input value={client?.name ?? ''} placeholder='Client' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item initialValue={phone} label='Tél. Contact' name='phone'>
                <Input value={phone} placeholder='Tel. contact' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item initialValue={orderCode} label='CDE Zoho' name='cdeZoho'>
                <Input value={orderCode} placeholder='CDE Zoho' disabled />
              </Form.Item>
            </Col>

            {/* <Col span={7}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0'>
                  <Checkbox>Chargement Express</Checkbox>
                </Form.Item>
              </Flex>
            </Col> */}
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={16}>
              <Form.Item label='Départ - Adressse Chantier' name='siteAddress'>
                <Input placeholder='Départ - Adressse Chantier' disabled />
              </Form.Item>
            </Col>
            <Col span={8}>
              <Form.Item
                label='Type Produit'
                name='productTypeId'
                rules={[{ required: true, message: 'Vous devez sélectionner une taille camio!' }]}
              >
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={typeCamionProducts.map((item) => ({
                    value: item.id,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={7}>
              <Form.Item label='Prise en charge' name='priseEnCharge'>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                    setPriseEnCharge(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_PRISE_EN_CHARGE.map((item) => ({
                    value: item.value,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            {priseEnCharge === "A l'Etage" && (
              <>
                <Col span={7}>
                  <Form.Item label='Ascenseur' name='lift'>
                    <Select
                      placeholder='Sélectionner'
                      onChange={(value) => {
                        console.log(value);
                      }}
                      showSearch
                      filterOption={(input, option) =>
                        ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                      }
                      options={LIST_LIFTS.map((item) => ({
                        value: item.value,
                        label: item.name,
                      }))}
                      loading={loading}
                    ></Select>
                  </Form.Item>
                </Col>
                <Col span={7}>
                  <Form.Item label='Etage' name='floorNumber'>
                    <Select
                      placeholder='Sélectionner'
                      onChange={(e) => {
                        console.log(e);
                      }}
                      showSearch
                      filterOption={(input, option) =>
                        ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                      }
                      options={LIST_FLOORS.map((item) => ({
                        value: item.value,
                        label: item.name,
                      }))}
                      loading={loading}
                    ></Select>
                  </Form.Item>
                </Col>
              </>
            )}
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={7}>
              <Form.Item label='Type de déchets à récupérer' name='globalGarbagePricingId'>
                <Select
                  placeholder='Sélectionner'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={typesDechets.map((item) => ({
                    value: item.id,
                    label: item.garbage_type.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item label='Quantité' name='quantity'>
                <Input placeholder='Quantité' type='number' />
              </Form.Item>
            </Col>
            <Col span={7}>
              <Form.Item label='Unité' name='garbageUnit'>
                <Select
                  placeholder='Unité'
                  onChange={(value) => {
                    console.log(value);
                  }}
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={garbageUnits.map((item) => ({
                    value: item.name,
                    label: item.name,
                  }))}
                  loading={loading}
                ></Select>
              </Form.Item>
            </Col>

            <Col span={3}>
              <Flex style={{ height: '100%', alignItems: 'center' }}>
                <Form.Item label='' className='mb-0'>
                  <Checkbox>Multipesées</Checkbox>
                </Form.Item>
              </Flex>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item label=''>
                <Button type='text' className='btn-add__ajouter-btn'>
                  <PlusCircleOutlined />
                  Ajouter un type de déchets à récupérer
                </Button>
              </Form.Item>
            </Col>
          </Row>
          <Row gutter={[16, 16]}>
            <Col span={24}>
              <Form.Item label='Commentaire' name='comment'>
                <TextArea rows={3} placeholder='Description' />
              </Form.Item>
            </Col>
          </Row>
        </Form>
      </Modal>
    </>
  );
};

export default CamionNouvelleCommamdeModal;
