import { ReactNode } from "react";
import { Space, Card } from "antd";

const OptionCard = ({
  children,
  title,
  otherStyles,
  headerStyles,
}: {
  children: ReactNode;
  title: string;
  otherStyles: object;
  headerStyles?: object;
}) => {
  return (
    <Space
      direction="vertical"
      size="large"
      style={{ width: "100%", ...otherStyles }}
    >
      <Card title={title} style={{ width: "100%" }} headStyle={headerStyles}>
        {children}
      </Card>
    </Space>
  );
};
export default OptionCard;
