// import React from 'react';
// import { GoogleMap, LoadScriptNext } from '@react-google-maps/api';

// const mapContainerStyle = {
//   width: '100%',
//   height: '400px',
//   marginTop: '20px',
// };

// interface MapProps {
//   center: { lat: number; lng: number };
//   zoom?: number;
// }

// const MapGoogle: React.FC<MapProps> = ({ center, zoom = 12 }) => {
//   if (!process.env.REACT_APP_GOOGLE_MAPS_SEARCH_API_KEY) {
//     return (
//       <p style={{ color: 'red' }}>
//         Google Maps API Key is not configured. Please check your environment variables.
//       </p>
//     );
//   }

//   return (
//     <LoadScriptNext googleMapsApiKey={process.env.REACT_APP_GOOGLE_MAPS_SEARCH_API_KEY}>
//       <GoogleMap mapContainerStyle={mapContainerStyle} center={center} zoom={zoom} />
//     </LoadScriptNext>
//   );
// };

// export default MapGoogle;

const MapGoogle = () => {
  return <div>MapGoogle</div>;
};

export default MapGoogle;
