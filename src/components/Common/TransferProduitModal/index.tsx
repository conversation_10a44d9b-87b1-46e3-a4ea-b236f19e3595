import { LoadingOutlined } from "@ant-design/icons";
import { Button, Form, Modal, Space, Spin, Transfer } from "antd";
import { TransferDirection } from "antd/es/transfer";
interface RecordType {
  key: string;
  title: string;
  description: string;
  chosen: boolean;
}
const TransferProduitsModal = ({
    isOpenModal,
    handleCancel,
    onSubmit,
    submitLoading,
    handleSubmit,
    form,
    isSubmited,
    loadingProductList,
    dataSource,
    targetKeys,
    setTargetKeys,
    loadingProducts,
    loadingServiceProviderPriceLineProducts
}:{
    isOpenModal: boolean;
    handleCancel?: () => void;
    onSubmit?: () => void;
    submitLoading?: any;
    handleSubmit?: any
    form?: any;
    isSubmited?: boolean;
    loadingProductList?: any;
    dataSource?: any;
    targetKeys?: any;
    setTargetKeys?: any;
    loadingProducts?: any;
    loadingServiceProviderPriceLineProducts?: any
}) => {
  const filterOption = (inputValue: string, option: RecordType) =>
    option.description.indexOf(inputValue) > -1;

  const handleChange = (newTargetKeys: string[]) => {
    setTargetKeys(newTargetKeys);
  };

  const handleSearch = (dir: TransferDirection, value: string) => {
    console.log('search:', dir, value);
  };
  return (
    <>
      <Modal
        title='MODIFIER LE GROUPE DE PRODUITS'
        open={isOpenModal}
        onOk={onSubmit}
        maskClosable={false}
        onCancel={handleCancel}
        className='modify-produits-modal'
        width={778}
        centered
        footer={[
          <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
            Annuler
          </Button>,
          <Button
            key='ajouter'
            type='primary'
            className='ant-modal-content__add-btn'
            loading={submitLoading}
            onClick={handleSubmit}
          >
            Valider
          </Button>,
        ]}
      >
        <Space direction='vertical' className='modify-produits-modal__body'>
          <Form
            form={form}
            validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
            autoComplete='off'
            validateMessages={{ required: '' }}
          >
            <Space direction='vertical' className='modify-produits-modal__body'>
              <Form
                form={form}
                validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
                autoComplete='off'
                validateMessages={{ required: '' }}
              >
                <Spin spinning={loadingProductList}>
                  <Transfer
                    dataSource={dataSource}
                    targetKeys={targetKeys}
                    showSearch
                    filterOption={filterOption}
                    onChange={handleChange}
                    onSearch={handleSearch}
                    className='modify-produits-modal__transfer-selection transfer-component'
                    selectAllLabels={[
                      () => (
                        <>
                          {`${dataSource.length - targetKeys.length} Produits disponibles`}
                          {loadingProducts === 'pending' && (
                            <LoadingOutlined className='product-exclusions__transfer-loading' />
                          )}
                        </>
                      ),
                      () => (
                        <>
                          {`${targetKeys.length} produits sélectionnés`}
                          {loadingServiceProviderPriceLineProducts === 'pending' && (
                            <LoadingOutlined className='product-exclusions__transfer-loading' />
                          )}
                        </>
                      ),
                    ]}
                    render={(item) => <div>{item.title}</div>}
                  />
                </Spin>
              </Form>
            </Space>
          </Form>
        </Space>
      </Modal>
    </>
  );
};

export default TransferProduitsModal;
