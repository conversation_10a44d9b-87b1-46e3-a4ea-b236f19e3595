import { CloudSyncOutlined, SyncOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { ServiceProvider } from 'models';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { boService } from 'services';

const ButtonSync = ({
  serviceProvider,
  getServiceProvider,
}: {
  serviceProvider: ServiceProvider;
  getServiceProvider: () => Promise<void>;
}) => {
  const [isSyncLoading, setIsSyncLoading] = useState<boolean>(false);

  // Function to handle syncing the service provider with BO
  const syncBOStatus = async (serviceProvider: ServiceProvider) => {
    try {
      setIsSyncLoading(true);
      // Sort contacts by id before finding the first active one
      const dataContact = serviceProvider?.Contacts?.slice() // create a shallow copy to avoid mutating original
        .sort((a, b) => a.id - b.id)
        .find((contact) => contact.isActive);
      // Prepare data to submit to BO
      const dataSubmit = {
        ServiceProvider: {
          ...serviceProvider,
          // If Contacts are available, use the first contact's email and phone
          // Otherwise, fallback to email and phone on serviceProvider
          // Only get contact_name when have contact data
          contact_name: dataContact ? dataContact?.name : null,
          email: dataContact ? dataContact?.email : serviceProvider?.email,
          phone: dataContact ? dataContact?.phone : serviceProvider?.phone,
        },
        WasteCenters: serviceProvider?.WasteCenters || [],
      };
      // Call BO API to sync service provider
      const rs = await boService.createOrUpdateSpFromQBO(dataSubmit);
      if (rs) {
        // when call api success we will fetch api get list by this to refetch data for service provider
        setIsSyncLoading(false);
        toast.success('Succès');
        await getServiceProvider();
      }
    } catch (error) {
      // On error, show error toast and reset loading
      console.error(error);
      toast.error((error as Error)?.message);
      setIsSyncLoading(false);
    }
  };
  return (
    <>
      <Button
        type='link'
        className='service-provider-list__sync_bo__button-sync'
        onClick={() => {
          syncBOStatus(serviceProvider);
        }}
      >
        {isSyncLoading ? <SyncOutlined spin /> : <CloudSyncOutlined />}
        Sync BO
      </Button>
    </>
  );
};
export default ButtonSync;
