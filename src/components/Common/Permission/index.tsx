import { ReactNode } from 'react';
import { usePermission } from 'hooks/usePermission';
import { Permissions } from 'services/permission.service';

interface PermissionProps {
  permissions: Permissions | Permissions[];
  type?: 'all' | 'any';
  children: ReactNode;
  fallback?: ReactNode;
}

export const Permission = ({ permissions, type = 'any', children, fallback = null }: PermissionProps) => {
  const { hasPermission, hasAnyPermission, hasAllPermissions } = usePermission();

  const hasAccess = Array.isArray(permissions)
    ? type === 'all'
      ? hasAllPermissions(permissions)
      : hasAnyPermission(permissions)
    : hasPermission(permissions);

  return hasAccess ? <>{children}</> : <>{fallback}</>;
};
