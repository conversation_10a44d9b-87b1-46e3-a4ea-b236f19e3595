import { Tag, Typography } from 'antd';
import { ORDER_STATUSES, STATUSES } from 'utils/constant';
const { Title } = Typography;

const MainTitle = ({
  parent,
  child,
  otherStyles,
  type,
  status,
}: {
  type?: 'estimate' | 'order' | string;
  parent: string;
  child?: string;
  otherStyles?: object;
  status?: string;
}) => {
  const statusFormat =
    type === 'order'
      ? ORDER_STATUSES[status as keyof typeof ORDER_STATUSES]
      : STATUSES[status as keyof typeof STATUSES];
  return (
    <Title level={3} className='main-title' style={{ display: 'flex', ...otherStyles }}>
      <span>{parent}</span> / <strong className='main-title__child'>{child}</strong>
      {statusFormat && <Tag className={`ml-5 ${statusFormat?.color}`}>{statusFormat?.title}</Tag>}
    </Title>
  );
};
export default MainTitle;
