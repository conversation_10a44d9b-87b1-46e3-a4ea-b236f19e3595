import { CopyOutlined, DeleteOutlined } from '@ant-design/icons';
import { Button, message } from 'antd';
import { useState } from 'react';
import { catalogPriceLineService } from 'services';
import { QueryParams } from 'types';

type CatalogPriceActionsProps = {
  catalogPriceLineId: number;
  onFetch: (options?: QueryParams) => void;
  page: number;
  limit: number | string;
  fetchCatalogPriceLinesByUrbanCenterIds: () => void;
  refreshUrbanCenters: () => void;
};

const CatalogPriceActions = (props: CatalogPriceActionsProps) => {
  const { catalogPriceLineId, onFetch, page, limit, fetchCatalogPriceLinesByUrbanCenterIds, refreshUrbanCenters } =
    props;
  const [isDeleting, setIsDeleting] = useState(false);
  const [isDuplicating, setIsDuplicating] = useState(false);

  const onDuplicateCatalogPriceLine = async () => {
    try {
      setIsDuplicating(true);
      await catalogPriceLineService.duplicateCatalogPriceLine(catalogPriceLineId);
      await Promise.all([onFetch({ page, limit }), refreshUrbanCenters()]);
      message.success('Succès');
      setIsDuplicating(false);
    } catch (error) {
      console.log(error);
      setIsDuplicating(false);
      message.error('Erreur');
    }
  };

  const onDeleteCatalogPriceLine = async () => {
    try {
      setIsDeleting(true);
      await catalogPriceLineService.deactivateCatalogPriceLine(catalogPriceLineId);
      await onFetch({ page, limit });
      message.success('Succès');
      setIsDeleting(false);
      await Promise.all([fetchCatalogPriceLinesByUrbanCenterIds(), refreshUrbanCenters()]);
    } catch (error) {
      console.log(error);
      setIsDeleting(false);
      message.error('Erreur');
    }
  };

  return (
    <div style={{ display: 'flex', justifyContent: 'center' }}>
      <Button
        type='link'
        icon={<CopyOutlined />}
        className='datatable__action-edit-button'
        loading={isDuplicating}
        onClick={onDuplicateCatalogPriceLine}
      />
      <Button
        type='link'
        icon={<DeleteOutlined />}
        className='datatable__action-destroy-button'
        loading={isDeleting}
        onClick={onDeleteCatalogPriceLine}
      />
    </div>
  );
};

export default CatalogPriceActions;
