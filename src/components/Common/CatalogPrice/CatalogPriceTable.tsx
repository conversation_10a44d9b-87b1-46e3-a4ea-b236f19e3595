import { useEffect, useState } from 'react';
import { QueryParams } from 'types';
import { catalogPriceLineService, catalogPriceLineZoneCheckedService } from 'services';
import { CombineCatalogPriceLine, useMergeState } from 'hooks';
import { Checkbox, message, Table, TableProps } from 'antd';
import { CatalogPriceLine, CatalogPriceLineProduct, UrbanCenter } from 'models';
import {
  EditableCatalogPriceCell,
  EditableCatalogPriceRow,
  CatalogPriceActions,
  CatalogPriceProductList,
} from 'components/Common';

type EditableTableProps = Parameters<typeof Table>[0];
export type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

type CatalogPriceTableProps = {
  urbanCenter: UrbanCenter;
  catalogPriceLines: CatalogPriceLine[];
  total?: number;
  onFetch: (options?: QueryParams) => void;
  fetchCatalogPriceLinesByUrbanCenterIds: () => void;
  refreshUrbanCenters: () => void;
};

const CatalogPriceTable = (props: CatalogPriceTableProps) => {
  const {
    urbanCenter,
    catalogPriceLines,
    onFetch,
    // total,
    fetchCatalogPriceLinesByUrbanCenterIds,
    refreshUrbanCenters,
  } = props;
  const zones = urbanCenter?.Zones ?? [];
  const [dataSource, setDataSource] = useState<CombineCatalogPriceLine[]>([]);
  const [page] = useState(1);
  const [limit] = useState('unlimited');
  const [isCheckedAll, setIsCheckedAll] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useMergeState<{
    [key: number]: boolean;
  }>({});
  const [selectedZonesKeys, setSelectedZonesKeys] = useMergeState<{
    [key: number]: { [key: number]: boolean };
  }>({});
  const [selectedAllZone, setSelectedAllZone] = useMergeState<{
    [key: number]: boolean;
  }>({});

  useEffect(() => {
    setDataSource(catalogPriceLines);
    const selectedRows = catalogPriceLines?.reduce((acc: { [key: number]: boolean }, item) => {
      if (item.isChecked) {
        acc[item.id] = true;
      } else {
        acc[item.id] = false;
      }
      return acc;
    }, {});
    checkIsSelectedAll(selectedRows);
    setSelectedRowKeys(selectedRows, true);
    if (catalogPriceLines && catalogPriceLines.length > 0 && zones && zones.length > 0) {
      const selectedZones: { [key: number]: { [key: number]: boolean } } = {};
      for (let i = 0; i < catalogPriceLines.length; i++) {
        const catalogPriceLinesZoneChecked: { [key: number]: boolean } = {};
        const catalogPriceLinesItem = catalogPriceLines[i];
        // Get item All Zone
        // const itemCheckAllZone = catalogPriceLinesItem?.CatalogPriceLinesZoneCheckeds?.find((i) => i.zoneId === null);
        // const dataAllZone = itemCheckAllZone ? itemCheckAllZone.isChecked : false;
        // catalogPriceLinesZoneChecked[0] = dataAllZone;
        for (let j = 0; j < zones.length; j++) {
          const zoneItem = zones[j];
          const itemCheck = catalogPriceLinesItem?.CatalogPriceLinesZoneCheckeds?.find((i) => i.zoneId === zoneItem.id);
          const data = itemCheck ? itemCheck.isChecked : false;
          catalogPriceLinesZoneChecked[zoneItem?.id ?? 0] = data;
          if (!itemCheck && itemCheck === false) {
            setSelectedAllZone({ [zoneItem.id ?? 0]: false });
          }
        }
        selectedZones[catalogPriceLinesItem.id] = catalogPriceLinesZoneChecked;
      }
      setSelectedZonesKeys(selectedZones, true);
      if (selectedZones) {
        const selectedZone = zones?.reduce((acc: { [key: number]: boolean }, item) => {
          acc[item.id ?? 0] = true;
          return acc;
        }, {});
        setSelectedAllZone(selectedZone, true);
        // setSelectedAllZone({ [0]: true });
        let selectAllRow = true;
        const selectedZoneKeys = Object.keys(selectedZone);
        if (selectedZoneKeys && selectedZoneKeys.length > 0) {
          for (let i = 0; i < catalogPriceLines.length; i++) {
            const catalogPriceLinesItem = catalogPriceLines[i];
            // Check select all item row
            let isSelectAllRow = true;
            for (const zoneKey of selectedZoneKeys) {
              const zoneKeyNumber = parseInt(zoneKey);
              const itemCheck = selectedZones[catalogPriceLinesItem.id]?.[zoneKeyNumber];
              if (itemCheck === null || itemCheck === false) {
                setSelectedAllZone({ [zoneKey]: false });
                isSelectAllRow = false;
                selectAllRow = false;
              }
            }
            if (selectedRows[catalogPriceLinesItem.id] != isSelectAllRow) {
              // Update selectedRows
              onSelectRow(catalogPriceLinesItem.id, isSelectAllRow, false);
            }
          }
        }
        setIsCheckedAll(selectAllRow);
      }
    }
  }, [catalogPriceLines]);
  const checkIsSelectedAll = (keys: { [key: number]: boolean }) => {
    if (!dataSource?.length) {
      return setIsCheckedAll(false);
    }
    const selectedKeys = Object.fromEntries(Object.entries(keys).filter(([, value]) => value !== false));
    const amountOfRows =
      dataSource?.reduce((acc, item: CombineCatalogPriceLine) => {
        if (item.rowSpan) {
          acc -= item.rowSpan - 1;
        }
        return acc;
      }, 0) + dataSource?.length;
    if (Object.keys(selectedKeys).length === amountOfRows) {
      return setIsCheckedAll(true);
    }
    setIsCheckedAll(false);
  };

  const selectAll = async (value: boolean) => {
    try {
      // const selectedRows = dataSource?.reduce((acc: { [key: number]: boolean }, item) => {
      //   if (item.isChecked) {
      //     acc[item.id] = value;
      //   }
      //   return acc;
      // }, {});

      // setSelectedRowKeys(selectedRows, true);
      // setIsCheckedAll(value);
      // await catalogPriceLineService.multipleCheckCatalogPriceLines({
      //   catalogPriceLineIds: dataSource.map((i) => i.id),
      //   isChecked: value,
      // });
      setIsCheckedAll(value);
      const listData = [];
      const selectedZoneKeys = Object.keys(selectedAllZone);
      if (selectedZoneKeys && selectedZoneKeys.length > 0) {
        for (let i = 0; i < catalogPriceLines.length; i++) {
          const catalogPriceLinesItem = catalogPriceLines[i];
          // Check select all item row
          for (const zoneKey of selectedZoneKeys) {
            const zoneKeyNumber = parseInt(zoneKey);
            listData.push({
              catalogPriceLineId: catalogPriceLinesItem.id,
              zoneId: zoneKeyNumber == 0 ? null : zoneKeyNumber,
              isChecked: value,
            });
          }
        }
      }
      await catalogPriceLineZoneCheckedService.createOrUpdateCatalogPriceLinesZoneChecked(listData);
      onFetch({ page, limit });
    } catch (error) {
      console.log(error);
      message.error('Erreur');
    }
  };
  const headerSelectZone = async (zoneId: number | null, value: boolean) => {
    if (zoneId) {
      const listData = [];
      for (const catalogPriceLinesItem of catalogPriceLines) {
        listData.push({
          catalogPriceLineId: catalogPriceLinesItem.id,
          zoneId: zoneId,
          isChecked: value,
        });
      }
      await catalogPriceLineZoneCheckedService.createOrUpdateCatalogPriceLinesZoneChecked(listData);
      onFetch({ page, limit });
    }
  };
  // const headerSelectAllZone = async (value: boolean) => {
  //   const listData = [];
  //   for (const catalogPriceLinesItem of catalogPriceLines) {
  //     listData.push({
  //       catalogPriceLineId: catalogPriceLinesItem.id,
  //       zoneId: null,
  //       isChecked: value,
  //     });
  //   }
  //   await catalogPriceLineZoneCheckedService.createOrUpdateCatalogPriceLinesZoneChecked(listData);
  //   onFetch({ page, limit });
  // };
  const reloadTable = () => {
    onFetch({ page, limit });
  };

  const onSelectRow = async (id: number, value: boolean, isChangeDataSelectedZone: boolean) => {
    try {
      setSelectedRowKeys({ [id]: value });
      checkIsSelectedAll({ ...selectedRowKeys, [id]: value });
      await catalogPriceLineService.updateCatalogPriceLine(id, {
        isChecked: value,
      });
      if (isChangeDataSelectedZone) {
        const listData = [];
        listData.push({
          catalogPriceLineId: id,
          zoneId: null,
          isChecked: value,
        });
        for (const zoneItem of zones) {
          listData.push({
            catalogPriceLineId: id,
            zoneId: zoneItem.id,
            isChecked: value,
          });
        }
        await catalogPriceLineZoneCheckedService.createOrUpdateCatalogPriceLinesZoneChecked(listData);
        onFetch({ page, limit });
      }
    } catch (error) {
      console.log(error);
      message.error('Erreur');
    }
  };

  const handleChangeDataSource = (catalogPriceLine: CatalogPriceLine) => {
    const catalogPrice = catalogPriceLine.CatalogPrices?.[0];
    setDataSource((prev) => {
      const result = [...prev];
      const index = result.findIndex((item) => {
        const itemCatalogPrice = item.CatalogPrices?.[0];
        return (
          item.id === catalogPriceLine.id &&
          itemCatalogPrice?.id === catalogPrice?.id &&
          itemCatalogPrice?.priceFamilyId === catalogPrice?.priceFamilyId &&
          itemCatalogPrice?.priceId === catalogPrice?.priceId &&
          itemCatalogPrice?.priceOptionId === catalogPrice?.priceOptionId &&
          itemCatalogPrice?.CatalogPriceLineCatalogPrice?.id === catalogPrice?.CatalogPriceLineCatalogPrice?.id &&
          itemCatalogPrice?.PriceSubOption?.id === catalogPrice?.PriceSubOption?.id
        );
      });
      result[index] = catalogPriceLine;
      return result;
    });
  };
  const handleChangeSelectedZonesKeys = (catalogPriceLineId: number, zoneId: number, value: boolean) => {
    if (value === false) {
      onSelectRow(catalogPriceLineId, false, false);
    } else {
      let isSetSelectRow = true;
      const selectedZoneKeys = Object.keys(selectedAllZone);
      for (const zoneKey of selectedZoneKeys) {
        if (parseInt(zoneKey) !== zoneId && selectedZonesKeys[catalogPriceLineId][parseInt(zoneKey)] === false) {
          isSetSelectRow = false;
          break;
        }
      }
      if (isSetSelectRow) {
        onSelectRow(catalogPriceLineId, true, true);
      }
    }
  };
  const defaultColumns = [
    {
      title: <Checkbox checked={isCheckedAll} onChange={(e) => selectAll(e.target.checked)} />,
      dataIndex: 'rowSelect',
      width: 36,
      onCell: (record: CombineCatalogPriceLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: CombineCatalogPriceLine) => (
        <Checkbox
          checked={!!selectedRowKeys[record.id]}
          onChange={(e) => onSelectRow(record.id, e.target.checked, true)}
        />
      ),
    },
    {
      title: 'Produit',
      dataIndex: 'CatalogPriceLineProducts',
      width: 200,
      onCell: (record: CombineCatalogPriceLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (value: CatalogPriceLineProduct[]) => <CatalogPriceProductList items={value} onFetch={onFetch} />,
    },
    {
      title: 'Prix',
      dataIndex: 'CatalogPrices',
      width: 180,
      editable: true,
    },
    {
      dataIndex: 'TouteZone',
      title: (
        <>
          <span>Toute Zone</span>
        </>
      ),
      editable: true,
      width: 120,
      zoneId: null,
    },
    ...(zones?.map((zone) => ({
      dataIndex: 'RealPrice',
      title: (
        <>
          {zone.name + ` (${zone.maxDistance} km)`}
          <Checkbox
            className='position-absolute r-4'
            checked={selectedAllZone[zone.id ?? 0]}
            onChange={(e) => headerSelectZone(zone.id ?? null, e.target.checked)}
          />
        </>
      ),
      editable: true,
      width: 120,
      zoneId: zone.id,
    })) ?? []),
    {
      width: 75,
      title: 'Action',
      dataIndex: 'item_id',
      fixed: 'right',
      onCell: (record: CombineCatalogPriceLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: CombineCatalogPriceLine) => {
        return (
          <CatalogPriceActions
            catalogPriceLineId={record.id}
            onFetch={onFetch}
            page={page}
            limit={limit}
            fetchCatalogPriceLinesByUrbanCenterIds={fetchCatalogPriceLinesByUrbanCenterIds}
            refreshUrbanCenters={refreshUrbanCenters}
          />
        );
      },
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: CombineCatalogPriceLine) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        zoneId: col.zoneId,
        zones: zones,
        handleChangeDataSource,
        selectedZonesKeys,
        handleChangeSelectedZonesKeys,
        reloadTable,
      }),
    };
  }) as TableProps<CombineCatalogPriceLine>['columns'];
  return (
    <section className='mt-2 section quotation'>
      <Table
        scroll={{ x: 1300 }}
        bordered
        rowKey={(row: CombineCatalogPriceLine) =>
          `${row.id}-${row.CatalogPrices?.[0]?.id}-${row.CatalogPrices?.[0]?.priceFamilyId}-${row.CatalogPrices?.[0]?.priceId}-${row.CatalogPrices?.[0]?.priceOptionId}-${row.CatalogPrices?.[0]?.PriceSubOption?.id}`
        }
        components={{
          body: {
            row: EditableCatalogPriceRow,
            cell: EditableCatalogPriceCell,
          },
        }}
        className='catalog-price-table'
        dataSource={dataSource}
        columns={columns}
        pagination={false}
      />
      {/* <div className='pagination__pagination-items'>
        <span className='pagination__number-total'>Total {total ?? 0} items</span>
        <Pagination
          showSizeChanger
          total={total}
          current={page}
          pageSize={limit}
          onChange={(_page, _limit) => {
            setPage(_page);
            setLimit(_limit);
            onFetch({ page: _page, limit: _limit });
          }}
          className='pagination'
        />
      </div> */}
    </section>
  );
};

export default CatalogPriceTable;
