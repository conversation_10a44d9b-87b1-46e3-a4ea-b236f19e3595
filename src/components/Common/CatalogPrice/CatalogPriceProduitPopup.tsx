import { useEffect, useMemo, useState } from 'react';
import { Form } from 'antd';
import { toast } from 'react-toastify';
import { PaginationData } from 'types';
import { CatalogPriceLineProduct, Product } from 'models';
import { catalogPriceLineProductService, productService } from 'services';
import TransferProduitsModal from 'components/Common/TransferProduitModal';

const CatalogPriceProduitPopup = ({
  isModalOpen,
  handleCancel,
  catalogPriceLineId,
  productTypeId,
  onFetch,
  catalogPriceLineProducts,
}: {
  isModalOpen: boolean;
  handleCancel: () => void;
  onFetch: () => void;
  catalogPriceLineId?: number;
  productTypeId?: number;
  catalogPriceLineProducts: CatalogPriceLineProduct[];
}) => {
  const [form] = Form.useForm();
  const [products, setProducts] = useState<PaginationData<Product>>();
  const [targetKeys, setTargetKeys] = useState<string[]>([]);
  const [submitLoading, setSubmitLoading] = useState<boolean>(false);
  const [isSubmited] = useState<boolean>(false);
  const [loadingProductList, setLoadingProductList] = useState<boolean>(false);
  const dataSource = useMemo(() => {
    return products?.rows?.map((product) => ({
      key: product.id.toString(),
      title: product.name,
      description: product.name,
    }));
  }, [products]);
  const handleSubmit = async () => {
    try {
      setSubmitLoading(true);
      const newTargetKeys = targetKeys.map((targetKey) => parseInt(targetKey));
      const dataSubmit = {
        catalogPriceLineId: catalogPriceLineId,
        productIds: newTargetKeys,
      };
      await catalogPriceLineProductService.multipleCreateOrUpdateCatalogPriceLineProducts(dataSubmit);
      toast.success('Succès');
      onFetch();
      setSubmitLoading(false);
      handleCancel();
    } catch (error) {
      console.log(error);
      setSubmitLoading(false);
      toast.error('Erreur');
    }
  };
  const getProducts = async () => {
    try {
      const response = await productService.getProducts({
        productTypeId,
        exclude: 0,
        isActive: 1,
        limit: 'unlimited',
        orderBy: 'name',
      });
      setProducts(response);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const getCatalogPriceLineProducts = async () => {
    try {
      const newTargetKeys = catalogPriceLineProducts?.map((item: CatalogPriceLineProduct) => item.productId.toString());
      setTargetKeys(newTargetKeys);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const fetchData = async () => {
    try {
      setLoadingProductList(true);
      await Promise.all([getProducts(), getCatalogPriceLineProducts()]);
      setLoadingProductList(false);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
      setLoadingProductList(false);
    }
  };
  useEffect(() => {
    if (isModalOpen) fetchData();
  }, [isModalOpen]);
  return (
    <>
      <TransferProduitsModal
        isOpenModal={isModalOpen}
        handleCancel={handleCancel}
        onSubmit={handleSubmit}
        submitLoading={submitLoading}
        handleSubmit={handleSubmit}
        form={form}
        isSubmited={isSubmited}
        loadingProductList={loadingProductList}
        targetKeys={targetKeys}
        dataSource={dataSource ?? []}
        setTargetKeys={setTargetKeys}
      />
    </>
  );
};

export default CatalogPriceProduitPopup;
