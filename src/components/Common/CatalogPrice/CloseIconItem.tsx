import { EditOutlined } from '@ant-design/icons';
import { TrashButton, UrbanCenterModal } from 'components/Common';
import { UrbanCenter } from 'models';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { urbanCenterService } from 'services';

const CloseIconItem = ({
  urbanCenter,
  refreshUrbanCenters,
  selectedYearId,
  onChange,
  isShowDeleteButton,
}: {
  urbanCenter: UrbanCenter;
  refreshUrbanCenters: () => void;
  selectedYearId?: number;
  onChange: (activeKey: string) => void;
  isShowDeleteButton: boolean;
}) => {
  const [isOpenModal, setIsOpenModal] = useState(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const handleShowModal = (value: boolean) => {
    setIsOpenModal(value);
  };

  const handleSubmit = async (data: UrbanCenter, urbanCenter: UrbanCenter) => {
    try {
      const submitData = {
        ...data,
        yearId: selectedYearId,
      };
      await urbanCenterService
        .updateUrbanCenter(urbanCenter?.regionId as number, urbanCenter.id as number, submitData)
        .then(async () => {
          await Promise.all([refreshUrbanCenters()]);
          onChange(`${(urbanCenter.id as number).toString()}-${selectedYearId}`);
          toast.success('Succès');
        });
      setIsOpenModal(false);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const handleDeactivateUrbanCenter = async () => {
    if (!urbanCenter?.id) return;
    try {
      setDeleteLoading(true);
      await urbanCenterService.deactivateUrbanCenter(urbanCenter?.regionId as number, urbanCenter?.id as number);
      await refreshUrbanCenters();
      setDeleteLoading(false);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      setDeleteLoading(false);
      toast.error('Erreur');
    }
  };
  return (
    <>
      <EditOutlined className='urban-center-edit-icon' onClick={() => handleShowModal(true)} />
      {isShowDeleteButton && (
        <TrashButton className='trash-button ml-2' onClick={handleDeactivateUrbanCenter} loading={deleteLoading} />
      )}
      <UrbanCenterModal
        action='update'
        urbanCenter={urbanCenter}
        isOpenModal={isOpenModal}
        onCancel={() => setIsOpenModal(false)}
        onSubmit={handleSubmit}
      />
    </>
  );
};
export default CloseIconItem;
