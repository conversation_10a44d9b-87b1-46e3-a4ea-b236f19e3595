import { Row, Space, Spin } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { catalogPriceLineService } from 'services';
import { CatalogPriceLine, ServiceProvider, UrbanCenter } from 'models';
import { useCatalogPriceLines, useCatalogPriceLinesQuery } from 'hooks';
import { AddProduitsModal, ButtonAdd, CatalogPriceTable, ProduitsDuplicateModal } from 'components/Common';

type CatalogPriceTabItemProps = {
  showDuplicateProduct?: boolean;
  urbanCenter: UrbanCenter;
  selectedYearId?: number;
  fetchCatalogPriceLinesByUrbanCenterIds: () => void;
  serviceProviderId?: number;
  fetchCatalogPriceLineNumbers: () => void;
  refreshUrbanCenters: () => void;
  serviceProvider?: ServiceProvider | null;
};
const CatalogPriceTabItem = (props: CatalogPriceTabItemProps) => {
  const {
    showDuplicateProduct = false,
    urbanCenter,
    selectedYearId,
    serviceProviderId,
    serviceProvider,
    fetchCatalogPriceLinesByUrbanCenterIds,
    fetchCatalogPriceLineNumbers,
    refreshUrbanCenters,
  } = props;
  const isCatalog = serviceProviderId ? false : true;
  const [isOpenAddProduitsModal, setIsOpenAddProduitsModal] = useState(false);
  const [isOpenDuplicateProduitsModal, setIsOpenDuplicateProduitsModal] = useState(false);
  const [params] = useCatalogPriceLinesQuery({ urbanCenterId: urbanCenter.id, serviceProviderId, limit: 'unlimited' });
  const [catalogPriceLines, onFetch, loading] = useCatalogPriceLines(params);
  const handleShowModalAdd = (value: boolean) => {
    setIsOpenAddProduitsModal(value);
  };

  const handleShowModalDuplicate = (value: boolean) => {
    setIsOpenDuplicateProduitsModal(value);
  };

  const handleSubmit = async (submitData: CatalogPriceLine) => {
    try {
      await catalogPriceLineService.createCatalogPriceLine({
        ...submitData,
        serviceProviderId,
        serviceProviderActive: serviceProvider?.isActive,
        urbanCenterId: urbanCenter.id,
        yearId: selectedYearId,
      });
      setIsOpenAddProduitsModal(false);
      Promise.all([onFetch(), fetchCatalogPriceLinesByUrbanCenterIds(), refreshUrbanCenters()]);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleDuplicateSubmit = async (catalogPriceLines: object[]) => {
    try {
      for (const catalogPriceLine of catalogPriceLines ?? []) {
        await catalogPriceLineService.createCatalogPriceLine({
          ...catalogPriceLine,
          serviceProviderId,
          urbanCenterId: urbanCenter.id,
          yearId: selectedYearId,
        });
      }
      setIsOpenDuplicateProduitsModal(false);
      Promise.all([onFetch(), fetchCatalogPriceLinesByUrbanCenterIds(), refreshUrbanCenters()]);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  return (
    <Spin spinning={loading === 'pending'}>
      <CatalogPriceTable
        urbanCenter={urbanCenter}
        catalogPriceLines={catalogPriceLines?.rows}
        onFetch={onFetch}
        // total={catalogPriceLines?.count}
        fetchCatalogPriceLinesByUrbanCenterIds={fetchCatalogPriceLinesByUrbanCenterIds}
        refreshUrbanCenters={refreshUrbanCenters}
      />
      {isOpenAddProduitsModal && (
        <AddProduitsModal
          isCatalog={isCatalog}
          isOpenModal={isOpenAddProduitsModal}
          onCancel={() => setIsOpenAddProduitsModal(false)}
          onSubmit={handleSubmit}
          urbanCenterId={urbanCenter.id}
          fetchCatalogPriceLineNumbers={fetchCatalogPriceLineNumbers}
        />
      )}
      {isOpenDuplicateProduitsModal && (
        <ProduitsDuplicateModal
          isOpenModal={isOpenDuplicateProduitsModal}
          onCancel={() => setIsOpenDuplicateProduitsModal(false)}
          onSubmit={handleDuplicateSubmit}
          urbanCenterId={urbanCenter.id}
          fetchCatalogPriceLineNumbers={fetchCatalogPriceLineNumbers}
        />
      )}
      <Row justify='space-between' className='mt-8'>
        <Space direction='horizontal' align='start' className='quotation__button-block'>
          <ButtonAdd
            otherStyles={{
              height: '32px',
              borderRadius: '6px',
              marginTop: '43px',
              background: '#A6C84D',
            }}
            className='service-provider-price-line-products-btn'
            handleClick={() => handleShowModalAdd(true)}
          >
            Ajouter un produit
          </ButtonAdd>
          {/*only show on prestataire*/}
          {showDuplicateProduct && (
            <ButtonAdd
              otherStyles={{
                height: '32px',
                borderRadius: '6px',
                marginTop: '43px',
                background: '#A6C84D',
              }}
              className='service-provider-price-line-products-btn'
              handleClick={() => handleShowModalDuplicate(true)}
            >
              Duplication produits
            </ButtonAdd>
          )}
        </Space>
      </Row>
    </Spin>
  );
};
export default CatalogPriceTabItem;
