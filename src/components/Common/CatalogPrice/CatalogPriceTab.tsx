import { useEffect, useState } from 'react';
import { PlusCircleOutlined } from '@ant-design/icons';
import { Tabs } from 'antd';
import { UrbanCenter, ServiceProvider } from 'models';
import type { Tab } from 'rc-tabs/lib/interface';
import { toast } from 'react-toastify';
import { catalogPriceLineService, urbanCenterService } from 'services';
import { useParams, useSearchParams } from 'react-router-dom';
import { UrbanCenterModal, CloseIconItem, CatalogPriceTabItem } from 'components/Common';

type CatalogPriceTabProps = {
  showDuplicateProduct: boolean;
  serviceProviderId?: number;
  urbanCenters?: UrbanCenter[];
  refreshUrbanCenters: () => void;
  selectedYearId?: number;
  fetchCatalogPriceLinesByUrbanCenterIds: () => void;
  isLoading?: boolean;
  isDuplicatingYearPrice?: boolean;
  fetchCatalogPriceLineNumbers: () => void;
  serviceProvider?: ServiceProvider | null;
};

const CatalogPriceTab = (props: CatalogPriceTabProps) => {
  const {
    showDuplicateProduct,
    urbanCenters,
    refreshUrbanCenters,
    selectedYearId,
    fetchCatalogPriceLinesByUrbanCenterIds,
    serviceProviderId,
    isLoading,
    fetchCatalogPriceLineNumbers,
    serviceProvider,
  } = props;
  const [searchParams, setSearchParams] = useSearchParams();
  const [activeKey, setActiveKey] = useState('');
  const [isOpenCreateModal, setIsOpenCreateModal] = useState(false);
  const params = useParams();
  const regionId = parseInt(params.regionId as string);
  const [items, setItems] = useState<Tab[]>([]);

  const onChange = (newActiveKey: string) => {
    setSearchParams({ yearId: `${selectedYearId}`, urbanCenterId: `${newActiveKey?.split('-')[0]}` });
    setActiveKey(newActiveKey);
  };

  // const items = useMemo(
  //   () =>
  //     urbanCenters?.map((urbanCenter) => {
  //       return {
  //         label: urbanCenter.name,
  //         children: (
  //           <CatalogPriceTabItem
  //             urbanCenter={urbanCenter}
  //             serviceProviderId={serviceProviderId}
  //             selectedYearId={selectedYearId}
  //             refreshCatalogPriceUrbanCenters={refreshCatalogPriceUrbanCenters}
  //             fetchCatalogPriceLinesByUrbanCenterIds={fetchCatalogPriceLinesByUrbanCenterIds}
  //           />
  //         ),
  //         key: `${urbanCenter.id}-${selectedYearId}`,
  //         closeIcon: (
  //           <CloseIconItem
  //             urbanCenter={urbanCenter}
  //             refreshCatalogPriceUrbanCenters={refreshCatalogPriceUrbanCenters}
  //             refreshUrbanCenters={refreshUrbanCenters}
  //             selectedYearId={selectedYearId}
  //             onChange={onChange}
  //           />
  //         ),
  //       } as Tab;
  //     }),
  //   [urbanCenters, selectedYearId],
  // );

  useEffect(() => {
    if (urbanCenters?.length) {
      const _items = urbanCenters?.map(async (urbanCenter) => {
        const catalogPriceLineNumbers = await catalogPriceLineService.countCatalogPriceLines({
          'urbanCenterIds[]': `[${urbanCenter.id}]`,
        });
        return {
          label: urbanCenter.name,
          children: (
            <CatalogPriceTabItem
              showDuplicateProduct={showDuplicateProduct}
              urbanCenter={urbanCenter}
              serviceProviderId={serviceProviderId}
              serviceProvider={serviceProvider}
              selectedYearId={selectedYearId}
              fetchCatalogPriceLinesByUrbanCenterIds={fetchCatalogPriceLinesByUrbanCenterIds}
              fetchCatalogPriceLineNumbers={fetchCatalogPriceLineNumbers}
              refreshUrbanCenters={refreshUrbanCenters}
            />
          ),
          key: `${urbanCenter.id}-${selectedYearId}`,
          closeIcon: (
            <CloseIconItem
              urbanCenter={urbanCenter}
              refreshUrbanCenters={refreshUrbanCenters}
              selectedYearId={selectedYearId}
              onChange={onChange}
              isShowDeleteButton={catalogPriceLineNumbers === 0}
            />
          ),
        } as Tab;
      });
      Promise.all(_items).then((items) => {
        if (!isLoading) {
          setItems(items);
          if (
            !searchParams.get('urbanCenterId') ||
            !urbanCenters.some((item) => item?.id?.toString() === searchParams.get('urbanCenterId'))
          ) {
            const key = `${(urbanCenters?.[0]?.id as number).toString()}-${selectedYearId}`;
            setActiveKey(key);
            onChange(key);
          }
        }
      });
    }
  }, [urbanCenters, selectedYearId, isLoading]);

  useEffect(() => {
    if (searchParams.get('urbanCenterId')) {
      setActiveKey(`${searchParams.get('urbanCenterId') as string}-${selectedYearId}`);
    }
  }, [searchParams]);

  const handleShowCreateModal = (value: boolean) => {
    setIsOpenCreateModal(value);
  };

  const add = () => {
    handleShowCreateModal(true);
  };

  const onEdit = (targetKey: React.MouseEvent | React.KeyboardEvent | string, action: 'add' | 'remove') => {
    if (action === 'add') {
      add();
    }
  };

  const handleSubmit = async (data: UrbanCenter) => {
    try {
      const submitData = {
        ...data,
        regionId: regionId,
        yearId: selectedYearId,
        ...(serviceProviderId ? { serviceProviderId, serviceProviderName: serviceProvider?.name } : {}),
      };
      let response;
      if (serviceProviderId) {
        response = await urbanCenterService.createUrbanCenterWithoutRegion(submitData);
      } else {
        response = await urbanCenterService.createUrbanCenter(regionId, submitData);
      }
      console.log('aafewf');
      await Promise.all([refreshUrbanCenters()]);
      onChange(`${(response?.id as number).toString()}-${selectedYearId}`);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  return (
    <div className='mb-10'>
      {items?.length ? (
        <Tabs
          type='editable-card'
          className='service-provider-tarification-tabs'
          onChange={onChange}
          onEdit={onEdit}
          activeKey={activeKey}
          items={items}
          // destroyInactiveTabPane
          addIcon={<PlusCircleOutlined className='urban-center-add-icon' />}
        />
      ) : null}
      <UrbanCenterModal
        action='create'
        isOpenModal={isOpenCreateModal}
        onCancel={() => setIsOpenCreateModal(false)}
        onSubmit={handleSubmit}
      />
    </div>
  );
};

export default CatalogPriceTab;
