import React, { useContext, useEffect, useRef, useState } from 'react';
import type { FormInstance, InputRef } from 'antd';
import { Button, Checkbox, Col, Form, Input, Row, message } from 'antd';
import { CatalogPriceLine, CatalogPriceLineSubOptionZone, CatalogPriceLineZone, Zone } from 'models';
import { MinusCircleOutlined, PlusCircleOutlined, SyncOutlined, WarningOutlined } from '@ant-design/icons';
import { CombineCatalogPriceLine } from 'hooks';
import { Loading } from 'types';
import TextArea, { TextAreaRef } from 'antd/es/input/TextArea';
import {
  catalogPriceLineService,
  catalogPriceLineSubOptionZoneService,
  catalogPriceLineZoneCheckedService,
  catalogPriceLineZoneService,
} from 'services';

const EditableContext = React.createContext<FormInstance | null>(null);

export const EditableCatalogPriceRow = (props: React.HTMLAttributes<HTMLTableRowElement>) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableCatalogPriceCellProps {
  title: string;
  editable: boolean;
  children: React.ReactNode;
  inputType: string;
  required: boolean;
  dataIndex: string;
  zoneId: number;
  record?: CombineCatalogPriceLine;
  zones: Zone[];
  className?: string;
  handleChangeDataSource: (catalogPriceLine: CatalogPriceLine) => void;
  selectedZonesKeys?: Record<number, Record<number, boolean>>;
  handleChangeSelectedZonesKeys: (catalogPriceLineId: number, zoneId: number, value: boolean) => void;
  reloadTable: () => void;
}

const getCatalogPriceLineZone = (record?: CombineCatalogPriceLine, zoneId?: number | null) => {
  const catalogPrice = record?.CatalogPrices?.[0];
  if (catalogPrice?.priceOptionId) {
    if (catalogPrice?.PriceSubOption) {
      return (
        record?.CatalogPriceLineSubOptionZones?.findIndex(
          (i) =>
            i.priceSubOptionId === catalogPrice?.PriceSubOption?.id &&
            i.zoneId === zoneId &&
            i.catalogPriceLineCatalogPriceId === catalogPrice?.CatalogPriceLineCatalogPrice?.id,
        ) ?? -1
      );
    }
  }
  return (
    record?.CatalogPriceLineZones?.findIndex(
      (i) => i.zoneId === zoneId && i.catalogPriceLineCatalogPriceId === catalogPrice?.CatalogPriceLineCatalogPrice?.id,
    ) ?? -1
  );
};

const getPriceValue = (record?: CombineCatalogPriceLine, zoneId?: number) => {
  const catalogPrice = record?.CatalogPrices?.[0];
  if (catalogPrice?.priceOptionId) {
    if (catalogPrice?.PriceSubOption) {
      return parseFloat(
        record?.CatalogPriceLineSubOptionZones?.find(
          (i) =>
            i.priceSubOptionId === catalogPrice?.PriceSubOption?.id &&
            i.zoneId === zoneId &&
            i.catalogPriceLineCatalogPriceId === catalogPrice?.CatalogPriceLineCatalogPrice?.id,
        )?.priceValue || '0',
      );
    }
  }
  return parseFloat(
    record?.CatalogPriceLineZones?.find(
      (i) => i.zoneId === zoneId && i.catalogPriceLineCatalogPriceId === catalogPrice?.CatalogPriceLineCatalogPrice?.id,
    )?.priceValue || '0',
  );
};

export const EditableCatalogPriceCell: React.FC<EditableCatalogPriceCellProps> = ({
  title,
  editable,
  children,
  dataIndex,
  record,
  zoneId,
  zones,
  handleChangeDataSource,
  selectedZonesKeys,
  handleChangeSelectedZonesKeys,
  reloadTable,
  ...restProps
}) => {
  const fieldName = `zone-${zoneId}`;
  const [value, setValue] = useState<number>(getPriceValue(record, zoneId));
  const [editing, setEditing] = useState<boolean>(false);
  const [loading, setLoading] = useState<Loading>('idle');
  const [loadingComment, setLoadingComment] = useState<Loading>('idle');
  const [comment, setComment] = useState<string>(record?.comment || '');
  const [isOpenCommentaire, setIsOpenCommentaire] = useState<boolean>(!!comment);
  const inputRef = useRef<InputRef>(null);
  const textareaRef = useRef<TextAreaRef>(null);
  const form = useContext(EditableContext)!;

  const isSubOption = record?.CatalogPrices?.[0]?.PriceSubOption ? true : false;
  const priceLineZones = (isSubOption ? record?.CatalogPriceLineSubOptionZones : record?.CatalogPriceLineZones) ?? [];
  useEffect(() => {
    if (editing) {
      if (inputRef.current) inputRef.current!.focus();
    }
  }, [editing]);

  useEffect(() => {
    setValue(getPriceValue(record, zoneId));
  }, [record, zoneId]);

  useEffect(() => {
    if (isOpenCommentaire) {
      form.setFieldValue('comment', comment);
      if (textareaRef.current) textareaRef.current!.focus();
    }
  }, [isOpenCommentaire]);

  const toggleEdit = () => {
    setEditing(!editing);
    form.setFieldsValue({ [fieldName]: value });
  };

  const onSelect = async (value: boolean) => {
    try {
      if (record?.id) {
        handleChangeSelectedZonesKeys(record.id, zoneId ?? 0, value);
        await catalogPriceLineZoneCheckedService.createOrUpdateCatalogPriceLinesZoneChecked([
          {
            catalogPriceLineId: record.id,
            zoneId: zoneId,
            isChecked: value,
          },
        ]);
        reloadTable();
      }
    } catch (error) {
      console.log(error);
      message.error('Erreur');
    }
  };

  const save = async () => {
    try {
      const values = await form.validateFields([fieldName]);
      toggleEdit();
      if (form.isFieldTouched(fieldName)) {
        try {
          setLoading('pending');
          let updatedCatalogPriceLineZone;
          if (isSubOption) {
            updatedCatalogPriceLineZone =
              await catalogPriceLineSubOptionZoneService.createCatalogPriceLineSubOptionZone({
                catalogPriceLineCatalogPriceId: record?.CatalogPrices?.[0]?.CatalogPriceLineCatalogPrice?.id,
                priceSubOptionId: record?.CatalogPrices?.[0]?.PriceSubOption?.id,
                isAllZone: dataIndex === 'TouteZone',
                zoneId: zoneId,
                priceValue: values[fieldName],
                ...(dataIndex === 'TouteZone' ? { zoneIds: zones.map((i) => i.id) } : {}),
              } as CatalogPriceLineSubOptionZone);
          } else {
            updatedCatalogPriceLineZone = await catalogPriceLineZoneService.createCatalogPriceLineZone({
              catalogPriceLineCatalogPriceId: record?.CatalogPrices?.[0]?.CatalogPriceLineCatalogPrice?.id,
              isAllZone: dataIndex === 'TouteZone',
              zoneId: zoneId,
              priceValue: values[fieldName],
              ...(dataIndex === 'TouteZone' ? { zoneIds: zones.map((i) => i.id) } : {}),
            } as CatalogPriceLineZone);
          }
          onChangeDataSource(values[fieldName], updatedCatalogPriceLineZone);
          setLoading('idle');
        } catch (error) {
          console.log(error);
          message.error('Erreur');
          setLoading('failed');
        }
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  const onChangeDataSource = (price: string, updatedRecord: CatalogPriceLineZone) => {
    if (dataIndex === 'TouteZone') {
      const updatedPriceLineZones = zones.map((zone) => {
        const catalogPriceLineZoneIndex = getCatalogPriceLineZone(record, zone.id as number);
        if (catalogPriceLineZoneIndex !== -1) {
          return {
            ...priceLineZones[catalogPriceLineZoneIndex],
            priceValue: price,
          };
        } else {
          return {
            zoneId: zone.id,
            catalogPriceLineCatalogPriceId: record?.CatalogPrices?.[0]?.CatalogPriceLineCatalogPrice?.id,
            priceValue: price,
            isAllZone: false,
            priceSubOptionId: record?.CatalogPrices?.[0]?.PriceSubOption?.id
              ? record?.CatalogPrices?.[0]?.PriceSubOption?.id
              : updatedRecord?.priceSubOptionId,
          } as CatalogPriceLineZone;
        }
      });
      updatedPriceLineZones.push(updatedRecord);
      handleChangeDataSource({
        ...record,
        ...(isSubOption
          ? { CatalogPriceLineSubOptionZones: updatedPriceLineZones }
          : { CatalogPriceLineZones: updatedPriceLineZones }),
      } as CatalogPriceLine);
    } else {
      const touteZoneIndex = getCatalogPriceLineZone(record, null);
      const catalogPriceLineZoneIndex = getCatalogPriceLineZone(record, zoneId);
      const updatedPriceLineZones = priceLineZones;
      if (touteZoneIndex !== -1) {
        updatedPriceLineZones[touteZoneIndex].priceValue = '0';
      }
      if (catalogPriceLineZoneIndex !== -1) {
        updatedPriceLineZones[catalogPriceLineZoneIndex] = updatedRecord;
      } else {
        updatedPriceLineZones.push(updatedRecord);
      }
      handleChangeDataSource({
        ...record,
        ...(isSubOption
          ? { CatalogPriceLineSubOptionZones: updatedPriceLineZones }
          : { CatalogPriceLineZones: updatedPriceLineZones }),
      } as CatalogPriceLine);
    }
  };
  const saveComment = async () => {
    try {
      const values = await form.validateFields(['comment']);
      if (form.isFieldTouched('comment')) {
        try {
          setLoadingComment('pending');
          await catalogPriceLineService.updateCatalogPriceLine(
            record?.id as number,
            {
              comment: values['comment'],
            } as CatalogPriceLine,
          );
          form.setFields([{ name: 'comment', value: comment, touched: false }]);
          setLoadingComment('idle');
        } catch (error) {
          console.log(error);
          message.error('Erreur');
          setLoadingComment('failed');
        }
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };
  let selectedZonesComponent: JSX.Element | null = null;
  let childNode = children;
  if (title === 'Prix') {
    const catalogPrice = record?.CatalogPrices?.[0];
    if (catalogPrice?.priceOptionId) {
      childNode = (
        <div className='sub-option-container'>
          <div className='sub-option-name'>
            {catalogPrice?.PriceOption?.name}
            {catalogPrice?.PriceSubOption && ':'}
          </div>
          {catalogPrice?.PriceSubOption && <div> {catalogPrice?.PriceSubOption?.name}</div>}
        </div>
      );
    } else if (catalogPrice?.priceId) {
      childNode = <span className='text-underline'>{catalogPrice?.Price?.name}</span>;
    } else {
      childNode = <span className='text-underline'>{catalogPrice?.PriceFamily?.name}</span>;
    }
  } else if (editable) {
    childNode = editing ? (
      <Form.Item
        style={{ margin: 0 }}
        name={fieldName}
        rules={[
          {
            required: true,
            message: '',
          },
          () => ({
            validator(_, value: string | number) {
              if (value === '') {
                return Promise.reject();
              }
              if (isNaN(value as number)) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input
          ref={inputRef}
          onPressEnter={save}
          onBlur={save}
          suffix={'€'}
          onChange={(e) => setValue(parseFloat(e.target.value))}
        />
      </Form.Item>
    ) : (
      <div
        className={`editable-cell-value-wrap ${loading === 'pending' || loading === 'failed' ? 'right-icon' : ''}`}
        onClick={toggleEdit}
      >
        {value?.toFixed(2)} €
        {loading === 'pending' && <SyncOutlined style={{ fontSize: 16, marginLeft: 8, color: '#A6C84D' }} spin />}
        {loading === 'failed' && <WarningOutlined style={{ fontSize: 16, marginLeft: 8, color: '#FF4D4F' }} />}
      </div>
    );
    childNode = (
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 12 }}>{childNode}</Col>
      </Row>
    );
    if (dataIndex === 'RealPrice' && record?.isFirst) {
      selectedZonesComponent = (
        <Checkbox
          className='position-absolute r-4 t-4'
          checked={selectedZonesKeys?.[record.id]?.[zoneId ?? 0]}
          onChange={(e) => onSelect(e.target.checked)}
        />
      );
    }
  }
  const commentComponent = (
    <div className='catalog-price-comment'>
      <div>
        <Button
          type='text'
          size='small'
          loading={loadingComment === 'pending'}
          className='btn-add__ajouter-btn mb-2'
          onClick={() => setIsOpenCommentaire(!isOpenCommentaire)}
        >
          {isOpenCommentaire ? (
            <MinusCircleOutlined className='btn-add__add_icon' />
          ) : (
            <PlusCircleOutlined className='btn-add__add_icon' />
          )}
          Commentaire
        </Button>
      </div>
      <div>
        {isOpenCommentaire && (
          <Form.Item style={{ margin: 0 }} name={'comment'}>
            <TextArea
              size='large'
              rows={3}
              onPressEnter={saveComment}
              onBlur={saveComment}
              ref={textareaRef}
              onChange={(e) => setComment(e.target.value)}
              placeholder='Description'
              className='region-information__description-textarea'
            />
          </Form.Item>
        )}
      </div>
    </div>
  );
  const className = restProps?.className;

  return (
    <td
      {...restProps}
      className={`${className} ant-table-cell ${zoneId ? 'service-provider-price-table__zone' : ''} ${
        record?.isLast === false ? 'non-bottom-border' : ''
      } ${isSubOption ? 'sub-option-line' : ''}`}
    >
      {(dataIndex === 'TouteZone' || dataIndex === 'RealPrice') && !record?.isDisplay ? null : childNode}
      {selectedZonesComponent}
      {title === 'Prix' && record?.isLast ? commentComponent : null}
    </td>
  );
};
