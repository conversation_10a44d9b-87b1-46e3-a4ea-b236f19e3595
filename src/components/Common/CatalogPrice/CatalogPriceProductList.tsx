import { EditOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { useState } from 'react';
import { CatalogPriceLineProduct } from 'models';
import CatalogPriceProduitPopup from './CatalogPriceProduitPopup';

const CatalogPriceProductList = ({ items, onFetch }: { items: CatalogPriceLineProduct[]; onFetch: () => void }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const handleCancel = () => {
    setIsModalOpen(false);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  return (
    <div
      style={{
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
      }}
    >
      <div>
        {items?.map((item) => (
          <p key={item.id} style={{ marginBottom: '8px' }} className={item.Product?.isVisible ? '' : 'strike-through'}>
            {item.Product?.name}
          </p>
        ))}
      </div>
      <Button
        onClick={handleOpenModal}
        type='link'
        icon={<EditOutlined />}
        style={{ marginRight: 0, marginLeft: 5 }}
        className='datatable__action-edit-button'
      />
      <CatalogPriceProduitPopup
        isModalOpen={isModalOpen}
        handleCancel={handleCancel}
        catalogPriceLineId={items[0]?.catalogPriceLineId}
        productTypeId={items[0]?.Product?.productTypeId}
        onFetch={onFetch}
        catalogPriceLineProducts={items}
      />
    </div>
  );
};

export default CatalogPriceProductList;
