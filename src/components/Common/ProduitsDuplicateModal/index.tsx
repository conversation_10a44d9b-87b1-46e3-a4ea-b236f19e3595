import { Button, Form, Modal, Select, Space, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { catalogPriceLineService, urbanCenterService, yearService } from '../../../services';
import { ServiceProvider, UrbanCenter, Year } from '../../../models';
import service_providerService from '../../../services/service_provider.service';
const { Option } = Select;

const ProduitsDuplicateModal = ({
  isOpenModal,
  onCancel,
  onSubmit,
  urbanCenterId,
  fetchCatalogPriceLineNumbers,
}: {
  isOpenModal: boolean;
  onCancel: () => void;
  onSubmit: (submitData: object[]) => void;
  urbanCenterId?: number;
  fetchCatalogPriceLineNumbers: () => void;
}) => {
  const [form] = Form.useForm();
  const [isSubmited, setIsSubmited] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [selectedServiceProviderId, setSelectedServiceProviderId] = useState<number | null>(null);
  const [selectedUrbanCenterId, setSelectedUrbanCenterId] = useState<number | null>(null);
  const [selectedYearId, setSelectedYearId] = useState<number | null>(null);
  const [serviceProviders, setServiceProviders] = useState<ServiceProvider[]>([]);
  const [years, setYears] = useState<Year[]>([]);
  const [urbanCenters, setUrbanCenters] = useState<UrbanCenter[]>([]);

  useEffect(() => {
    if (isOpenModal) {
      getListServiceProviderIds();
    }
  }, [isOpenModal]);

  const handleCancel = () => {
    form.resetFields();
    setIsSubmited(false);
    onCancel();
  };

  const getListServiceProviderIds = async () => {
    const listCatalogPriceLineProducts = await catalogPriceLineService.getCatalogPriceLines({
      limit: 'unlimited',
      include: 'CatalogPriceUrbanCenter',
      isActive: 1,
    });
    const serviceProviderIds = [
      ...new Set(listCatalogPriceLineProducts.rows.map((item) => item.CatalogPriceUrbanCenter?.serviceProviderId)),
    ];
    const serviceProvidersResponse = await service_providerService.getServiceProviders({
      'id[]': JSON.stringify(serviceProviderIds),
      limit: 'unlimited',
    });
    setServiceProviders(serviceProvidersResponse.rows);
  };

  const getUrbanCentersByServiceProvider = async (serviceProviderId: number) => {
    const listCatalogPriceLineProducts = await catalogPriceLineService.getCatalogPriceLines({
      limit: 'unlimited',
      include: 'CatalogPriceUrbanCenter',
      serviceProviderId: serviceProviderId,
      isActive: 1,
    });
    const urbanCenterIds = [
      ...new Set(listCatalogPriceLineProducts.rows.map((item) => item.CatalogPriceUrbanCenter?.urbanCenterId)),
    ];
    const response = await urbanCenterService.getUrbanCenters(undefined, { serviceProviderId });
    const urbanCenterWithProduct = response.filter(
      (center) => center.id !== undefined && urbanCenterIds.includes(center.id),
    );
    setUrbanCenters(urbanCenterWithProduct);
  };

  const getYearByServiceProviderAndUrbanCenter = async (urbanCenterId: number) => {
    const listCatalogPriceLineProducts = await catalogPriceLineService.getCatalogPriceLines({
      limit: 'unlimited',
      include: 'CatalogPriceUrbanCenter',
      serviceProviderId: selectedServiceProviderId,
      urbanCenterId: urbanCenterId,
      isActive: 1,
    });
    const yearIds = [...new Set(listCatalogPriceLineProducts.rows.map((item) => item.CatalogPriceUrbanCenter?.yearId))];
    const yearsReponse = await yearService.getYears({
      'id[]': JSON.stringify(yearIds),
    });
    setYears(yearsReponse);
  };

  const handleSubmit = async () => {
    setIsSubmited(true);
    try {
      await form.validateFields();
      const filteredParams = {
        urbanCenterId: selectedUrbanCenterId,
        yearId: selectedYearId,
        serviceProviderId: selectedServiceProviderId,
        isActive: 1,
        include: ['CatalogPrices', 'CatalogPriceLineProducts'].join('|'),
        limit: 'unlimited',
      };
      const catalogPriceLineNumbers = await fetchCatalogPriceLineNumbers();
      const isYearPriceActive = catalogPriceLineNumbers !== undefined && catalogPriceLineNumbers > 0;
      setIsSubmiting(true);
      const catalogPriceLines = await catalogPriceLineService.getCatalogPriceLines(filteredParams);
      const submitDatas = [];
      for (const catalogPriceLine of catalogPriceLines?.rows ?? []) {
        const productIds = [...new Set(catalogPriceLine.CatalogPriceLineProducts?.map((p) => p.productId))];
        const submitData = {
          priceFamilyId: catalogPriceLine.CatalogPrices?.[0]?.priceFamilyId,
          productIds: productIds,
          productTypeId: catalogPriceLine.CatalogPrices?.[0]?.productTypeId,
          ...(urbanCenterId ? { urbanCenterId } : {}),
          isVisible: isYearPriceActive,
        };
        submitDatas.push(submitData);
      }
      onSubmit(submitDatas);
      toast.success('Succès');
      setIsSubmiting(false);
      setIsSubmited(false);
    } catch (error) {
      console.error(error);
      setIsSubmiting(false);
      toast.error('Erreur');
    }
  };

  return (
    <Modal
      title='DUPLICATION PRODUITS'
      open={isOpenModal}
      maskClosable={false}
      onCancel={handleCancel}
      className='duplicate-produits-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={handleSubmit}
          loading={isSubmiting}
        >
          Valider
        </Button>,
      ]}
    >
      <Space direction='vertical' className='duplicate-produits-modal__body'>
        <Form
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
          validateMessages={{ required: '' }}
        >
          <Space direction='vertical' className='duplicate-produits-modal__body'>
            <Form.Item
              label='Sélectionner le prestataire'
              name='serviceProviderId'
              className='duplicate-produits-modal__form-input'
              rules={[{ required: true }]}
            >
              <Select
                placeholder='Sélectionner'
                className='duplicate-produits-modal__selection'
                value={selectedServiceProviderId}
                onChange={(value) => {
                  setSelectedServiceProviderId(value);
                  getUrbanCentersByServiceProvider(value);
                }}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={serviceProviders?.map((option) => ({
                  value: option.id,
                  label: option.name,
                }))}
              ></Select>
            </Form.Item>
            <Form.Item
              label='Sélectionner le centre urbain'
              name='centerUrbanId'
              className='duplicate-produits-modal__form-input'
              rules={[{ required: true }]}
            >
              <Select
                placeholder='Sélectionner'
                className='duplicate-produits-modal__selection'
                value={selectedUrbanCenterId}
                onChange={(value) => {
                  setSelectedUrbanCenterId(value);
                  getYearByServiceProviderAndUrbanCenter(value);
                }}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={urbanCenters?.map((option) => ({
                  value: option.id,
                  label: option.name,
                }))}
              ></Select>
            </Form.Item>
            <Form.Item
              label="Sélectionner l'année"
              name='year'
              initialValue={selectedYearId}
              className='duplicate-produits-modal__form-input'
              rules={[{ required: true }]}
            >
              <Select
                placeholder='Année'
                className='duplicate-produits-modal__selection'
                value={selectedYearId}
                onChange={(value) => setSelectedYearId(value)}
              >
                {years.map((year) => (
                  <Option key={year.id} value={year.id}>
                    {year.year}
                  </Option>
                ))}
              </Select>
            </Form.Item>
            <Typography.Text style={{ fontWeight: 'bold' }}>
              Note: Toute la base produits du prestataire sélectionné, pour l&apos;année selectionée, sera dupliqués
              vers ce prestataire. Les produits importés auront le même type de tarif. Prix et commentaires ne seront
              pas dupliqués.
            </Typography.Text>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};
export default ProduitsDuplicateModal;
