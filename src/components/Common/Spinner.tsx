import { CSSProperties, ReactElement } from 'react';
import { Spin } from 'antd';
import { HashLoader } from 'react-spinners';

const override: CSSProperties = {
  margin: '0 auto',
  borderColor: 'red',
  position: 'fixed',
  top: 0,
  left: 0,
  height: '100%',
  width: '100%',
};

function Spinner({ children, loading }: { children?: ReactElement, loading?: boolean }) {
  return (
      <Spin
        spinning={loading}
        indicator={
          <HashLoader
            color="#A6C84D"
            size={60}
            cssOverride={override}
            aria-label="Loading Spinner"
            data-testid="loader"
          />
        }
      >
        {children}
      </Spin>
  );
}

export default Spinner;
