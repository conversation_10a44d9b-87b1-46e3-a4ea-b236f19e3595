import React from 'react';
import { ReactNode } from 'react';

interface FlexProps {
  children?: ReactNode;
  direction?: string;
  justify?: string;
  align?: string;
  className?: string;
  vertical?: boolean;
  style?: React.CSSProperties;
}

const Flex = ({ children, direction, justify, align, className, vertical, style }: FlexProps) => {
  const classNames = [
    'flex',
    direction && `flex-${direction}`,
    justify && `justify-${justify}`,
    align && `align-${align}`,
    vertical && 'flex-vertical', // Use the value of vertical here
    className,
  ].filter(Boolean).join(' ');

  return (
    <div className={classNames} style={style}>
      {children}
    </div>
  );
};

Flex.defaultProps = {
  direction: 'row',
  justify: 'start',
  align: 'start',
};

export default Flex;