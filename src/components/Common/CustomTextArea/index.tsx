import React, { ReactNode } from 'react';
const CustomTextArea = ({ children }: { children?: ReactNode }) => {
  return (
    <div className="custom-textarea">
      {React.Children.map(children, (child) => {
        if (React.isValidElement(child) && child.props?.str) {
          return child;
        }
        return <label className="custom-textarea__label">Description</label>;
      })}
    </div>
  );
};

export default CustomTextArea;
