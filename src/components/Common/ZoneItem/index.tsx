import { InputNumber } from 'antd';
import { Zone } from 'models';
import { useState, useEffect } from 'react';

const ZoneItem = ({
  zone,
  zoneIndex,
  zoneList,
  handleChangeZoneList
}: {
  zone: Zone;
  zoneIndex: number;
  zoneList: Zone[];
  handleChangeZoneList: (newValue: number | null, zoneIndex: number) => void;
}) => {
  const [error, setError] = useState<boolean>(false);

  const handleValueValidation = (
    value: number | null,
    existingList: Zone[]
  ) => {
    if (value !== null) {
      if (zoneIndex !== 0) {
        // Check for the last item
        if (zoneIndex === existingList.length - 1) {
          const previousZone = existingList[zoneIndex - 1]
            ?.maxDistance as number;
          const currentZone = value;
          if (currentZone <= previousZone) {
            return false;
          } else {
            return true;
          }
        }
        // Check for the middle item
        if (zoneIndex > 0) {
          const previousZone = existingList[zoneIndex - 1]
            ?.maxDistance as number;
          const immediateZone = existingList[zoneIndex + 1]
            ?.maxDistance as number;
          const currentZone = value;
          if (
            (previousZone != null && currentZone <= previousZone) ||
            (immediateZone != null && currentZone >= immediateZone)
          ) {
            return false;
          } else {
            return true;
          }
        }
      } else {
        // Check for the first item
        const immediateZone = existingList[zoneIndex + 1]
          ?.maxDistance as number;
        const currentZone = value;
        if (immediateZone && currentZone >= immediateZone) {
          return false;
        } else {
          return true;
        }
      }
    } else {
      return true;
    }
  };

  useEffect(() => {
    const validationResult = handleValueValidation(zone.maxDistance, zoneList);
    if (!validationResult) {
      setError(true);
      return;
    }
    setError(false);
  }, [zone, zoneList]);

  const handleChangeZoneValue = (maxDistance: number | null) => {
    handleChangeZoneList(maxDistance, zoneIndex);
    const validationResult = handleValueValidation(maxDistance, zoneList);
    if (!validationResult) {
      setError(true);
      return;
    }
    setError(false);
  };

  return (
    <InputNumber
      placeholder='Input'
      value={zone.maxDistance}
      onChange={handleChangeZoneValue}
      className='center-urban__zone-input input-number-non-spinner'
      style={{ borderColor: error ? '#FF4D4F' : '' }}
    />
  );
};

export default ZoneItem;
