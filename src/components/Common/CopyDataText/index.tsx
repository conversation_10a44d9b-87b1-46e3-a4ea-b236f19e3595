import { CopyOutlined } from '@ant-design/icons';
import { Row, Col } from 'antd';
import { useState } from 'react';
import { toast } from 'react-toastify';
import { Tooltip } from 'antd';

interface CopyDataTextProps {
  label?: string;
  text: string;
  htmlContent?: string;
  className?: string;
  onClick?: () => void;
  styleMode?: 'default' | 'form';
}

export default function CopyDataTextDocument({
  label,
  text,
  htmlContent,
  className = '',
  onClick,
  styleMode = 'default',
}: CopyDataTextProps) {
  const [isShowIconCopy, setIsShowIconCopy] = useState(false);

  const handleMouseOver = () => {
    setIsShowIconCopy(true);
  };

  const handleMouseOut = () => {
    setIsShowIconCopy(false);
  };

  const handleCopy = () => {
    if (onClick) {
      onClick();
    } else {
      if (htmlContent) {
        const clipboardItem = new ClipboardItem({
          'text/plain': new Blob([text], { type: 'text/plain' }),
          'text/html': new Blob([htmlContent], { type: 'text/html' }),
        });
        navigator.clipboard.write([clipboardItem]);
      } else {
        navigator.clipboard.writeText(text);
      }
      toast.success(<div>Copié dans le presse-papier</div>);
    }
  };

  if (styleMode === 'form') {
    return (
      <Tooltip title='Copier' placement='bottomLeft'>
        <CopyOutlined onClick={handleCopy} className={`copy-icon ${className}`} />
      </Tooltip>
    );
  }

  return (
    <Row
      align='middle'
      onMouseOver={handleMouseOver}
      onMouseOut={handleMouseOut}
      gutter={[16, 0]}
      className={`copy-data-text ${className}`}
    >
      {label ? (
        <>
          <Col span={10} className='label-col'>
            <p>{label} :</p>
          </Col>
          <Col span={14} className='value-col'>
            {htmlContent ? <div dangerouslySetInnerHTML={{ __html: htmlContent }} /> : <div>{text}</div>}
            <Tooltip title='Copier' placement='bottomLeft'>
              {isShowIconCopy && text && text.length > 0 ? (
                <CopyOutlined onClick={handleCopy} className='copy-icon' />
              ) : (
                <div />
              )}
            </Tooltip>
          </Col>
        </>
      ) : (
        <Col span={24} className='value-col'>
          {htmlContent ? <div dangerouslySetInnerHTML={{ __html: htmlContent }} /> : <div>{text}</div>}
          <Tooltip title='Copier' placement='bottomLeft'>
            {isShowIconCopy && text && text.length > 0 ? (
              <CopyOutlined onClick={handleCopy} className='copy-icon' />
            ) : (
              <div />
            )}
          </Tooltip>
        </Col>
      )}
    </Row>
  );
}
