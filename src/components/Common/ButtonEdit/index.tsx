import { CheckOutlined, CloseOutlined, EditOutlined, LoadingOutlined } from '@ant-design/icons';

type ButtonEditProps = {
  loading?: boolean;
  className?: string;
  isEditing?: boolean;
  onEdit: () => void;
  onSubmit: () => void;
  onCancel: () => void;
};

const ButtonEdit = (props: ButtonEditProps) => {
  const { loading, className, isEditing, onEdit, onSubmit, onCancel } = props;
  return (
    <div className={`btn-edit ${className ?? ''}`}>
      {!isEditing ? (
        <EditOutlined className='edit-icon' onClick={onEdit} />
      ) : (
        <>
          {loading ? (
            <LoadingOutlined className='product-description__description-loading-icon ignore-blur' />
          ) : (
            <CheckOutlined
              className='product-description__description-apply-edit-icon check-button ignore-blur'
              onClick={onSubmit}
            />
          )}
          <CloseOutlined className='product-description__description-cancel-icon cancel-button' onClick={onCancel} />
        </>
      )}
    </div>
  );
};

const ButtonEditSave = (props: ButtonEditProps) => {
  const { loading, className, isEditing, onEdit, onSubmit } = props;
  return (
    <div className={`btn-edit ${className ?? ''}`}>
      {!isEditing ? (
        <EditOutlined className='edit-icon' onClick={onEdit} />
      ) : (
        <>
          {loading ? (
            <LoadingOutlined className='product-description__description-loading-icon ignore-blur' />
          ) : (
            <CheckOutlined
              className='product-description__description-apply-edit-icon check-button ignore-blur'
              onClick={onSubmit}
            />
          )}
        </>
      )}
    </div>
  );
};
export { ButtonEdit, ButtonEditSave };
