import { useEffect, useState } from 'react';
import ReactGoogleAutocomplete from 'react-google-autocomplete';

interface AddressInputProps {
  error?: boolean;
  defaultAddress?: string;
  onSelect?: any;
  onChange?: any;
  otherStyles?: any;
}

const AddressInput = (props: AddressInputProps) => {
  const { error, defaultAddress, onSelect, onChange } = props;
  const [inputValue, setInputValue] = useState<string>(defaultAddress ?? '');

  useEffect(() => {
    setInputValue(defaultAddress ?? '');
  }, [defaultAddress]);

  const handleChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setInputValue(event.target.value);
    if (onChange) {
      onChange(event);
    }
  };

  return (
    <ReactGoogleAutocomplete
      style={{ ...props.otherStyles }}
      className={`urban-center-modal google-auto-complete-input ${error ? 'input-error' : ''}`}
      value={inputValue}
      apiKey={`${process.env.REACT_APP_GOOGLE_MAPS_API_KEY}`}
      onChange={handleChange}
      placeholder='Adresse'
      onPlaceSelected={onSelect}
      options={{
        types: ['address'],
        fields: [
          'address_components',
          'geometry.location',
          'place_id',
          'formatted_address',
        ],
      }}
    />
  );
};

export default AddressInput;
