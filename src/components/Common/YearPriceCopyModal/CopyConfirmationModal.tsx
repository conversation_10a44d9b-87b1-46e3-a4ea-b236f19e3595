import { Button, Modal, Space, Form, Typography, FormInstance } from 'antd';
import { Year } from 'models';
const { Text } = Typography;

const CopyConfirmationModal = ({
  form,
  isOpenModal,
  onCancel,
  onSubmit,
  selectedYear,
  isLoading,
}: {
  form: FormInstance;
  isOpenModal: boolean;
  onCancel: () => void;
  onSubmit: () => void;
  selectedYear?: Year;
  isLoading: boolean;
}) => {
  return (
    <Modal
      title='Copier les prix de l’année'
      open={isOpenModal}
      onOk={onSubmit}
      maskClosable={false}
      onCancel={onCancel}
      className='adding-produits-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={onCancel} className='ant-modal-content__cancel-btn'>
          Non
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={onSubmit}
          loading={isLoading}
        >
          Oui
        </Button>,
      ]}
    >
      <Space direction='vertical' className='adding-produits-modal__body'>
        <Form form={form} autoComplete='off' validateMessages={{ required: '' }}>
          <Space direction='vertical' className='adding-produits-modal__body'>
            <Text>Tous les prix de {selectedYear?.year ?? ''} vont être effacés, êtes-vous sûr ?</Text>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};

export default CopyConfirmationModal;
