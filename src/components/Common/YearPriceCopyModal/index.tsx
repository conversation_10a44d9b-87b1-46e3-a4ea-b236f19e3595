import { Button, Modal, Space, Form, Select } from 'antd';
import { Year } from 'models';
import { useState } from 'react';
import { toast } from 'react-toastify';
const { Option } = Select;

const YearPriceCopyModal = ({
  isOpenModal,
  onCancel,
  onShowCopyConfirmation,
  yearList,
  onSelectYear,
}: {
  isOpenModal: boolean;
  onCancel: () => void;
  onShowCopyConfirmation: (isOpen: boolean) => void;
  yearList: Year[];
  onSelectYear: (year: Year) => void;
}) => {
  const [form] = Form.useForm();
  const [isSubmited, setIsSubmited] = useState(false);

  const handleCancel = () => {
    form.resetFields();
    onCancel();
  };

  const handleChangeYear = (yearId: number) => {
    const year = yearList.find((year) => year.id === yearId);
    if (year) {
      onSelectYear(year);
    }
  };

  const handleCopy = async () => {
    try {
      await form.validateFields();
      onCancel();
      onShowCopyConfirmation(true);
    } catch (error) {
      toast.error('Erreur');
    }
  };

  return (
    <Modal
      title='Copier les prix de l’année'
      open={isOpenModal}
      maskClosable={false}
      onCancel={handleCancel}
      className='adding-produits-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button key='ajouter' type='primary' className='ant-modal-content__add-btn' onClick={handleCopy}>
          Valider
        </Button>,
      ]}
    >
      <Space direction='vertical' className='adding-produits-modal__body'>
        <Form
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
          validateMessages={{ required: '' }}
        >
          <Space direction='vertical' className='adding-produits-modal__body'>
            <Form.Item
              label="Sélectionner l'année à partir de laquelle cloner"
              name='duplicatedYearId'
              rules={[{ required: true }]}
              className='adding-produits-modal__form-input'
            >
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                onChange={handleChangeYear}
              >
                {yearList.map((year) => (
                  <Option key={year.id} value={year.id}>
                    {year.year}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};

export default YearPriceCopyModal;
