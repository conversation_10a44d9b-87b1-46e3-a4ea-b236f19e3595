import { Button, Form, Modal, Select, Space, Tag } from 'antd';
import {
  useProductTypeById,
  useProductTypeByIdQuery,
  useCatalogPriceProductTypes,
  useCatalogPriceProductTypesQuery,
  useCatalogPricesQuery,
  useCatalogPrices,
} from 'hooks';
import { CatalogPriceLine, PriceFamily, Product, ProductType } from 'models';
import React, { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
const { Option } = Select;

const AddProduitsModal = ({
  isCatalog,
  isOpenModal,
  onCancel,
  onSubmit,
  urbanCenterId,
  fetchCatalogPriceLineNumbers
}: {
  isCatalog: boolean;
  isOpenModal: boolean;
  onCancel: () => void;
  onSubmit: (submitData: CatalogPriceLine) => void;
  urbanCenterId?: number;
  fetchCatalogPriceLineNumbers: () => void
}) => {
  const [form] = Form.useForm();
  const [isSubmited, setIsSubmited] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [selectedProductTypeId, setSelectedProductTypeId] = useState<number | null>(null);
  const [products, setProducts] = useState<Product[]>([]);
  const [priceFamilies, setPriceFamilies] = useState<PriceFamily[]>([]);
  const [productTypesQuery] = useCatalogPriceProductTypesQuery({
    isCatalog,
    limit: 'unlimited',
    isVisible: 1,
    isActive: 1,
  });
  const [serviceProviderProductTypes, , loadingCatalogPriceProductTypes] =
    useCatalogPriceProductTypes(productTypesQuery);
  const [productTypeByIdQuery] = useProductTypeByIdQuery({
    productTypeId: selectedProductTypeId,
    include: 'Products',
    isVisible: 1,
    isActive: 1,
  });
  const [productType, , loadingProductType] = useProductTypeById(productTypeByIdQuery);
  const [catalogPricesQuery] = useCatalogPricesQuery({
    productTypeId: selectedProductTypeId,
    isCatalog,
  });
  const [catalogPrices] = useCatalogPrices(catalogPricesQuery);

  const fetchProductsAndPriceFamiliesList = async (selectedProductType: ProductType) => {
    try {
      if (selectedProductType.Products) {
        setProducts(selectedProductType.Products.filter((product) => product.isVisible));
      }
      if (selectedProductTypeId) {
        const priceFamilyInCatalogPrices = catalogPrices
          .filter((item) => item?.PriceFamily && item.PriceFamily !== null)
          .map((item) => item.PriceFamily as PriceFamily)
          .reduce((acc, current) => {
            if (current && current.id) {
              acc.set(current.id, current);
            }
            return acc;
          }, new Map())
          .values();
        setPriceFamilies(Array.from(priceFamilyInCatalogPrices));
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  useEffect(() => {
    if (productType) {
      fetchProductsAndPriceFamiliesList(productType);
    }
  }, [productType?.Products, productType?.PriceFamilies, catalogPrices]);

  const handleCancel = () => {
    form.resetFields();
    setIsSubmited(false);
    onCancel();
  };

  const handleSubmit = async () => {
    setIsSubmited(true);
    try {
      const catalogPriceLineNumbers = await fetchCatalogPriceLineNumbers();
      const isYearPriceActive = catalogPriceLineNumbers !== undefined && catalogPriceLineNumbers > 0;
      await form.validateFields();
      if (!urbanCenterId) return;
      setIsSubmiting(true);
      const values = form.getFieldsValue();
      const submitData = {
        ...values,
        ...(urbanCenterId ? { urbanCenterId } : {}),
        isVisible: isYearPriceActive
      };
      await onSubmit(submitData);
      toast.success('Succès');
      setIsSubmiting(false);
      setIsSubmited(false);
    } catch (error) {
      console.log(error);
      setIsSubmiting(false);
      toast.error('Erreur');
    }
  };

  return (
    <Modal
      title='AJOUTER DES PRODUITS'
      open={isOpenModal}
      maskClosable={false}
      onCancel={handleCancel}
      className='adding-produits-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={handleSubmit}
          loading={isSubmiting}
        >
          Valider
        </Button>,
      ]}
    >
      <Space direction='vertical' className='adding-produits-modal__body'>
        <Form
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
          validateMessages={{ required: '' }}
        >
          <Space direction='vertical' className='adding-produits-modal__body'>
            <Form.Item
              label='Type de produit'
              name='productTypeId'
              rules={[{ required: true }]}
              className='adding-produits-modal__form-input'
            >
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                value={selectedProductTypeId}
                loading={loadingCatalogPriceProductTypes === 'pending'}
                onChange={(value) => {
                  form.resetFields(['productIds', 'priceFamilyId']);
                  setSelectedProductTypeId(value);
                }}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={serviceProviderProductTypes?.map((option) => ({
                  value: option.id,
                  label: option.name,
                }))}
              ></Select>
            </Form.Item>
            <Form.Item
              label='Produits'
              name='productIds'
              rules={[{ required: true }]}
              className='adding-produits-modal__form-input'
            >
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                mode='multiple'
                showSearch
                maxTagCount='responsive'
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                loading={loadingProductType === 'pending'}
                options={products?.map((option) => ({
                  value: option.id,
                  label: option.name,
                }))}
                tagRender={(props) => (
                  <Tag
                    style={{
                      background: 'rgba(0, 0, 0, 0.04)',
                      border: '1px solid rgba(0, 0, 0, 0.06)',
                      borderRadius: 4,
                      display: 'inline-block',
                      padding: '0px 8px',
                      marginTop: '2px',
                      marginBottom: '2px',
                      marginRight: '2px',
                      cursor: 'pointer',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    onMouseDown={(event: React.MouseEvent<HTMLSpanElement>) => {
                      event.preventDefault();
                      event.stopPropagation();
                    }}
                  >
                    {props.label}
                  </Tag>
                )}
              ></Select>
            </Form.Item>
            <Form.Item
              label='Type de tarifs'
              name='priceFamilyId'
              className='adding-produits-modal__form-input'
              rules={[{ required: isCatalog ? !productType?.isCatalog : !productType?.isFournisseur }]}
            >
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                loading={loadingProductType === 'pending'}
                disabled={!selectedProductTypeId || (isCatalog ? productType?.isCatalog : productType?.isFournisseur)}
              >
                {priceFamilies?.map((priceFamily) => (
                  <Option key={priceFamily.id} value={priceFamily.id}>
                    {priceFamily.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};
export default AddProduitsModal;
