import { Button, Modal, Typography } from 'antd';
const { Text } = Typography;

const ValidateAndCreateOrderConfirmModal = ({
  isOpenModal,
  onCancel,
  onConfirmation,
  loading,
}: {
  isOpenModal: boolean;
  onCancel: () => void;
  onConfirmation: () => void;
  loading: boolean;
}) => {
  const handleCancel = () => {
    onCancel();
  };
  return (
    <Modal
      title={`Confirmer pour créer et envoyer le devis et la commande`}
      open={isOpenModal}
      maskClosable={false}
      onCancel={handleCancel}
      width={550}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__btn-color-dark'>
          Non
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          loading={loading}
          onClick={onConfirmation}
        >
          Oui
        </Button>,
      ]}
    >
      <Text
        style={{
          paddingTop: 20,
          paddingBottom: 8,
        }}
      >
        Le devis sera envoyé au client. <br />
        La commande sera envoyée à la logistique. <br />
        Etes-vous sûr des informations de votre document <br />
        (Dates, adresses, taille de benne, type de benne,…) ?
      </Text>
    </Modal>
  );
};

export default ValidateAndCreateOrderConfirmModal;
