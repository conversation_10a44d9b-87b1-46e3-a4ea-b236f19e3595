import { ColorPickerProps } from 'antd';
import ColorPicker from 'antd/es/color-picker';

const ColorPickerInput = (
  props: ColorPickerProps & { customText?: string; readonlyValue?: string }
) => {
  const { disabled, customText, showText, readonlyValue, ...otherProps } =
    props;
  return disabled ? (
    <div className='color-picker-readonly'>
      <div
        className='color-box'
        style={readonlyValue ? { backgroundColor: readonlyValue } : {}}
      />
      <span>{customText}</span>
    </div>
  ) : (
    <ColorPicker
      format={'hex'}
      showText={customText ? () => customText : showText}
      {...otherProps}
    />
  );
};

export default ColorPickerInput;
