import { Select, Spin } from 'antd';
import { Fragment, useMemo, useRef } from 'react';
import { debounce } from 'utils';
import { useMergeState } from 'hooks';
interface RemoteSelectProps<T> {
  fetchData: (params: { search: string; page: number }) => Promise<T[]>;
  onChange?: (value: number, option?: T) => void;
  excludeIds?: (number | string)[];
  placeholder?: string;
  style?: React.CSSProperties;
  optionKey?: keyof T;
  optionLabel?: keyof T;
  pageSize?: number;
  customRenderOption?: (option: T) => React.ReactNode;
  loading?: boolean;
}

interface SelectState<T> {
  options: T[];
  searchValue: string;
  page: number;
  hasMore: boolean;
  isLoadingMore: boolean;
  error: string | null;
}

function RemoteSelect<T>({
  fetchData,
  onChange,
  excludeIds = [],
  placeholder,
  style,
  optionKey = 'id' as keyof T,
  optionLabel = 'name' as keyof T,
  pageSize = 20,
  customRenderOption,
  loading,
}: RemoteSelectProps<T>) {
  const [state, setMergedState] = useMergeState<SelectState<T>>({
    options: [],
    searchValue: '',
    page: 1,
    hasMore: true,
    isLoadingMore: false,
    error: null,
  });

  const isInitialFetchDone = useRef(false);

  const fetch = async (search: string, pageNum: number = 1) => {
    setMergedState({ isLoadingMore: true, error: null });
    try {
      const result = await fetchData({ search, page: pageNum });
      const newOptions = pageNum === 1 ? result : [...state.options, ...result];
      const uniqueOptions = Array.from(new Map(newOptions.map((item) => [item[optionKey], item])).values());
      setMergedState({
        options: uniqueOptions,
        hasMore: result.length === pageSize,
        page: pageNum,
      });
    } catch (e) {
      setMergedState({
        error: 'Failed to load options. Please try again.',
        hasMore: false,
      });
      throw e;
    } finally {
      setMergedState({ isLoadingMore: false });
    }
  };

  const debouncedFetch = useMemo(
    () =>
      debounce((value: string) => {
        setMergedState({ searchValue: value, page: 1 });
        fetch(value, 1);
        setTimeout(() => {
          const dropdown = document.querySelector('.ant-select-dropdown .rc-virtual-list-holder');
          if (dropdown) {
            dropdown.scrollTop = 0;
          }
        }, 100);
      }, 300),
    [],
  );

  const handleScroll = async (e: React.UIEvent<HTMLDivElement>) => {
    if (!state.hasMore || state.isLoadingMore) return;
    const target = e.target as HTMLDivElement;
    const isBottom = target.scrollHeight - target.scrollTop <= target.clientHeight + 1;
    if (isBottom) {
      const nextPage = state.page + 1;
      await fetch(state.searchValue, nextPage);
    }
  };

  const filteredOptions = useMemo(
    () => state.options.filter((item) => !excludeIds.includes(item[optionKey] as string | number)),
    [state.options, excludeIds, optionKey],
  );

  const dropdownRender = (menu: React.ReactElement) => (
    <Fragment>
      {menu}
      {state.isLoadingMore && (
        <div style={{ padding: 15, textAlign: 'center' }}>
          <Spin className='select-spinner' />
        </div>
      )}
      {state.error && <div style={{ padding: 8, textAlign: 'center', color: 'red' }}>{state.error}</div>}
    </Fragment>
  );

  return (
    <Select
      showSearch
      allowClear
      placeholder={placeholder}
      style={style}
      onChange={(value) => {
        const selected = filteredOptions.find((item) => item[optionKey] === value);
        onChange?.(value, selected);
      }}
      onSearch={(value) => {
        debouncedFetch(value);
      }}
      onPopupScroll={handleScroll}
      onFocus={() => {
        if (!isInitialFetchDone.current) {
          debouncedFetch('');
          isInitialFetchDone.current = true;
        }
      }}
      filterOption={(input, option) => (option?.label as string)?.toLowerCase().includes(input.toLowerCase())}
      dropdownRender={dropdownRender}
      loading={loading}
    >
      {filteredOptions.map((item) => (
        <Select.Option
          key={item[optionKey] as string}
          value={item[optionKey] as number}
          label={item[optionLabel] as string}
          className='fade-in-option'
        >
          {customRenderOption ? customRenderOption(item) : (item[optionLabel] as string)}
        </Select.Option>
      ))}
    </Select>
  );
}

export default RemoteSelect;
