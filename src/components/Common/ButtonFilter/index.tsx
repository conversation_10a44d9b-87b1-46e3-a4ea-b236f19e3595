import { FilterOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const ButtonFilter = ({
  children,
  handleClick,
  otherStyles,
  className,
  disabled = false,
  isActive = false,
}: {
  children: string;
  handleClick?: () => void;
  otherStyles?: object;
  className?: string;
  disabled?: boolean;
  isActive?: boolean;
}) => {
  return (
    <div className='btn-add__creation'>
      <Button
        size='large'
        type='primary'
        className={`btn-add__${isActive ? 'filled-creation-button' : 'creation-button'} ${className}`}
        onClick={handleClick}
        style={{ width: 'auto', ...(isActive && { background: '#A6C84D' }), ...otherStyles }}
        disabled={disabled}
      >
        <FilterOutlined className='btn-add__creation-icon' />
        <span className='btn-add__creation-text'>{children}</span>
      </Button>
    </div>
  );
};
export default ButtonFilter;
