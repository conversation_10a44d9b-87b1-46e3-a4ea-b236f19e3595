import { Button, Modal, Typography } from 'antd';
const { Text } = Typography;

const ChangementAddressModal = ({
  isOpenModal,
  onCancel,
  onConfirmation,
}: {
  isOpenModal: boolean;
  onCancel: () => void;
  onConfirmation: () => void;
}) => {
  const handleCancel = () => {
    onCancel();
  };
  return (
    <>
      <Modal
        title={`Changement d'adresse`}
        open={isOpenModal}
        maskClosable={false}
        onCancel={handleCancel}
        centered
        footer={[
          <Button key='ajouter' type='primary' className='ant-modal-content__add-btn' onClick={onConfirmation}>
            Mettre à jour les prix
          </Button>,
          <Button key='annuler' onClick={handleCancel} className='ant-modal-content__btn-color-dark'>
            Ne pas modifier les prix
          </Button>,
        ]}
      >
        <Text
          style={{
            paddingTop: 20,
            paddingBottom: 8,
          }}
        >
          Souhaitez-vous mettre à jour les prix en fonction de cette nouvelle adresse chantier ?
        </Text>
      </Modal>
    </>
  );
};

export default ChangementAddressModal;
