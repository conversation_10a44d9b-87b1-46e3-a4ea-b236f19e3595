import { PlusCircleOutlined } from '@ant-design/icons';
import { Button } from 'antd';

const ButtonAdd = ({
  children,
  handleClick,
  otherStyles,
  className,
  disabled = false,
}: {
  children: string;
  handleClick?: () => void;
  otherStyles?: object;
  className?: string;
  disabled?: boolean;
}) => {
  return (
    <div className='btn-add__creation'>
      <Button
        size='large'
        type='primary'
        className={`btn-add__creation-button ${className}`}
        onClick={handleClick}
        style={{ width: 'auto', ...otherStyles }}
        disabled={disabled}
      >
        <PlusCircleOutlined className='btn-add__creation-icon' />
        <span className='btn-add__creation-text'>{children}</span>
      </Button>
    </div>
  );
};
export default ButtonAdd;
