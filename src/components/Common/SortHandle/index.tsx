import { MenuOutlined } from '@ant-design/icons';
import { SyntheticListenerMap } from '@dnd-kit/core/dist/hooks/utilities';
import React from 'react';

interface SortHandleProps {
  setActivatorNodeRef?: (element: HTMLElement | null) => void;
  listeners?: SyntheticListenerMap | undefined;
  isDraggable?: boolean;
}

const SortHandle = ({ setActivatorNodeRef, listeners }: SortHandleProps) => {
  return (
    <div className='text-center'>
      <MenuOutlined
        ref={setActivatorNodeRef}
        style={{ touchAction: 'none', cursor: 'move', color: 'rgb(149, 197, 21)' }}
        {...listeners}
      />
    </div>
  );
};

export default SortHandle;
