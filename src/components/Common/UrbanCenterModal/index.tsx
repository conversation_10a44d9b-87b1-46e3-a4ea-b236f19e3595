import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Input, Modal, Space, Form, Typography } from 'antd';
import { AddressInput, ZoneItem } from 'components/Common';
import { useMergeState } from 'hooks';
import { UrbanCenter, Zone } from 'models';
import { useState } from 'react';
import { Address } from 'types';
import { convertAddress, validateZones } from 'utils';
import BulletPointIcon from '../../Icons/BulletPointIcon';
import { toast } from 'react-toastify';
const { Text } = Typography;

const UrbanCenterModal = ({
  action,
  isOpenModal,
  onCancel,
  onSubmit,
  urbanCenter = { Zones: [] },
}: {
  action: 'create' | 'update';
  isOpenModal: boolean;
  onCancel: () => any;
  onSubmit: (data: any, urbanCenter: UrbanCenter) => any;
  urbanCenter?: UrbanCenter;
}) => {
  const [form] = Form.useForm();
  const [isSubmited, setIsSubmited] = useState(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [addressDetail, setAddressDetail] = useState<Address | null>({
    city: urbanCenter?.city,
    postalcode: urbanCenter?.postalcode,
    country: urbanCenter?.country,
    address: urbanCenter?.address,
    formattedAddress: urbanCenter?.formattedAddress,
  });
  const [selectedUrbanCenter, setSelectedUrbanCenter] = useMergeState<UrbanCenter>(urbanCenter);

  const handleCancel = () => {
    form.resetFields();
    setSelectedUrbanCenter(urbanCenter);
    setAddressDetail(
      urbanCenter
        ? {
            city: urbanCenter?.city,
            postalcode: urbanCenter?.postalcode,
            country: urbanCenter?.country,
            address: urbanCenter?.address,
            formattedAddress: urbanCenter?.formattedAddress,
          }
        : null
    );
    setIsSubmited(false);
    onCancel();
  };

  const handleAddZoneItemInList = () => {
    const updateZones = [...(selectedUrbanCenter?.Zones as Zone[])];
    updateZones.push({ maxDistance: null });
    setSelectedUrbanCenter({ Zones: updateZones });
  };

  const handleSelectAddress = async (place: any) => {
    console.log('place', place);
    const addressDetail = convertAddress(place);
    form.setFieldValue('formattedAddress', addressDetail?.formattedAddress);
    setAddressDetail(addressDetail);
  };

  const handleChangeZoneList = (maxDistance: number | null, zoneIndex: number) => {
    const updateZone = selectedUrbanCenter?.Zones?.map((zone) => ({
      ...zone,
    })) as Zone[];
    updateZone[zoneIndex].maxDistance = maxDistance;
    setSelectedUrbanCenter({ Zones: updateZone });
  };

  const handleChangeAddress = () => {
    if (addressDetail) {
      setAddressDetail(null);
    }
    form.setFieldValue('formattedAddress', null);
  };

  const handleSubmit = async () => {
    setIsSubmited(true);
    try {
      await form.validateFields();
      if (!validateZones(selectedUrbanCenter?.Zones)) return;
      setIsSubmiting(true);
      const values = form.getFieldsValue();
      const submitData = {
        ...values,
        ...addressDetail,
        Zones: selectedUrbanCenter?.Zones?.map((item) => ({
          maxDistance: item.maxDistance,
        })),
      };
      await onSubmit(submitData, urbanCenter);
      setIsSubmiting(false);
      setIsSubmited(false);
      action === 'create' ? handleCancel() : onCancel();
    } catch (error) {
      setIsSubmiting(false);
      toast.error('Erreur');
    }
  };

  return (
    <Modal
      title={action === 'create' ? 'AJOUTER UN CENTRE URBAIN' : 'MODIFIER LE CENTRE URBAIN'}
      open={isOpenModal}
      onOk={handleSubmit}
      maskClosable={false}
      onCancel={handleCancel}
      className='urban-center-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={handleSubmit}
          loading={isSubmiting}
        >
          Valider
        </Button>,
      ]}
    >
      <Space direction='vertical' className='urban-center-modal__body'>
        <Form
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
          validateMessages={{ required: '' }}
        >
          <Form.Item
            label='Nom du centre urbain'
            initialValue={urbanCenter?.name ?? ''}
            name='name'
            rules={[{ required: true }]}
            className='urban-center-modal__form-input'
          >
            <Input placeholder='Input' className='urban-center-modal__input' />
          </Form.Item>
          <Form.Item
            label='Adresse du centre urbain'
            name='formattedAddress'
            validateStatus={isSubmited && !addressDetail?.formattedAddress ? 'error' : 'validating'}
            initialValue={addressDetail?.formattedAddress}
            rules={[{ required: true }]}
            className='urban-center-modal__form-input'
          >
            <AddressInput
              onSelect={handleSelectAddress}
              onChange={handleChangeAddress}
              defaultAddress={addressDetail?.formattedAddress}
              otherStyles={{
                borderColor: isSubmited && !addressDetail?.formattedAddress ? 'red' : '',
              }}
            />
          </Form.Item>
          <Space className='center-urban__zone-list urban-center-modal__service-provider-urban-center'>
            {selectedUrbanCenter?.Zones?.map((zone, index) => (
              <Space className='center-urban__zone-item' key={`${zone.id}-${index}`}>
                <BulletPointIcon className='center-urban__bullet-point-icon' />
                <Text className='center-urban__zone-label'>Zone {index + 1}:</Text>
                <ZoneItem
                  zone={zone}
                  zoneIndex={index}
                  zoneList={selectedUrbanCenter?.Zones as Zone[]}
                  handleChangeZoneList={handleChangeZoneList}
                />
                <Text className='center-urban__zone-unit'>Km</Text>
              </Space>
            ))}
          </Space>
          <Space>
            <Button
              type='text'
              size='small'
              className='center-urban__add-parameter-btn'
              onClick={handleAddZoneItemInList}
            >
              <PlusCircleOutlined />
              Ajouter une zone
            </Button>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};
export default UrbanCenterModal;
