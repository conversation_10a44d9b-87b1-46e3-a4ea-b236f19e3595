import { DeleteTwoTone, LoadingOutlined } from '@ant-design/icons';

const TrashButton = ({
  onClick,
  className,
  loading = false,
}: {
  loading?: boolean;
  className?: string;
  onClick?: () => void;
}) => {
  return loading ? (
    <LoadingOutlined className={`${className} loading`} />
  ) : (
    <DeleteTwoTone
      className={className}
      onClick={(e) => {
        e.stopPropagation();
        onClick && onClick();
      }}
    />
  );
};
export default TrashButton;
