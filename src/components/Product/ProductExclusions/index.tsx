import { useEffect, useMemo, useState } from 'react';
import { Transfer } from 'antd';
import { OptionCard } from 'components/Common';
import type { TransferDirection } from 'antd/es/transfer';
import { useAppSelector } from 'store';
import { selectProducts } from 'store/slices/product.slices';
import { toast } from 'react-toastify';
import { productService } from 'services';
import { Product } from 'models';
import { LoadingOutlined } from '@ant-design/icons';

interface RecordType {
  key: string;
  title: string;
  description: string;
}

const ProductExclusions = ({ getProducts, productTypeId }: { getProducts: () => void; productTypeId: number }) => {
  const originProducts = useAppSelector(selectProducts);
  const [products, setProducts] = useState<Product[]>(originProducts);
  const [direction, setDirection] = useState<'left' | 'right' | null>(null);

  useEffect(() => {
    setProducts(originProducts);
  }, [originProducts]);

  useEffect(() => {
    const inputs = document.querySelectorAll('.ant-transfer-list-search .ant-input');
    if (inputs) {
      inputs.forEach((input) => {
        input.setAttribute('placeholder', 'Search');
      });
    }
  }, []);

  const filterOption = (inputValue: string, option: RecordType) => option.title.indexOf(inputValue) > -1;

  const handleChange = async (newTargetKeys: string[], direction: TransferDirection, moveKeys: string[]) => {
    try {
      setDirection(direction);
      const updatedProducts = products.map((product) => ({
        ...product,
        exclude: newTargetKeys.includes(product.id.toString()),
      }));
      setProducts(updatedProducts);
      const exclusion = direction === 'left' ? false : true;
      await productService.excludeProducts(productTypeId, {
        exclusion,
        ids: moveKeys,
      });
      await getProducts();
      setDirection(null);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      setDirection(null);
      toast.error(
        'La liste des produits est en cours de synchronisation avec ZOHO, veuillez attendre la fin de la synchronisation avant de continuer à exclure ou à régénérer.',
      );
    }
  };

  const handleSearch = (dir: TransferDirection, value: string) => {
    console.log('search:', dir, value);
  };

  const dataSource = useMemo(() => {
    return products.map((product) => ({
      key: product.id.toString(),
      title: product.name,
      description: product.description,
    }));
  }, [products]);

  const targetKeys = useMemo(() => {
    return products.filter((product) => product.exclude).map((product) => product.id.toString());
  }, [products]);

  return (
    <OptionCard
      title='Exclusions'
      otherStyles={{
        marginTop: '16px',
        borderRadius: '6px',
      }}
      headerStyles={
        {
          // backgroundColor: !isActived && "rgba(0, 0, 0, 0.15)",
          // borderBottom: !isActived && "1px solid rgba(0, 0, 0, 0.25)",
        }
      }
    >
      <Transfer
        dataSource={dataSource}
        targetKeys={targetKeys}
        showSearch
        filterOption={filterOption}
        onChange={handleChange}
        onSearch={handleSearch}
        className='transfer-component'
        selectAllLabels={[
          () => (
            <>
              {`${dataSource.length - targetKeys.length} produits`}
              {direction === 'left' && <LoadingOutlined className='product-exclusions__transfer-loading' />}
            </>
          ),
          () => (
            <>
              {`${targetKeys.length} exclusions`}
              {direction === 'right' && <LoadingOutlined className='product-exclusions__transfer-loading' />}
            </>
          ),
        ]}
        render={(item) => item.title}
        // disabled={!isActived}
      />
    </OptionCard>
  );
};

export default ProductExclusions;
