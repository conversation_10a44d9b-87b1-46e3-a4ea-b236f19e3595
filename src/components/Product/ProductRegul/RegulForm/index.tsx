import { useEffect, useRef, useState } from 'react';
import { CloseOutlined, EditOutlined, PlusCircleOutlined, PlusSquareOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import { Button, Col, Form, Input, Row, Select, Space } from 'antd';
import { CheckButton, CustomTextArea, TrashButton } from 'components/Common';
import TextArea, { TextAreaRef } from 'antd/es/input/TextArea';
import { productService, productTypeRegulService } from 'services';
import { useMergeState } from 'hooks';
import { OptionType, Product, ProductType, ProductTypeRegul } from 'models';
import { AddTagsPriceRegul } from 'utils/converters';
import { numberWithCommas } from 'utils';
import { selectProductTypeReguls } from 'store/slices/product_type_regul.slices';
import { useAppSelector } from 'store';

const RegulForm = ({
  regul,
  productType,
  productTypeId,
  getListReguls,
  handleShowCreateForm,
  action,
  selectedProduit,
  setSelectedProduit,
}: {
  regul?: ProductTypeRegul;
  productType?: ProductType[];
  isFirstLoading?: boolean;
  productTypeId: number;
  regulOption?: OptionType;
  getListReguls: () => void;
  handleShowCreateForm: (showCreateForm: boolean) => void;
  action: 'create' | 'update';
  selectedProduit?: number;
  setSelectedProduit?: (value: number) => void;
}) => {
  const [form] = Form.useForm();
  const textareaRef = useRef<TextAreaRef>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [isSubmited, setIsSubmited] = useState<boolean>(false);
  const [isDeleting, setIsDeleting] = useState<boolean>(false);
  const [products, setProducts] = useState<Product[]>([]);
  const [selectedRegul] = useMergeState<ProductTypeRegul | null>(regul ? regul : {});
  const [selectedProductId, setSelectedProductId] = useState<number | null>(null);
  const [isEditingRegul, setIsEditingRegul] = useState<boolean>(action === 'create' ? true : false);
  const [selectTypeProduct, setSelectTypeProduct] = useState<number | null>(regul?.regulProductTypeId ?? null);
  const regulList = useAppSelector(selectProductTypeReguls);
  const initialForm = () => {
    setSelectedProductId(regul?.productId as number);
    setSelectTypeProduct(regul?.regulProductTypeId as number);
    form.setFieldsValue({
      productTypeId,
      defaultPrice: regul?.defaultPrice,
      description: regul?.description,
    });
    const itemProductType = productType?.find((item: ProductType) => item.id === regul?.regulProductTypeId);
    if (itemProductType?.isVisible) {
      form.setFieldsValue({
        regulProductTypeId: regul?.regulProductTypeId,
        productId: regul?.productId,
      });
    }
  };

  useEffect(() => {
    if (regul && !isEditingRegul) {
      initialForm();
    }
  }, [regul]);

  useEffect(() => {
    if (selectTypeProduct) {
      getProducts(selectTypeProduct);
    }
  }, [selectTypeProduct]);
  const getProducts = async (productTypeId: number) => {
    const response = await productService.getProducts({
      productTypeId,
      exclude: 0,
      isActive: 1,
      isVisible: 1,
      limit: 'unlimited',
      orderBy: 'name',
    });
    setProducts(response.rows);
  };
  const handleSubmitRegul = async () => {
    setIsSubmited(true);
    try {
      const values = await form.validateFields();
      try {
        setLoading(true);
        if (selectedRegul?.id) {
          await productTypeRegulService.updateProductTypeRegul(selectedRegul?.id, { ...values, productTypeId });
        } else {
          await productTypeRegulService.createProductTypeRegul({
            ...values,
            productTypeId,
          });
        }
        await getListReguls();
        if (action === 'create') {
          handleShowCreateForm(false);
        } else {
          setIsEditingRegul(false);
        }
        setLoading(false);
        setIsSubmited(false);
      } catch {
        toast.error('Erreur');
        setLoading(false);
      }
    } catch (errInfo) {
      setLoading(false);
      console.log('Save failed:', errInfo);
    }
  };

  const handleCloseEditRegul = () => {
    initialForm();
    setIsEditingRegul(false);
  };

  const handleDeleteRegul = async () => {
    if (!regul?.id) return;
    try {
      setIsDeleting(true);
      await productTypeRegulService.deleteProductTypeRegul(regul?.id);
      await getListReguls();
      setIsDeleting(false);
    } catch {
      setIsDeleting(false);
      toast.error('Erreur');
    }
  };

  const insertConvertedString = () => {
    const textarea = textareaRef.current?.resizableTextArea?.textArea;
    if (textarea instanceof HTMLTextAreaElement) {
      const { selectionStart, selectionEnd, value } = textarea;
      const addedString = '[Prix-Regul-' + selectedProductId + '](Regul)';
      // Insert the converted string at the current cursor position
      const updatedValue = value.substring(0, selectionStart) + addedString + value.substring(selectionEnd) + ' ';
      // Update the textarea value and move the cursor after the inserted text
      form.setFieldValue('description', updatedValue);
      setTimeout(() => {
        textarea.selectionStart = selectionStart + addedString?.length;
        textarea.selectionEnd = selectionStart + addedString?.length;
        textarea.focus();
      }, 100);
    }
  };

  const onChangeProduct = (value: number) => {
    let description = form.getFieldValue('description');
    description = description?.replaceAll(
      '[Prix-Regul-' + selectedProductId + '](Regul)',
      '[Prix-Regul-' + value + '](Regul)',
    );
    form.setFieldValue('description', description);
    setSelectedProductId(value);
    setSelectedProduit?.(value);
  };

  const onEdit = () => {
    setIsEditingRegul(true);
  };

  return (
    <Form
      validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
      key={selectedRegul?.id}
      form={form}
      className='product-regul__form'
      disabled={!isEditingRegul}
    >
      <Row gutter={32}>
        <Col span={8}>
          <Form.Item name='regulProductTypeId' label='Type de Produit' rules={[{ required: true, message: '' }]}>
            <Select
              placeholder='Type de Produit'
              // style={{ width: 250 }}
              showSearch
              onChange={(value) => {
                setSelectTypeProduct(value);
                form.setFieldValue('productId', null);
              }}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={productType?.map((option) => ({
                value: option.id,
                label: option.name,
              }))}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item name='productId' label='Produit' rules={[{ required: true, message: '' }]}>
            <Select
              placeholder='Produit'
              // style={{ width: 300 }}
              showSearch
              onChange={onChangeProduct}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={products?.map((option) => ({
                value: option.id,
                label: option.name,
                disabled: regulList?.find(
                  (i) => (i.productId === option.id || selectedProduit === option.id) && i.id !== regul?.id,
                )
                  ? true
                  : false,
              }))}
            />
          </Form.Item>
        </Col>
        <Col span={8}>
          <Form.Item
            name='defaultPrice'
            label='Prix Ecodrop HT'
            rules={[
              { required: true, message: '' },
              () => ({
                validator(_, value) {
                  if (!value || isNaN(value) || value < 0) {
                    return Promise.reject(new Error(''));
                  }
                  return Promise.resolve();
                },
              }),
            ]}
          >
            {isEditingRegul ? (
              <Input
                placeholder='Input'
                className='product-regul__prix-ht-input'
                onPressEnter={handleSubmitRegul}
                suffix='€'
              />
            ) : (
              numberWithCommas(regul?.defaultPrice) + ' €'
            )}
          </Form.Item>
        </Col>
      </Row>

      <Space className='product-regul__add-prices-block'>
        <Button
          type='text'
          size='small'
          disabled={!selectedProductId || !isEditingRegul}
          className='btn-add__add_icon'
          onClick={insertConvertedString}
        >
          <PlusSquareOutlined className='btn-add__add_icon' color='#A6C84D' />
          Ajouter le prix
        </Button>
      </Space>
      <Space>
        <Space className='product-regult__description-block'>
          <Form.Item
            name='description'
            label='Description'
            className='product-regul__description'
            wrapperCol={{ span: 24 }}
          >
            {isEditingRegul ? (
              <TextArea
                ref={textareaRef}
                size='large'
                rows={4}
                placeholder='Description'
                className='product-regul__description-textarea'
              />
            ) : (
              <CustomTextArea>
                <AddTagsPriceRegul
                  str={regul?.description as string}
                  handleUpdateWordState={getListReguls}
                  selectedRegulId={regul?.id}
                  onChangeDescription={(value: string) => form.setFieldValue('description', value)}
                />
              </CustomTextArea>
            )}
          </Form.Item>
        </Space>
        <Space className='product-regul__edit-delete-block'>
          {/* <Form.Item noStyle rules={[{ required: true }]} className='product-regul__edit-delete'> */}
          {action === 'update' &&
            (isEditingRegul ? (
              <>
                <CheckButton className='check-button' onClick={handleSubmitRegul} loading={loading} />
                <CloseOutlined
                  className='product-regul__title-of-product-cancel-icon cancel-button'
                  onClick={handleCloseEditRegul}
                />
              </>
            ) : (
              <>
                <EditOutlined className='product-regul__description-edit-icon edit-icon' onClick={onEdit} />
                <TrashButton
                  className='product-regul__description-trash-icon trash-button'
                  loading={isDeleting}
                  onClick={handleDeleteRegul}
                />
              </>
            ))}
          {action === 'create' && (
            <>
              <Button
                type='text'
                size='small'
                className='btn-add__ajouter-btn'
                loading={loading}
                onClick={handleSubmitRegul}
              >
                <PlusCircleOutlined className='btn-add__add_icon' />
                Ajouter
              </Button>
              <CloseOutlined
                className='product-regul__close-icon cancel-button'
                onClick={() => handleShowCreateForm(false)}
              />
            </>
          )}
          {/* </Form.Item> */}
        </Space>
      </Space>
    </Form>
  );
};

export default RegulForm;
