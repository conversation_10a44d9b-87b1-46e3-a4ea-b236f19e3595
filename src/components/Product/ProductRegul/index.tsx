import { OptionCard } from 'components/Common';
import ButtonAdd from 'components/Common/ButtonAdd';
import { useState, useEffect } from 'react';
import RegulForm from './RegulForm';
import { useParams } from 'react-router-dom';
import { OPTION_TYPES } from 'utils/constant';
import { useAppDispatch, useAppSelector } from 'store';
import {
  fetchRegulOptions,
  selectOptionTypes,
  selectRegulOptions,
} from 'store/slices/option.slices';
import { fetchProductTypes, selectProductTypes } from 'store/slices/product.slices';
import { toast } from 'react-toastify';
import { fetchProductTypeReguls, selectProductTypeReguls } from 'store/slices/product_type_regul.slices';

const ProductRegul = () => {
  const dispatch = useAppDispatch();
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [isFirstLoading, setIsFirstLoading] = useState<boolean>(true);
  const regulList = useAppSelector(selectProductTypeReguls);
  const data = useAppSelector(selectProductTypes);
  const params = useParams();
  const productTypeId = parseInt(params.productTypeId as string);
  const optionTypes = useAppSelector(selectOptionTypes);
  const regulOption = optionTypes.find((i) => i.key === OPTION_TYPES.regul);
  const [selectedProduit, setSelectedProduit] = useState<any>()
  const getListReguls = async () => {
    await dispatch(
      fetchProductTypeReguls({
        productTypeId,
        limit: 'unlimited',
        include: 'Product',
      })
    ).unwrap();
    setTimeout(() => {
      setIsFirstLoading(false);
    });
  };
  const getProductTypes = async () => {
    await dispatch(
      fetchProductTypes({
        page: 1,
        limit: 'unlimited',
        orderBy: 'createdAt,desc',
      })
    )
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  };
  useEffect(() => {
    getProductTypes();
    getListReguls();
  }, []);

  const handleShowCreateForm = (showCreateForm: boolean): void => {
    setShowCreateForm(showCreateForm);
  };

  const handleAddRegul = () => {
    handleShowCreateForm(true);
  };

  return (
    <OptionCard
      title="Regul"
      otherStyles={{
        marginTop: '16px',
        borderRadius: '6px',
      }}
    >
      {regulList.map((regul) => (
        <div key={regul?.id}>
          <RegulForm
            regul={regul}
            productType={data}
            productTypeId={productTypeId}
            regulOption={regulOption}
            isFirstLoading={isFirstLoading}
            getListReguls={getListReguls}
            handleShowCreateForm={handleShowCreateForm}
            action={'update'}
            selectedProduit={selectedProduit}
            setSelectedProduit={setSelectedProduit}
          />
          <hr className="product-regul__end-line" />
        </div>
      ))}
      {showCreateForm && (
        <>
          <RegulForm
            productType={data}
            getListReguls={getListReguls}
            regulOption={regulOption}
            productTypeId={productTypeId}
            handleShowCreateForm={handleShowCreateForm}
            action={'create'}
            selectedProduit={selectedProduit}
            setSelectedProduit={setSelectedProduit}
          />
          <hr
            className="product-regul__end-line"
            style={{
              marginBottom: showCreateForm ? '72px' : '0px',
            }}
          />
        </>
      )}
      {!showCreateForm && (
        <ButtonAdd
          otherStyles={{
            height: '32px',
            marginTop: '45px',
          }}
          handleClick={() => handleAddRegul()}
        >
          Ajouter une regul
        </ButtonAdd>
      )}
    </OptionCard>
  );
};

export default ProductRegul;
