import { Button, Col, Form, FormInstance, Input, List, Row, Select, Space, Spin } from 'antd';
import BulletPointIcon from 'components/Icons/BulletPointIcon';
import { CloseOutlined, LoadingOutlined, PlusOutlined } from '@ant-design/icons';
import { CheckButton, ThreeDotDropdown } from 'components/Common';
import PriceOptionItem from '../PriceOptionItem';
import { useState, useEffect, useRef } from 'react';
import { CatalogPrice, Price, PriceOption, PriceSubOption } from 'models';
import { toast } from 'react-toastify';
import { priceOptionService, priceService } from 'services';
import { PulseLoader } from 'react-spinners';
import { useParams } from 'react-router-dom';
import { v4 as uuidv4 } from 'uuid';
import PriceDescription from './PriceDescription';
import { usePriceTypeLogicQuery } from 'hooks';
import { usePriceTypeLogics } from '../../../../hooks/priceTypeLogicHook';
import { PRICE_TYPE_LOGICS } from 'utils/constant';
import { TextAreaRef } from 'antd/es/input/TextArea';
const { Option } = Select;

export type CustomPriceOption = PriceOption & {
  tempId?: string;
  catalogPriceStatus?: boolean;
  serviceProviderPriceStatus?: boolean;
};
const ParameterBlock = ({
  form,
  priceFamilyId,
  price,
  handleShowParameterBlock,
  fetchListPrices,
  action,
}: {
  form: FormInstance;
  priceFamilyId: number;
  price?: Price & { tempId?: string };
  fetchListPrices: (priceFamilyId: number) => void;
  handleShowParameterBlock: (value: boolean) => void;
  action: 'create' | 'update';
}) => {
  const params = useParams();
  const productTypeId = parseInt(params.productTypeId as string);
  const textareaRef = useRef<TextAreaRef>(null);
  const [catalogPriceStatus, setCatalogPriceStatus] = useState<boolean>(false);
  const [serviceProviderPriceStatus, setServiceProviderPriceStatus] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [priceOptionList, setPriceOptionList] = useState<CustomPriceOption[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [priceOptionListInput, setPriceOptionListInput] = useState<CustomPriceOption[]>([]);
  const [updatingLoading, setUpdatingLoading] = useState<boolean>(false);
  const [creatingLoading, setCreatingLoading] = useState<boolean>(false);
  const [priceTypeLogicsQuery] = usePriceTypeLogicQuery();
  const [priceTypeLogics] = usePriceTypeLogics(priceTypeLogicsQuery);
  const defaultPriceTypeLogic = priceTypeLogics.find(
    (priceTypeLogic) => priceTypeLogic.key === PRICE_TYPE_LOGICS.indicative_purpose,
  );
  const [selectedPriceTypeLogic, setSelectedPriceTypeLogic] = useState<string>('');

  useEffect(() => {
    const keys: string[] = [];
    price?.CatalogPrices?.filter((item) => item.priceId && !item.priceOptionId).forEach((item) => {
      if (item.isActive && item.isCatalog) {
        setCatalogPriceStatus(true);
        if (!keys.includes('catalog-price')) {
          keys.push('catalog-price');
        }
      } else if (item.isActive && !item.isCatalog) {
        setServiceProviderPriceStatus(true);
        if (!keys.includes('service-provider-price')) {
          keys.push('service-provider-price');
        }
      }
    });
    if (price?.description) {
      form.setFieldValue('description', price?.description);
    }
    if (price?.defaultPrice) {
      form.setFieldValue('defaultPrice', price?.defaultPrice);
    }
    setSelectedKeys(keys);
  }, [price]);

  useEffect(() => {
    if (price?.priceTypeLogicId) {
      if (price?.priceTypeLogicId) {
        const priceTypeLogic = priceTypeLogics.find((priceTypeLogic) => priceTypeLogic.id === price?.priceTypeLogicId);
        if (priceTypeLogic) {
          form.setFieldValue('priceTypeLogicId', priceTypeLogic.id);
          setSelectedPriceTypeLogic(priceTypeLogic?.key);
        }
      }
    }
    if (!price?.priceTypeLogicId && defaultPriceTypeLogic?.id) {
      form.setFieldValue('priceTypeLogicId', defaultPriceTypeLogic?.id);
      setSelectedPriceTypeLogic(defaultPriceTypeLogic?.key);
    }
  }, [price?.priceTypeLogicId, priceTypeLogics, defaultPriceTypeLogic]);

  const getListPriceOptions = async (priceId?: number) => {
    if (!priceId) return;
    try {
      const priceOptions = await priceOptionService.getPriceOptions(priceId, {
        include: 'PriceType|Price|PriceSubOptions|CatalogPrices',
      });
      const newPriceOptions = priceOptions.map((priceOption: CustomPriceOption) => {
        let newPriceOption = priceOption;
        priceOption?.CatalogPrices?.forEach((item: CatalogPrice) => {
          if (item.isCatalog) {
            newPriceOption = {
              ...newPriceOption,
              catalogPriceStatus: item.isActive,
            };
          } else {
            newPriceOption = {
              ...newPriceOption,
              serviceProviderPriceStatus: item.isActive,
            };
          }
        });
        return newPriceOption;
      });
      setPriceOptionList(newPriceOptions);
      setPriceOptionListInput(newPriceOptions);
      setLoading(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
      toast.error('Erreur');
    }
  };

  useEffect(() => {
    if (action === 'update') {
      getListPriceOptions(price?.id);
    }
  }, []);

  const handleAddPriceOption = () => {
    const newPriceOption = {
      tempId: uuidv4(),
      name: '',
      priceTypeId: null,
      unit: '',
      catalogPriceStatus: false,
      serviceProviderPriceStatus: false,
    };
    const newPriceOptionList = [...priceOptionList, newPriceOption];
    const newPriceOptionListInput = [...priceOptionListInput, newPriceOption];
    setPriceOptionList(newPriceOptionList);
    setPriceOptionListInput(newPriceOptionListInput);
  };

  const handleChangePriceOptionList = (
    priceOptionId?: number | null,
    name?: string | null,
    priceTypeId?: number | null,
    unit?: string | null,
    defaultValue?: string | null,
    catalogPriceStatus?: boolean,
    serviceProviderPriceStatus?: boolean,
    priceOptionFakeId?: number | string | null,
    PriceSubOptions?: PriceSubOption[],
  ) => {
    let newPriceOptionListInput: CustomPriceOption[] = [];
    if (priceOptionId && !priceOptionFakeId) {
      newPriceOptionListInput = priceOptionListInput.map((option) => {
        if (option.id === priceOptionId) {
          const updatedOption = {
            ...option,
            name: name,
            priceTypeId,
            unit: unit,
            catalogPriceStatus,
            serviceProviderPriceStatus,
            PriceSubOptions,
            defaultValue: defaultValue,
          };
          return updatedOption;
        }
        return option;
      });
    }
    if (!priceOptionId && priceOptionFakeId) {
      newPriceOptionListInput = priceOptionListInput.map((option) => {
        if (option?.tempId === priceOptionFakeId) {
          const updatedOption = {
            ...option,
            name: name,
            priceTypeId,
            unit: unit,
            catalogPriceStatus,
            serviceProviderPriceStatus,
            PriceSubOptions,
            defaultValue: defaultValue,
          };
          return updatedOption;
        }
        return option;
      });
    }
    setPriceOptionListInput(newPriceOptionListInput);
  };

  const handleCreatePriceParameters = async (priceFamilyId: number) => {
    try {
      const values = await form.validateFields();
      try {
        setCreatingLoading(true);
        const submitData = {
          priceFamilyId,
          tempId: price?.tempId,
          name: values?.priceName || '',
          labelName: values?.labelNameBlock || '',
          description: values.description,
          priceTypeLogicId: values.priceTypeLogicId,
          PriceOptions: priceOptionListInput,
          catalogPriceStatus,
          serviceProviderPriceStatus,
          defaultPrice: values.defaultPrice,
        };
        console.log(submitData);
        await priceService.createPrice(submitData);
        handleShowParameterBlock(false);
        await fetchListPrices(priceFamilyId);
        setCreatingLoading(false);
        form.resetFields();
        toast.success('Succès');
      } catch (error) {
        console.error(error);
        toast.error('Erreur');
      }
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const handleUpdatePriceParameters = async (priceFamilyId: number, priceId?: number) => {
    if (!priceId) return;
    try {
      const values = await form.validateFields();
      try {
        setUpdatingLoading(true);
        const submitData = {
          priceFamilyId,
          name: values?.priceName || '',
          labelName: values?.labelNameBlock || '',
          description: values.description,
          priceTypeLogicId: values.priceTypeLogicId,
          PriceOptions: priceOptionListInput,
          catalogPriceStatus,
          serviceProviderPriceStatus,
          defaultPrice: values.defaultPrice,
        };
        await priceService.updatePrice(priceId, submitData);
        handleShowParameterBlock(false);
        await fetchListPrices(priceFamilyId);
        setUpdatingLoading(false);
        form.resetFields();
        toast.success('Succès');
      } catch (error) {
        console.log(error);
        toast.error('Erreur');
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleChangeCatalogPriceStatus = (selectedKey: string) => {
    let value = false;
    if (selectedKeys.findIndex((key) => key === selectedKey) === -1) {
      value = true;
      setSelectedKeys([...selectedKeys, selectedKey]);
    } else {
      value = false;
      const newSelectedArray = selectedKeys.filter((key) => key !== selectedKey);
      setSelectedKeys(newSelectedArray);
    }
    if (selectedKey === 'service-provider-price') {
      setServiceProviderPriceStatus(value);
    } else {
      setCatalogPriceStatus(value);
    }
  };

  const handlePressEnterOnPriceOption = (formAction: 'create' | 'update') => {
    if (formAction === 'create') {
      handleCreatePriceParameters(priceFamilyId);
    } else {
      handleUpdatePriceParameters(priceFamilyId, price?.id);
    }
  };

  const handleChangeSuggestions = (selectedPriceOptionId: string | number, value: string) => {
    let description = form.getFieldValue('description');
    description = description?.replaceAll(`[${selectedPriceOptionId}](Tarif)`, `[${value}](Tarif)`);
    form.setFieldValue('description', description);
    insertConvertedString(selectedPriceOptionId);
  };

  const insertConvertedString = (selectedPriceOptionId: string | number) => {
    const textarea = textareaRef.current?.resizableTextArea?.textArea;
    if (!textarea) return;
    const { selectionStart, selectionEnd, value } = textarea;
    const addedString = `[${selectedPriceOptionId}](Tarif)`;
    // Insert the converted string at the current cursor position
    const updatedValue = value.substring(0, selectionStart) + addedString + value.substring(selectionEnd);
    // Update the textarea value and move the cursor after the inserted text
    form.setFieldValue('description', updatedValue);
    setTimeout(() => {
      textarea.selectionStart = selectionStart + addedString?.length;
      textarea.selectionEnd = selectionStart + addedString?.length;
      textarea.focus();
    }, 100);
  };

  const getListPrices = async () => {
    await fetchListPrices(priceFamilyId);
  };

  const handleChangePriceTypeLogic = (priceTypeLogicId: number) => {
    const priceTypeLogic = priceTypeLogics.find((priceTypeLogic) => priceTypeLogic.id === priceTypeLogicId);
    if (priceTypeLogic) {
      setSelectedPriceTypeLogic(priceTypeLogic.key);
    }
  };

  return (
    <Space className='product-tarifs__parameter-space'>
      <Space className='product-tarifs__parameter-block'>
        <Row style={{ marginBottom: 18 }}>
          <Col>
            <BulletPointIcon className='product-tarifs__bullet-point-icon' />
          </Col>
          <Col span={22}>
            <Row
              gutter={24}
              align='middle'
              style={{ marginRight: 8, justifyContent: 'space-evenly', flexWrap: 'nowrap' }}
            >
              <Col span={22}>
                <Row gutter={24}>
                  <Col span={12}>
                    <Form.Item
                      name='priceName'
                      label='Titre du paramètre'
                      className='mb-4'
                      initialValue={price?.name}
                      rules={[{ required: true, message: '' }]}
                      style={{ marginBottom: 0, width: '100%' }}
                    >
                      <Input
                        className='product-tarifs__parameter-title-input'
                        placeholder='Input'
                        onPressEnter={() =>
                          action === 'create'
                            ? handleCreatePriceParameters(priceFamilyId)
                            : handleUpdatePriceParameters(priceFamilyId, price?.id)
                        }
                      />
                    </Form.Item>
                  </Col>
                  <Col span={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                    <Form.Item
                      name='priceTypeLogicId'
                      label='Type de paramètre'
                      className='mb-4'
                      initialValue={price?.priceTypeLogicId}
                      rules={[{ required: true, message: '' }]}
                      style={{ marginBottom: 0 }}
                    >
                      <Select
                        style={{ width: 180 }}
                        placeholder='Type de paramètre'
                        defaultValue={defaultPriceTypeLogic?.id}
                        className='product-tarifs__price-option-input-type-select'
                        onChange={(value) => handleChangePriceTypeLogic(value)}
                      >
                        {priceTypeLogics.map((priceTypeLogic) => (
                          <Option value={priceTypeLogic.id} key={priceTypeLogic.id}>
                            {priceTypeLogic.name}
                          </Option>
                        ))}
                      </Select>
                    </Form.Item>
                  </Col>
                  <Col span={12}>
                    <Form.Item
                      name='labelNameBlock'
                      label='Nom du paramètre affiché'
                      initialValue={price?.labelName}
                      rules={[
                        { required: true, message: '' },
                        { max: 255, message: '' },
                      ]}
                      style={{ marginBottom: 0, width: '100%' }}
                    >
                      <Input
                        className='product-tarifs__parameter-title-input'
                        placeholder='Input'
                        onPressEnter={() =>
                          action === 'create'
                            ? handleCreatePriceParameters(priceFamilyId)
                            : handleUpdatePriceParameters(priceFamilyId, price?.id)
                        }
                      />
                    </Form.Item>
                  </Col>
                  {selectedPriceTypeLogic === PRICE_TYPE_LOGICS.indicative_purpose && (
                    <Col span={12} style={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end' }}>
                      <Form.Item
                        name='defaultPrice'
                        label='Prix par défaut'
                        initialValue={price?.defaultPrice}
                        rules={[
                          { required: true, message: '' },
                          () => ({
                            validator(_, value) {
                              if (!value || isNaN(value)) {
                                return Promise.reject(new Error(''));
                              }
                              if (value < 0) {
                                return Promise.reject(new Error(''));
                              }
                              return Promise.resolve();
                            },
                          }),
                        ]}
                      >
                        <Input placeholder='Entrez un prix' style={{ width: '180px' }} suffix='€' />
                      </Form.Item>
                    </Col>
                  )}
                </Row>
              </Col>
            </Row>
          </Col>
          <Col>
            <ThreeDotDropdown
              items={[
                {
                  key: 'catalog-price',
                  label: 'Catalogue',
                  onClick: (e) => handleChangeCatalogPriceStatus(e.key),
                },
                {
                  key: 'service-provider-price',
                  label: 'Prestataire',
                  onClick: (e) => handleChangeCatalogPriceStatus(e.key),
                },
              ]}
              selectedKeys={selectedKeys}
            />
          </Col>
        </Row>
        <div style={{ marginBottom: 18 }}>
          <PriceDescription
            form={form}
            textareaRef={textareaRef}
            isEditing={true}
            isShowSuggestions={true}
            priceOptionList={priceOptionListInput}
            onChangeSuggestions={handleChangeSuggestions}
            productTypeId={productTypeId}
            price={price}
            getListPrices={getListPrices}
          />
          <hr className='product-tarifs__end-line' />
        </div>

        <div className='product-tarifs__price-option-list'>
          {action === 'update' && loading ? (
            <Spin
              spinning={loading}
              indicator={
                <PulseLoader
                  size={8}
                  color='#A6C84D'
                  cssOverride={{
                    margin: '0 auto',
                    borderColor: 'red',
                    height: '100%',
                    width: '100%',
                  }}
                  aria-label='Loading Spinner'
                  data-testid='loader'
                />
              }
            ></Spin>
          ) : (
            <List
              itemLayout='horizontal'
              dataSource={priceOptionList}
              renderItem={(priceOptionItem) => {
                return !priceOptionItem?.id ? (
                  <PriceOptionItem
                    key={priceOptionItem?.tempId}
                    parameterBlockAction={action}
                    action='create'
                    tempId={priceOptionItem?.tempId}
                    handleChangePriceOptionList={handleChangePriceOptionList}
                    onPressEnterPriceOption={handlePressEnterOnPriceOption}
                    form={form}
                    priceOptionId={priceOptionItem?.tempId}
                  />
                ) : (
                  <PriceOptionItem
                    key={priceOptionItem.id}
                    parameterBlockAction={action}
                    action='update'
                    priceOption={priceOptionItem}
                    handleChangePriceOptionList={handleChangePriceOptionList}
                    onPressEnterPriceOption={handlePressEnterOnPriceOption}
                    form={form}
                    priceOptionId={priceOptionItem.id}
                  />
                );
              }}
            />
          )}
        </div>
        <Button
          type='text'
          size='small'
          className='product-tarifs__add-typeform-btn'
          onClick={() => handleAddPriceOption()}
        >
          <PlusOutlined />
          Ajouter un champ de formulaire
        </Button>
      </Space>
      <Space className='product-tarifs__edit-cancel-btn'>
        {(action === 'update' && updatingLoading) || (action === 'create' && creatingLoading) ? (
          <LoadingOutlined className='product-description__description-loading-icon' />
        ) : (
          <CheckButton
            className='check-button'
            onClick={() =>
              action === 'create'
                ? handleCreatePriceParameters(priceFamilyId)
                : handleUpdatePriceParameters(priceFamilyId, price?.id)
            }
          />
        )}

        <CloseOutlined
          className='product-tarifs__parameter-cancel-icon cancel-button'
          onClick={() => handleShowParameterBlock(false)}
        />
      </Space>
    </Space>
  );
};

export default ParameterBlock;
