import { DownOutlined } from '@ant-design/icons';
import { Button, Dropdown, Form, FormInstance, Menu, MenuProps, Space } from 'antd';
import TextArea, { TextAreaRef } from 'antd/es/input/TextArea';
import { CustomTextArea } from 'components/Common';
import { Price } from 'models';
import { AddTagsPriceOption } from 'utils/converters';
import { CustomPriceOption } from '.';
import { ItemType } from 'antd/es/menu/hooks/useItems';
import { MenuItemType } from 'antd/es/menu/hooks/useItems';

interface PriceDescriptionProps {
  form: FormInstance;
  textareaRef?: React.RefObject<TextAreaRef>;
  isEditing: boolean;
  isShowSuggestions: boolean;
  priceOptionList?: CustomPriceOption[];
  onChangeSuggestions?: (selectedPriceOptionId: string | number, value: string) => void;
  productTypeId: number;
  price?: Price & { tempId?: string };
  getListPrices: () => void;
}

const PriceDescription = (props: PriceDescriptionProps) => {
  const {
    form,
    textareaRef,
    isEditing,
    isShowSuggestions,
    priceOptionList,
    onChangeSuggestions,
    price,
    getListPrices,
  } = props;

  const menuItems: MenuProps['items'] = [
    {
      id: `Prix-${price?.id ?? price?.tempId}-Prix`,
      key: `Prix-${price?.id ?? price?.tempId}-prix`,
      label: 'Prix',
      title: 'Prix',
    } as ItemType<MenuItemType>,
    ...(priceOptionList ?? [])
      .filter((i) => i.name)
      .map((priceOption) => ({
        id: `Prix-Option-${priceOption.id ?? priceOption.tempId}-Input`,
        key: `${priceOption.id ?? priceOption.tempId}-price-option-input`,
        label: `${priceOption.name} Input`,
        title: `${priceOption.name}`,
      })),
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    const selectedItem = menuItems?.find((item) => item?.key === e.key) as {
      id: string;
      key: string;
      title: string;
    };
    if (selectedItem) {
      if (onChangeSuggestions) {
        onChangeSuggestions(selectedItem.id, selectedItem.title);
      }
    }
  };

  const menu = <Menu onClick={(e) => handleMenuClick(e)} items={menuItems} />;
  return (
    <>
      {isShowSuggestions && (
        <Form.Item noStyle className='product-description__select-item'>
          <Dropdown overlay={menu} className='product-description__selection-dropdown' disabled={false}>
            <Button>
              <Space>
                Suggestions
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Form.Item>
      )}
      <Form.Item name='description' noStyle className='product-description__description-textarea-item'>
        {isEditing ? (
          <TextArea
            ref={textareaRef}
            size='large'
            rows={6}
            placeholder='Description'
            className='product-description__description-textarea'
            value={price?.description ?? ''}
          />
        ) : (
          <CustomTextArea>
            <AddTagsPriceOption
              str={price?.description ?? ''}
              handleUpdateWordState={getListPrices}
              selectedPrice={price}
              onChangeDescription={(value: string) => form.setFieldValue('description', value)}
            />
          </CustomTextArea>
        )}
      </Form.Item>
    </>
  );
};

export default PriceDescription;
