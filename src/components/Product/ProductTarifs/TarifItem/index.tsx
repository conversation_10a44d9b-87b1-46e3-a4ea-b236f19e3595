import { CloseOutlined, EditOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Input, List, Row, Typography } from 'antd';
import { CheckButton, ThreeDotDropdown, TrashButton } from 'components/Common';
import { Price, PriceFamily } from 'models';
import { useState, useEffect } from 'react';
import { priceFamilyService, priceService } from 'services';
import { useMergeState } from 'hooks';
import { pick } from 'utils';
import { toast } from 'react-toastify';
import ParameterBlock from '../ParameterBlock';
import PriceItem from '../PriceItem';
import { v4 as uuidv4 } from 'uuid';
const { Text } = Typography;

const TarifItem = ({
  priceFamily,
  productTypeId,
  refreshTarif,
}: {
  priceFamily: PriceFamily;
  productTypeId: number;
  refreshTarif: () => Promise<void>;
}) => {
  const [form] = Form.useForm();
  const [selectedPriceFamily, setSelectedPriceFamily] = useMergeState<PriceFamily | null>(null);
  const [loading, setIsLoading] = useState<boolean>(false);
  const [showAddParameterBlock, setShowAddParameterBlock] = useState<boolean>(false);
  const [deletingIds, setDeletingIds] = useState<(number | undefined)[]>([]);
  const [priceList, setPriceList] = useState<Price[]>([]);
  const [createPrice, setCreatePrice] = useState<Price & { tempId?: string }>();
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [catalogPriceStatusId, setCatalogPriceStatusId] = useState<number | null>(null);
  const [, setFournisseursPriceStatusId] = useState<number | null>(null);
  useEffect(() => {
    const keys: string[] = [];
    priceFamily?.CatalogPrices?.filter((item) => !item.priceId && !item.priceOptionId).forEach((item) => {
      if (item.isCatalog) {
        setCatalogPriceStatusId(item.id as number);
        if (!keys.includes('catalog-price') && item.isActive) {
          keys.push('catalog-price');
        }
      } else if (!item.isCatalog) {
        setFournisseursPriceStatusId(item.id as number);
        if (!keys.includes('fournisseurs-price') && item.isActive) {
          keys.push('fournisseurs-price');
        }
      }
    });
    setSelectedKeys(keys);
  }, [priceFamily]);

  const getListPrices = async (priceFamilyId: number) => {
    try {
      const prices = await priceService.getPrices({
        priceFamilyId: priceFamilyId,
        include: 'PriceOptions|PriceFamily|CatalogPrices',
      });
      setPriceList(prices?.rows || []);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const refreshListPrices = async (priceFamilyId: number) => {
    await Promise.all([getListPrices(priceFamilyId)]);
  };

  useEffect(() => {
    getListPrices(priceFamily?.id);
  }, []);

  const handleDeactivatePriceFamilyItem = async (productTypeId: number, priceFamilyId: number) => {
    try {
      setDeletingIds((prevState) => [...prevState, priceFamilyId as number]);
      const [deletedPriceFamily] = await Promise.all([
        priceFamilyService.deactivatePriceFamily(productTypeId, priceFamilyId),
      ]);
      await refreshTarif();
      setDeletingIds(deletingIds.filter((id) => id !== deletedPriceFamily.id));
      toast.success('Succès');
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const handleActiveCatalogPriceOfPriceFamily = async (selectedKey: string) => {
    try {
      let value = false;
      if (selectedKeys.findIndex((key) => key === selectedKey) === -1) {
        value = true;
        setSelectedKeys([...selectedKeys, selectedKey]);
      } else {
        value = false;
        const newSelectedArray = selectedKeys.filter((key) => key !== selectedKey);
        setSelectedKeys(newSelectedArray);
      }
      await priceFamilyService.updatePriceFamily(productTypeId, priceFamily?.id, {
        CatalogPrice: { id: catalogPriceStatusId, isActive: value, isCatalog: true },
      });
      toast.success('Succès');
      await refreshTarif();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleActiveFournisseursOfPriceFamily = async (selectedKey: string) => {
    try {
      let value = false;
      if (selectedKeys.findIndex((key) => key === selectedKey) === -1) {
        value = true;
        setSelectedKeys([...selectedKeys, selectedKey]);
      } else {
        value = false;
        const newSelectedArray = selectedKeys.filter((key) => key !== selectedKey);
        setSelectedKeys(newSelectedArray);
      }
      await priceFamilyService.updatePriceFamily(productTypeId, priceFamily?.id, {
        CatalogPrice: { isActive: value, isCatalog: false },
      });
      toast.success('Succès');
      await refreshTarif();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleSelectPriceFamily = () => {
    setSelectedPriceFamily(priceFamily, true);
  };

  const handleShowAddParameterBlock = (value: boolean) => {
    setShowAddParameterBlock(value);
    setCreatePrice({ tempId: uuidv4() } as Price & { tempId?: string });
  };

  const handleEditPriceFamily = async () => {
    const values = await form.validateFields();
    try {
      setIsLoading(true);
      await priceFamilyService.updatePriceFamily(productTypeId, selectedPriceFamily?.id as number, {
        ...pick(['name', 'labelName'], values),
      });
      setIsLoading(false);
      await refreshTarif();
      setSelectedPriceFamily(null, true);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      setIsLoading(false);
      toast.error('Erreur');
    }
  };

  return (
    <>
      <div className='product-tarifs__tarif-item'>
        {!selectedPriceFamily ? (
          <>
            <Text className='product-tarifs__title-label'>
              {selectedPriceFamily ? 'Titre du tarif' : priceFamily.name}:
            </Text>
            <EditOutlined className='product-tarifs__edit-icon edit-icon' onClick={handleSelectPriceFamily} />
            <TrashButton
              className='product-tarifs__trash-icon trash-button'
              onClick={() => handleDeactivatePriceFamilyItem(productTypeId as number, priceFamily?.id as number)}
              loading={deletingIds.includes(priceFamily?.id)}
            />
          </>
        ) : (
          <>
            <Form form={form} name='price-family' autoComplete='off'>
              <Row gutter={[16, 16]}>
                <Col span={12}>
                  <Form.Item
                    name='name'
                    label='Titre du tarif'
                    initialValue={selectedPriceFamily?.name}
                    rules={[{ required: true, message: '' }]}
                    style={{ marginBottom: 0, width: '100%' }}
                  >
                    <Input
                      placeholder='Input'
                      className='product-tarifs__item-content-input'
                      onPressEnter={handleEditPriceFamily}
                    />
                  </Form.Item>
                </Col>
                <Col span={12}>
                  <Form.Item
                    name='labelName'
                    label='Nom tarif'
                    initialValue={selectedPriceFamily?.labelName}
                    rules={[
                      { required: true, message: '' },
                      { max: 255, message: '' },
                    ]}
                    style={{ marginBottom: 0, width: '100%' }}
                  >
                    <Input
                      placeholder='Input'
                      className='product-tarifs__item-content-input'
                      onPressEnter={handleEditPriceFamily}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
            <CheckButton className='check-button' onClick={handleEditPriceFamily} loading={loading} />
            <CloseOutlined
              className='product-tarifs__item-cancel-icon cancel-button'
              onClick={() => setSelectedPriceFamily(null, true)}
            />
          </>
        )}
        <ThreeDotDropdown
          items={[
            {
              key: 'catalog-price',
              label: 'Catalogue',
              onClick: (e) => handleActiveCatalogPriceOfPriceFamily(e.key),
            },
            {
              key: 'fournisseurs-price',
              label: 'Prestataire',
              onClick: (e) => handleActiveFournisseursOfPriceFamily(e.key),
            },
          ]}
          selectedKeys={selectedKeys}
        />
      </div>
      <List
        itemLayout='horizontal'
        dataSource={priceList}
        renderItem={(priceItem) => (
          <>
            <PriceItem
              key={priceItem.id}
              getListPrices={refreshListPrices}
              price={priceItem}
              productTypeId={productTypeId}
              priceFamilyId={priceFamily?.id}
            />
          </>
        )}
      />
      {showAddParameterBlock && (
        <Form name='product-type-price' form={form} autoComplete='off'>
          <ParameterBlock
            form={form}
            priceFamilyId={priceFamily.id}
            fetchListPrices={refreshListPrices}
            handleShowParameterBlock={handleShowAddParameterBlock}
            action='create'
            price={createPrice}
          />
        </Form>
      )}
      {!showAddParameterBlock && (
        <Button
          type='text'
          size='small'
          className='product-tarifs__add-parameter-btn'
          onClick={() => handleShowAddParameterBlock(true)}
        >
          <PlusOutlined />
          Ajouter un paramètre
        </Button>
      )}
      <hr className='product-tarifs__end-line' />
    </>
  );
};
export default TarifItem;
