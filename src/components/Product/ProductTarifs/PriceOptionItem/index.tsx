import { PlusCircleOutlined } from '@ant-design/icons';
import BulletPointEmptyIcon from 'components/Icons/BulletPointEmptyIcon';
import { Button, Col, Form, FormInstance, Input, List, Row, Select, Tag } from 'antd';
import { ThreeDotDropdown, TrashButton } from 'components/Common';
import { useState, useEffect } from 'react';
import { PriceOption, PriceSubOption } from 'models';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchPriceTypes, selectPriceTypes } from 'store/slices/price.slices';
import { toast } from 'react-toastify';
import { PRICE_TYPES } from 'utils/constant';
import { priceSubOptionService } from 'services';
import { useMergeState } from 'hooks';
import { v4 as uuidv4 } from 'uuid';
const { Option } = Select;

type CustomPriceSubOption = PriceSubOption & {
  tempId?: string;
};
const PriceOptionItem = ({
  priceOption,
  parameterBlockAction,
  action,
  handleChangePriceOptionList,
  onPressEnterPriceOption,
  form,
  priceOptionId,
}: {
  priceOption?: PriceOption;
  parameterBlockAction: 'create' | 'update';
  action: 'create' | 'update';
  tempId?: string;
  handleChangePriceOptionList: (
    priceOptionId?: number,
    name?: string | null,
    priceTypeId?: number | null,
    unit?: string | null,
    defaultValue?: string | null,
    catalogPriceStatus?: boolean,
    serviceProviderPriceStatus?: boolean,
    priceOptionFakeId?: number | string | null,
    PriceSubOptions?: PriceSubOption[],
  ) => void;
  onPressEnterPriceOption: (formAction: 'create' | 'update') => void;
  form?: FormInstance;
  priceOptionId?: number | null | string;
}) => {
  const dispatch = useAppDispatch();
  // const [form] = Form.useForm();
  const [showPriceSubOption, setShowPriceSubOption] = useState<boolean>(true);
  const [isFirstLoading, setIsFirstLoading] = useState<boolean>(true);
  const [catalogPriceStatus, setCatalogPriceStatus] = useState<boolean>(false);
  const [serviceProviderPriceStatus, setServiceProviderPriceStatus] = useState<boolean>(false);
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [priceOptionName, setPriceOptionName] = useState<string | null>('');
  const [selectedPriceSubOption, setSelectedPriceSubOption] = useMergeState<PriceSubOption | null>(null);
  const [selectedPriceSubOptionList, setSelectedPriceSubOptionList] = useState<CustomPriceSubOption[]>([]);
  const [unit, setUnit] = useState<string | null>('');
  const [deletingPriceSubOptionIds, setDeletingPriceSubOptionIds] = useState<(number | undefined)[]>([]);
  const [deletingPriceSubOptionFakeIds, setDeletingPriceSubOptionFakeIds] = useState<(number | string | undefined)[]>(
    [],
  );
  const [selectedPriceType, setSelectedPriceType] = useState<number | null>();
  const [loading, setLoading] = useState<boolean>(false);
  const [defaultValue, setDefaultValue] = useState<string | null>('');
  const priceTypes = useAppSelector(selectPriceTypes);
  const priceType = priceTypes.find((i) => i.key === PRICE_TYPES.selection);

  const getListPriceTypes = async () => {
    await dispatch(fetchPriceTypes({}))
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
    setTimeout(() => {
      setIsFirstLoading(false);
    });
  };

  const getListPriceSubOptions = async (priceOptionId: number) => {
    try {
      const priceSubOptions = await priceSubOptionService.getPriceSubOptions(priceOptionId);
      setSelectedPriceSubOptionList(priceSubOptions);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleChangeInputType = (value: number | undefined) => {
    setSelectedPriceType(value);
    if (value !== priceType?.id) {
      setShowPriceSubOption(false);
      return;
    }
    setUnit(null);
    setShowPriceSubOption(true);
  };
  const initialValues = () => {
    form?.setFieldsValue({
      priceOptionList: {
        [`${priceOptionId}`]: {
          priceOptionName: priceOption?.name,
          selectedPriceType: priceOption?.PriceType?.id,
          unit: priceOption?.unit,
          defaultValue: priceOption?.defaultValue,
        },
      },
    });
  };
  useEffect(() => {
    getListPriceTypes();
    if (action === 'update') {
      setPriceOptionName(priceOption?.name ?? null);
      setUnit(priceOption?.unit ?? null);
      setDefaultValue(priceOption?.defaultValue ?? null);
      setSelectedPriceSubOptionList(priceOption?.PriceSubOptions ?? []);
      setSelectedPriceType(priceOption?.priceTypeId ?? null);
      const keys: string[] = [];
      priceOption?.CatalogPrices?.filter((item) => item.priceOptionId).forEach((item) => {
        if (item.isCatalog) {
          setCatalogPriceStatus(item.isActive as boolean);
          if (!keys.includes('catalog-price') && item.isActive) {
            keys.push('catalog-price');
          }
        } else {
          setServiceProviderPriceStatus(item.isActive as boolean);
          if (!keys.includes('service-provider-price') && item.isActive) {
            keys.push('service-provider-price');
          }
        }
      });
      setSelectedKeys(keys);
    }
    if (action === 'create') {
      handleChangePriceOptionList(
        priceOption?.id,
        priceOptionName,
        selectedPriceType,
        unit,
        defaultValue,
        catalogPriceStatus,
        serviceProviderPriceStatus,
        priceOptionId,
      );
    }
    initialValues();
  }, []);

  useEffect(() => {
    handleChangeInputType(priceOption?.PriceType?.id);
  }, [isFirstLoading]);

  useEffect(() => {
    handleChangePriceOptionList(
      priceOption?.id,
      priceOptionName,
      selectedPriceType,
      unit,
      defaultValue,
      catalogPriceStatus,
      serviceProviderPriceStatus,
      priceOptionId === priceOption?.id ? null : priceOptionId,
      selectedPriceSubOptionList,
    );
  }, [
    priceOptionName,
    selectedPriceType,
    unit,
    selectedPriceSubOptionList,
    catalogPriceStatus,
    serviceProviderPriceStatus,
    defaultValue,
  ]);

  const resetSelectedPriceSubOption = () => {
    setSelectedPriceSubOption(null, true);
  };

  const handleAddPriceSubOption = async (priceOptionId: number) => {
    if (!selectedPriceSubOption?.name) return;
    try {
      setLoading(true);
      if (action === 'create') {
        setSelectedPriceSubOptionList([
          ...selectedPriceSubOptionList,
          {
            priceOptionId,
            tempId: uuidv4(),
            name: selectedPriceSubOption?.name,
          },
        ]);
        resetSelectedPriceSubOption();
      } else if (action === 'update') {
        await priceSubOptionService.createPriceSubOption(priceOptionId, {
          name: selectedPriceSubOption?.name,
        });
        resetSelectedPriceSubOption();
        await getListPriceSubOptions(priceOptionId);
      }
      setLoading(false);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleDeletePriceSubOption = async (priceOptionId?: string | number, priceSubOptionId?: string | number) => {
    if (!priceOptionId || !priceSubOptionId) return;
    try {
      if (action === 'update') {
        setDeletingPriceSubOptionIds((prevState) => [...prevState, priceSubOptionId as number]);
        await priceSubOptionService.deletePriceSubOption(priceOptionId as number, priceSubOptionId as number);
        await getListPriceSubOptions(priceOptionId as number);
        setDeletingPriceSubOptionIds(deletingPriceSubOptionIds.filter((id) => id !== priceSubOptionId));
      } else if (action === 'create') {
        setDeletingPriceSubOptionFakeIds((prevState) => [...prevState, priceSubOptionId as number]);
        setSelectedPriceSubOptionList(selectedPriceSubOptionList.filter((item) => item?.tempId !== priceSubOptionId));
        setDeletingPriceSubOptionFakeIds(deletingPriceSubOptionIds.filter((id) => id !== priceSubOptionId));
      }
    } catch (error) {
      console.log(error);
      if (action === 'update') {
        setDeletingPriceSubOptionIds(deletingPriceSubOptionIds.filter((id) => id !== priceSubOptionId));
      }
      toast.error('Erreur');
    }
  };

  const handleChangeCatalogPriceStatus = (selectedKey: string) => {
    let value = false;
    if (selectedKeys.findIndex((key) => key === selectedKey) === -1) {
      value = true;
      setSelectedKeys([...selectedKeys, selectedKey]);
    } else {
      value = false;
      const newSelectedArray = selectedKeys.filter((key) => key !== selectedKey);
      setSelectedKeys(newSelectedArray);
    }
    if (selectedKey === 'service-provider-price') {
      setServiceProviderPriceStatus(value);
    } else {
      setCatalogPriceStatus(value);
    }
  };

  return (
    <Row className='product-tarifs__price-option-item'>
      <Col>
        <BulletPointEmptyIcon className='product-tarifs__bullet-point-empty-icon' />
      </Col>
      <Col span={22}>
        <Row gutter={24}>
          <Col span={12}>
            <Form.Item
              name={['priceOptionList', `${priceOptionId}`, 'priceOptionName']}
              label='Titre'
              rules={[{ required: true, message: '' }]}
              className='product-tarifs__price-option-input'
              initialValue={priceOption?.name}
            >
              <Input
                className='product-tarifs__price-option-name-input'
                placeholder='Input'
                value={priceOptionName ?? ''}
                onChange={(e) => setPriceOptionName(e.target.value)}
                onPressEnter={() => onPressEnterPriceOption(parameterBlockAction)}
              />
            </Form.Item>
          </Col>
          <Col span={12}>
            <Form.Item
              name={['priceOptionList', `${priceOptionId}`, 'selectedPriceType']}
              label='Type de champ'
              rules={[{ required: true, message: '' }]}
              className='product-tarifs__price-option-item-left'
              initialValue={priceOption?.PriceType?.id}
            >
              <Select
                placeholder='Select'
                value={selectedPriceType}
                className='product-tarifs__price-option-input-type-select'
                onChange={(value) => handleChangeInputType(value)}
              >
                {priceTypes.map((priceType) => (
                  <Option key={priceType?.id} value={priceType?.id}>
                    {priceType?.name}
                  </Option>
                ))}
              </Select>
            </Form.Item>
          </Col>
          <Col span={12} style={{ display: 'flex' }}>
            {showPriceSubOption ? (
              <>
                <Form.Item name='valeur' label='Valeurs' className='product-tarifs__price-option-input'>
                  <Input
                    className='product-tarifs__price-option-name-input'
                    placeholder='Input'
                    value={selectedPriceSubOption?.name}
                    onChange={(e) => setSelectedPriceSubOption({ name: e.target.value })}
                    onPressEnter={() => handleAddPriceSubOption(priceOptionId as number)}
                    style={{ marginBottom: '10px' }}
                  />
                  {action === 'update' && (
                    <List
                      itemLayout='vertical'
                      dataSource={selectedPriceSubOptionList}
                      renderItem={(priceSubOptionItem) => (
                        <Tag
                          className='product-tarifs__sub-option-tag ignore-blur'
                          key={priceSubOptionItem?.id}
                          style={{ marginBottom: '4px' }}
                          tabIndex={0}
                        >
                          {priceSubOptionItem?.name}
                          <TrashButton
                            className='product-tarifs__sub-option-trash-icon trash-button ignore-blur'
                            onClick={() => handleDeletePriceSubOption(priceOptionId as number, priceSubOptionItem?.id)}
                            loading={deletingPriceSubOptionIds.includes(priceSubOptionItem?.id)}
                          />
                        </Tag>
                      )}
                    />
                  )}
                  {action === 'create' && (
                    <List
                      itemLayout='vertical'
                      dataSource={selectedPriceSubOptionList}
                      renderItem={(priceSubOptionItem) => (
                        <Tag
                          className='product-tarifs__sub-option-tag ignore-blur'
                          key={priceSubOptionItem?.tempId}
                          style={{ marginBottom: '4px' }}
                          tabIndex={0}
                        >
                          {priceSubOptionItem?.name}
                          <TrashButton
                            className='product-tarifs__sub-option-trash-icon trash-button ignore-blur'
                            onClick={() =>
                              handleDeletePriceSubOption(priceOptionId as number, priceSubOptionItem?.tempId)
                            }
                            loading={deletingPriceSubOptionFakeIds.includes(priceSubOptionItem?.tempId)}
                          />
                        </Tag>
                      )}
                    />
                  )}
                </Form.Item>
                <div className='product-tarifs__title-ajouter'>
                  <Button
                    type='text'
                    size='small'
                    loading={loading}
                    className='btn-add__ajouter-btn'
                    onClick={() => handleAddPriceSubOption(priceOptionId as number)}
                  >
                    <PlusCircleOutlined className='btn-add__add_icon' />
                    Ajouter
                  </Button>
                </div>
              </>
            ) : (
              <Form.Item
                name={['priceOptionList', `${priceOptionId}`, 'unit']}
                label='Unité'
                className='product-tarifs__price-option-input'
              >
                <Input
                  className='product-tarifs__price-option-name-input'
                  placeholder='Input'
                  value={unit ?? ''}
                  onChange={(e) => setUnit(e.target.value)}
                  onPressEnter={() => onPressEnterPriceOption(parameterBlockAction)}
                />
              </Form.Item>
            )}
          </Col>
          {!showPriceSubOption && (
            <Col span={12}>
              <Form.Item
                name={['priceOptionList', `${priceOptionId}`, 'defaultValue']}
                label='Valeur par défaut'
                className='product-tarifs__price-option-item-left'
                initialValue={priceOption?.defaultValue}
              >
                <Input
                  className='product-tarifs__price-option-name-input-left'
                  placeholder='Input'
                  value={defaultValue ?? ''}
                  onChange={(e) => setDefaultValue(e.target.value)}
                  onPressEnter={() => onPressEnterPriceOption(parameterBlockAction)}
                />
              </Form.Item>
            </Col>
          )}
        </Row>
      </Col>
      <Col>
        <ThreeDotDropdown
          items={[
            {
              key: 'catalog-price',
              label: 'Catalogue',
              onClick: (e) => handleChangeCatalogPriceStatus(e.key),
            },
            {
              key: 'service-provider-price',
              label: 'Prestataire',
              onClick: (e) => handleChangeCatalogPriceStatus(e.key),
            },
          ]}
          selectedKeys={selectedKeys}
        />
      </Col>
    </Row>
  );
};

export default PriceOptionItem;
