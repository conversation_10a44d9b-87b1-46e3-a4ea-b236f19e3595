import { useEffect, useState } from 'react';
import { OptionCard } from 'components/Common';
import ButtonAdd from 'components/Common/ButtonAdd';
import { Button, Col, Form, Input, List, Row, Switch, Typography } from 'antd';
import { PlusCircleOutlined, CloseOutlined } from '@ant-design/icons';
import TarifItem from './TarifItem';
import { useMergeState } from 'hooks';
import { ProductType } from 'models';
import { catalogPriceService, priceFamilyService, productService } from 'services';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchPriceFamilies, selectPriceFamilies } from 'store/slices/price.slices';
const { Text } = Typography;

const ProductTarifs = ({ productTypeId, productType }: { productTypeId: number; productType: ProductType | null }) => {
  const dispatch = useAppDispatch();
  const [form] = Form.useForm();
  const [showCreateForm, setShowCreateForm] = useState<boolean>(false);
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const priceFamilies = useAppSelector(selectPriceFamilies);
  const [toggle, setToggle] = useMergeState<ProductType | null>(productType);
  const getListPriceFamilies = async () => {
    await dispatch(fetchPriceFamilies({ include: 'CatalogPrices', productTypeId })).unwrap();
  };

  const refreshTarif = async () => {
    await Promise.all([getListPriceFamilies()]);
  };

  useEffect(() => {
    getListPriceFamilies();
  }, []);

  const handleShowCreateForm = (value: boolean) => {
    setShowCreateForm(value);
  };

  const handleAddPriceFamily = async () => {
    const values = await form.validateFields();
    try {
      setIsCreating(true);
      await priceFamilyService.createPriceFamily(productTypeId, {
        name: values?.titleTarif,
        labelName: values?.nomTarif,
      });
      setIsCreating(false);
      toast.success('Succès');
      await refreshTarif();
      handleShowCreateForm(false);
      form.resetFields();
    } catch (error) {
      console.log(error);
      setIsCreating(false);
      toast.error('Erreur');
    }
  };
  const handleChangeCatalogToggle = async (value: boolean, isCatalog: boolean) => {
    try {
      if (productType) {
        const data = {
          ...toggle,
          ...(isCatalog ? { isCatalog: value } : { isFournisseur: value }),
        };
        setToggle(data);
        await Promise.all([
          productService.updateProductType(productTypeId, data),
          catalogPriceService.createCatalogPrice({
            productTypeId: productTypeId,
            isCatalog,
          }),
        ]);
        toast.success('Succès');
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  return (
    <OptionCard
      title='Tarifs'
      otherStyles={{
        marginTop: '16px',
        marginBottom: '16px',
        borderRadius: '6px',
      }}
    >
      <div className='product-tarifs__tarif-list'>
        <List
          itemLayout='horizontal'
          dataSource={priceFamilies}
          renderItem={(item) => (
            <TarifItem key={item.id} refreshTarif={refreshTarif} priceFamily={item} productTypeId={productTypeId} />
          )}
        />
      </div>
      {showCreateForm && (
        <div className='product-tarifs__title'>
          <Form form={form} name='price-family' autoComplete='off'>
            <Row gutter={[16, 16]}>
              <Col span={12}>
                <Form.Item
                  name='titleTarif'
                  label='Titre du tarif'
                  rules={[{ required: true, message: '' }]}
                  style={{ marginBottom: 0, width: '100%' }}
                >
                  <Input
                    placeholder='Input'
                    className='product-tarifs__title-content-input'
                    onPressEnter={handleAddPriceFamily}
                  />
                </Form.Item>
              </Col>
              <Col span={12}>
                <Form.Item
                  name='nomTarif'
                  label='Nom tarif'
                  rules={[
                    { required: true, message: '' },
                    { max: 255, message: '' },
                  ]}
                  style={{ marginBottom: 0, width: '100%' }}
                >
                  <Input
                    placeholder='Input'
                    className='product-tarifs__title-content-input'
                    onPressEnter={handleAddPriceFamily}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Form>
          <Button
            type='text'
            size='small'
            loading={isCreating}
            className='btn-add__ajouter-btn'
            onClick={handleAddPriceFamily}
          >
            <PlusCircleOutlined className='btn-add__add_icon' />
            Ajouter
          </Button>
          <CloseOutlined
            className='product-tarifs__title-of-product-cancel-icon cancel-button'
            onClick={() => handleShowCreateForm(false)}
          />
        </div>
      )}
      {!showCreateForm && (
        <>
          {priceFamilies.length === 0 && (
            <div className='d-flex align-center'>
              <Text className='product-tarifs__title-label'>Catalogue</Text>
              <Switch
                className='product-tarifs__switch'
                onChange={(value) => handleChangeCatalogToggle(value, true)}
                checked={toggle?.isCatalog}
              />
              <Text className='product-tarifs__title-label ml-4'>Prestataire</Text>
              <Switch
                className='product-tarifs__switch'
                onChange={(value) => handleChangeCatalogToggle(value, false)}
                checked={toggle?.isFournisseur}
              />
            </div>
          )}
          <ButtonAdd
            otherStyles={{
              height: '32px',
              marginTop: '28px',
            }}
            handleClick={() => handleShowCreateForm(true)}
            disabled={toggle?.isCatalog || toggle?.isFournisseur}
          >
            Ajouter un tarif
          </ButtonAdd>
        </>
      )}
    </OptionCard>
  );
};

export default ProductTarifs;
