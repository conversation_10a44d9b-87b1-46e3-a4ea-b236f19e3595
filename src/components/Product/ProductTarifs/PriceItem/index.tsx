import { Form, Typography } from 'antd';
import BulletPointIcon from 'components/Icons/BulletPointIcon';
import { EditOutlined } from '@ant-design/icons';
import ParameterBlock from '../ParameterBlock';
import { useState } from 'react';
import { Price } from 'models';
import PriceDescription from '../ParameterBlock/PriceDescription';
import BulletPointEmptyIcon from 'components/Icons/BulletPointEmptyIcon';
const { Text } = Typography;

const PriceItem = ({
  price,
  productTypeId,
  priceFamilyId,
  getListPrices,
}: {
  price: Price;
  productTypeId: number;
  priceFamilyId: number;
  getListPrices: (priceFamilyId: number) => void;
}) => {
  const [form] = Form.useForm();
  const [showEditParameterBlock, setShowEditParameterBlock] = useState<boolean>(false);

  const handleShowEditParameterBlock = (value: boolean) => {
    setShowEditParameterBlock(value);
  };

  const fetchListPrices = async () => {
    await getListPrices(priceFamilyId);
  };

  return (
    <Form name='product-type-price' form={form} autoComplete='off'>
      {!showEditParameterBlock && (
        <>
          <div className='product-tarifs__price-option-items'>
            <BulletPointIcon className='product-tarifs__price-option-bullet-point-icon' />
            <Text className='product-tarifs__price-option-label'>{price?.name}</Text>
            <EditOutlined
              className='product-tarifs__edit-icon edit-icon'
              onClick={() => handleShowEditParameterBlock(true)}
            />
          </div>
          <div style={{ marginBottom: 18 }}>
            <PriceDescription
              form={form}
              isEditing={false}
              isShowSuggestions={false}
              productTypeId={productTypeId}
              price={price}
              getListPrices={fetchListPrices}
            />
          </div>
          <div style={{ marginLeft: 35, marginBottom: 24 }}>
            {price?.PriceOptions?.map((priceOption) => (
              <div key={priceOption.id} style={{ marginBottom: 8 }}>
                <BulletPointEmptyIcon className='product-tarifs__bullet-point-empty-icon' />
                <Text>{priceOption.name}</Text>
              </div>
            ))}
          </div>
        </>
      )}
      {showEditParameterBlock && (
        <ParameterBlock
          form={form}
          priceFamilyId={priceFamilyId}
          price={price}
          fetchListPrices={getListPrices}
          handleShowParameterBlock={handleShowEditParameterBlock}
          action='update'
        />
      )}
    </Form>
  );
};

export default PriceItem;
