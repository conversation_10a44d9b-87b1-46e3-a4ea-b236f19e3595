import { useState, useEffect } from 'react';
import { Button, Input, List, Typography } from 'antd';
import { toast } from 'react-toastify';
import { OptionCard } from 'components/Common';
import { Option } from 'models';
import ButtonAdd from 'components/Common/ButtonAdd';
import VariationItem from './VariationItem';
import { useMergeState } from 'hooks';
import { optionService } from 'services';
import { OPTION_TYPES } from 'utils/constant';
import { CloseOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchVariationOptions, selectOptionTypes, selectVariationOptions } from 'store/slices/option.slices';

const { Text } = Typography;

const ProductVariation = ({ productTypeId }: { productTypeId: number }) => {
  const dispatch = useAppDispatch();
  const [selectedVariation, setSelectedVariation] = useMergeState<Option | null>(null);
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const optionTypes = useAppSelector(selectOptionTypes);
  const variationOption = optionTypes.find((i) => i.key === OPTION_TYPES.variation);
  const variationList = useAppSelector(selectVariationOptions);

  const getListVariations = async () => {
    await dispatch(
      fetchVariationOptions({
        productTypeId,
        query: {
          optionType: OPTION_TYPES.variation,
        },
      }),
    ).unwrap();
  };

  useEffect(() => {
    getListVariations();
  }, []);

  const resetSelectedVariation = () => {
    setSelectedVariation(null, true);
  };

  const handleAddVariation = async () => {
    if (!selectedVariation?.name) return;
    try {
      setIsCreating(true);
      await optionService.createOption(productTypeId, {
        ...selectedVariation,
        optionTypeId: variationOption?.id,
      });
      setIsCreating(false);
      resetSelectedVariation();
      toast.success('Succès');
      await getListVariations();
    } catch (error) {
      setIsCreating(false);
      toast.error('Erreur');
      console.log(error);
    }
  };

  return (
    <OptionCard
      title='Déclinaisons'
      otherStyles={{
        marginTop: '16px',
        borderRadius: '6px',
      }}
    >
      <div className='product-variation__option-list'>
        <List
          itemLayout='horizontal'
          dataSource={variationList}
          style={{ width: '100%' }}
          renderItem={(item) => (
            <VariationItem
              key={item.id}
              getListVariations={getListVariations}
              variation={item}
              productTypeId={productTypeId}
            />
          )}
        />
      </div>
      {selectedVariation && (
        <div className='product-variation__title'>
          <Text className='product-variation__title-label'>Titre de la déclinaison:</Text>
          <Input
            placeholder='Input'
            value={selectedVariation?.name}
            className='product-variation__title-content-input'
            onChange={(e) => setSelectedVariation({ name: e.target.value })}
            onPressEnter={handleAddVariation}
          />
          <Button
            type='text'
            size='small'
            loading={isCreating}
            className='btn-add__ajouter-btn'
            onClick={handleAddVariation}
          >
            <PlusCircleOutlined className='btn-add__add_icon' />
            Ajouter
          </Button>
          <Button
            type='link'
            icon={<CloseOutlined style={{ fontSize: '18px', color: 'black' }} />}
            onClick={() => resetSelectedVariation()}
          />
        </div>
      )}
      {!selectedVariation && (
        <ButtonAdd
          otherStyles={{
            height: '32px',
            marginTop: '45px',
          }}
          handleClick={() => setSelectedVariation({ name: '' })}
        >
          Ajouter une déclinaison
        </ButtonAdd>
      )}
    </OptionCard>
  );
};

export default ProductVariation;
