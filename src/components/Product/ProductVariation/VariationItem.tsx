import { useState } from 'react';
import { Button, Input, List, Tag, Typography } from 'antd';
import { CheckButton, TrashButton } from 'components/Common';
import {
  CloseOutlined,
  EditOutlined,
  PlusCircleOutlined,
} from '@ant-design/icons';
import { Option, SubOption } from 'models';
import { useFocus, useMergeState } from 'hooks';
import { optionService } from 'services';
import { useAppSelector } from 'store';
import { selectOptionTypes } from 'store/slices/option.slices';
import { OPTION_TYPES } from 'utils/constant';
import { toast } from 'react-toastify';
import { pick } from 'utils';

const { Text } = Typography;
const { TextArea } = Input;

const VariationItem = ({
  productTypeId,
  variation,
  getListVariations,
}: {
  productTypeId: number;
  variation: Option;
  getListVariations: () => {};
}) => {
  const [focusRef, setFocus] = useFocus<HTMLButtonElement>();
  const [selectedVariation, setSelectedVariation] =
    useMergeState<Option | null>(null);
  const [newSubOption, setNewSubOption] = useMergeState<SubOption | null>(null);
  const [selectedSubOption, setSelectedSubOption] =
    useMergeState<SubOption | null>(null);
  const [isEditingSubOption, setIsEditingSubOption] = useState<boolean>(false);
  const [loading, setIsLoading] = useState<boolean>(false);
  const [isSubmitSubOption, setIsSubmitSubOption] = useState<boolean>(false);
  const [deletingIds, setDeletingIds] = useState<(number | undefined)[]>([]);
  const optionTypes = useAppSelector(selectOptionTypes);
  const variationOption = optionTypes.find(
    (i) => i.key === OPTION_TYPES.variation
  );

  const handleSelectSubOption = (item: SubOption) => {
    setFocus();
    setSelectedVariation(null, true);
    setIsEditingSubOption(false);
    setSelectedSubOption(item, true);
  };

  const handleSelectVariation = () => {
    setSelectedSubOption(null, true);
    setIsEditingSubOption(false);
    setSelectedVariation(variation, true);
  };

  const handleAddVariation = async () => {
    if (!selectedVariation?.name) return;
    try {
      setIsLoading(true);
      await optionService.updateOption(productTypeId, selectedVariation?.id, {
        ...pick(['name'], selectedVariation),
        optionTypeId: variationOption?.id,
      });
      setIsLoading(false);
      setSelectedVariation(null, true);
      getListVariations();
      toast.success('Succès');
    } catch (error) {
      setIsLoading(false);
      toast.error('Erreur');
    }
  };

  const handleDeleteSubOption = async (item: SubOption) => {
    try {
      setDeletingIds((prevState) => [...prevState, item.id as number]);
      await optionService.deleteSubOption(
        productTypeId,
        variation?.id as number,
        item.id as number
      );
      await getListVariations();
      if (item.id === selectedSubOption?.id) {
        setSelectedSubOption(null, true);
        setIsEditingSubOption(false);
      }
      setDeletingIds(deletingIds.filter((id) => id !== item.id));
    } catch (error) {
      setDeletingIds(deletingIds.filter((id) => id !== item.id));
      toast.error('Erreur');
    }
  };

  const handleSubmitSubOption = async () => {
    const subOptionData = isEditingSubOption ? selectedSubOption : newSubOption;
    if (!subOptionData?.name) return;
    try {
      setIsSubmitSubOption(true);
      if (subOptionData?.id) {
        await optionService.updateSubOption(
          productTypeId,
          variation.id,
          subOptionData
        );
      } else {
        await optionService.createSubOption(
          productTypeId,
          variation.id,
          subOptionData
        );
      }
      setNewSubOption(null, true);
      setIsSubmitSubOption(false);
      setIsEditingSubOption(false);
      getListVariations();
      setFocus();
    } catch (error) {
      setFocus();
      setIsSubmitSubOption(false);
      toast.error('Erreur');
    }
  };

  const handleDeleteOptionItem = async (
    productTypeId: number,
    optionId: number
  ) => {
    setDeletingIds((prevState) => [...prevState, optionId as number]);
    const deletedOption: Option = await optionService.deleteOption(
      productTypeId,
      optionId
    );
    getListVariations();
    setDeletingIds(deletingIds.filter((id) => id !== deletedOption.id));
  };

  const onBlurSubDescription = (
    e: React.FocusEvent<HTMLTextAreaElement, Element>
  ) => {
    if (!isEditingSubOption) {
      const relatedTarget = e.relatedTarget;
      if (
        relatedTarget?.className &&
        relatedTarget?.className.includes('ignore-blur')
      ) {
        return;
      }
      setSelectedSubOption(null, true);
      setIsEditingSubOption(false);
    }
  };

  const handleCancelEditSubOption = () => {
    setIsEditingSubOption(false);
    const currentSubOption = variation?.SubOptions?.find(
      (item) => item.id === selectedSubOption?.id
    );
    setSelectedSubOption(currentSubOption, true);
    setFocus();
  };
  return (
    <div className='product-variation__option-item'>
      <div className='product-variation__option-item-title'>
        <Text className='product-variation__option-item-title-label'>
          {selectedVariation?.id ? 'Titre de la declinaison' : variation.name}:
        </Text>
        {!selectedVariation && (
          <EditOutlined className='edit-icon' onClick={handleSelectVariation} />
        )}
        {selectedVariation ? (
          <>
            <Input
              placeholder='Input'
              value={selectedVariation?.name}
              onChange={(e) => setSelectedVariation({ name: e.target.value })}
              className='product-variation__option-item-content-input'
              onPressEnter={handleAddVariation}
            />
            <CheckButton
              className='check-button'
              onClick={handleAddVariation}
              loading={loading}
            />
            <CloseOutlined
              className='product-variation__option-item-cancel-icon cancel-button'
              onClick={() => setSelectedVariation(null, true)}
            />
          </>
        ) : (
          <>
            {isEditingSubOption ? (
              <Input
                placeholder='Input'
                className='product-variation__option-item-content-input'
                value={selectedSubOption?.name}
                onChange={(e) => setSelectedSubOption({ name: e.target.value })}
                onPressEnter={handleSubmitSubOption}
              />
            ) : (
              <Input
                placeholder='Input'
                className='product-variation__option-item-content-input'
                value={newSubOption?.name}
                onChange={(e) => setNewSubOption({ name: e.target.value })}
                onPressEnter={handleSubmitSubOption}
              />
            )}
            {!isEditingSubOption && (
              <>
                <Button
                  type='text'
                  size='small'
                  className='btn-add__ajouter-btn'
                  onClick={handleSubmitSubOption}
                  loading={isSubmitSubOption}
                >
                  <PlusCircleOutlined className='btn-add__add_icon' />
                  Ajouter
                </Button>
                <TrashButton
                  onClick={() =>
                    handleDeleteOptionItem(productTypeId, variation?.id)
                  }
                  className='product-variation__option-item-trash-icon trash-button'
                  loading={deletingIds.includes(variation?.id)}
                />
              </>
            )}
          </>
        )}
      </div>
      <div>
        <List
          itemLayout='vertical'
          dataSource={variation.SubOptions}
          renderItem={(subItem) =>
            subItem.isActive && (
              <Tag
                className={`product-variation__sub-option-tag ignore-blur ${
                  selectedSubOption?.id === subItem.id ? 'active' : ''
                }`}
                key={subItem.id}
                tabIndex={0}
                onClick={() => handleSelectSubOption(subItem)}
              >
                {subItem.name}
                <TrashButton
                  className='product-variation__sub-option-trash-icon trash-button ignore-blur'
                  onClick={() => handleDeleteSubOption(subItem)}
                  loading={deletingIds.includes(subItem.id)}
                />
              </Tag>
            )
          }
        />
      </div>
      {selectedSubOption?.id && (
        <div className='product-variation__sub-option-description-container'>
          <TextArea
            value={selectedSubOption?.description}
            onChange={(e) =>
              setSelectedSubOption({ description: e.target.value })
            }
            autoFocus
            ref={focusRef}
            onBlur={onBlurSubDescription}
            readOnly={!isEditingSubOption}
            size='large'
            autoSize={{ minRows: 2, maxRows: 20 }}
            placeholder='Description'
          />
          {isEditingSubOption ? (
            <>
              <CheckButton
                className='check-button ignore-blur'
                onClick={handleSubmitSubOption}
                loading={isSubmitSubOption}
              />
              <CloseOutlined
                className='product-variation__sub-option-description-cancel-icon cancel-button ignore-blur'
                onClick={handleCancelEditSubOption}
              />
            </>
          ) : (
            <EditOutlined
              className='edit-icon ignore-blur'
              onClick={() => {
                setIsEditingSubOption(true);
                setNewSubOption(null, true);
              }}
            />
          )}
        </div>
      )}
    </div>
  );
};

export default VariationItem;
