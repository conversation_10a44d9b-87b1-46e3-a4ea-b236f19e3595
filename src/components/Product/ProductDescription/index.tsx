import { useState, useRef, useEffect } from 'react';
import { CheckOutlined, CloseOutlined, DownOutlined, EditOutlined, LoadingOutlined } from '@ant-design/icons';
import { Button, Dropdown, Form, Menu, Space, Input, MenuProps } from 'antd';
import { CustomTextArea, OptionCard } from 'components/Common';
import { ConvertFromStringToJSX } from 'utils/converters';
import { useAppSelector } from 'store';
import { selectVariationOptions } from 'store/slices/option.slices';
import { ProductType } from 'models';
import { toast } from 'react-toastify';
import { productService } from 'services';
import { selectProductTypeReguls } from 'store/slices/product_type_regul.slices';
import { TextAreaRef } from 'antd/es/input/TextArea';
const { TextArea } = Input;

const ProductDescription = ({
  productTypeId,
  productType,
}: {
  productTypeId: number;
  productType: ProductType | null;
}) => {
  const textareaRef = useRef<TextAreaRef>(null);
  const [form] = Form.useForm();
  const [loading, setLoading] = useState<boolean>(false);
  const [isEditing, setIsEditing] = useState<boolean>(false);
  const [viewText, setViewText] = useState<JSX.Element | null>(<></>);
  const [editText, setEditText] = useState<string>('');
  const regulList = useAppSelector(selectProductTypeReguls);
  const variationList = useAppSelector(selectVariationOptions);

  const menuItems = [
    ...variationList.flatMap((variation) => [
      {
        id: variation.id,
        key: `${variation.id}-variation-nom`,
        label: `Déclinaisons ${variation.name} Nom`,
        title: `Déclinaisons ${variation.name} Nom`,
      },
      {
        id: variation.id,
        key: `${variation.id}-variation-description`,
        label: `Déclinaisons ${variation.name} Description`,
        title: `Déclinaisons ${variation.name} Description`,
      },
    ]),
    ...regulList.map((regul) => ({
      id: regul.id,
      key: `${regul.id}-regul-description`,
      label: `Regul ${regul.Product?.name} Description`,
      title: `Regul ${regul.Product?.name} Description`,
    })),
  ];

  const handleMenuClick: MenuProps['onClick'] = (e) => {
    const selectedItem = menuItems?.find((item) => item?.key === e.key) as {
      id?: number;
      key: string;
      title: string;
    };
    const suggestionKey = selectedItem.id;
    const suggestionTitle = selectedItem.title;
    const words = suggestionTitle.split(' ');
    const firstWord = words[0];
    const lastWord = words[words.length - 1];
    const convertedString = firstWord + '-' + suggestionKey + '-' + lastWord;
    insertConvertedString(convertedString);
  };

  const menu = <Menu onClick={handleMenuClick} items={menuItems} />;

  const getProductType = async (productTypeId: number) => {
    try {
      const result = await productService.findProductType(productTypeId);
      return result;
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  useEffect(() => {
    setEditText(productType?.description || '');
    const convertedStr = (
      <ConvertFromStringToJSX str={editText} menuList={menuItems} handleUpdateWordState={handleUpdateWordState} />
    );
    setViewText(convertedStr);
  }, []);

  useEffect(() => {
    const convertedStr = (
      <ConvertFromStringToJSX str={editText} menuList={menuItems} handleUpdateWordState={handleUpdateWordState} />
    );
    setViewText(convertedStr);
  }, [regulList, variationList]);

  const insertConvertedString = (convertedString: string) => {
    const textarea = textareaRef.current?.resizableTextArea?.textArea;
    if (!textarea) {
      return;
    }
    const { selectionStart, selectionEnd, value } = textarea;

    // Insert the converted string at the current cursor position
    const updatedValue =
      value.substring(0, selectionStart) +
      '[' +
      convertedString +
      '](suggestion)' +
      value.substring(selectionEnd) +
      ' ';

    // Update the textarea value and move the cursor after the inserted text
    textarea.value = updatedValue;
    setEditText(textarea.value);
    textarea.selectionStart = selectionStart + convertedString.length + '(suggestion)'.length + 3; // Adjust the selectionStart value to account for added characters
    textarea.selectionEnd = selectionStart + convertedString.length + '(suggestion)'.length + 3; // Adjust the selectionEnd value to account for added characters
    textarea.focus();
  };

  const handleUpdateProductTypeDescription = async (productTypeId: number, description: string | undefined) => {
    try {
      await productService.updateProductType(productTypeId, { description: description });
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleApplyEditProductDescription = async () => {
    try {
      setLoading(true);
      await handleUpdateProductTypeDescription(productTypeId, editText);
      const convertedStr = (
        <ConvertFromStringToJSX str={editText} menuList={menuItems} handleUpdateWordState={handleUpdateWordState} />
      );
      setViewText(convertedStr);
      setEditText(convertedStr.props.str);
      setLoading(false);
      setIsEditing(false);
    } catch (error) {
      console.log(error);
      setLoading(false);
    }
  };

  const handleUpdateWordState = (wordState: string) => {
    setEditText(wordState);
  };

  const handleEditProductDescription = () => {
    const convertedStr = (
      <ConvertFromStringToJSX str={editText} menuList={menuItems} handleUpdateWordState={handleUpdateWordState} />
    );
    setEditText(convertedStr.props.str);
    setIsEditing(true);
  };

  const handleCancelUpdate = async () => {
    const result = await getProductType(productTypeId);
    setEditText(result?.description || '');
    setIsEditing(false);
  };

  // const handleDeleteProductDescription = async () => {
  //   await handleUpdateProductTypeDescription(productTypeId, '');
  //   const convertedStr = (
  //     <ConvertFromStringToJSX
  //       str={' '}
  //       menuList={menuItems}
  //       handleUpdateWordState={handleUpdateWordState}
  //     />
  //   );
  //   setViewText(convertedStr);
  //   setEditText('');
  //   setIsEditing(false);
  // };

  return (
    <OptionCard
      title='Description Produit'
      otherStyles={{
        marginTop: '16px',
        marginBottom: '16px',
        borderRadius: '6px',
      }}
    >
      <Form form={form} className='product-description__form'>
        <Form.Item noStyle rules={[{ required: true }]} className='product-description__select-item'>
          <Dropdown overlay={menu} className='product-description__selection-dropdown' disabled={!isEditing}>
            <Button>
              <Space>
                Suggestions
                <DownOutlined />
              </Space>
            </Button>
          </Dropdown>
        </Form.Item>
        <Form.Item noStyle rules={[{ required: true }]} className='product-description__description-textarea-item'>
          {!isEditing && <CustomTextArea>{viewText}</CustomTextArea>}
          {isEditing && (
            <TextArea
              ref={textareaRef}
              size='large'
              rows={6}
              placeholder='Description'
              className='product-description__description-textarea'
              value={editText}
              onChange={(e) => setEditText(e.target.value)}
            />
          )}
          {!isEditing ? (
            <EditOutlined className='edit-icon' onClick={() => handleEditProductDescription()} />
          ) : (
            <>
              {loading ? (
                <LoadingOutlined className='product-description__description-loading-icon' />
              ) : (
                <CheckOutlined
                  className='product-description__description-apply-edit-icon check-button'
                  onClick={() => handleApplyEditProductDescription()}
                />
              )}
              <CloseOutlined
                className='product-description__description-cancel-icon cancel-button'
                onClick={handleCancelUpdate}
              />
            </>
          )}
        </Form.Item>
      </Form>
    </OptionCard>
  );
};

export default ProductDescription;
