/* eslint-disable @typescript-eslint/no-explicit-any */
import { useState, useEffect } from 'react';
import { Button, Input, Select, Tag, Typography } from 'antd';
import { OptionCard, TrashButton } from 'components/Common';
import { ProductType } from 'models';
import { CheckOutlined, CloseOutlined, PlusCircleOutlined, EditOutlined, LoadingOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';
import optionService from 'services/option.service';
import { OPTION_TYPES } from 'utils/constant';
import { Option } from 'models';
import { useMergeState } from 'hooks';
import { useAppSelector } from 'store';
import { selectOptionTypes } from 'store/slices/option.slices';
import { productService } from 'services';
import {
  selectFacturationType,
  selectFacturationTypeLoading,
  selectProductTypeInterventions,
  selectProductTypeInterventionsLoading,
  selectProductTypeUnit,
  selectProductTypeUnitLoading,
} from 'store/slices/product.slices';
const { Text } = Typography;

const ProductInformation = ({
  productType,
  productTypeId,
  handleUpdateProductType,
}: {
  productType: ProductType | null;
  productTypeId: number;
  handleUpdateProductType: (
    productTypeId: number,
    value: {
      titleOfProduct?: string;
      purchaseAccountingCode?: string;
      salesAccountingCode?: string;
      productTypeUnitId?: number | string;
      interventionId?: number | string;
      facturationTypeId?: number | string;
    },
  ) => void;
}) => {
  const [productTypeState, setProductTypeState] = useState<ProductType | null>();
  const [isTitleEditing, setIsTitleEditing] = useState<boolean>(false);
  const [isPurchaseAccountingCodeEditing, setIsPurchaseAccountingCodeEditing] = useState<boolean>(false);
  const [isSalesAccountingCodeEditing, setIsSalesAccountingCodeEditing] = useState<boolean>(false);
  const [timeSlot, setTimeSlot] = useMergeState<Option | any>({ name: '' });
  const [timeSlots, setTimeSlots] = useState<Option[]>([]);
  const [loading, setLoading] = useState<boolean>(false);
  const [titleLoading, setTitleLoading] = useState<boolean>(false);
  const [purchaseAccountingCodeLoading, setPurchaseAccountingCodeLoading] = useState<boolean>(false);
  const [salesAccountingCodeLoading, setSalesAccountingCodeLoading] = useState<boolean>(false);
  const [deletingIds, setDeletingIds] = useState<number[]>([]);
  const [titleOfProduct, setTitleOfProduct] = useState<string | undefined>(productType?.name);
  const [purchaseAccountingCode, setPurchaseAccountingCode] = useState<string | undefined>(
    productType?.purchaseAccountingCode,
  );
  const [salesAccountingCode, setSalesAccountingCode] = useState<string | undefined>(productType?.salesAccountingCode);
  const optionTypes = useAppSelector(selectOptionTypes);
  const productTypeUnits = useAppSelector(selectProductTypeUnit);
  const productTypeUnitsLoading = useAppSelector(selectProductTypeUnitLoading);
  const timeSlotOption = optionTypes.find((i) => i.key === OPTION_TYPES.timeSlot);
  const productTypeInterventions = useAppSelector(selectProductTypeInterventions);
  const productTypeinterventionsLoading = useAppSelector(selectProductTypeInterventionsLoading);
  const facturationTypes = useAppSelector(selectFacturationType);
  const facturationTypesLoading = useAppSelector(selectFacturationTypeLoading);

  const resetTimeSlot = () => {
    setTimeSlot({ name: '' }, true);
  };

  const getListTimeSlots = async () => {
    const res = await optionService.getAllOptions(productTypeId, {
      optionType: OPTION_TYPES.timeSlot,
      include: 'OptionType|SubOptions',
    });
    setTimeSlots(res);
  };

  useEffect(() => {
    setProductTypeState(productType);
    getListTimeSlots();
  }, []);

  const getProductType = async (productTypeId: number) => {
    const res = await productService.findProductType(productTypeId);
    setProductTypeState(res);
  };

  const handleTitleEdit = () => {
    setIsTitleEditing(true);
  };

  const handleCancelTitle = () => {
    setIsTitleEditing(false);
    setTitleOfProduct(productTypeState?.name);
  };

  const handlePurchaseAccountingCodeEdit = () => {
    setIsPurchaseAccountingCodeEditing(true);
  };

  const handleCancelPurchaseAccountingCode = () => {
    setIsPurchaseAccountingCodeEditing(false);
    setPurchaseAccountingCode(productTypeState?.purchaseAccountingCode);
  };

  const handleSalesAccountingCodeEdit = () => {
    setIsSalesAccountingCodeEditing(true);
  };

  const handleCancelSalesAccountingCode = () => {
    setIsSalesAccountingCodeEditing(false);
    setSalesAccountingCode(productTypeState?.salesAccountingCode);
  };

  const handleApplyProductType = async (
    productTypeId: number,
    value: {
      titleOfProduct?: string;
      purchaseAccountingCode?: string;
      salesAccountingCode?: string;
    },
    type: 'title' | 'purchaseAccountingCode' | 'salesAccountingCode',
  ) => {
    switch (type) {
      case 'title':
        if (!titleOfProduct) return;
        setTitleLoading(true);
        await handleUpdateProductType(productTypeId, value);
        await getProductType(productTypeId);
        setTitleLoading(false);
        setIsTitleEditing(false);
        break;
      case 'purchaseAccountingCode':
        if (!purchaseAccountingCode) return;
        setPurchaseAccountingCodeLoading(true);
        await handleUpdateProductType(productTypeId, value);
        await getProductType(productTypeId);
        setPurchaseAccountingCodeLoading(false);
        setIsPurchaseAccountingCodeEditing(false);
        break;
      case 'salesAccountingCode':
        if (!salesAccountingCode) return;
        setSalesAccountingCodeLoading(true);
        await handleUpdateProductType(productTypeId, value);
        await getProductType(productTypeId);
        setSalesAccountingCodeLoading(false);
        setIsSalesAccountingCodeEditing(false);
        break;
      default:
        break;
    }
  };

  const handleAddTimeslot = async () => {
    if (!timeSlot.name) return;
    try {
      setLoading(true);
      if (timeSlot.id) {
        await optionService.updateOption(productTypeId, timeSlot.id, {
          ...timeSlot,
          optionTypeId: timeSlotOption?.id,
        });
      } else {
        await optionService.createOption(productTypeId, {
          ...timeSlot,
          optionTypeId: timeSlotOption?.id,
        });
      }
      setLoading(false);
      resetTimeSlot();
      await getListTimeSlots();
    } catch (error) {
      console.log(error);
      setLoading(false);
      toast.error('Erreur');
    }
  };

  const handleDeleteTimeSlot = async (option: Option) => {
    if (!option.id) return;
    try {
      setDeletingIds((prevState) => [...prevState, option.id as number]);
      await optionService.deleteOption(productTypeId, option.id);
      resetTimeSlot();
      await getListTimeSlots();
      setDeletingIds(deletingIds.filter((id) => id !== option.id));
    } catch (error) {
      setDeletingIds(deletingIds.filter((id) => id !== option.id));
      toast.error((error as Error).message);
    }
  };
  const handleSelectUnit = async (value: number | string) => {
    try {
      if (!value) {
        return;
      }
      await handleUpdateProductType(productTypeId, {
        productTypeUnitId: value,
      });
    } catch (error) {
      console.log(error);
      toast.error((error as Error).message);
    }
  };
  const handleProductTypeIntervention = async (value: number | string) => {
    try {
      if (!value) {
        return;
      }
      await handleUpdateProductType(productTypeId, {
        interventionId: value,
      });
    } catch (error) {
      console.log(error);
      toast.error((error as Error).message);
    }
  };
  const handleFacturationType = async (value: number | string) => {
    try {
      if (!value) {
        return;
      }
      await handleUpdateProductType(productTypeId, {
        facturationTypeId: value,
      });
    } catch (error) {
      console.log(error);
      toast.error((error as Error).message);
    }
  };
  return (
    <OptionCard title='Informations Produit' otherStyles={{ marginTop: '30px' }}>
      <div className='product-information'>
        <div className='product-information__left-block'>
          <div className='product-information__title-of-product'>
            <Text className='product-information__title-of-product-label'>Titre de Produit:</Text>
            {!isTitleEditing ? (
              <>
                <Text className='product-information__title-of-product-content'>{productTypeState?.name}</Text>
                <EditOutlined className='edit-icon' onClick={handleTitleEdit} />
              </>
            ) : (
              <>
                <Input
                  placeholder='Input'
                  value={titleOfProduct}
                  className='product-information__title-of-product-content-input'
                  onChange={(e) => setTitleOfProduct(e.target.value)}
                  onPressEnter={() =>
                    handleApplyProductType(
                      productTypeId,
                      {
                        titleOfProduct,
                      },
                      'title',
                    )
                  }
                />
                {titleLoading ? (
                  <LoadingOutlined className='product-information__loading-icon' />
                ) : (
                  <CheckOutlined
                    className='check-button'
                    onClick={() =>
                      handleApplyProductType(
                        productTypeId,
                        {
                          titleOfProduct,
                        },
                        'title',
                      )
                    }
                  />
                )}
                <CloseOutlined className='product-information__cancel-icon cancel-button' onClick={handleCancelTitle} />
              </>
            )}
          </div>
          <div className='product-information__title-of-product'>
            <Text className='product-information__title-of-product-label'>Unite:</Text>
            <Select
              loading={productTypeUnitsLoading === 'pending'}
              defaultValue={productType?.productTypeUnitId}
              placeholder='Sélectionner'
              style={{ width: 200 }}
              showSearch
              onChange={(value) => {
                handleSelectUnit(value);
              }}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={productTypeUnits?.map((productTypeUnit) => ({
                value: productTypeUnit.id,
                label: productTypeUnit.name,
              }))}
            />
          </div>
          <div className='product-information__timeslot'>
            <Text className='product-information__timeslot-label'>Créneaux Horaires:</Text>
            <Input
              placeholder='Input'
              value={timeSlot.name}
              className='product-information__timeslot-content-input'
              onChange={(e) => setTimeSlot({ name: e.target.value })}
              onPressEnter={handleAddTimeslot}
            />
            {timeSlot?.id ? (
              <div style={{ paddingLeft: 10 }}>
                {loading ? (
                  <LoadingOutlined className='product-information__loading-icon' />
                ) : (
                  <CheckOutlined className='check-button' onClick={handleAddTimeslot} />
                )}
                <CloseOutlined
                  className='product-information__cancel-icon cancel-button'
                  onClick={() => resetTimeSlot()}
                />
              </div>
            ) : (
              <Button
                type='text'
                size='small'
                className='btn-add__ajouter-btn'
                loading={loading}
                onClick={handleAddTimeslot}
              >
                <PlusCircleOutlined className='btn-add__add_icon' />
                Ajouter
              </Button>
            )}
          </div>
          <div className='product-information__timeslot-list'>
            {timeSlots.map((option, index) => (
              <Tag
                key={option.id + ' ' + index}
                className={`product-information__timeslot-tag ${option.id === timeSlot.id ? 'active' : ''}`}
              >
                <span className='span-text' onClick={() => setTimeSlot(option, true)}>
                  {option.name}
                </span>
                <TrashButton
                  onClick={() => handleDeleteTimeSlot(option)}
                  className='product-information__timeslot-trash-icon trash-button'
                  loading={deletingIds.includes(option.id as number)}
                />
              </Tag>
            ))}
          </div>
          <div className='product-information__title-of-product'>
            <Text className='product-information__title-of-product-label'>Type de facturation:</Text>
            <Select
              loading={facturationTypesLoading === 'pending'}
              defaultValue={productType?.facturationTypeId}
              placeholder='Sélectionner'
              style={{ width: 200 }}
              showSearch
              onChange={(value) => {
                handleFacturationType(value);
              }}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={facturationTypes?.map((facturationType) => ({
                value: facturationType.id,
                label: facturationType.name,
              }))}
            />
          </div>
        </div>
        <div>
          <div className='product-information__purchase-accounting-code-block'>
            <Text className='product-information__purchase-accounting-code-label'>Code comptable achat:</Text>
            {!isPurchaseAccountingCodeEditing ? (
              <>
                <Text className='product-information__purchase-accounting-code-content'>{purchaseAccountingCode}</Text>
                <EditOutlined className='edit-icon' onClick={handlePurchaseAccountingCodeEdit} />
              </>
            ) : (
              <>
                <Input
                  placeholder='Input'
                  value={purchaseAccountingCode}
                  className='product-information__purchase-accounting-code-content-input'
                  onChange={(e) => setPurchaseAccountingCode(e.target.value)}
                  onPressEnter={() =>
                    handleApplyProductType(
                      productTypeId,
                      {
                        purchaseAccountingCode,
                      },
                      'purchaseAccountingCode',
                    )
                  }
                />
                {purchaseAccountingCodeLoading ? (
                  <LoadingOutlined className='product-information__loading-icon' />
                ) : (
                  <CheckOutlined
                    className='check-button'
                    onClick={() =>
                      handleApplyProductType(
                        productTypeId,
                        {
                          purchaseAccountingCode,
                        },
                        'purchaseAccountingCode',
                      )
                    }
                  />
                )}
                <CloseOutlined
                  className='product-information__cancel-icon cancel-button'
                  onClick={handleCancelPurchaseAccountingCode}
                />
              </>
            )}
          </div>
          <div className='product-information__sales-accounting-code-block'>
            <Text className='product-information__sales-accounting-code-label'>Code comptable vente:</Text>
            {!isSalesAccountingCodeEditing ? (
              <>
                <Text className='product-information__sales-accounting-code-content'>{salesAccountingCode}</Text>
                <EditOutlined className='edit-icon' onClick={handleSalesAccountingCodeEdit} />
              </>
            ) : (
              <>
                <Input
                  placeholder='Input'
                  value={salesAccountingCode}
                  className='product-information__sales-accounting-code-content-input'
                  onChange={(e) => setSalesAccountingCode(e.target.value)}
                  onPressEnter={() =>
                    handleApplyProductType(
                      productTypeId,
                      {
                        salesAccountingCode,
                      },
                      'salesAccountingCode',
                    )
                  }
                />
                {salesAccountingCodeLoading ? (
                  <LoadingOutlined className='product-information__loading-icon' />
                ) : (
                  <CheckOutlined
                    className='check-button'
                    onClick={() =>
                      handleApplyProductType(
                        productTypeId,
                        {
                          salesAccountingCode,
                        },
                        'salesAccountingCode',
                      )
                    }
                  />
                )}
                <CloseOutlined
                  className='product-information__cancel-icon cancel-button'
                  onClick={handleCancelSalesAccountingCode}
                />
              </>
            )}
          </div>
          <div className='product-information__title-of-product'>
            <Text className='product-information__title-of-product-label'>Intervention:</Text>
            <Select
              loading={productTypeinterventionsLoading === 'pending'}
              defaultValue={productType?.interventionId}
              placeholder='Sélectionner'
              style={{ width: 200 }}
              showSearch
              onChange={(value) => {
                handleProductTypeIntervention(value);
              }}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={productTypeInterventions?.map((productTypeIntervention) => ({
                value: productTypeIntervention.id,
                label: productTypeIntervention.name,
              }))}
            />
          </div>
        </div>
      </div>
    </OptionCard>
  );
};

export default ProductInformation;
