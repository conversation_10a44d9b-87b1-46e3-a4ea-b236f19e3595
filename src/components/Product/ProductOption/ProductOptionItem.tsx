import { CloseOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Checkbox, Input, InputRef, List, Tag, Typography } from 'antd';
import { ButtonEdit, CheckButton, TrashButton } from 'components/Common';
import { Option, OptionType, ProductType, ReferenceType, SubOption } from 'models';
import ProductSubOptionReferenceType from './ProductSubOptionReferenceType';
import { useFocus, useMergeState } from 'hooks';
import { toast } from 'react-toastify';
import { useState } from 'react';
import { optionService } from 'services';
import { pick } from 'utils';
const { Text } = Typography;

interface ProductOptionItemProps {
  productType: ProductType;
  optionOption?: OptionType;
  option: Option;
  fetchListOptions: () => void;
  referenceTypeList: ReferenceType[];
  optionList: Option[];
}

export default function ProductOptionItem(props: ProductOptionItemProps) {
  const { productType, optionOption, option, referenceTypeList, fetchListOptions, optionList } = props;
  const [focusRef, setFocus] = useFocus<InputRef>();
  const [selectedOption, setSelectedOption] = useMergeState<Option | null>(null);
  const [selectedSubOption, setSelectedSubOption] = useMergeState<SubOption | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [deletingIds, setDeletingIds] = useState<number[]>([]);
  const [deletingReferenceTypesIds, setDeletingReferenceTypesIds] = useState<
    { subOptionId: number; referenceTypeId: number }[]
  >([]);

  const handleUpdateOption = async () => {
    if (!selectedOption?.id || !selectedOption?.name) return;
    try {
      setLoading(true);
      await optionService.updateOption(productType.id, selectedOption?.id, {
        ...pick(['name'], selectedOption),
        optionTypeId: optionOption?.id,
      });
      setLoading(false);
      setSelectedOption(null, true);
      fetchListOptions();
      toast.success('Succès');
    } catch {
      setLoading(false);
      toast.error('Erreur');
    }
  };
  const handleUpdateIsPrestation = async (value: boolean) => {
    try {
      setLoading(true);
      const unPrestationOptionIds = optionList
        .filter((i) => i.id !== option?.id && i.isPrestationOption)
        .map((i) => i.id);
      await optionService
        .updateOption(productType.id, option?.id, {
          optionTypeId: optionOption?.id,
          isPrestationOption: value,
          unPrestationOptionIds,
        })
        // eslint-disable-next-line
        .then((rs: any) => {
          if (rs) {
            fetchListOptions();
            toast.success('Succès');
          }
        });
      setLoading(false);
    } catch {
      setLoading(false);
      toast.error('Erreur');
    }
  };
  const handleSelectOption = () => {
    setSelectedSubOption(null, true);
    setSelectedOption(option, true);
  };

  const handleDeselectOption = () => {
    setSelectedOption(null, true);
  };

  const handleSelectSubOption = (item: SubOption) => {
    setSelectedOption(null, true);
    setFocus();
    setSelectedSubOption(item, true);
  };

  const handleDeselectSubOption = () => {
    setSelectedSubOption(null, true);
  };

  const onBlurSubOption = (e: React.FocusEvent<HTMLInputElement, Element>) => {
    const relatedTarget = e.relatedTarget;
    if (relatedTarget && relatedTarget?.className && relatedTarget?.className.includes('ignore-blur')) {
      return;
    }
    if (selectedSubOption?.id) {
      setSelectedSubOption(null, true);
    }
    setSelectedSubOption(null, true);
  };

  const handleAddOrEditSubOption = async (value?: string) => {
    if (!value || !option?.id) return;
    try {
      setLoading(true);
      const submitData = {
        ...(selectedSubOption?.id ? { id: selectedSubOption?.id } : {}),
        name: value,
        ...(option?.SubOptions && option?.SubOptions?.length > 0 ? {} : { setByDefault: true }),
      } as SubOption;
      if (!selectedSubOption?.id) {
        await optionService.createSubOption(productType.id, option.id, submitData);
      } else {
        await optionService.updateSubOption(productType.id, option.id, submitData);
      }
      setLoading(false);
      handleDeselectSubOption();
      await fetchListOptions();
    } catch {
      setLoading(false);
      toast.error('Erreur');
    }
  };

  const handleDeleteSubOption = async (item: SubOption) => {
    try {
      setDeletingIds((prevState) => [...prevState, item.id as number]);
      await optionService.deleteSubOption(productType.id, option.id as number, item.id as number);
      await fetchListOptions();
      if (item.id === selectedSubOption?.id) {
        setSelectedSubOption(null, true);
      }
      setDeletingIds(deletingIds.filter((id) => id !== item.id));
    } catch {
      setDeletingIds(deletingIds.filter((id) => id !== item.id));
      toast.error('Erreur');
    }
  };

  const handleSwitchDefault = async (optionId: number, subOption: SubOption, defaultValue: boolean) => {
    try {
      setSelectedSubOption({
        ...subOption,
        setByDefault: !defaultValue,
      });
      await optionService.updateSubOption(productType.id, optionId, {
        ...subOption,
        setByDefault: !defaultValue,
      });
      await fetchListOptions();
    } catch {
      toast.error('Erreur');
    }
  };

  const handleSwitchShowOnClientViewDefault = async (
    optionId: number,
    subOption: SubOption,
    showOnClientView: boolean,
  ) => {
    try {
      setSelectedSubOption({
        ...subOption,
        showOnClientView: !showOnClientView,
      });
      await optionService.updateSubOption(productType.id, optionId, {
        ...subOption,
        showOnClientView: !showOnClientView,
      });
      await fetchListOptions();
    } catch {
      toast.error('Erreur');
    }
  };

  const handleSelectReferenceTypes = async (
    optionId: number,
    subOptionId: number,
    referenceTypeId: number,
    action?: 'select' | 'deselect',
  ) => {
    try {
      const updatedSubOption = { ...selectedSubOption };
      if (action === 'select') {
        updatedSubOption.ReferenceTypes = [...(updatedSubOption.ReferenceTypes || []), { id: referenceTypeId }];
        setSelectedSubOption(updatedSubOption);
        await optionService.addSubOptionReferenceType(productType.id, optionId, subOptionId, { referenceTypeId });
      } else {
        updatedSubOption.ReferenceTypes = updatedSubOption?.ReferenceTypes?.filter((rt) => rt.id !== referenceTypeId);
        setSelectedSubOption(updatedSubOption);
        await optionService.deleteSubOptionReferenceType(productType.id, optionId, subOptionId, referenceTypeId);
      }
      await fetchListOptions();
    } catch {
      toast.error('Erreur');
    }
  };

  const handleRemoveReferenceTypes = async (optionId: number, subOptionId: number, referenceTypeId: number) => {
    try {
      setDeletingReferenceTypesIds((prevState) => [...prevState, { subOptionId, referenceTypeId }]);
      const updatedSubOption = { ...selectedSubOption };
      await optionService.deleteSubOptionReferenceType(productType.id, optionId, subOptionId, referenceTypeId);
      updatedSubOption.ReferenceTypes = updatedSubOption?.ReferenceTypes?.filter((rt) => rt.id !== referenceTypeId);
      setSelectedSubOption(updatedSubOption);
      setDeletingReferenceTypesIds(
        deletingReferenceTypesIds.filter(
          (item) => item.subOptionId !== subOptionId && item.referenceTypeId !== referenceTypeId,
        ),
      );
      await fetchListOptions();
    } catch {
      toast.error('Erreur');
    }
  };

  const handleDeleteOptionItem = async (productTypeId: number, optionId?: number) => {
    if (!optionId) return;
    setDeletingIds((prevState) => [...prevState, optionId as number]);
    const deletedOption: Option = await optionService.deleteOption(productTypeId, optionId);
    fetchListOptions();
    setDeletingIds(deletingIds.filter((id) => id !== deletedOption.id));
  };

  return (
    <>
      <div className='option__option_item'>
        <Text className='option__option-title-label'>{selectedOption?.id ? "Type de l'option" : option.name}:</Text>
        {!selectedOption && <EditOutlined className='edit-icon mr-4 ignore-blur' onClick={handleSelectOption} />}
        <Input
          placeholder='Input'
          value={!selectedOption ? selectedSubOption?.name : selectedOption?.name}
          ref={focusRef}
          className='option__option-title-content-input'
          onChange={(e) =>
            !selectedOption
              ? setSelectedSubOption({
                  ...selectedSubOption,
                  name: e.target.value,
                })
              : setSelectedOption({ ...selectedOption, name: e.target.value })
          }
          onPressEnter={() =>
            !selectedOption ? handleAddOrEditSubOption(selectedSubOption?.name) : handleUpdateOption()
          }
          onBlur={(e) => onBlurSubOption(e)}
        />
        {!selectedOption?.id ? (
          selectedSubOption?.id ? (
            <ButtonEdit
              loading={loading}
              isEditing={true}
              onEdit={() => {}}
              onSubmit={() => handleAddOrEditSubOption(selectedSubOption?.name)}
              onCancel={() => handleDeselectSubOption()}
              className='ignore-blur'
            />
          ) : (
            <>
              <Button
                type='text'
                size='small'
                loading={loading}
                className='btn-add__ajouter-btn ignore-blur'
                onClick={() => handleAddOrEditSubOption(selectedSubOption?.name)}
              >
                <PlusCircleOutlined className='btn-add__add_icon' />
                Ajouter
              </Button>
              <Checkbox
                className='ml-4 mr-1'
                onChange={(e) => {
                  handleUpdateIsPrestation(e.target.checked);
                }}
                checked={option?.isPrestationOption}
              >
                Prestation
              </Checkbox>
              <TrashButton
                onClick={() => handleDeleteOptionItem(productType.id, option.id)}
                className='product-variation__option-item-trash-icon trash-button ml-2'
                loading={deletingIds.includes(option.id)}
              />
            </>
          )
        ) : (
          <>
            <CheckButton className='check-button' onClick={handleUpdateOption} loading={loading} />
            <CloseOutlined
              className='product-variation__option-item-cancel-icon cancel-button'
              onClick={handleDeselectOption}
            />
          </>
        )}
      </div>
      <div className={`option__sub-option ${!selectedSubOption?.id && 'mb-5'}`}>
        <List
          itemLayout='horizontal'
          dataSource={option?.SubOptions}
          renderItem={(subOption) =>
            subOption.isActive && (
              <Tag
                className={`option__sub-option-tag ignore-blur mb-4 ${
                  selectedSubOption?.id === subOption.id ? 'active' : ''
                }`}
                key={subOption.id}
                tabIndex={0}
                onClick={() => handleSelectSubOption(subOption)}
              >
                {subOption.name}
                <TrashButton
                  className='option__sub-option-trash-icon trash-button ignore-blur'
                  onClick={() => handleDeleteSubOption(subOption)}
                  loading={deletingIds.includes(subOption.id)}
                />
              </Tag>
            )
          }
        />
      </div>
      {selectedSubOption?.id && (
        <div className='option__option-attributes'>
          <ProductSubOptionReferenceType
            productType={productType}
            subOption={selectedSubOption}
            referenceTypeList={referenceTypeList}
            deletingReferenceTypesIds={deletingReferenceTypesIds}
            onSwitchDefault={handleSwitchDefault}
            onSwitchShowOnClientViewDefault={handleSwitchShowOnClientViewDefault}
            onSelectReferenceTypes={handleSelectReferenceTypes}
            onRemoveReferenceTypes={handleRemoveReferenceTypes}
          />
        </div>
      )}
    </>
  );
}
