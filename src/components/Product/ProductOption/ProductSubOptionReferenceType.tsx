import React, { useEffect } from 'react';
import { Select, Switch, Tag } from 'antd';
import { TrashButton } from 'components/Common';
import { ProductType, ReferenceType, SubOption } from 'models';
const { Option: AntdOption } = Select;

interface ProductSubOptionReferenceTypeProps {
  productType: ProductType;
  subOption: SubOption;
  referenceTypeList: ReferenceType[];
  deletingReferenceTypesIds: { subOptionId: number; referenceTypeId: number }[];
  onSwitchDefault: (optionId: number, subOption: SubOption, defaultValue: boolean) => void;
  onSwitchShowOnClientViewDefault: (optionId: number, subOption: SubOption, defaultValue: boolean) => void;
  onSelectReferenceTypes: (
    optionId: number,
    subOptionId: number,
    referenceTypeId: number,
    action?: 'select' | 'deselect',
  ) => void;
  onRemoveReferenceTypes: (optionId: number, subOptionId: number, referenceTypeId: number) => void;
}

const ProductSubOptionReferenceType: React.FC<ProductSubOptionReferenceTypeProps> = ({
  subOption,
  referenceTypeList,
  deletingReferenceTypesIds,
  onSwitchDefault,
  onSwitchShowOnClientViewDefault,
  onSelectReferenceTypes,
  onRemoveReferenceTypes,
}) => {
  useEffect(() => {
    const selectInput = document.querySelector('.ignore-blur .ant-select-selector input');
    if (selectInput) {
      selectInput.className += ' ignore-blur';
    }
  }, []);

  const handleSwitchChange = () =>
    onSwitchDefault(subOption.optionId as number, subOption, subOption.setByDefault as boolean);

  const handleSwitchShowOnClientViewChange = () =>
    onSwitchShowOnClientViewDefault(subOption.optionId as number, subOption, subOption.showOnClientView as boolean);

  const handleSelect = (value: number, action: 'select' | 'deselect') =>
    onSelectReferenceTypes(subOption.optionId as number, subOption.id as number, value, action);

  // eslint-disable-next-line
  const renderTag = (props: any) => {
    const isLoading = deletingReferenceTypesIds.some(
      (drt) => drt.subOptionId === subOption.id && drt.referenceTypeId === props.value,
    );
    return (
      <Tag
        style={{
          background: 'rgba(0, 0, 0, 0.04)',
          border: '1px solid rgba(0, 0, 0, 0.06)',
          borderRadius: 4,
          display: 'inline-block',
          padding: '2px 8px',
          marginTop: '2px',
          marginBottom: '2px',
          marginRight: '2px',
          cursor: 'pointer',
        }}
        className='ignore-blur'
        onClick={(e) => e.stopPropagation()}
        onMouseDown={(event) => {
          event.preventDefault();
          event.stopPropagation();
        }}
      >
        {props.label}
        <TrashButton
          onClick={() => onRemoveReferenceTypes(subOption.optionId as number, subOption.id as number, props.value)}
          className='option__reference-types-trash-icon trash-button ignore-blur'
          loading={isLoading}
        />
      </Tag>
    );
  };

  return (
    <div className='option__option-attributes-default-block'>
      <div className='d-flex align-items-center mb-3'>
        <Switch
          size='small'
          className='option__option-attributes-switch-show-client-view mr-2 ignore-blur'
          checked={subOption.showOnClientView}
          onChange={handleSwitchShowOnClientViewChange}
        />
        <span className='option__option-attributes-label mr-2'>Afficher sur les devis</span>
      </div>

      <div className='d-flex align-items-center mb-3'>
        <Switch
          size='small'
          className='option__option-attributes-switch-default mr-2 ignore-blur'
          checked={subOption.setByDefault}
          onChange={handleSwitchChange}
        />
        <span className='option__option-attributes-label mr-2'>Option par défaut</span>
      </div>

      <div className='d-flex align-items-center'>
        <Select
          mode='multiple'
          className='ignore-blur'
          showSearch={false}
          style={{ width: 320 }}
          placeholder='Choisir une connexion'
          value={subOption.ReferenceTypes?.map((rt) => rt?.id)}
          onSelect={(value) => handleSelect(value, 'select')}
          onDeselect={(value) => handleSelect(value, 'deselect')}
          tagRender={renderTag}
        >
          {referenceTypeList.map((rt) => (
            <AntdOption key={rt.id} value={rt.id}>
              {rt.name}
            </AntdOption>
          ))}
        </Select>
      </div>
    </div>
  );
};

export default ProductSubOptionReferenceType;
