import { useEffect, useState } from 'react';
import { ButtonAdd, OptionCard } from 'components/Common';
import { Option, ProductType, ReferenceType } from 'models';
import { useAppDispatch, useAppSelector } from 'store';
import { selectOptionTypes } from 'store/slices/option.slices';
import { OPTION_TYPES } from 'utils/constant';
import { toast } from 'react-toastify';
import { optionService } from 'services';
import { fetchOptions, fetchReferenceTypes, selectOptions, selectReferenceTypes } from 'store/slices/product.slices';
import ProductOptionItem from './ProductOptionItem';
import { Button, Input, List, Typography } from 'antd';
import { useMergeState } from 'hooks';
import { CloseOutlined, PlusCircleOutlined } from '@ant-design/icons';
const { Text } = Typography;

const ProductOption = ({ productTypeId, productType }: { productTypeId: number; productType: ProductType }) => {
  const dispatch = useAppDispatch();
  const [isCreating, setIsCreating] = useState<boolean>(false);
  const [createdOption, setCreatedOption] = useMergeState<Option | null>(null);
  const optionList = useAppSelector(selectOptions);
  const optionTypes = useAppSelector(selectOptionTypes);
  const referenceTypes: ReferenceType[] | null = useAppSelector(selectReferenceTypes);
  const optionOption = optionTypes.find((i) => i.key === OPTION_TYPES.option);

  const getListOptions = async () => {
    await dispatch(
      fetchOptions({
        productTypeId: productTypeId,
        optionType: OPTION_TYPES.option,
      }),
    )
      .unwrap()
      .catch((error) => {
        console.log(error);
        toast.error('Erreur');
      });
  };

  const getReferenceTypes = async () => {
    await dispatch(await fetchReferenceTypes({}))
      .unwrap()
      .catch((error) => {
        console.log(error);
        toast.error('Erreur');
      });
  };

  const fetchAllData = async () => {
    await Promise.all([getListOptions(), getReferenceTypes()]);
  };

  useEffect(() => {
    fetchAllData();
  }, []);

  const resetCreatedOption = () => {
    setCreatedOption(null, true);
  };

  const handleAddOption = async () => {
    if (!createdOption?.name) return;
    try {
      setIsCreating(true);
      await optionService.createOption(productTypeId, {
        ...createdOption,
        optionTypeId: optionOption?.id,
      });
      setIsCreating(false);
      resetCreatedOption();
      toast.success('Succès');
      await getListOptions();
    } catch (error) {
      console.log(error);
      setIsCreating(false);
      toast.error('Erreur');
    }
  };

  return (
    <OptionCard title='Options' otherStyles={{ marginTop: '16px' }}>
      <List
        itemLayout='vertical'
        dataSource={optionList}
        style={{ width: '100%' }}
        renderItem={(item) => (
          <ProductOptionItem
            productType={productType}
            optionOption={optionOption}
            option={item}
            fetchListOptions={getListOptions}
            referenceTypeList={referenceTypes}
            optionList={optionList}
          />
        )}
      />
      {createdOption && (
        <div className='product-variation__title'>
          <Text className='product-variation__title-label'>
            {/* eslint-disable-next-line react/no-unescaped-entities */}
            Type de l'option:
          </Text>
          <Input
            placeholder='Input'
            value={createdOption?.name}
            className='product-variation__title-content-input'
            onChange={(e) => setCreatedOption({ name: e.target.value })}
            onPressEnter={handleAddOption}
          />
          <Button
            type='text'
            size='small'
            loading={isCreating}
            className='btn-add__ajouter-btn'
            onClick={handleAddOption}
          >
            <PlusCircleOutlined className='btn-add__add_icon' />
            Ajouter
          </Button>
          <CloseOutlined className='product-regul__close-icon cancel-button' onClick={() => resetCreatedOption()} />
        </div>
      )}
      {!createdOption && (
        <ButtonAdd
          otherStyles={{
            height: '32px',
            marginTop: '45px',
          }}
          handleClick={() => setCreatedOption({ name: '' })}
        >
          Ajouter une option
        </ButtonAdd>
      )}
    </OptionCard>
  );
};

export default ProductOption;
