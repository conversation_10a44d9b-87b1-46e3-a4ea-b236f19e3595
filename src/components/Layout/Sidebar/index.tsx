import { Layout, Menu, Card, Image, Button } from 'antd';
import TruckIcon from '../../../components/Icons/TruckIcon';
import LocationIcon from '../../../components/Icons/LocationIcon';
import PaperIcon from '../../Icons/PaperIcon';
import EuroOutlined from 'components/Icons/EuroOutlined';
import Logo from '../../../assets/images/main-logo-ecodrop.png';
import { NavLink, useLocation } from 'react-router-dom';
import moment from 'moment';
import useAuthContext from '../../../store/auth-context';
import ProviderIcon from 'components/Icons/ProviderIcon';
import { DownOutlined, UpOutlined } from '@ant-design/icons';
import { useState } from 'react';
import useGlobalContext from 'store/global-context';
import { envService } from 'services';
import { HiddenFeatures } from 'services/env.service';
import { Permission } from 'components/Common/Permission';
import { Permissions } from 'services/permission.service';
import CircularArrowsIcon from '../../Icons/CircularArrowsIcon';
const { Sider } = Layout;

const Sidebar = () => {
  const { onSignOut, landingPage } = useAuthContext();
  const location = useLocation();
  const [isOpenProviders, setIsOpenProviders] = useState<boolean>(false);
  const [isOpenLogistique, setIsOpenLogistique] = useState<boolean>(false);
  const [isOpenSearch, setIsOpenSearch] = useState<boolean>(false);
  const { globalState } = useGlobalContext();

  const handleOpenProviders = (value: boolean) => {
    setIsOpenProviders(value);
  };
  const handleOpenLogistique = (value: boolean) => {
    setIsOpenLogistique(value);
  };

  const handleOpenSearch = (value: boolean) => {
    setIsOpenSearch(value);
  };

  return (
    <Sider width={globalState?.isDevisPath ? 95 : 264} theme='dark' className='main-sidebar'>
      <Card bordered={false} className='main-sidebar__main-logo'>
        <NavLink to={landingPage?.path || '/'}>
          <Image
            width={globalState?.isDevisPath ? 75 : 193}
            height={globalState?.isDevisPath ? 45 : 45}
            preview={false}
            src={Logo}
            className='main-sidebar__logo-image'
          />
        </NavLink>
      </Card>
      {!globalState?.isDevisPath && (
        <Menu
          mode='vertical'
          theme='dark'
          defaultSelectedKeys={[location?.state?.pathname || location.pathname]}
          selectedKeys={[location?.state?.pathname || location.pathname]}
          className='main-sidebar__main-menu'
        >
          <Permission permissions={Permissions.DEVIS}>
            <Menu.Item
              key='/devis'
              icon={<PaperIcon className='main-sidebar__icon' />}
              disabled={envService.isFeatureHidden(HiddenFeatures.DEVIS)}
              className={`main-sidebar__item main-sidebar__region-item ${
                location.pathname === '/devis' ? 'ant-menu-item-selected' : ''
              }`}
            >
              {envService.isFeatureHidden(HiddenFeatures.DEVIS) ? (
                <span>Devis</span>
              ) : (
                <NavLink to='/quotation' className='main-sidebar__navigation'>
                  Devis
                </NavLink>
              )}
            </Menu.Item>
          </Permission>
          <Permission permissions={Permissions.PRODUITS}>
            <Menu.Item
              key='/products'
              icon={<TruckIcon className='main-sidebar__icon' />}
              className={`main-sidebar__item main-sidebar__region-item ${
                location.pathname === '/products' ? 'ant-menu-item-selected' : ''
              }`}
            >
              <NavLink to='/products' className='main-sidebar__navigation'>
                Produits
              </NavLink>
            </Menu.Item>
          </Permission>
          <Permission permissions={Permissions.REGIONS}>
            <Menu.Item
              key='/regions'
              icon={<LocationIcon className='main-sidebar__icon' />}
              className={`main-sidebar__item main-sidebar__region-item ${
                location.pathname === '/regions' || location.pathname.startsWith('/regions')
                  ? 'ant-menu-item-selected'
                  : ''
              }`}
            >
              <NavLink to='/regions' className='main-sidebar__navigation'>
                Regions
              </NavLink>
            </Menu.Item>
          </Permission>
          {!envService.isFeatureHidden(HiddenFeatures.MULTIFLOW) && (
            <Permission permissions={Permissions.LOGISTIQUE}>
              <Menu.Item
                key='/logistique'
                icon={<CircularArrowsIcon className='main-sidebar__dropdown-icon' />}
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname.startsWith('/logistique') ? 'ant-menu-item-selected' : ''
                }`}
                onClick={() => handleOpenLogistique(!isOpenLogistique)}
              >
                <span className='main-sidebar__navigation'>Logistique</span>
                <div className='main-sidebar__arrow-icon'>
                  {isOpenLogistique ? (
                    <UpOutlined className='main-sidebar__arrow-icon-up' />
                  ) : (
                    <DownOutlined className='main-sidebar__arrow-icon-down' />
                  )}
                </div>
              </Menu.Item>
            </Permission>
          )}
          {!envService.isFeatureHidden(HiddenFeatures.MULTIFLOW) && (
            <div
              className={`main-sidebar__submenu ${isOpenLogistique ? 'main-sidebar__submenu-open' : ''}`}
              key={'main-siderbar__submenu-logistique'}
            >
              <Permission permissions={Permissions.LOGISTIQUE}>
                <Menu.Item
                  key='/logistique/nouvelles-commandes'
                  className={`main-sidebar__item main-sidebar__region-item ${
                    location.pathname === '/logistique/nouvelles-commandes' ||
                    location.pathname.startsWith('/logistique/nouvelles-commandes')
                      ? 'ant-menu-sub-item-selected'
                      : ''
                  }`}
                >
                  <NavLink to='/logistique/nouvelles-commandes' className='main-sidebar__sub-navigation'>
                    Nouvelles commandes
                  </NavLink>
                </Menu.Item>
              </Permission>
              <Permission permissions={[Permissions.PRESTATAIRE_BENNEUR, Permissions.PRESTATAIRE_CAMION, Permissions.PRESTATAIRE_PUP]}>
                <Menu.Item
                  key='/logistique/factures-fournisseurs'
                  className={`main-sidebar__item main-sidebar__region-item ${
                    location.pathname === '/logistique/factures-fournisseurs' ||
                    location.pathname.startsWith('/logistique/factures-fournisseurs')
                      ? 'ant-menu-sub-item-selected'
                      : ''
                  }`}
                >
                  <NavLink to='/logistique/factures-fournisseurs' className='main-sidebar__sub-navigation'>
                    Factures fournisseurs
                  </NavLink>
                </Menu.Item>
              </Permission>
            </div>
          )}
          <Permission
            permissions={[Permissions.PRESTATAIRE_BENNEUR, Permissions.PRESTATAIRE_CAMION, Permissions.PRESTATAIRE_PUP]}
          >
            <Menu.Item
              key='/prestataires'
              icon={<ProviderIcon className='main-sidebar__dropdown-icon' />}
              className={`main-sidebar__item main-sidebar__region-item ${
                location.pathname.startsWith('/prestataires') ? 'ant-menu-item-selected' : ''
              }`}
              onClick={() => handleOpenProviders(isOpenProviders ? false : true)}
            >
              <span className='main-sidebar__navigation'>Prestataires</span>
              <div className='main-sidebar__arrow-icon'>
                {isOpenProviders ? (
                  <UpOutlined className='main-sidebar__arrow-icon-up' />
                ) : (
                  <DownOutlined className='main-sidebar__arrow-icon-down' />
                )}
              </div>
            </Menu.Item>
          </Permission>
          <div
            className={`main-sidebar__submenu ${isOpenProviders ? 'main-sidebar__submenu-open' : ''}`}
            key={'main-siderbar__submenu'}
          >
            <Permission permissions={Permissions.PRESTATAIRE_BENNEUR}>
              <Menu.Item
                key='/prestataires/benneur'
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname === '/prestataires/benneur' || location.pathname.startsWith('/prestataires/benneur')
                    ? 'ant-menu-sub-item-selected'
                    : ''
                }`}
              >
                <NavLink to='/prestataires/benneur' className='main-sidebar__sub-navigation'>
                  Benneurs
                </NavLink>
              </Menu.Item>
            </Permission>
            <Permission permissions={Permissions.PRESTATAIRE_CAMION}>
              <Menu.Item
                key='/prestataires/camionneur'
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname === '/prestataires/camionneur' ||
                  location.pathname.startsWith('/prestataires/camionneur')
                    ? 'ant-menu-sub-item-selected'
                    : ''
                }`}
              >
                <NavLink to='/prestataires/camionneur' className='main-sidebar__sub-navigation'>
                  Camionneurs
                </NavLink>
              </Menu.Item>
            </Permission>
            <Permission permissions={Permissions.PRESTATAIRE_PUP}>
              <Menu.Item
                key='/prestataires/pup'
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname === '/prestataires/pup' || location.pathname.startsWith('/prestataires/pup')
                    ? 'ant-menu-sub-item-selected'
                    : ''
                }`}
              >
                <NavLink to='/prestataires/pup' className='main-sidebar__sub-navigation'>
                  PUP
                </NavLink>
              </Menu.Item>
            </Permission>
          </div>
          <Permission permissions={[Permissions.PRESTATAIRE_BENNEUR, Permissions.DEVIS]}>
            <Menu.Item
              key='/search'
              icon={<EuroOutlined className='main-sidebar__icon' />}
              className={`main-sidebar__item main-sidebar__region-item ${
                location.pathname.startsWith('/search') ? 'ant-menu-item-selected' : ''
              }`}
              onClick={() => handleOpenSearch(isOpenSearch ? false : true)}
            >
              <span className='main-sidebar__navigation'>Recherche de prix</span>
              <div className='main-sidebar__arrow-icon'>
                {isOpenSearch ? (
                  <UpOutlined className='main-sidebar__arrow-icon-up' />
                ) : (
                  <DownOutlined className='main-sidebar__arrow-icon-down' />
                )}
              </div>
            </Menu.Item>
          </Permission>
          <div
            className={`main-sidebar__submenu ${isOpenSearch ? 'main-sidebar__submenu-open' : ''}`}
            key={'main-siderbar__submenu_search'}
          >
            <Permission permissions={Permissions.PRESTATAIRE_BENNEUR}>
              <Menu.Item
                key='/search/logistique'
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname === '/search/logistique' || location.pathname.startsWith('/search/logistique')
                    ? 'ant-menu-sub-item-selected'
                    : ''
                }`}
              >
                <NavLink to='/search/logistique' className='main-sidebar__sub-navigation'>
                  Logistique
                </NavLink>
              </Menu.Item>
            </Permission>
            <Permission permissions={Permissions.DEVIS}>
              <Menu.Item
                key='/search/commercial'
                className={`main-sidebar__item main-sidebar__region-item ${
                  location.pathname === '/search/commercial' || location.pathname.startsWith('/search/commercial')
                    ? 'ant-menu-sub-item-selected'
                    : ''
                }`}
              >
                <NavLink to='/search/commercial' className='main-sidebar__sub-navigation'>
                  Commerce
                </NavLink>
              </Menu.Item>
            </Permission>
          </div>
        </Menu>
      )}
      <div className='bottom-sidebar'>
        <Button type='link' className='logout-link' onClick={onSignOut}>
          Déconnexion
        </Button>
        <p className='copyright-text'>{moment().format('YYYY')} @ ecodrop</p>
      </div>
    </Sider>
  );
};
export default Sidebar;
