import { ReactNode } from "react";
import { Layout as AntdLayout } from "antd";
import Sidebar from "./Sidebar";
import useGlobalContext from "store/global-context";
const { Content } = AntdLayout;

const Layout = ({ children }: { children: ReactNode }) => {
  const { globalState } = useGlobalContext();
  return (
    <AntdLayout className="layout">
      <Sidebar />
      <Content className={`main-content ${globalState?.isDevisPath ? 'main-content-devis' : ''}`}>{children}</Content>
    </AntdLayout>
  );
};

export default Layout;