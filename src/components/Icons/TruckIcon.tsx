const TruckIcon = ({...others}) => {
  return (
    <svg
      {...others}
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <ellipse
        cx="4.66634"
        cy="11.3333"
        rx="1.33333"
        ry="1.33333"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <circle
        cx="11.3333"
        cy="11.3333"
        r="1.33333"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.33301 12.0833C3.74722 12.0833 4.08301 11.7475 4.08301 11.3333C4.08301 10.919 3.74722 10.5833 3.33301 10.5833V12.0833ZM1.99967 11.3333H1.24967C1.24967 11.7475 1.58546 12.0833 1.99967 12.0833V11.3333ZM2.74967 8.66659C2.74967 8.25237 2.41389 7.91659 1.99967 7.91659C1.58546 7.91659 1.24967 8.25237 1.24967 8.66659H2.74967ZM1.33301 2.58325C0.918794 2.58325 0.583008 2.91904 0.583008 3.33325C0.583008 3.74747 0.918794 4.08325 1.33301 4.08325V2.58325ZM8.66634 3.33325H9.41634C9.41634 2.91904 9.08055 2.58325 8.66634 2.58325V3.33325ZM7.91634 11.3333C7.91634 11.7475 8.25213 12.0833 8.66634 12.0833C9.08055 12.0833 9.41634 11.7475 9.41634 11.3333H7.91634ZM5.99967 10.5833C5.58546 10.5833 5.24967 10.919 5.24967 11.3333C5.24967 11.7475 5.58546 12.0833 5.99967 12.0833V10.5833ZM9.99967 12.0833C10.4139 12.0833 10.7497 11.7475 10.7497 11.3333C10.7497 10.919 10.4139 10.5833 9.99967 10.5833V12.0833ZM12.6663 10.5833C12.2521 10.5833 11.9163 10.919 11.9163 11.3333C11.9163 11.7475 12.2521 12.0833 12.6663 12.0833V10.5833ZM13.9997 11.3333V12.0833C14.4139 12.0833 14.7497 11.7475 14.7497 11.3333H13.9997ZM13.9997 7.33325H14.7497C14.7497 6.91904 14.4139 6.58325 13.9997 6.58325V7.33325ZM8.66634 6.58325C8.25213 6.58325 7.91634 6.91904 7.91634 7.33325C7.91634 7.74747 8.25213 8.08325 8.66634 8.08325V6.58325ZM8.66634 3.24992C8.25213 3.24992 7.91634 3.58571 7.91634 3.99992C7.91634 4.41413 8.25213 4.74992 8.66634 4.74992V3.24992ZM11.9997 3.99992L12.6428 3.61405C12.5073 3.38814 12.2631 3.24992 11.9997 3.24992V3.99992ZM13.3566 7.71912C13.5697 8.07431 14.0304 8.18948 14.3855 7.97637C14.7407 7.76326 14.8559 7.30257 14.6428 6.94738L13.3566 7.71912ZM3.33301 10.5833H1.99967V12.0833H3.33301V10.5833ZM2.74967 11.3333V8.66659H1.24967V11.3333H2.74967ZM1.33301 4.08325H8.66634V2.58325H1.33301V4.08325ZM7.91634 3.33325V11.3333H9.41634V3.33325H7.91634ZM5.99967 12.0833H9.99967V10.5833H5.99967V12.0833ZM12.6663 12.0833H13.9997V10.5833H12.6663V12.0833ZM14.7497 11.3333V7.33325H13.2497V11.3333H14.7497ZM13.9997 6.58325H8.66634V8.08325H13.9997V6.58325ZM8.66634 4.74992H11.9997V3.24992H8.66634V4.74992ZM11.3566 4.38579L13.3566 7.71912L14.6428 6.94738L12.6428 3.61405L11.3566 4.38579Z"
        fill="white"
      />
      <path
        d="M2 6.00008H4.66667"
        stroke="white"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export default TruckIcon;
