import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>, Typo<PERSON>, Badge, Input } from 'antd';
import { CommentOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';

const { Text } = Typography;
const { TextArea } = Input;

type Comment = {
  id: number;
  createdBy: string;
  createdAt: string;
  comment: string;
};

type InvoiceCommentProps = {
  comments: Comment[];
  currentUser: string;
  onAddComment: (newComment: Comment) => void;
  isCommentLine?: boolean;
};

const InvoiceComment: React.FC<InvoiceCommentProps> = ({ comments, currentUser, onAddComment, isCommentLine }) => {
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [newCommentText, setNewCommentText] = useState('');

  const handleOpenModal = () => setIsModalOpen(true);
  const handleCloseModal = () => {
    setIsModalOpen(false);
    setNewCommentText('');
  };

  const handleSubmitComment = () => {
    if (!newCommentText.trim()) {
      toast.warning('Le commentaire ne peut pas être vide.');
      return;
    }

    const newComment: Comment = {
      id: Date.now(),
      createdBy: currentUser,
      createdAt: new Date().toISOString(),
      comment: newCommentText.trim()
    };

    onAddComment(newComment);
    setNewCommentText('');
    toast.success('Commentaire ajouté.');
  };

  // const lastComment = comments[comments.length - 1];
  const lastComment = [...comments].sort((a, b) => new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime())[0];


  return (
    <div style={{ display: isCommentLine ? 'flex' : 'block', alignItems: 'flex-start' }}>
      {!isCommentLine ? (
        <Badge count={comments.length} style={{ backgroundColor: '#f5222d' }}>
          <Button
            style={{ fontWeight: 'bold' }}
            type="link"
            onClick={handleOpenModal}
          >
            <CommentOutlined style={{ fontSize: '22px' }} className="btn-add__creation-icon" />
            Commentaires
          </Button>
        </Badge>
      ) : (
        <Button
          style={{ fontWeight: 'bold', padding: 0, marginRight: 6  }}
          type="link"
          onClick={handleOpenModal}
        >
          <CommentOutlined style={{ fontSize: '22px' }} className="btn-add__creation-icon" />
        </Button>
      )}

      {lastComment && (
        <div style={{ marginLeft: isCommentLine ? 8 : 0 }}>
          {isCommentLine ? (
            <div>
              <Text style={{ fontSize: '12px' }}>
                {lastComment.createdBy} (
                {new Date(lastComment.createdAt).toLocaleDateString('fr-FR')} -{' '}
                {new Date(lastComment.createdAt).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
                )
              </Text>
              <Text style={{ fontSize: '12px' }}> : {lastComment.comment}</Text>
            </div>
          ) : (
            <>
              <br />
              <Text style={{ fontSize: '16px' }}>
                Dernier commentaire - {lastComment.createdBy} (
                {new Date(lastComment.createdAt).toLocaleDateString('fr-FR')} -{' '}
                {new Date(lastComment.createdAt).toLocaleTimeString('fr-FR', {
                  hour: '2-digit',
                  minute: '2-digit',
                })}
                ) :
              </Text>
              <br />
              <Text style={{ fontSize: '14px' }}>{lastComment.comment}</Text>
            </>
          )}
        </div>
      )}

      <Modal
        title="Tous les commentaires"
        open={isModalOpen}
        onCancel={handleCloseModal}
        onOk={handleSubmitComment}
        okText="Ajouter le commentaire"
        cancelText="Fermer"
      >
        <div style={{ display: 'flex', flexDirection: 'column', height: '400px' }}>
          <div style={{ flex: 1, overflowY: 'auto', paddingRight: '8px' }}>
            {comments.length === 0 ? (
              <Text type="secondary">Aucun commentaire pour le moment.</Text>
            ) : (
              [...comments]
                .sort((a, b) => new Date(a.createdAt).getTime() - new Date(b.createdAt).getTime())
                .map((item) => (
                  <div key={item.id} style={{ marginBottom: '1rem' }}>
                    <Text strong>
                      {item.createdBy} -{' '}
                      {new Date(item.createdAt).toLocaleDateString('fr-FR')} à{' '}
                      {new Date(item.createdAt).toLocaleTimeString('fr-FR', {
                        hour: '2-digit',
                        minute: '2-digit',
                      })}
                    </Text>
                    <br />
                    <Text>{item.comment}</Text>
                    <Divider style={{ margin: '8px 0' }} />
                  </div>
                ))
            )}
          </div>
          <div style={{ marginTop: '1rem' }}>
            <TextArea
              rows={4}
              placeholder="Ajouter un nouveau commentaire..."
              value={newCommentText}
              onChange={(e) => setNewCommentText(e.target.value)}
            />
          </div>
        </div>
      </Modal>
    </div>
  );



};

export default InvoiceComment;
