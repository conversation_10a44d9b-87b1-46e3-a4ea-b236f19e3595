import { useState, useEffect} from 'react';
import { PageTitle, MainTitle, Spinner } from 'components/Common';
import { Divider, TableProps} from 'antd';


const ProviderInvoiceModal = () => {
    const [loading, setLoading] = useState<boolean>(false);

    const simulateLoading = (timeout: number): void => {
        setLoading(true);
        setTimeout(() => {
        setLoading(false);
        }, timeout);
    };

    // Simulate loading for 2 seconds
    useEffect(() => {
        simulateLoading(2000);
    }, []);

    return (
        <>
            <PageTitle>FACTURES FOURNISSEURS</PageTitle>
            <Spinner loading={loading}>
                <>
                <MainTitle parent="Validation" child="FACTURES FOURNISSEURS" />
                    <Divider className="horizontal-bar" />
                    <div className="product-page__searching">
                    </div>
                </>
            </Spinner>

        </>
    );
};

export default ProviderInvoiceModal;