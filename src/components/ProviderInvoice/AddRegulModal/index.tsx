import React, { useState } from 'react';
import { Modal, Button, Select, InputNumber, Row, Col, DatePicker, Typography, Table, Input, Upload } from 'antd';
import { DeleteOutlined, PlusCircleFilled } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import { toast } from 'react-toastify';
import type { RcFile } from 'antd/es/upload/interface';

const { Text } = Typography;
const { TextArea } = Input;

interface Regul {
  id: string;
  name: string;
  prixUnitaire: number;
  prixClient: number;
  margin: number;
  quantity: number;
}

interface WasteType {
  id: string;
  name: string;
  prixUnitaire: number;
  prixClient: number;
  margin: number;
  quantity: number;
  unit: string;
}

interface RegulEntry {
  id: string;
  regul: string;
  regulName: string;
  date: string;
  montant: number;
  quantity: number;
  total: number;
  prixClient: number;
  margin: number;
  status: string;
}

interface FormSection {
  id: string;
  regul: string;
  quantity: number | null;
  price: number | null;
  date: string | null;
  status: string;
  wasteType?: string;
  wasteUnit?: string;
}

interface AddRegulModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (reguls: RegulEntry[]) => void;
  initialReguls?: RegulEntry[];
  invoiceNumber?: string;
  prestataire?: string;
  initialFiles?: UploadFile[];
}

const regulOptions: Regul[] = [
  { id: 'REG001', name: 'Régularisation Transport', prixUnitaire: 150.00, prixClient: 180.00, margin: 30.00, quantity: 1 },
  { id: 'REG002', name: 'Régul Traitement de dechet', prixUnitaire: 200.00, prixClient: 250.00, margin: 50.00, quantity: 1 },
  { id: 'REG003', name: 'Régul Surpoids', prixUnitaire: 120.00, prixClient: 140.00, margin: 20.00, quantity: 1 },
  { id: 'REG004', name: 'Régul Passage à vide', prixUnitaire: 80.00, prixClient: 100.00, margin: 20.00, quantity: 1 },
  { id: 'REG005', name: 'Régul déclassement de dechet', prixUnitaire: 90.00, prixClient: 110.00, margin: 20.00, quantity: 1 },
  { id: 'REG006', name: 'Régul Valorisation', prixUnitaire: 300.00, prixClient: 360.00, margin: 60.00, quantity: 1 },
];

const statusOptions = [
  { value: 'a_facturer', label: 'A facturer' },
  { value: 'interne', label: 'Interne' },
];


const wasteTypeOptions: WasteType[] = [
  { id: 'WASTE001', name: 'DIB', prixUnitaire: 150.00, prixClient: 180.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE002', name: 'Carton', prixUnitaire: 120.00, prixClient: 150.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE003', name: 'Plastique', prixUnitaire: 200.00, prixClient: 250.00, margin: 50.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE004', name: 'Métal', prixUnitaire: 180.00, prixClient: 220.00, margin: 40.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE005', name: 'Verre', prixUnitaire: 100.00, prixClient: 130.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE006', name: 'Bois', prixUnitaire: 90.00, prixClient: 120.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE007', name: 'Papier', prixUnitaire: 80.00, prixClient: 100.00, margin: 20.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE008', name: 'Textile', prixUnitaire: 110.00, prixClient: 140.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE009', name: 'Déchets électroniques', prixUnitaire: 300.00, prixClient: 380.00, margin: 80.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE010', name: 'Déchets organiques', prixUnitaire: 70.00, prixClient: 90.00, margin: 20.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE011', name: 'Déchets dangereux', prixUnitaire: 500.00, prixClient: 650.00, margin: 150.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE012', name: 'Déchets inertes', prixUnitaire: 60.00, prixClient: 80.00, margin: 20.00, quantity: 1, unit: 't' },
];


const unitOptions = [
  { value: 'kg', label: 'Kilogrammes' },
  { value: 't', label: 'Tonnes' },
  { value: 'm3', label: 'Mètres cubes' },
  { value: 'l', label: 'Litres' },
  { value: 'unite', label: 'Unité' },
];

const AddRegulModal: React.FC<AddRegulModalProps> = ({
  visible,
  onClose,
  onSave,
  initialReguls = [],
  initialFiles = []
}) => {
  const [formSections, setFormSections] = useState<FormSection[]>([
    { id: Date.now().toString(), regul: '', quantity: null, price: null, date: null, status: '' }
  ]);
  const [regulEntries, setRegulEntries] = useState<RegulEntry[]>(initialReguls);
  const [saving, setSaving] = useState(false);
  const [fileList, setFileList] = useState<UploadFile[]>(initialFiles);

  const [showWasteForm, setShowWasteForm] = useState(false);
  const [newCommentText, setNewCommentText] = useState('');

  // Update form section
  const handleUpdateFormSection = (id: string, field: keyof FormSection, value: RegulEntry | string | number | null) => {
    setFormSections(prev => prev.map(section =>
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  // Handle regul selection - auto-populate prix unitaire
  const handleRegulSelection = (sectionId: string, regulId: string) => {
    const selectedRegul = regulOptions.find(r => r.id === regulId);
    if (regulId === 'REG002' || regulId === 'REG005') {
      setShowWasteForm(true);
    } else {
      setShowWasteForm(false);
    }
    if (selectedRegul) {
      // Update regul selection
      handleUpdateFormSection(sectionId, 'regul', regulId);
      // Auto-populate prix unitaire
      handleUpdateFormSection(sectionId, 'price', selectedRegul.prixUnitaire);
      handleUpdateFormSection(sectionId, 'quantity', selectedRegul.quantity);
      
    }
  };

  // Add regul from form section
  const handleAddRegulFromSection = (sectionId: string) => {
    const section = formSections.find(s => s.id === sectionId);
    console.log('section', section);
    if (!section || !section.regul || !section.quantity || section.price === null || !section.date || !section.status) {
      toast.warning('Veuillez remplir tous les champs obligatoires.');
      return;
    }
    if (section.regul === 'REG002' || section.regul === 'REG005') {
      if (!section.wasteType || !section.wasteUnit) {
        toast.warning('Veuillez remplir tous les champs obligatoires.');
        return;
      }
    }

    const selectedRegul = regulOptions.find(r => r.id === section.regul);
    if (!selectedRegul) {
      toast.error('Régularisation non trouvée.');
      return;
    }

    const total = section.quantity * section.price;
    const prixClient = selectedRegul.prixClient * section.quantity;
    const margin = prixClient - total;

    const newRegulEntry: RegulEntry = {
      id: Date.now().toString(),
      regul: section.regul,
      regulName: selectedRegul.name,
      date: section.date,
      montant: section.price,
      quantity: section.quantity,
      total: total,
      prixClient: prixClient,
      margin: margin,
      status: section.status,
    };

    setRegulEntries(prev => [...prev, newRegulEntry]);

    // Reset the form section
    handleUpdateFormSection(sectionId, 'regul', '');
    
    handleUpdateFormSection(sectionId, 'quantity', null);
    handleUpdateFormSection(sectionId, 'price', null);
    handleUpdateFormSection(sectionId, 'date', null);
    onChange={(_, dateString) => handleUpdateFormSection(section.id, 'date', dateString)}
    handleUpdateFormSection(sectionId, 'status', '');

    toast.success('Régularisation ajoutée avec succès!');
  };

  // Remove regul entry
  const handleRemoveRegulEntry = (id: string) => {
    setRegulEntries(prev => prev.filter(item => item.id !== id));
    toast.info('Régularisation supprimée.');
  };

  // Handle save
  const handleSave = () => {
    if (regulEntries.length === 0) {
      toast.warning('Veuillez ajouter au moins une régularisation.');
      return;
    }

    setSaving(true);

    // Simulate save process
    setTimeout(() => {
      onSave(regulEntries);
      setSaving(false);
      toast.success('Régularisations sauvegardées avec succès!');
      onClose();
    }, 500);
  };

  // Handle file upload
  const handleUpload: UploadProps['customRequest'] = (options) => {
    const { file, onSuccess, onError } = options;
    const rcFile = file as RcFile;

    // Simulate upload process
    setTimeout(() => {
      try {
        const uploadFile: UploadFile = {
          uid: Date.now().toString(),
          name: rcFile.name,
          status: 'done',
          size: rcFile.size,
          type: rcFile.type,
          originFileObj: rcFile,
        };

        setFileList(prev => [...prev, uploadFile]);
        onSuccess?.(uploadFile);
        toast.success(`${rcFile.name} téléchargé avec succès.`);
      } catch (error) {
        onError?.(error as Error);
        toast.error(`Échec du téléchargement de ${rcFile.name}.`);
      }
    }, 1000);
  };

  // Handle file removal
  const handleRemove = (file: UploadFile) => {
    setFileList(prev => prev.filter(item => item.uid !== file.uid));
    toast.info(`${file.name} supprimé.`);
  };

  // Upload props
  const uploadProps: UploadProps = {
    customRequest: handleUpload,
    showUploadList: false,
    multiple: true,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt',
  };

  // Handle cancel
  const handleCancel = () => {
    setRegulEntries(initialReguls);
    setFormSections([{ id: Date.now().toString(), regul: '', quantity: null, price: null, date: null, status: '' }]);
    setFileList(initialFiles);
    onClose();
  };

  return (
    <Modal
      title={'REGUL SUR COMMANDE'}
      open={visible}
      onCancel={handleCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Annuler
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSave}
          loading={saving}
        >
          Valider
        </Button>,
      ]}
    >
      <div style={{ minHeight: '150px' }}>
        {/* Dynamic Form Sections */}
        {formSections.map((section) => (
          <div key={section.id} style={{ marginBottom: 4, padding: '8px' }}>
            <Row gutter={24}>
                <Col span={6}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Regul
                    </label>
                    <Select
                      placeholder="Sélectionner une régularisation"
                      showSearch
                      style={{ width: '100%' }}
                      value={section.regul}
                      onChange={(value) => handleRegulSelection(section.id, value)}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={regulOptions.map(regul => ({ value: regul.id, label: regul.name }))}
                    />
                  </div>
                </Col>
                <Col span={3}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Date
                    </label>
                    <DatePicker
                      placeholder='Sélectionner une date'
                      style={{ width: '100%' }}
                      onChange={(_, dateString) => handleUpdateFormSection(section.id, 'date', dateString)}
                    />
                  </div>
                </Col>
                <Col span={3}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Prix Unitaire
                    </label>
                    <InputNumber
                      placeholder="0.00"
                      style={{ width: '100%' }}
                      min={0.01}
                      step={0.01}
                      precision={2}
                      value={section.price}
                      onChange={(value) => handleUpdateFormSection(section.id, 'price', value)}
                    />
                  </div>
                </Col>
                <Col span={2}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Quantité
                    </label>
                    <InputNumber
                      placeholder="0.00"
                      style={{ width: '100%' }}
                      min={0.01}
                      step={0.01}
                      precision={2}
                      value={section.quantity}
                      onChange={(value) => handleUpdateFormSection(section.id, 'quantity', value)}
                    />
                  </div>
                </Col>
                <Col span={2}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Total
                    </label>
                    <InputNumber
                      placeholder="0.00"
                      style={{ width: '100%' }}
                      min={0}
                      step={0.01}
                      precision={2}
                      value={section.quantity && section.price ? section.quantity * section.price : 0}
                      disabled
                    />
                  </div>
                </Col>
                <Col span={3}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      Statut
                    </label>
                    <Select
                      placeholder="Statut"
                      showSearch
                      style={{ width: '100%' }}
                      value={section.status}
                      onChange={(value) => handleUpdateFormSection(section.id, 'status', value)}
                      filterOption={(input, option) =>
                        (option?.label ?? '').toLowerCase().includes(input.toLowerCase())
                      }
                      options={statusOptions}
                    />
                  </div>
                </Col>
                <Col span={3}>
                  {(() => {
                    const selectedRegul = regulOptions.find(r => r.id === section.regul);
                    const prixClient = selectedRegul && section.quantity ? selectedRegul.prixClient * section.quantity : 0;
                    const total = section.quantity && section.price ? section.quantity * section.price : 0;
                    const margin = prixClient - total;

                    return (
                      <div style={{ marginTop: '30px' }}>
                        <Text style={{ fontSize: '12px', display: 'block' }}>
                          Prix client : {prixClient.toFixed(2)}€
                        </Text>
                        <Text style={{ fontSize: '12px', display: 'block' }}>
                          Marge Total : {margin.toFixed(2)}€
                        </Text>
                      </div>
                    );
                  })()}
                </Col>
                <Col span={1}>
                  <div>
                    <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                      &nbsp;
                    </label>
                    <Button
                      type="primary"
                      style={{ fontWeight: 'bold' }}
                      onClick={() => handleAddRegulFromSection(section.id)}
                    >
                      Ajouter
                    </Button>
                  </div>
                </Col>
            </Row>
            {showWasteForm && (
              <>
                <div style={{ marginBottom: 4, padding: '8px' }}>
                <Row gutter={24}>
                  <Col span={6}>  </Col>
                  <Col span={6}>
                    <div>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                        Type de déchet
                      </label>
                      <Select
                        placeholder="Sélectionner un type de déchet"
                        showSearch
                        style={{ width: '100%' }}
                        filterOption={(input, option) =>
                          (option?.children ?? '').toString().toLowerCase().includes(input.toLowerCase())
                        }
                        onChange={(value) => handleUpdateFormSection(section.id, 'wasteType', value)}
                        value={section.wasteType}
                      >
                        {wasteTypeOptions.map(option => (
                          <Select.Option key={option.id} value={option.id}>
                            {option.name}
                          </Select.Option>
                        ))}
                      </Select>
                    </div>
                  </Col>
                  <Col span={4}>
                    <div>
                      <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                        Unité
                      </label>
                      <Select
                        placeholder="Unité"
                        style={{ width: '100%' }}
                        value={section.wasteUnit}
                        options={unitOptions}
                        onChange={(value) => handleUpdateFormSection(section.id, 'wasteUnit', value)}
                      />
                    </div>
                  </Col>
                </Row>
                </div>
              </>
              )}
          </div>
        ))}

        {/* Reguls Table */}
        <div style={{ marginTop: 24 }}>
          {regulEntries.length > 0 && (
            <Table
            dataSource={regulEntries}
            pagination={false}
            size="small"
            columns={[
              {
                title: 'Reguls',
                dataIndex: 'regulName',
                key: 'regulName',
                width: 200,
              },
              {
                title: 'Date',
                dataIndex: 'date',
                key: 'date',
                width: 100,
              },
              {
                title: 'Montant',
                dataIndex: 'montant',
                key: 'montant',
                width: 100,
                render: (value) => `${value?.toFixed(2)}€`,
              },
              {
                title: 'Quantité',
                dataIndex: 'quantity',
                key: 'quantity',
                width: 100,
              },
              {
                title: 'Total',
                dataIndex: 'total',
                key: 'total',
                width: 100,
                render: (value) => `${value?.toFixed(2)}€`,
              },
              {
                title: 'Prix Client',
                dataIndex: 'prixClient',
                key: 'prixClient',
                width: 100,
                render: (value) => `${value?.toFixed(2)}€`,
              },
              {
                title: 'Marge',
                dataIndex: 'margin',
                key: 'margin',
                width: 100,
                render: (value) => `${value?.toFixed(2)}€`,
              },
              {
                title: 'Action',
                key: 'action',
                width: 80,
                render: (_, record) => (
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemoveRegulEntry(record.id)}
                  >
                    Supprimer
                  </Button>
                ),
              },
            ]}
          />
          )}
        </div>

        {/* Upload Section */}
        <div style={{ marginTop: 24, marginBottom: 24 }}>

          <div style={{ marginBottom: 16 }}>
            <Upload {...uploadProps}>
              <Button
                type="dashed"
                icon={<PlusCircleFilled />}
                size="large"
                style={{ width: '100%', height: '50px' }}
              >
                Ajouter un fichier
              </Button>
            </Upload>
          </div>

          {/* Display uploaded files */}
          {fileList.length > 0 && (
            <div style={{ border: '1px solid #d9d9d9', borderRadius: '6px', padding: '12px' }}>
              <Text strong style={{ marginBottom: '12px', display: 'block' }}>
                Fichiers téléchargés ({fileList.length})
              </Text>
              {fileList.map((file) => (
                <div
                  key={file.uid}
                  style={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    alignItems: 'center',
                    padding: '8px 0',
                    borderBottom: '1px solid #f0f0f0'
                  }}
                >
                  <div style={{ display: 'flex', alignItems: 'center', flex: 1 }}>
                    <Text style={{ marginRight: '8px' }}>{file.name}</Text>
                    {file.size && (
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        ({(file.size / 1024).toFixed(1)} KB)
                      </Text>
                    )}
                  </div>
                  <Button
                    type="text"
                    danger
                    size="small"
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemove(file)}
                    title="Supprimer le fichier"
                  />
                </div>
              ))}
            </div>
          )}
        </div>
        <div style={{ marginTop: 24 }}>
          <TextArea
              rows={4}
              placeholder="Ajouter un nouveau commentaire..."
              onChange={(e) => setNewCommentText(e.target.value)}
            />
        </div>
      </div>
    </Modal>
  );
};

export default AddRegulModal;
