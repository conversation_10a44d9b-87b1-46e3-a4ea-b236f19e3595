import React, { useState } from 'react';
import { Modal, Button, Upload, List, Space, Typography, Divider } from 'antd';
import { PlusCircleFilled , DeleteOutlined, FileOutlined } from '@ant-design/icons';
import type { UploadFile, UploadProps } from 'antd';
import type { RcFile } from 'antd/es/upload/interface';
import { toast } from 'react-toastify';

const { Text } = Typography;

interface FileUploadModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (files: UploadFile[]) => void;
  initialFiles?: UploadFile[];
  title?: string;
  invoiceNumber?: string;
  prestataire?: string;
}

const FileUploadModal: React.FC<FileUploadModalProps> = ({
  visible,
  onClose,
  onSave,
  initialFiles = [],
  title = "Gestion des fichiers", // eslint-disable-line @typescript-eslint/no-unused-vars
  invoiceNumber, // eslint-disable-line @typescript-eslint/no-unused-vars
  prestataire // eslint-disable-line @typescript-eslint/no-unused-vars
}) => {
  const [fileList, setFileList] = useState<UploadFile[]>(initialFiles);
  const [uploading, setUploading] = useState(false);

  // Handle file upload
  const handleUpload: UploadProps['customRequest'] = (options) => {
    const { file, onSuccess, onError } = options;
    const rcFile = file as RcFile;

    // Simulate upload process
    setTimeout(() => {
      try {
        const uploadFile: UploadFile = {
          uid: Date.now().toString(),
          name: rcFile.name,
          status: 'done',
          size: rcFile.size,
          type: rcFile.type,
          originFileObj: rcFile,
        };
        
        setFileList(prev => [...prev, uploadFile]);
        onSuccess?.(uploadFile);
        toast.success(`${rcFile.name} téléchargé avec succès.`);
      } catch (error) {
        onError?.(error as Error);
        toast.error(`Échec du téléchargement de ${rcFile.name}.`);
      }
    }, 1000);
  };

  // Handle file removal
  const handleRemove = (file: UploadFile) => {
    setFileList(prev => prev.filter(item => item.uid !== file.uid));
    toast.info(`${file.name} supprimé.`);
  };

  // Handle save
  const handleSave = () => {
    setUploading(true);
    
    // Simulate save process
    setTimeout(() => {
      onSave(fileList);
      setUploading(false);
      toast.success('Fichiers sauvegardés avec succès!');
      onClose();
    }, 500);
  };

  // Handle cancel
  const handleCancel = () => {
    setFileList(initialFiles);
    onClose();
  };

  // Upload props
  const uploadProps: UploadProps = {
    customRequest: handleUpload,
    showUploadList: false,
    multiple: true,
    accept: '.pdf,.doc,.docx,.xls,.xlsx,.jpg,.jpeg,.png,.txt',
  };

  return (
    <Modal
      title={'AJOUTER UN FICHER'}
      open={visible}
      onCancel={handleCancel}
      width={600}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Annuler
        </Button>,
        <Button 
          key="save" 
          type="primary" 
          onClick={handleSave}
          loading={uploading}
          disabled={fileList.length === 0}
        >
          Valider
        </Button>,
      ]}
    >
      <div style={{ width: '100%', maxWidth: '800px', minHeight: '300px' }}>
        {/* Upload Section */}
        <div style={{ marginBottom: 24 }}>
          <Upload {...uploadProps} style={{ width: '100%' }}>
            <Button 
              type="link" 
              icon={<PlusCircleFilled />} 
              size="large"
              className=''
            >
              Ajouter un ficher
            </Button>
          </Upload>
        </div>

        <Divider orientation="left">
          Fichiers importés ({fileList.length})
        </Divider>

        {/* File List */}
        {fileList.length > 0 ? (
          <List
            style={{ width: '100%' }}
            dataSource={fileList}
            renderItem={(file) => (
              <List.Item
                actions={[
                  <Button
                    key="delete"
                    type="text"
                    danger
                    icon={<DeleteOutlined />}
                    onClick={() => handleRemove(file)}
                    size="small"
                  >
                    Supprimer
                  </Button>
                ]}
              >
                <List.Item.Meta
                  avatar={<FileOutlined style={{ fontSize: '20px', color: '#1890ff' }} />}
                  title={
                    <Space>
                      <Text strong>{file.name}</Text>
                      {file.status === 'uploading' && (
                        <Text type="secondary" style={{ fontSize: '12px' }}>
                          Téléchargement...
                        </Text>
                      )}
                      {file.status === 'done' && (
                        <Text type="success" style={{ fontSize: '12px' }}>
                          ✓ Téléchargé
                        </Text>
                      )}
                    </Space>
                  }
                  description={
                    <Space>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {file.size ? `${(file.size / 1024).toFixed(1)} KB` : 'Taille inconnue'}
                      </Text>
                      <Text type="secondary" style={{ fontSize: '12px' }}>
                        {file.type || 'Type inconnu'}
                      </Text>
                    </Space>
                  }
                />
              </List.Item>
            )}
          />
        ) : (
          <div 
            style={{ 
              textAlign: 'center', 
              padding: '40px 0',
              color: '#999',
              backgroundColor: '#fafafa',
              borderRadius: '6px',
              border: '1px dashed #d9d9d9'
            }}
          >
            <FileOutlined style={{ fontSize: '48px', marginBottom: '16px' }} />
            <div>Aucun fichier téléchargé</div>
          </div>
        )}
      </div>
    </Modal>
  );
};

export default FileUploadModal;
