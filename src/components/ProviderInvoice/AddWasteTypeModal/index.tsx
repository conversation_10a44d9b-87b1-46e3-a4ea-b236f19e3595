import React, { useState } from 'react';
import { Mo<PERSON>, Button, Select, InputNumber, Row, Col, Table } from 'antd';
import { DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { toast } from 'react-toastify';

interface WasteType {
  id: string;
  name: string;
  prixUnitaire: number;
  prixClient: number;
  margin: number;
  quantity: number;
  unit: string;
}

interface WasteTypeEntry {
  id: string;
  wasteType: string;
  wasteTypeName: string;
  montant: number;
  quantity: number;
  unit: string;
  total: number;
  prixClient: number;
  margin: number;
}

interface FormSection {
  id: string;
  wasteType: string;
  quantity: number | null;
  unit: string;
  price: number | null;
}

interface AddWasteTypeModalProps {
  visible: boolean;
  onClose: () => void;
  onSave: (wasteTypes: WasteTypeEntry[]) => void;
  initialWasteTypes?: WasteTypeEntry[];
  invoiceNumber?: string;
  prestataire?: string;
}

const wasteTypeOptions: WasteType[] = [
  { id: 'WASTE001', name: 'DIB', prixUnitaire: 150.00, prixClient: 180.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE002', name: 'Carton', prixUnitaire: 120.00, prixClient: 150.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE003', name: 'Plastique', prixUnitaire: 200.00, prixClient: 250.00, margin: 50.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE004', name: 'Métal', prixUnitaire: 180.00, prixClient: 220.00, margin: 40.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE005', name: 'Verre', prixUnitaire: 100.00, prixClient: 130.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE006', name: 'Bois', prixUnitaire: 90.00, prixClient: 120.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE007', name: 'Papier', prixUnitaire: 80.00, prixClient: 100.00, margin: 20.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE008', name: 'Textile', prixUnitaire: 110.00, prixClient: 140.00, margin: 30.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE009', name: 'Déchets électroniques', prixUnitaire: 300.00, prixClient: 380.00, margin: 80.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE010', name: 'Déchets organiques', prixUnitaire: 70.00, prixClient: 90.00, margin: 20.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE011', name: 'Déchets dangereux', prixUnitaire: 500.00, prixClient: 650.00, margin: 150.00, quantity: 1, unit: 'kg' },
  { id: 'WASTE012', name: 'Déchets inertes', prixUnitaire: 60.00, prixClient: 80.00, margin: 20.00, quantity: 1, unit: 't' },
];

const unitOptions = [
  { value: 'kg', label: 'Kilogrammes' },
  { value: 't', label: 'Tonnes' },
  { value: 'm3', label: 'Mètres cubes' },
  { value: 'l', label: 'Litres' },
  { value: 'unite', label: 'Unité' },
];

const AddWasteTypeModal: React.FC<AddWasteTypeModalProps> = ({
  visible,
  onClose,
  onSave,
  initialWasteTypes = [],
  invoiceNumber,
  prestataire
}) => {
  const [formSections, setFormSections] = useState<FormSection[]>([
    { id: Date.now().toString(), wasteType: '', quantity: null, unit: '', price: null}
  ]);
  const [wasteTypeEntries, setWasteTypeEntries] = useState<WasteTypeEntry[]>(initialWasteTypes);
  const [saving, setSaving] = useState(false);

  // Update form section
  const handleUpdateFormSection = (id: string, field: keyof FormSection, value: WasteTypeEntry | string | number | null) => {
    setFormSections(prev => prev.map(section =>
      section.id === id ? { ...section, [field]: value } : section
    ));
  };

  // Handle waste type selection - auto-populate prix unitaire
  const handleWasteTypeSelection = (sectionId: string, wasteTypeId: string) => {
    const selectedWasteType = wasteTypeOptions.find(w => w.id === wasteTypeId);
    if (selectedWasteType) {
      // Update waste type selection
      handleUpdateFormSection(sectionId, 'wasteType', wasteTypeId);
      // Auto-populate prix unitaire and unit
      handleUpdateFormSection(sectionId, 'price', selectedWasteType.prixUnitaire);
      handleUpdateFormSection(sectionId, 'quantity', selectedWasteType.quantity);
      handleUpdateFormSection(sectionId, 'unit', selectedWasteType.unit);
    }
  };

  // Add waste type from form section
  const handleAddWasteTypeFromSection = (sectionId: string) => {
    const section = formSections.find(s => s.id === sectionId);
    if (!section || !section.wasteType || !section.quantity || !section.unit || section.price === null) {
      toast.warning('Veuillez remplir tous les champs obligatoires.');
      return;
    }

    const selectedWasteType = wasteTypeOptions.find(w => w.id === section.wasteType);
    if (!selectedWasteType) {
      toast.error('Type de déchet non trouvé.');
      return;
    }

    const total = section.quantity * section.price;
    const prixClient = selectedWasteType.prixClient * section.quantity;
    const marginAmount = prixClient - total;

    const newWasteTypeEntry: WasteTypeEntry = {
      id: Date.now().toString(),
      wasteType: section.wasteType,
      wasteTypeName: selectedWasteType.name,
      montant: section.price,
      quantity: section.quantity,
      unit: section.unit,
      total: total,
      prixClient: prixClient,
      margin: marginAmount,
    };

    setWasteTypeEntries(prev => [...prev, newWasteTypeEntry]);

    // Reset the form section
    handleUpdateFormSection(sectionId, 'wasteType', '');
    handleUpdateFormSection(sectionId, 'quantity', null);
    handleUpdateFormSection(sectionId, 'unit', '');
    handleUpdateFormSection(sectionId, 'price', null);

    toast.success('Type de déchet ajouté avec succès!');
  };

  // Remove waste type entry
  const handleRemoveWasteTypeEntry = (id: string) => {
    setWasteTypeEntries(prev => prev.filter(item => item.id !== id));
    toast.info('Type de déchet supprimé.');
  };

  // Handle save
  const handleSave = () => {
    if (wasteTypeEntries.length === 0) {
      toast.warning('Veuillez ajouter au moins un type de déchet.');
      return;
    }

    setSaving(true);

    // Simulate save process
    setTimeout(() => {
      onSave(wasteTypeEntries);
      setSaving(false);
      toast.success('Types de déchets sauvegardés avec succès!');
      onClose();
    }, 500);
  };

  // Handle cancel
  const handleCancel = () => {
    setWasteTypeEntries(initialWasteTypes);
    setFormSections([{ id: Date.now().toString(), wasteType: '', quantity: null, unit: '', price: null }]);
    onClose();
  };

  return (
    <Modal
      title={'AJOUTER UN TYPE DE DÉCHETS'}
      open={visible}
      onCancel={handleCancel}
      width={1200}
      footer={[
        <Button key="cancel" onClick={handleCancel}>
          Annuler
        </Button>,
        <Button
          key="save"
          type="primary"
          onClick={handleSave}
          loading={saving}
        >
          Valider
        </Button>,
      ]}
    >
      <div style={{ minHeight: '150px' }}>

        {/* Dynamic Form Sections */}
        {formSections.map((section) => (
          <div key={section.id} style={{ marginBottom: 4, padding: '8px' }}>
            <Row gutter={24}>
              <Col span={6}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    Type de déchet
                  </label>
                  <Select
                    placeholder="Sélectionner un type de déchet"
                    showSearch
                    style={{ width: '100%' }}
                    value={section.wasteType}
                    onChange={(value) => handleWasteTypeSelection(section.id, value)}
                    filterOption={(input, option) =>
                      (option?.children ?? '').toString().toLowerCase().includes(input.toLowerCase())
                    }
                  >
                    {wasteTypeOptions.map(option => (
                      <Select.Option key={option.id} value={option.id}>
                        {option.name}
                      </Select.Option>
                    ))}
                  </Select>
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    Prix Unitaire
                  </label>
                  <InputNumber
                    placeholder="0.00"
                    style={{ width: '100%' }}
                    min={0.01}
                    step={0.01}
                    precision={2}
                    value={section.price}
                    onChange={(value) => handleUpdateFormSection(section.id, 'price', value)}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    Quantité
                  </label>
                  <InputNumber
                    placeholder="0.00"
                    style={{ width: '100%' }}
                    min={0.01}
                    step={0.01}
                    precision={2}
                    value={section.quantity}
                    onChange={(value) => handleUpdateFormSection(section.id, 'quantity', value)}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    Unité
                  </label>
                  <Select
                    placeholder="Unité"
                    style={{ width: '100%' }}
                    value={section.unit}
                    options={unitOptions}
                    onChange={(value) => handleUpdateFormSection(section.id, 'unit', value)}
                  />
                </div>
              </Col>
              <Col span={4}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    Total
                  </label>
                  <InputNumber
                    placeholder="0.00"
                    style={{ width: '100%' }}
                    min={0}
                    step={0.01}
                    precision={2}
                    value={section.quantity && section.price ? section.quantity * section.price : 0}
                    disabled
                  />
                </div>
              </Col>
              <Col span={1}>
                <div>
                  <label style={{ display: 'block', marginBottom: '8px', fontWeight: '500' }}>
                    &nbsp;
                  </label>
                  <Button
                    type="primary"
                    style={{ fontWeight: 'bold' }}
                    onClick={() => handleAddWasteTypeFromSection(section.id)}
                    disabled={!section.wasteType || !section.quantity || section.price === null}
                  >
                    Ajouter
                  </Button>
                </div>
              </Col>
            </Row>
          </div>
        ))}
        {/* Table of added waste types */}
        {wasteTypeEntries.length > 0 && (
          <div style={{ marginTop: '20px', margin: '20px 16px 0 16px' }}>
            <Table
              dataSource={wasteTypeEntries}
              pagination={false}
              size="small"
              rowKey="id"
              columns={[
                {
                  title: 'Type de déchet',
                  dataIndex: 'wasteTypeName',
                  key: 'wasteTypeName',
                  width: '20%',
                },
                {
                  title: 'Quantité',
                  dataIndex: 'quantity',
                  key: 'quantity',
                  width: '10%',
                  render: (quantity: number, record: WasteTypeEntry) => `${quantity} ${record.unit}`,
                },
                {
                  title: 'Unité',
                  dataIndex: 'Unity',
                  key: 'Unity',
                  width: '10%',
                  render: (quantity: number, record: WasteTypeEntry) => `${quantity} ${record.unit}`,
                },
                {
                  title: 'Prix unitaire',
                  dataIndex: 'montant',
                  key: 'montant',
                  width: '15%',
                  render: (montant: number) => `${montant.toFixed(2)}€`,
                },
                {
                  title: 'Total',
                  dataIndex: 'total',
                  key: 'total',
                  width: '10%',
                  render: (total: number) => `${total.toFixed(2)}€`,
                },
                {
                  title: 'Prix client',
                  dataIndex: 'prixClient',
                  key: 'prixClient',
                  width: '15%',
                  render: (prixClient: number) => `${prixClient.toFixed(2)}€`,
                },
                {
                  title: 'Marge Totale',
                  dataIndex: 'margin',
                  key: 'margin',
                  width: '15%',
                  render: (_: WasteTypeEntry, record: WasteTypeEntry) => {
                    const marginPercentage = record.prixClient > 0 ? (record.margin / record.prixClient) * 100 : 0;
                    return <span >{marginPercentage.toFixed(1)}%</span>;
                  },
                },
                {
                  title: 'Actions',
                  key: 'actions',
                  width: '5%',
                  render: (_: WasteTypeEntry, record: WasteTypeEntry) => (
                    <Button
                      type="text"
                      danger
                      size="small"
                      icon={<DeleteOutlined />}
                      onClick={() => handleRemoveWasteTypeEntry(record.id)}
                    >
                      Supprimer
                    </Button>
                  ),
                },
              ]}
            />
          </div>
        )}
      </div>
    </Modal>
  );
};

export default AddWasteTypeModal;
