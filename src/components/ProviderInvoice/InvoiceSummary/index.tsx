// InvoiceSummary.tsx
import React, { useState } from 'react';
import {
  Card,
  Row,
  Col,
  Badge,
  Typography,
  Modal,
  Form,
  InputNumber,
  Input,
  Table,
  Button,
  Space,
} from 'antd';
import { PlusCircleOutlined, DeleteOutlined } from '@ant-design/icons';
import dayjs from 'dayjs';
import { toast } from 'react-toastify';

const { Text, Title } = Typography;

export interface Avoir {
  id: number;
  avoirNumber: string;
  createdAt: string;
  amount: number;
  createdBy: string;
}

export interface InvoiceSummaryProps {
  totalSelected: number;      // sum of invoicedPrice of selected lines
  totalAvoirs: number;        // sum of all avoir amounts
  avoirsCount: number;        // number of avoirs
  invoiceAmount: number;      // initialValues.amount
  avoirDetails: Avoir[];
  onAddAvoir: (data: { amount: number; avoirNumber: string }) => void;
  onRemoveAvoir: (id: number) => void;
}

const InvoiceSummary: React.FC<InvoiceSummaryProps> = ({
  totalSelected,
  totalAvoirs,
  avoirsCount,
  invoiceAmount,
  onAddAvoir,
  onRemoveAvoir,
  avoirDetails,
}) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [form] = Form.useForm();

  const openModal = () => {
    form.resetFields();
    setModalVisible(true);
  };

  const handleAddAvoir = async () => {
    try {
      const values = await form.validateFields();
      onAddAvoir({ 
        amount: values.amount, 
        avoirNumber: values.avoirNumber 
      });
    toast.success('Avoir ajouté avec succès!');
      form.resetFields();
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const columns = [
    {
      title: "N° de l'avoir",
      dataIndex: 'avoirNumber',
      key: 'avoirNumber',
    },
    {
      title: 'Date',
      dataIndex: 'createdAt',
      key: 'createdAt',
      render: (date: string) => dayjs(date).format('DD/MM/YYYY'),
    },
    {
      title: 'Montant',
      dataIndex: 'amount',
      key: 'amount',
      render: (amount: number) => amount.toFixed(2) + ' €',
    },
    {
      title: 'Ajouté par',
      dataIndex: 'createdBy',
      key: 'createdBy',
    },
    {
      title: 'Action',
      key: 'action',
      render: (_: Avoir, record: Avoir) => (
        <Button 
          type="link" 
          danger 
          icon={<DeleteOutlined />} 
          onClick={() => {
            onRemoveAvoir(record.id);
            toast.info('Régularisation supprimée.');
          }}
        >
          Supprimer
        </Button>
      ),
    },
  ];

  return (
    <>
      <Card 
        bordered 
        style={{ 
          justifyContent: 'right',
          width: 360, 
          padding: 0, 
          backgroundColor: 'transparent' 
        }} 
        bodyStyle={{ padding: 0 }}
      >
        {/* Row 1 - Total sélectionné */}
        <Row style={{ 
          borderBottom: '1px solid #d9d9d9', 
          borderTopLeftRadius: 8, 
          borderTopRightRadius: 8, 
          overflow: 'hidden' 
        }}>
          <Col span={14} style={{ backgroundColor: '#E0EDC2', padding: 8 }}>
            <Text>Total sélectionné</Text>
          </Col>
          <Col span={10} style={{ backgroundColor: '#ffffff', padding: 8, textAlign: 'right' }}>
            <Text>{totalSelected.toFixed(2)} €</Text>
          </Col>
        </Row>

        {/* Row 2 - Total Avoirs */}
        <Row style={{ borderBottom: '1px solid #d9d9d9', overflow: 'hidden', cursor: 'pointer' }} onClick={openModal}>
          <Col span={14} style={{ backgroundColor: '#E0EDC2', padding: 8 }}>
            <PlusCircleOutlined className="btn-add__creation-icon" style={{ marginRight: 6 }} />
            <Text>Total Avoirs</Text>
            <Badge count={avoirsCount} style={{ backgroundColor: '#f5222d', marginLeft: 8 }} />
          </Col>
          <Col span={10} style={{ backgroundColor: '#ffffff', padding: 8, textAlign: 'right' }}>
            <Text>{totalAvoirs.toFixed(2)} €</Text>
          </Col>
        </Row>

        {/* Row 3 - Total */}
        <Row style={{ borderBottom: '1px solid #d9d9d9', overflow: 'hidden' }}>
          <Col span={14} style={{ backgroundColor: '#E0EDC2', padding: 8 }}>
            <Text>Total</Text>
          </Col>
          <Col span={10} style={{ backgroundColor: '#ffffff', padding: 8, textAlign: 'right' }}>
            <Text strong>{(totalSelected + totalAvoirs).toFixed(2)} €</Text>
          </Col>
        </Row>

        {/* Row 4 - Montant facturé */}
        <Row style={{ 
          overflow: 'hidden', 
          borderBottomLeftRadius: 8, 
          borderBottomRightRadius: 8 
        }}>
          <Col span={14} style={{ padding: 8 }}>
            <Text strong>Montant facturé</Text>
          </Col>
          <Col span={10} style={{ backgroundColor: '#ffffff', padding: 8, textAlign: 'right' }}>
            <Title level={5} style={{ margin: 0 }}>
              <Text strong>{invoiceAmount.toFixed(2)} €</Text>
            </Title>
          </Col>
        </Row>
      </Card>


      <Modal
        title="Ajouter un avoir"
        open={modalVisible}
        onCancel={() => setModalVisible(false)}
        width={680}
        footer={null}    
      >
        <div style={{ display: 'flex', flexDirection: 'column', gap: 24 }}>
        {/* FORM - all fields on one line */}
        <Form
          form={form}
          layout="vertical"
          initialValues={{ amount: 0 }}
          style={{ display: 'flex', flexWrap: 'wrap', gap: '16px'}}
        >
          <Form.Item
            name="amount"
            label="Montant HT de l'avoir (€)"
            rules={[{ required: true, message: 'Veuillez saisir un montant' }]}
            style={{ flex: 1, minWidth: 200 }}
          >
            <InputNumber
              min={0}
              style={{ width: '100%' }}
              formatter={value => `${value}`.replace(/\B(?=(\d{3})+(?!\d))/g, ' ')}
            />
          </Form.Item>

          <Form.Item name="avoirNumber" label="Numéro de l'avoir" style={{ flex: 1, minWidth: 200 }}>
            <Input placeholder="ajouter un numéro avoir" />
          </Form.Item>

          <Form.Item style={{ alignSelf: 'end' }}>
            <Space>
              <Button type="primary" onClick={handleAddAvoir}>
                Ajouter
              </Button>
            </Space>
          </Form.Item>
        </Form>

        {/* TABLE BELOW */}
        {avoirDetails.length > 0 && (
          <Table
            dataSource={avoirDetails}
            columns={columns}
            pagination={false}
            rowKey="id"
          />
        )}

        {/* CARD AT THE BOTTOM */}
        <Row justify="end">
        <Card
          bordered
          style={{
            width: '60%',
            backgroundColor: 'transparent',
            justifyContent: 'right',
          }}
          bodyStyle={{ padding: 0 }}
        >
          <Row
            style={{
              borderBottom: '1px solid #d9d9d9',
              overflow: 'hidden',
              cursor: 'pointer',
            }}
            onClick={openModal}
          >
            <Col span={10} style={{ backgroundColor: '#E0EDC2', padding: 8 }}>
              <Text>Total Avoirs</Text>
            </Col>
            <Col span={10} style={{ backgroundColor: '#ffffff', padding: 8, textAlign: 'right' }}>
              <Text>{totalAvoirs.toFixed(2)} €</Text>
            </Col>
          </Row>
        </Card>
        </Row>
        </div>
      </Modal>

    </>
  );
};

export default InvoiceSummary;
