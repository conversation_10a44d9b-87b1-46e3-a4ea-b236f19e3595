import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PriceFamily, Option, ProductTypeRegul, Price } from 'models';
import React, { useState } from 'react';
import { usePriceFamilyRequest, useOptionsRequest, usePricesRequest } from 'hooks';
import { CatalogPriceZone, Loading, TarifType } from 'types';
import { useProductTypeRegulRequest } from 'hooks/productTypeRegulHook';
import useStateCallback from 'hooks/useStateCallback';
import { ParameterTarifLine } from '../ProductCell/PrestationCell';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

export type CatalogPriceZonesType = {
  tarifType: TarifType | null;
  countServiceProvider?: number;
  countServiceProviderForfait?: number;
  forfait?: CatalogPriceZone[];
  semiforfait?: CatalogPriceZone[];
} | null;
export const DocumentTableRowContext = React.createContext<{
  options: Option[];
  priceFamilies: PriceFamily[];
  catalogPriceZones: CatalogPriceZonesType;
  catalogPriceZonesLoading: boolean;
  priceFamiliesLoading: Loading;
  optionsLoading: Loading;
  prices: Price[];
  pricesLoading: Loading;
  productTypeReguls: ProductTypeRegul[];
  productTypeRegulsLoading: Loading;
  parameterTarifs: ParameterTarifLine[];
  fetchPriceFamilies: (params: { [key: string]: string }) => void;
  fetchOptions: (params: { [key: string]: string }) => void;
  setCatalogPriceZones: (state: CatalogPriceZonesType, cb?: (state: CatalogPriceZonesType) => void) => void;
  setCatalogPriceZonesLoading: (catalogPriceZonesLoading: boolean) => void;
  fetchProductTypeReguls: (params: { [key: string]: string }) => void;
  setParameterTarifs: React.Dispatch<React.SetStateAction<ParameterTarifLine[]>>;
  fetchPrices: (params: { [key: string]: string }) => void;
}>({
  options: [],
  priceFamilies: [],
  catalogPriceZones: null,
  catalogPriceZonesLoading: false,
  parameterTarifs: [],
  priceFamiliesLoading: 'idle',
  optionsLoading: 'idle',
  productTypeReguls: [],
  productTypeRegulsLoading: 'idle',
  prices: [],
  pricesLoading: 'idle',
  fetchPriceFamilies: () => {},
  fetchOptions: () => {},
  fetchProductTypeReguls: () => {},
  fetchPrices: () => {},
  setCatalogPriceZones: () => {},
  setCatalogPriceZonesLoading: () => {},
  setParameterTarifs: () => {},
});

const ProductRow = ({ children, ...props }: RowProps) => {
  const { attributes, setNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });
  const [priceFamilies, priceFamiliesLoading, fetchPriceFamilies] = usePriceFamilyRequest();
  const [options, optionsLoading, fetchOptions] = useOptionsRequest();
  const [prices, pricesLoading, fetchPrices] = usePricesRequest();
  const [productTypeReguls, productTypeRegulsLoading, fetchProductTypeReguls] = useProductTypeRegulRequest();
  const [catalogPriceZones, setCatalogPriceZones] = useStateCallback<CatalogPriceZonesType>(null);
  const [catalogPriceZonesLoading, setCatalogPriceZonesLoading] = useState<boolean>(false);
  const [parameterTarifs, setParameterTarifs] = useState<ParameterTarifLine[]>([]);

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 1000 } : {}),
  };

  return (
    <DocumentTableRowContext.Provider
      value={{
        options,
        priceFamilies,
        prices,
        pricesLoading,
        catalogPriceZones,
        catalogPriceZonesLoading,
        priceFamiliesLoading,
        optionsLoading,
        parameterTarifs,
        productTypeReguls,
        productTypeRegulsLoading,
        setParameterTarifs,
        fetchPriceFamilies,
        fetchOptions,
        fetchProductTypeReguls,
        fetchPrices,
        setCatalogPriceZones,
        setCatalogPriceZonesLoading,
      }}
    >
      <tr {...props} ref={setNodeRef} style={style} {...attributes}>
        {React.Children.map(children, (child) => {
          // if ((child as React.ReactElement).key === 'sort') {
          //   return React.cloneElement(child as React.ReactElement, {
          //     children: <SortHandle setActivatorNodeRef={setActivatorNodeRef} listeners={listeners}/>,
          //   });
          // }
          return child;
        })}
      </tr>
    </DocumentTableRowContext.Provider>
  );
};

export default ProductRow;
