import { PlusCircleOutlined } from '@ant-design/icons';
import { arrayMove, SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { DndContext, DragEndEvent } from '@dnd-kit/core';
import { Button, Card, Checkbox, Col, Divider, Form, FormInstance, Row, Space, Spin, Table } from 'antd';
import { useEffect, useMemo, useRef, useState, useImperativeHandle, forwardRef } from 'react';
import { restrictToVerticalAxis } from '@dnd-kit/modifiers';
import ProductRow from './ProductRow';
import ProductCell, { ProductCellRef } from './ProductCell';
import { ClientContact, DocumentProductLine, Product, ProductLineDevis, Quotation } from 'models';
import { ProductLineDevisType } from 'models/product_lines_devis';
import { v4 as uuidv4 } from 'uuid';
import { useOptionTypes, useOptionTypesQuery, usePriceTypesQuery, usePriceTypes } from 'hooks';
import { checkHeaderNameDuplicates, formSetValue, frenchCurrencyFormat } from 'utils';
import { DEFAULT_TAX, DOCUMENT_TYPES } from 'utils/constant';
import { UrbanCenterZoneIds } from 'types';
import { productService } from 'services';

type EditableTableProps = Parameters<typeof Table>[0];

export type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

export interface DevisProductsRef {
  refreshCatalogPrice: (args: {
    zoneIds: UrbanCenterZoneIds;
    isChangePrice?: boolean;
    isDocumentCatalog: boolean | null;
  }) => Promise<void>;
  updateIsCatalog: (isCatalog: boolean | null) => void;
}

interface DevisProductsProps {
  form: FormInstance;
  data?: Quotation;
  disabled?: boolean;
  status?: string;
  documentType: string;
  contact?: ClientContact | null;
  urbanCenterZoneIds?: UrbanCenterZoneIds;
  loading?: boolean;
  isDocumentCatalog?: boolean | null;
  isLoaded?: boolean;
  setIsLoaded?: React.Dispatch<React.SetStateAction<boolean>>;
  setSubTotal?: React.Dispatch<React.SetStateAction<number>>;
}
const DevisProducts = forwardRef<DevisProductsRef, DevisProductsProps>(
  (
    {
      form,
      data,
      disabled = false,
      status,
      documentType,
      contact,
      urbanCenterZoneIds,
      loading = false,
      isDocumentCatalog,
      setIsLoaded,
      setSubTotal,
    },
    ref,
  ) => {
    const { document } = data ?? {};
    const [dataSource, setDataSource] = useState<ProductLineDevis[]>([]);
    const [optionTypesQuery] = useOptionTypesQuery();
    const [optionTypes] = useOptionTypes(optionTypesQuery);
    const [priceTypesQuery] = usePriceTypesQuery();
    const [priceTypes] = usePriceTypes(priceTypesQuery);
    const productCellRefs = useRef<{ [key: string]: ProductCellRef | null }>({});
    const [isFirstLoading, setIsFirstLoading] = useState(false);
    const [convertLoading, setConvertLoading] = useState(false);
    const [taxPercentage, setTaxPercentage] = useState(DEFAULT_TAX);
    const subTotal = useMemo(() => {
      const rs = dataSource.reduce((total, secondItem) => {
        return total + (secondItem?.total || 0);
      }, 0);
      if (setSubTotal) {
        setSubTotal(rs);
      }
      return rs;
    }, [dataSource]);
    const lineHeaders = useMemo(
      () => dataSource?.filter((i) => i?.creationType === 'header' || i?.creationType === 'header-multi-product'),
      [dataSource],
    );
    useEffect(() => {
      if (lineHeaders?.length > 1) {
        checkHeaderNameDuplicates(form);
      }
    }, [lineHeaders?.length]);
    const refreshCatalogPrice = async (args: {
      zoneIds: UrbanCenterZoneIds;
      isChangePrice?: boolean;
      isDocumentCatalog: boolean | null;
    }) => {
      const { zoneIds, isChangePrice, isDocumentCatalog } = args;
      const promises = dataSource.map((row) => {
        const pRef = productCellRefs.current[(row.id || row.uuid) as string];
        if (pRef) {
          return pRef.refreshCatalogPrice({
            zoneIds,
            isChangePrice,
            isDocumentCatalog: isDocumentCatalog,
          });
        }
        return Promise.resolve();
      });
      await Promise.all(promises);
    };

    const getProducts = async (ids: (string | number | undefined)[]) => {
      try {
        const data = await productService.getProducts({
          'id[]': JSON.stringify(ids),
          limit: 'unlimited',
          orderBy: 'createdAt,desc|name',
          exclude: 0,
          isActive: 1,
          isVisible: 1,
        });
        return data?.rows || [];
      } catch (error) {
        console.log('get products: ', error);
      }
    };

    const updateIsCatalog = (isCatalog: boolean | null) => {
      setDataSource((prev) => prev.map((item) => ({ ...item, isCatalog })));
    };

    useImperativeHandle(ref, () => ({
      refreshCatalogPrice,
      updateIsCatalog,
    }));

    useEffect(() => {
      if (contact) {
        setTaxPercentage(contact?.taxPercentage || DEFAULT_TAX);
      }
    }, [contact]);

    useEffect(() => {
      if (document) {
        if (loading) {
          setIsFirstLoading(true);
        }
        convertDocument();
      }
    }, [document]);
    const convertDocument = async () => {
      try {
        setConvertLoading(true);
        const convertEstimate: ProductLineDevis = {
          ...document,
          lineItems: [...(document?.DocumentProductLines || [])],
        };
        let lineOrderNumber = 0;
        const listMainProductIds = [
          ...new Set(document?.DocumentProductLines?.flatMap((item) => item?.mainProductId)?.filter(Boolean) || []),
        ];
        const mainProducts = await getProducts(listMainProductIds);
        let parentKey: string | undefined = '';
        await getProducts(document?.DocumentProductLines?.flatMap((item) => item?.productId) || []).then((products) => {
          convertEstimate.lineItems = ((convertEstimate?.lineItems ?? []) as (DocumentProductLine & ProductLineDevis)[])
            ?.sort((a, b) => (a?.lineOrderNumber as number) - (b?.lineOrderNumber as number))
            .flatMap((item: ProductLineDevis) => {
              const result: ProductLineDevis[] = [];
              lineOrderNumber++;
              if (item?.headerName) {
                const headerItem: ProductLineDevis = {
                  uuid: item?.booksProductLineHeaderId ?? uuidv4(),
                  lineOrderNumber,
                  headerName: item?.headerName,
                  booksProductLineHeaderId: item?.booksProductLineHeaderId,
                  creationType: 'header',
                };
                if (item?.mainProductId) {
                  const mainProduct = mainProducts?.find((p) => String(p.id) === String(item?.mainProductId));
                  headerItem.creationType = 'header-multi-product';
                  headerItem.productNameForClient = item?.headerName;
                  headerItem.product = mainProduct;
                  parentKey = headerItem?.uuid;
                }
                result.push(headerItem);
                lineOrderNumber++;
              }

              let productItem = {
                uuid: uuidv4(),
                ...item,
              };
              if (item?.productId) {
                if (item?.mainProductId && item?.parentProductLineId) {
                  productItem.creationType = 'multi-product';
                  productItem.parentKey = parentKey;
                } else {
                  productItem.creationType = 'product';
                }
                const product = products?.find((i) => i.id === item?.productId) as Product;
                const priceFamilyValue = item?.DocumentProductLinePrices?.find(
                  (price) => price.documentProductLineId === item?.id && !price?.priceId && !price?.priceOptionId,
                );
                const listPriceOptionHasValuePrice = item?.DocumentProductLinePrices?.filter(
                  (priceOption) =>
                    priceOption.documentProductLineId === item?.id &&
                    (priceOption?.priceId || priceOption?.priceOptionId),
                ).map((priceOption) => {
                  const matchedPrice = item?.DocumentProductLinePrices?.find(
                    (itemPrice) => priceOption?.priceId === itemPrice?.priceId && !itemPrice?.priceOptionId,
                  );
                  return matchedPrice
                    ? {
                        ...priceOption,
                        priceValue: matchedPrice.value,
                        priceMargin: matchedPrice.priceMargin,
                        buyingPrice: matchedPrice.buyingPrice,
                        validSP: matchedPrice.validSP,
                        linePriceId: matchedPrice.id,
                      }
                    : priceOption;
                });

                productItem = {
                  ...productItem,
                  product: product,
                  interventionId: item.interventionId,
                  interventionKey: item.interventionKey,
                  productTypeId: product?.productTypeId,
                  priceFamilyValue: priceFamilyValue?.value,
                  priceFamilyPriceMargin: priceFamilyValue?.priceMargin,
                  priceFamilyBuyingPrice: priceFamilyValue?.buyingPrice,
                  priceFamilyValidSP: priceFamilyValue?.validSP,
                  listPriceOptions: listPriceOptionHasValuePrice,
                  priceFamilyIdOriginal: item?.priceFamilyId,
                  linePriceFamilyId: priceFamilyValue?.id,
                  lineOrderNumber,
                };
              }

              result.push(productItem);
              return result;
            });
          console.log('line 234:', convertEstimate?.lineItems);
          setDataSource((convertEstimate?.lineItems ?? []) as ProductLineDevis[]);
        });
        if (document?.rappelDevis) {
          const relanceDevis = document?.rappelDevis?.includes('Relance devis');
          const tacheRappel = document?.rappelDevis?.includes('Rappel');
          // Set each field individually
          form.setFieldValue('relance_devis_automatique', relanceDevis);
          form.setFieldValue('tache_de_rappel', tacheRappel);
        }
      } catch (error) {
        console.log('convertDocument: ', error);
      } finally {
        setConvertLoading(false);
        setIsFirstLoading(false);
        if (document?.DocumentProductLines && document?.DocumentProductLines?.length > 0) {
          setTimeout(() => {
            setIsLoaded?.(true);
          }, 1000);
        }
      }
    };
    const updateDataSource = (data: ProductLineDevis, uuid?: string) => {
      setDataSource((prev) => prev.map((item) => (item.uuid === uuid ? { ...item, ...data } : item)));
    };

    const defaultColumns: (ColumnTypes[number] & {
      editable?: boolean;
      dataIndex: string;
    })[] = [
      {
        key: 'sort',
        // title: 'Sort',
        width: 75,
        dataIndex: 'id',
        editable: true,
      },
      {
        width: 500,
        title: 'Produit/ Description',
        dataIndex: 'name',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: {
                colSpan: 6,
              },
            };
          }
        },
      },
      {
        width: 400,
        title: 'Prestation',
        dataIndex: 'prestation',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: {
                colSpan: 0,
              },
            };
          }
        },
      },
      {
        width: 100,
        title: 'Quantité',
        dataIndex: 'quantity',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: {
                colSpan: 0,
              },
            };
          }
        },
      },
      {
        width: 140,
        title: 'Sous-total',
        dataIndex: 'sub-total',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: { colSpan: 0 },
            };
          }
        },
      },
      {
        width: 150,
        title: 'Remise',
        dataIndex: 'discount',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: {
                colSpan: 0,
              },
            };
          }
        },
      },
      {
        width: 120,
        title: 'Total',
        dataIndex: 'total',
        fixed: 'right',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: { colSpan: 0 },
            };
          }
        },
      },
      {
        width: 75,
        title: 'Action',
        dataIndex: 'action',
        fixed: 'right',
        editable: true,
        render: (_, record: ProductLineDevis) => {
          if (record.creationType === 'header' || record.creationType === 'header-multi-product') {
            return {
              props: { colSpan: 1 },
            };
          }
        },
      },
    ];

    const columns = defaultColumns.map((col) => {
      if (!col.editable) {
        return col;
      }
      return {
        ...col,
        onCell: (record: ProductLineDevis) => ({
          record,
          status,
          documentType,
          editable: col.editable,
          dataIndex: col.dataIndex,
          form,
          isDocumentCatalog,
          title: col.title,
          dataSource,
          setDataSource,
          formtype: 'estimate',
          updateDataSource,
          onDelete: handleDelete,
          ref: (el: ProductCellRef) => {
            if (productCellRefs.current) {
              productCellRefs.current[(record.id || record.uuid) as string] = el;
            }
          },
          dataList: {
            optionTypes,
            priceTypes,
            urbanCenterZoneIds,
          },
        }),
      };
    });
    const handleDelete = (uuid?: string) => {
      try {
        if (!uuid) return;
        const targetRow = dataSource.find((i) => i.uuid === uuid);
        if (!targetRow) return;
        form.setFieldValue([`lineItems`, uuid], undefined);
        const children = dataSource.filter((i) => i.parentKey === uuid);
        const newData = dataSource.filter((i) => i.uuid !== uuid && !children.some((child) => child.uuid === i.uuid));
        formSetValue(form);
        setDataSource(newData);
      } catch (error) {
        console.error('handleDelete: ', error);
      }
    };

    const handleChangeDataSwap = (index: ProductLineDevis, changeIndex: number) => {
      form.setFieldsValue({
        lineItems: {
          [`${index?.id && index?.creationType === 'header' ? index?.id : index?.uuid}`]: {
            lineOrderNumber: changeIndex,
          },
        },
      });
    };
    // Helper function to move items to a new position
    const moveItems = (
      prevDataSource: ProductLineDevis[],
      itemsToMove: ProductLineDevis[],
      targetItem: ProductLineDevis,
      insertAfter = true,
    ) => {
      const filteredData = prevDataSource.filter((item) => !itemsToMove.includes(item));
      const insertIndex = filteredData.findIndex((item) => item?.uuid === targetItem?.uuid);
      filteredData.splice(insertIndex + (insertAfter ? 1 : 0), 0, ...(itemsToMove || []));
      return filteredData;
    };
    const handleDragEnd = ({ active, over }: DragEndEvent) => {
      // If the active and over elements are the same, do nothing.
      if (!over || active.id === over.id) return;
      // Get the current data source.
      const prevDataSource = [...(dataSource || [])];
      // Get the index of the active element.
      const activeIndex = prevDataSource?.findIndex((item) => item.uuid === active.id);
      // Get the index of the over element.
      const overIndex = prevDataSource?.findIndex((item) => item.uuid === over.id);
      // If either of the indexes is -1, do nothing.
      if (activeIndex === -1 || overIndex === -1) return;
      // Update the data source by moving the active element to the over element's index.
      let newData = prevDataSource;
      const activeItem = prevDataSource[activeIndex];
      const overItem = prevDataSource[overIndex];
      const children = prevDataSource.filter((item) => item.parentKey === activeItem.uuid);
      if (activeItem?.parentKey && overItem?.parentKey && overItem?.parentKey === activeItem.parentKey) {
        newData = arrayMove(prevDataSource, activeIndex, overIndex);
      } else if (!activeItem?.parentKey && !overItem?.parentKey) {
        const childrenOverItem = prevDataSource.filter((item) => item.parentKey === overItem.uuid);
        if (children?.length > 0) {
          newData = moveItems(prevDataSource, [activeItem, ...children], overItem, activeIndex < overIndex);
        } else {
          if (childrenOverItem?.length > 0) {
            newData = moveItems(prevDataSource, [overItem, ...childrenOverItem], activeItem, activeIndex > overIndex);
          } else {
            newData = arrayMove(prevDataSource, activeIndex, overIndex);
          }
        }
      } else if (!activeItem?.parentKey && overItem?.parentKey) {
        if (children?.length > 0) {
          const itemsToMove = [activeItem, ...children];
          const filteredData = prevDataSource.filter((item) => !itemsToMove.includes(item));
          const insertIndex = filteredData.findIndex((item) => item.uuid === overItem.uuid);
          filteredData.splice(insertIndex + 1, 0, ...itemsToMove);
          newData = filteredData;
        } else {
          const overGroupItems = prevDataSource.filter((item) => item.parentKey === overItem.parentKey);
          const lastItemInOverGroup = overGroupItems[overGroupItems.length - 1];
          if (overItem.uuid === lastItemInOverGroup.uuid && activeIndex < overIndex) {
            newData = arrayMove(prevDataSource, activeIndex, overIndex);
          }
        }
      }
      // Update the index of all elements in the data source from the minimum index to the maximum index.
      const newDataSource = newData.map((item, index) => {
        handleChangeDataSwap(item, index);
        return {
          ...item,
          lineOrderNumber: index,
        };
      });
      // Update the state with the new data source.
      setDataSource(newDataSource);
    };
    let lineOrderNumber = 0;
    const handleAddNewRow = (creationType: ProductLineDevisType) => {
      if (dataSource && dataSource.length > 0) {
        lineOrderNumber = (Number(dataSource[dataSource.length - 1]?.lineOrderNumber) || 0) + 1;
      } else {
        lineOrderNumber = 1;
      }
      let newRow: ProductLineDevis = {
        uuid: uuidv4(),
        creationType,
        lineOrderNumber,
      };
      if (creationType !== 'header') {
        newRow = {
          ...newRow,
          total: 0,
          creationType,
          quantity: 1,
          discount: 0,
          isCatalog: isDocumentCatalog,
          description: '',
          lineOrderNumber,
        };
      }
      setDataSource([...dataSource, newRow]);
    };

    const handleRedirectPrixResearch = () => {
      window.open('/search/commercial', '_blank');
    };
    return (
      <>
        <section className='mt-2 section quotation'>
          <Spin spinning={convertLoading}>
            <>
              {!loading && !isFirstLoading && (
                <DndContext modifiers={[restrictToVerticalAxis]} onDragEnd={handleDragEnd}>
                  <SortableContext
                    items={dataSource.map((item) => item.uuid ?? '')}
                    strategy={verticalListSortingStrategy}
                  >
                    <Table
                      scroll={{ x: 1300 }}
                      bordered
                      rowKey={'uuid'}
                      components={{
                        body: {
                          row: ProductRow,
                          cell: ProductCell,
                        },
                      }}
                      rowClassName={(record) => (record.creationType === 'header' ? 'quotation-title-line' : '')}
                      dataSource={dataSource ?? []}
                      columns={columns as ColumnTypes}
                      pagination={false}
                    />
                  </SortableContext>
                </DndContext>
              )}
            </>
          </Spin>
          <Row justify='space-between' className='mt-8'>
            <Space direction='horizontal' align='start' className='quotation__button-block'>
              {!disabled && (
                <>
                  <div className='btn-add__creation'>
                    <Button
                      size='large'
                      type='primary'
                      className={`btn-add__creation-button`}
                      onClick={() => handleAddNewRow('product')}
                      style={{
                        width: 'auto',
                        height: '32px',
                        borderRadius: '6px',
                      }}
                    >
                      <PlusCircleOutlined className='btn-add__creation-icon' />
                      <span className='btn-add__creation-text'>Ajouter un article du catalogue</span>
                    </Button>
                  </div>
                  <div className='btn-add__creation'>
                    <Button
                      size='large'
                      type='primary'
                      className={`btn-add__creation-button`}
                      onClick={() => handleAddNewRow('header')}
                      style={{
                        width: 'auto',
                        height: '32px',
                        borderRadius: '6px',
                      }}
                    >
                      <PlusCircleOutlined className='btn-add__creation-icon' />
                      <span className='btn-add__creation-text'>Ajouter une en-tête</span>
                    </Button>
                  </div>
                  <div className='btn-add__creation'>
                    <Button
                      size='large'
                      type='primary'
                      className='btn-add__filled-creation-button'
                      onClick={() => handleRedirectPrixResearch()}
                      style={{
                        height: '32px',
                        borderRadius: '6px',
                        background: '#A6C84D',
                      }}
                    >
                      <PlusCircleOutlined className='btn-add__creation-icon' />
                      <span className='btn-add__creation-text'>Recherche de prix</span>
                    </Button>
                  </div>
                </>
              )}
            </Space>
            <Space direction='vertical'>
              <Space direction='horizontal' align='start' className='quotation__subtotal-block'>
                <Card bordered={false} className='quotation__subtotal-card' style={{ width: '300px' }}>
                  <Row>
                    <Col className='quotation__subtotal-left-col' style={{ borderTopLeftRadius: '8px' }}>
                      Sous-total
                    </Col>
                    <Col className='quotation__subtotal-right-col' style={{ borderTopRightRadius: '8px' }}>
                      {`${frenchCurrencyFormat((Number(subTotal) || 0).toFixed(2))}`} €
                    </Col>
                  </Row>
                  <Row>
                    <Col className='quotation__subtotal-left-col'>
                      TVA ({`${frenchCurrencyFormat((Number(taxPercentage) || 0).toFixed(0))}`}%)
                    </Col>
                    <Col className='quotation__subtotal-right-col'>
                      {`${frenchCurrencyFormat(((Number(subTotal) || 0) * (taxPercentage / 100)).toFixed(2))}`} €
                    </Col>
                  </Row>
                  <Divider
                    style={{
                      borderBlockStart: '1px solid rgba(0, 0, 0, 0.25)',
                      margin: 0,
                    }}
                  />
                  <Row>
                    <Col className='quotation__subtotal-left-col' style={{ borderBottomLeftRadius: '8px' }}>
                      <span className='quotation__total-text'>Total</span>
                    </Col>
                    <Col className='quotation__subtotal-right-col' style={{ borderBottomRightRadius: '8px' }}>
                      <span className='quotation__total-text'>
                        {`${frenchCurrencyFormat(((Number(subTotal) || 0) + (Number(subTotal) || 0) * (taxPercentage / 100)).toFixed(2))}`}{' '}
                        €
                      </span>
                    </Col>
                  </Row>
                </Card>
              </Space>
              {documentType === DOCUMENT_TYPES.QUOTATION && (
                <Space direction='horizontal'>
                  <Space style={{ gap: '0px' }} direction='vertical' align='start'>
                    <Form.Item
                      name='relance_devis_automatique'
                      initialValue={false}
                      valuePropName='checked'
                      className='mb-0'
                    >
                      <Checkbox disabled={disabled}>
                        <label style={{ color: 'black' }}>Relance devis automatique (J+2 et J+5)</label>
                      </Checkbox>
                    </Form.Item>
                    <Form.Item name='tache_de_rappel' initialValue={false} valuePropName='checked'>
                      <Checkbox disabled={disabled}>
                        <label style={{ color: 'black' }}>Tâche de rappel à J+2</label>
                      </Checkbox>
                    </Form.Item>
                  </Space>
                </Space>
              )}
            </Space>
          </Row>
        </section>
      </>
    );
  },
);
DevisProducts.displayName = 'DevisProducts';
export default DevisProducts;
