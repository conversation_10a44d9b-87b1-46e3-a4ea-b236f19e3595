import { useEffect, useState } from 'react';
import { Form, Input, Select, Modal, Button } from 'antd';
import { toast } from 'react-toastify';
import { clientContactPersonService } from 'services';
import { ClientContactPerson, Sale } from 'models';
import { useAppSelector } from 'store';
import { selectClientContact } from 'store/slices/client_contact.slice';
import { AxiosResponse } from 'axios';
import { ZOHO_SYNC_STATUS } from '../../utils/constant';

interface CreateClientContactProps {
  isOpenModal: boolean;
  setIsOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  sales: Sale[];
  /* eslint-disable-next-line */
  getClientContact: Function;
  /* eslint-disable-next-line */
  setContactPersonCreateId: Function;
  lastModifySale?: Sale;
}
const CreateClientContact = (props: CreateClientContactProps) => {
  const { isOpenModal, setIsOpenModal, sales, getClientContact, lastModifySale, setContactPersonCreateId } = props;
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const contact = useAppSelector(selectClientContact);

  useEffect(() => {
    form.resetFields();
    setIsLoading(false);
  }, [isOpenModal]);

  const handleSubmit = async () => {
    try {
      const vaild = await form.validateFields();
      if (vaild) {
        setIsLoading(true);
        const values = form.getFieldsValue();
        values.contactId = contact?.id;
        values.crmContactId = contact?.crmContactId;
        values.booksContactPersonsId = null;
        values.zohoApiClientId = lastModifySale?.zohoApiClientId;
        values.zohoApiConnection = lastModifySale?.zohoApiConnection;
        console.log(values);
        await clientContactPersonService
          .createContactPerson(values)
          .then(async (response: AxiosResponse<ClientContactPerson>) => {
            if (response) {
              setIsOpenModal(false);
              console.log('createContactPerson: ', response);
              setContactPersonCreateId((await response)?.id);
              await getClientContact((await response)?.Contact?.id);
              setIsLoading(false);
              await checkZohoSyncStatus((await response)?.id);
            }
          })
          .catch((error) => {
            console.log(error);
            setIsLoading(false);
            toast.error(
              <div>
                Erreur
                <br />
                {error}
              </div>,
            );
          });
      }
    } catch (error) {
      console.error('Validation error:', error);
    }
  };

  const checkZohoSyncStatus = async (contactPersonId: number | undefined): Promise<void> => {
    if (contactPersonId) {
      return new Promise((resolve, reject) => {
        let attempts = 0;
        const maxAttempts = 10;

        const checkStatus = async () => {
          try {
            if (attempts >= maxAttempts) {
              toast.error(
                "Un problème de synchronisation est survenu lors de la création du client sur ZOHO. Veuillez contacter l'administrateur pour obtenir de l'aide.",
              );
              resolve();
              return;
            }

            attempts++;
            const response = await clientContactPersonService.findContactPerson(contactPersonId, {});

            if (response.zohoSyncStatus === ZOHO_SYNC_STATUS.IN_PROGRESS || response.zohoSyncStatus === null) {
              setTimeout(checkStatus, 7000);
            } else if (response.zohoSyncStatus === ZOHO_SYNC_STATUS.FAILED) {
              toast.error(
                "Un problème de synchronisation est survenu lors de la création du client sur ZOHO. Veuillez contacter l'administrateur pour obtenir de l'aide.",
              );
              resolve();
            } else if (response.zohoSyncStatus === ZOHO_SYNC_STATUS.SUCCESS) {
              resolve();
            }
          } catch (error) {
            console.error('Error checking sync status:', error);
            reject(error);
          }
        };

        checkStatus();
      });
    }
  };

  return (
    <Modal
      title='AJOUTER UN CONTACT'
      open={isOpenModal}
      onOk={() => setIsOpenModal(false)}
      onCancel={() => setIsOpenModal(false)}
      maskClosable={false}
      centered
      footer={[
        <Button key='annuler' onClick={() => setIsOpenModal(false)} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          onClick={handleSubmit}
          loading={isLoading}
          className='ant-modal-content__add-btn'
        >
          Ajouter
        </Button>,
      ]}
    >
      <div className='create-contact-form'>
        <Form form={form} layout='vertical' autoComplete='off'>
          <Form.Item name='firstName' label='Prénom'>
            <Input />
          </Form.Item>
          <Form.Item name='lastName' label='Nom' rules={[{ required: true }]}>
            <Input />
          </Form.Item>
          <Form.Item name='email' label='Email' rules={[{ required: true, type: 'email' }]}>
            <Input />
          </Form.Item>
          <Form.Item name='phone' label='Téléphone'>
            <Input />
          </Form.Item>
          <Form.Item label='Référent' name='referent' rules={[{ required: true }]}>
            <Select
              placeholder='Sélectionner'
              showSearch
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={sales?.map((option) => ({
                value: option.id,
                label: option.name,
              }))}
            />
          </Form.Item>
        </Form>
      </div>
    </Modal>
  );
};
export default CreateClientContact;
