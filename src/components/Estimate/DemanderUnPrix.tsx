import { DeleteTwoTone } from '@ant-design/icons';
import { Button, DatePicker, Form, Input, Modal, Radio, Select } from 'antd';
import { useEffect, useState } from 'react';
import {
  ACCESS_PL,
  DEFAULT_TYPE_DE_DEMANDE,
  DOCUMENT_STATUSES,
  DOCUMENT_TYPES,
  LIST_CHARGEMENT_PAR_LE_CLIENT,
  LIST_QUESTIONS,
  LIST_TYPE_DE_BESOIN,
  LIST_TYPE_DE_BESOIN_DD,
  LIST_TYPE_DE_DECHET,
  LIST_TYPE_DE_DECHET_DD,
  LIST_TYPE_DE_DEMANDE,
  LIST_VOLUME,
} from 'utils/constant';
import locale from 'antd/es/date-picker/locale/fr_FR';
import { toast } from 'react-toastify';
import { ClientContact, Demander, Quotation } from 'models';
import dayjs from 'dayjs';
import { DevisActionType } from 'types';
const { TextArea } = Input;

interface DemanderUnPrixProps {
  submit: (
    actionType: DevisActionType,
    value: Demander | null,
    isCopiedSubmit?: boolean,
    submitType?: string,
    submitStatus?: string,
  ) => void;
  isOpenModal: boolean;
  setIsOpenModal: React.Dispatch<React.SetStateAction<boolean>>;
  data: Quotation;
  lastModifyById?: string;
  contact: ClientContact | null;
  type?: string;
  showClientInput?: boolean;
}

const DemanderUnPrix = (props: DemanderUnPrixProps) => {
  const { isOpenModal, setIsOpenModal, lastModifyById, contact, type, submit, data, showClientInput } = props;
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [accesPLValue, setAccesPLValue] = useState(ACCESS_PL[0].value);
  const [chargementParLeClientValue, setChargementParLeClientValue] = useState(LIST_CHARGEMENT_PAR_LE_CLIENT[0].value);
  const [selectedTypeDeDemande, setSelectedTypeDeDemande] = useState(DEFAULT_TYPE_DE_DEMANDE);
  const [modalTitle, setModalTitle] = useState(DEFAULT_TYPE_DE_DEMANDE);
  const initData = () => {
    try {
      form?.setFieldsValue({
        ville_cp: data?.chantierAddress ? data?.chantierAddress?.formattedAddress : '',
        d_partement: data?.chantierAddress
          ? data?.chantierAddress?.postalcode
            ? data?.chantierAddress?.postalcode?.substring(0, 2)
            : data?.chantierAddress?.postalCode?.substring(0, 2)
          : '',
      });
    } catch (error) {
      console.log(error);
    }
  };
  useEffect(() => {
    form.resetFields();
    initData();
    setIsLoading(false);
    if (!isOpenModal) {
      setSelectedTypeDeDemande(DEFAULT_TYPE_DE_DEMANDE);
      setModalTitle(DEFAULT_TYPE_DE_DEMANDE);
    }
  }, [isOpenModal]);

  useEffect(() => {
    const selectedOption = LIST_TYPE_DE_DEMANDE.find((option) => option.value === selectedTypeDeDemande);
    setModalTitle(selectedOption?.name ?? DEFAULT_TYPE_DE_DEMANDE);
  }, [selectedTypeDeDemande]);

  const handleSubmit = async () => {
    try {
      const vaild = await form.validateFields();
      if (vaild) {
        setIsLoading(true);
        try {
          let values: Demander = form.getFieldsValue();
          values = {
            ...values,
            date_du_besoin: dayjs(values.date_du_besoin).format('YYYY-MM-DD'),
            owner: lastModifyById || '',
            compte: (contact?.crmAccountId ?? contact?.crmContactId) || '',
          };
          console.log(values);
          if (type === 'updateEstimate') {
            await submit('demander', values, false, DOCUMENT_TYPES?.QUOTATION, DOCUMENT_STATUSES.DRAFT.status);
          } else if (type === 'createEstimate') {
            await submit('demander', values, false, DOCUMENT_TYPES?.QUOTATION, DOCUMENT_STATUSES.DRAFT.status);
          }
          setIsLoading(false);
          setIsOpenModal(false);
        } catch (error) {
          console.log(error);
          setIsLoading(false);
          toast.error(
            <div>
              Erreur
              <br />
              {(error as Error)?.message ?? ''}
            </div>,
          );
        }
      }
    } catch (error) {
      setIsLoading(false);
      console.error('Validation error:', error);
    }
  };

  return (
    <Modal
      title={modalTitle}
      open={isOpenModal}
      onOk={() => setIsOpenModal(false)}
      onCancel={() => setIsOpenModal(false)}
      maskClosable={false}
      centered
      className='modal-demander-un-prix'
      footer={[
        <Button key='annuler' onClick={() => setIsOpenModal(false)} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          onClick={handleSubmit}
          loading={isLoading}
          className='ant-modal-content__add-btn'
        >
          Envoyer
        </Button>,
      ]}
    >
      <Form form={form} autoComplete='off'>
        <div className='demander-un-prix-form'>
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Type de demande'
            name='type_de_demande'
            initialValue={DEFAULT_TYPE_DE_DEMANDE}
            rules={[{ required: true }]}
          >
            <Select
              placeholder='Type de demande'
              showSearch
              defaultValue={DEFAULT_TYPE_DE_DEMANDE}
              onChange={setSelectedTypeDeDemande}
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={LIST_TYPE_DE_DEMANDE?.map((option) => ({
                value: option.value,
                label: option.name,
              }))}
            />
          </Form.Item>
          {showClientInput && (
            <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Client'
            name='Client'
            rules={[{ required: false }]}
          >
            <Input placeholder='Client' />
          </Form.Item>)}
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Adresse complète'
            name='ville_cp'
            rules={[{ required: true }]}
          >
            <Input placeholder='Adresse complète' />
          </Form.Item>
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Département'
            name='d_partement'
            rules={[
              { required: true },
              () => ({
                validator(_, value) {
                  if (value && value.length <= 2 && value.length > 0) {
                    return Promise.resolve();
                  }
                  return Promise.reject();
                },
              }),
            ]}
          >
            <Input placeholder='Département' maxLength={2} />
          </Form.Item>
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Date du besoin'
            name='date_du_besoin'
            rules={[{ required: true }]}
          >
            <DatePicker placeholder='Sélectionner une date' locale={locale} style={{ width: '60%' }} />
          </Form.Item>
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Pro / Particulier'
            name='pro_particulier'
            rules={[{ required: true }]}
          >
            <Select
              placeholder='Pro / Particulier'
              showSearch
              filterOption={(input, option) =>
                ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
              }
              options={LIST_QUESTIONS?.map((option) => ({
                value: option.value,
                label: option.name,
              }))}
            />
          </Form.Item>
          {selectedTypeDeDemande !== DEFAULT_TYPE_DE_DEMANDE ? (
            <>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Type de besoin DD'
                name='type_de_besoin_DD'
                rules={[{ required: true }]}
              >
                <Select
                  placeholder='Type de besoin DD'
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_TYPE_DE_BESOIN_DD?.map((option) => ({
                    value: option.value,
                    label: option.name,
                  }))}
                />
              </Form.Item>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Typologie de déchet DD'
                name='typologie_de_d_chet_DD'
                rules={[{ required: true }]}
              >
                <Select
                  placeholder='Typologie de déchet DD'
                  showSearch
                  removeIcon={<DeleteTwoTone style={{ paddingLeft: 4, fontSize: 12 }} twoToneColor='#ff4d4f' />}
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_TYPE_DE_DECHET_DD?.map((option) => ({
                    value: option.value,
                    label: option.name,
                  }))}
                />
              </Form.Item>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Quantité estimée'
                name='quantit_estim_e'
                rules={[{ required: true }]}
              >
                <Input placeholder='Quantité estimée' />
              </Form.Item>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Dimension des plaques'
                name='dimension_des_plaques'
                rules={[{ required: true }]}
              >
                <Input placeholder='Dimension des plaques' />
              </Form.Item>
            </>
          ) : (
            <>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Type de besoin'
                name='type_de_besoin'
                rules={[{ required: true }]}
              >
                <Select
                  placeholder='Type de besoin'
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_TYPE_DE_BESOIN?.map((option) => ({
                    value: option.value,
                    label: option.name,
                  }))}
                />
              </Form.Item>
              <Form.Item
                className='ant-form-item-modal-horizontal'
                label='Typologie de déchet'
                name='typologie_de_d_chet'
                rules={[{ required: true }]}
              >
                <Select
                  placeholder='Typologie de déchet'
                  mode='multiple'
                  showSearch
                  removeIcon={<DeleteTwoTone style={{ paddingLeft: 4, fontSize: 12 }} twoToneColor='#ff4d4f' />}
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={LIST_TYPE_DE_DECHET?.map((option) => ({
                    value: option.value,
                    label: option.name,
                  }))}
                />
              </Form.Item>
            </>
          )}
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Accès PL'
            name='acc_s_pl'
            rules={[{ required: true }]}
            initialValue={ACCESS_PL[0].value}
          >
            <Radio.Group defaultValue={accesPLValue} onChange={(e) => setAccesPLValue(e.target.value)}>
              <Radio value={ACCESS_PL[0].value}>{ACCESS_PL[0].name}</Radio>
              <Radio value={ACCESS_PL[1].value} style={{ marginLeft: '100px' }}>
                {ACCESS_PL[1].value}
              </Radio>
            </Radio.Group>
          </Form.Item>
          {selectedTypeDeDemande === DEFAULT_TYPE_DE_DEMANDE ? (
            <Form.Item
              className='ant-form-item-modal-horizontal'
              label='Volumne'
              name='taille_de_la_benne'
              rules={[{ required: true }]}
            >
              <Select
                placeholder='Volumne'
                mode='multiple'
                showSearch
                removeIcon={<DeleteTwoTone style={{ paddingLeft: 4, fontSize: 12 }} twoToneColor='#ff4d4f' />}
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={LIST_VOLUME?.map((option) => ({
                  value: option.value,
                  label: option.name,
                }))}
              />
            </Form.Item>
          ) : (
            <Form.Item
              className='ant-form-item-modal-horizontal'
              label='Chargement par le client'
              name='chargement_par_le_client'
              rules={[{ required: true }]}
              initialValue={LIST_CHARGEMENT_PAR_LE_CLIENT[0].value}
            >
              <Radio.Group
                defaultValue={chargementParLeClientValue}
                onChange={(e) => setChargementParLeClientValue(e.target.value)}
              >
                <Radio value={LIST_CHARGEMENT_PAR_LE_CLIENT[0].value}>{LIST_CHARGEMENT_PAR_LE_CLIENT[0].name}</Radio>
                <Radio value={LIST_CHARGEMENT_PAR_LE_CLIENT[1].value} style={{ marginLeft: '100px' }}>
                  {LIST_CHARGEMENT_PAR_LE_CLIENT[1].value}
                </Radio>
              </Radio.Group>
            </Form.Item>
          )}
          <Form.Item
            className='ant-form-item-modal-horizontal'
            label='Commentaire'
            name='demande'
            rules={[{ required: true }]}
          >
            <TextArea placeholder='Saisissez votre commentaire' style={{ height: '100px', maxHeight: '100px' }} />
          </Form.Item>
        </div>
      </Form>
    </Modal>
  );
};
export default DemanderUnPrix;
