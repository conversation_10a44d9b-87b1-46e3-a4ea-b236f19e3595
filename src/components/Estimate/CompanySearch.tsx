import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { Space, Table, TableProps, Typography } from 'antd';
const { Text } = Typography;
type EditableTableProps = Parameters<typeof Table>[0];
export type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;
export interface CompanySearch {
  nom_entreprise: string;
  siren: string;
  siege?: {
    adresse_ligne_1: string;
    code_postal: string;
    ville: string;
  };
  domaine_activite: string;
  forme_juridique: string;
  date_creation: string;
  uuid: string;
}

export interface CompanyList {
  resultats: CompanySearch[];
  total: number;
  page: number;
}

export interface CompanySearchData {
  list_company: CompanyList;
}

interface SearchCompanyProps {
  onSelectCompany: (value: CompanySearch) => void;
  company: CompanySearchData | null;
  isLoading?: boolean;
}

const CompanySearch = (props: SearchCompanyProps) => {
  const { onSelectCompany, company, isLoading } = props;
  const resultats = company?.list_company?.resultats;
  const items = Array.isArray(resultats) ? resultats.map((item: CompanySearch) => item.uuid) : [];
  const columns = [
    {
      width: '18%',
      title: 'Société',
      dataIndex: 'company_name',
      render: (_: string, record: CompanySearch) => (
        <div style={{ minWidth: '120px' }}>
          <Space direction='vertical' align='baseline'>
            <Text>{record.nom_entreprise}</Text>
            <Text>{record.siren}</Text>
          </Space>
        </div>
      ),
    },
    {
      width: '30%',
      title: 'Adresse',
      dataIndex: 'company_address',
      render: (_: string, record: CompanySearch) => (
        <div>
          <Text>
            {record.siege?.adresse_ligne_1}, {record.siege?.code_postal} {record.siege?.ville}
          </Text>
        </div>
      ),
    },
    {
      width: '20%',
      title: "Domaine d'activité",
      dataIndex: 'company_domain',
      render: (_: string, record: CompanySearch) => (
        <div>
          <Text>{record.domaine_activite}</Text>
        </div>
      ),
    },
    {
      width: '20%',
      title: 'Forme juridique',
      dataIndex: 'company_forme_juridique',
      render: (_: string, record: CompanySearch) => (
        <div>
          <Text>{record.forme_juridique}</Text>
        </div>
      ),
    },
    {
      width: '12%',
      title: 'Date de création',
      dataIndex: 'company_date_created',
      render: (_: string, record: CompanySearch) => (
        <div>
          <Text>{record.date_creation}</Text>
        </div>
      ),
    },
  ] as TableProps<CompanySearch>['columns'];
  return (
    <>
      <Typography.Title level={3} style={{ textAlign: 'center', fontStyle: 'italic' }}>
        Proposition Sociétés existantes
      </Typography.Title>
      <SortableContext items={items} strategy={verticalListSortingStrategy}>
        <Table
          className='search-table'
          tableLayout='fixed'
          scroll={{ x: '100%' }}
          rowKey='uuid'
          dataSource={resultats}
          columns={columns}
          onRow={(record) => ({
            onClick: () => onSelectCompany(record),
          })}
          rowClassName='hover-cursor-pointer'
          loading={isLoading}
          style={{ width: '88vw' }}
        />
      </SortableContext>
    </>
  );
};

export default CompanySearch;
