import { useEffect, useState, useCallback } from 'react';
import {
  Button,
  Space,
  Modal,
  Table,
  Pagination,
  PaginationProps,
  Typography,
  Tag,
  Form,
  Col,
  Row,
  Input,
  Divider,
  Flex,
  TableProps,
  message,
} from 'antd';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { toast } from 'react-toastify';
import CompanySearch, { CompanySearchData } from './CompanySearch';
import { CLIENT_SEARCHING_MESSAGE, CONTACT_TYPES } from 'utils/constant';
import { Address } from 'types';
import { clientContactService, pappersService } from 'services';
import { ClientContact, ClientContactPerson, Company, Sale } from 'models';
import { AxiosResponse } from 'axios';
import type { SearchProps } from 'antd/es/input/Search';
import useDebounce from 'hooks/useDebounce';
import { v4 as uuidv4 } from 'uuid';
import { frenchCurrencyFormat } from 'utils';

const { Text } = Typography;
type EditableTableProps = Parameters<typeof Table>[0];
export type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

// interface Deal {
//   id: string;
//   adresse_compl_te: string;
//   deal_status?: string;
//   deal_value?: number;
//   start_date?: Date;
//   end_date?: Date;
//   client_id?: string;
// }

interface DuplicateClient {
  id: number;
  contact_id: number;
  crm_contact_id: string;
  name: string;
  siren: string;
  billing_addresses: Address[];
  site_addresses: Address[];
  client_type: string;
  sales?: string;
  contact_persons: ClientContactPerson[];
}

interface DuplicateClientProps {
  contact: ClientContact | null;
  open: boolean;
  setOpen: (value: number) => void;
  onSelectClient: (value: DuplicateClient) => void;
  setChangedClient: (value: DuplicateClient | null) => void;
  lastModifyById?: string | null;
  /* eslint-disable-next-line */
  getClientContact: Function;
  resetDataFieldFormClientChantier: () => void;
  lastModifySale?: Sale;
}

interface SearchParams {
  clientType?: string;
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  name?: string;
  siren?: string;
  service?: string;
  address_search?: string;
}

const DuplicateClient = (props: DuplicateClientProps) => {
  const {
    contact,
    open,
    setOpen,
    onSelectClient,
    setChangedClient,
    getClientContact,
    resetDataFieldFormClientChantier,
    lastModifySale,
  } = props;
  const clientTypeCurrent = CONTACT_TYPES.BUSINESS;
  const [dataSource, setDataSource] = useState<DuplicateClient[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [form] = Form.useForm();
  const [clientType, setClientType] = useState<string>(clientTypeCurrent);
  const [pagination, setPagination] = useState<{
    page: number;
    pageSize: number;
    total: number;
  }>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchValueObject, setSearchValueObject] = useState<SearchParams>();
  const [dataCompany, setDataCompany] = useState<CompanySearchData | null>(null);
  const [showModalPaper, setShowModalPaper] = useState<boolean>(false);
  const [showClientTable, setShowClientTable] = useState<boolean>(false);
  const [selectCompany, setSelectCompany] = useState<boolean>(false);
  const [selectAddressFacturation, setSelectAddressFacturation] = useState<Address>({});
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [companyDetail, setCompanyDetail] = useState<Company | null>(null);
  const initialValues = () => {
    setSearchValueObject({
      clientType: CONTACT_TYPES.BUSINESS,
      name: `*${contact?.name}*`,
    });
    setSearchValue('');
    setDataSource([]);
    setClientType(CONTACT_TYPES.BUSINESS);
    setPagination({
      page: 1,
      pageSize: 10,
      total: 0,
    });
    form.resetFields();
    form.setFieldValue('address_facturation', null);
    form.setFieldValue('name', contact?.name);
    setChangedClient(null);
  };

  const onSelectCompany = async (value: object) => {
    try {
      if (value) {
        setIsLoading(true);
        const dataCompany = value as Company;
        const dataCompanyDetail: Company | null = await pappersService.pappersGetCompanyDetail({
          siren: dataCompany?.siren,
        });
        const papperAddress = dataCompanyDetail?.etablissements[0];
        const address: Address = {
          address_id: uuidv4(),
          formattedAddress: papperAddress?.adresse_ligne_1,
          address: papperAddress?.adresse_ligne_1,
          postalcode: papperAddress?.code_postal,
          city: papperAddress?.pays,
          latitude: papperAddress?.latitude ?? '',
          longitude: papperAddress?.longitude ?? '',
        };
        setCompanyDetail(dataCompanyDetail);
        form.setFieldsValue({
          name: dataCompanyDetail?.nom_entreprise,
          siren: dataCompanyDetail?.siren,
          address_facturation: papperAddress?.adresse_ligne_1,
        });
        setSelectAddressFacturation(address);
        setSearchValueObject({
          ...searchValueObject,
          name: dataCompanyDetail?.nom_entreprise,
          siren: dataCompanyDetail?.siren,
          address_search: papperAddress?.adresse_ligne_1,
        });
        setSelectCompany(true);
        setDataCompany(null);
        setShowModalPaper(false);
      }
      setIsLoading(false);
    } catch (error) {
      console.error(error);
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setSelectCompany(false);
    setOpen(0);
    setSelectAddressFacturation({});
  };

  const handleRowClick = (record: DuplicateClient) => {
    setSelectCompany(false);
    setIsLoading(true);
    onSelectClient(record);
  };

  const onSearchSearchField: SearchProps['onSearch'] = async (value) => {
    if (value.trim() !== '') {
      setIsLoading(true);
      try {
        setSearchValueObject({
          firstName: `*${searchValue.trim()}*`,
          lastName: `*${searchValue.trim()}*`,
          phone: `*${searchValue.trim()}*`,
          email: `*${searchValue.trim()}*`,
        });
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    }
  };
  const searchClientWithParams = useDebounce(async () => {
    setIsLoading(true);
    setShowModalPaper(false);
    setShowClientTable(true);
    const requestParam = {
      ...searchValueObject,
      page: pagination.page.toString(),
      limit: pagination.pageSize.toString(),
      include: 'ContactPersons',
      orderBy: 'clientType,asc',
    };
    const valuesSearch = form.getFieldsValue();
    const { firstName, lastName, phone, email, address_facturation, name, siren } = valuesSearch;
    const shouldShowTableResultSearch = [
      firstName,
      lastName,
      phone,
      email,
      address_facturation,
      clientTypeCurrent === CONTACT_TYPES.BUSINESS && (name || siren),
    ].some((value) => value !== undefined && value?.toString().trim() !== '' && value !== null);
    // if (clientTypeCurrent === CONTACT_TYPES.BUSINESS) {
    //   // Prevent search if adding characters to a previously failed search
    //   if (lastFailedName && name?.startsWith(lastFailedName) && name.length >= lastFailedName.length) {
    //     setIsLoading(false);
    //     return;
    //   }
    //   if (lastFailedSiren && siren?.startsWith(lastFailedSiren) && siren.length >= lastFailedSiren.length) {
    //     setIsLoading(false);
    //     return;
    //   }
    // }

    if (shouldShowTableResultSearch) {
      message.destroy();
      message.loading(CLIENT_SEARCHING_MESSAGE.SEARCHING);
      const clients = await clientContactService.getContacts(requestParam);
      message.destroy();
      if (clients.count === 0) {
        message.warning(CLIENT_SEARCHING_MESSAGE.NO_EXISTING);
      }
      setPagination({
        ...pagination,
        total: clients?.count,
      });
      if (clients?.count === 0 && selectCompany) {
        setDataSource([]);
        setIsLoading(false);
        const vaild = await form.validateFields();
        if (!vaild) return;
        const values = form.getFieldsValue();
        let submitData: ClientContact | ClientContactPerson = {
          ...searchValueObject,
          firstName: values.firstName ?? '',
          lastName: values.lastName ?? '',
          crmOwnerId: lastModifySale?.crmUserId ?? '',
          crmOwnerName: lastModifySale?.name ?? '',
          clientType: clientType,
          phone: values.phone,
          email: values.email,
        };
        if (selectAddressFacturation?.address) {
          submitData = {
            ...submitData,
            billingAddress: {
              formattedAddress: selectAddressFacturation?.formattedAddress,
              address: selectAddressFacturation?.address,
              city: selectAddressFacturation?.city ?? '',
              country: selectAddressFacturation?.country ?? '',
              postalCode: selectAddressFacturation?.postalCode
                ? selectAddressFacturation?.postalCode
                : selectAddressFacturation?.postalcode || '',
              latitude: selectAddressFacturation?.latitude ?? '',
              longitude: selectAddressFacturation?.longitude ?? '',
            },
          };
        }
        if (clientType === CONTACT_TYPES.BUSINESS) {
          submitData = {
            ...submitData,
            name: values.name,
            service: values.service,
            siren: values.siren,
            enCompte: 'Paiement requis',
            companyDetail: companyDetail,
          };
        } else {
          submitData = {
            ...submitData,
            name: values.firstName + ' ' + values.lastName,
            service: values.service,
            siren: values.siren,
            enCompte: 'Paiement requis',
            companyDetail: companyDetail,
            modeDeRGlement1: 'Carte bancaire, Virement bancaire, Chèque (ordre : Ecodrop)',
          };
        }
        await clientContactService
          .createAccountContact({
            ...submitData,
            zohoApiClientId: lastModifySale?.zohoApiClientId,
            zohoApiConnection: lastModifySale?.zohoApiConnection,
          })
          .then(async (response: AxiosResponse<ClientContact | ClientContactPerson>) => {
            if (response) {
              setOpen(0);
              toast.success('Succès');
              handleCancel();
              resetDataFieldFormClientChantier();
              await getClientContact((await response)?.id);
            }
          })
          .catch((error) => {
            console.error('error:', error);
            throw error;
          });
      } else {
        const dataRows: DuplicateClient[] = [];
        clients.rows.forEach((client: ClientContact) => {
          dataRows.push({
            ...client,
            id: client.id ?? 0,
            contact_id: client?.id ?? 0,
            crm_contact_id: client?.crmContactId ?? '',
            name: client?.name ?? '',
            siren: client?.siren ?? '',
            billing_addresses:
              client.ContactAddresses?.map((contactAddress) =>
                contactAddress.Address && contactAddress.addressType == 'billing' ? contactAddress.Address : [],
              ) ?? [],
            site_addresses:
              client.ContactAddresses?.map((contactAddress) =>
                contactAddress.Address && contactAddress.addressType == 'site' ? contactAddress.Address : [],
              ) ?? [],
            client_type: client?.clientType ?? '',
            sales: client?.sales ?? '',
            contact_persons: client?.ContactPersons ?? [],
          });
        });
        setDataSource(dataRows);
        console.log(dataRows, 'dataRows');
      }
    } else {
      setDataSource([]);
      setPagination({
        page: 1,
        pageSize: 10,
        total: 0,
      });
    }
    setIsLoading(false);
  }, 150);

  useEffect(() => {
    if (open) {
      initialValues();
      onSearchSearchField?.(searchValue);
    }
  }, [open]);

  useEffect(() => {
    searchClientWithParams();
  }, [searchValueObject, pagination.page, pagination.pageSize]);

  // useEffect(() => {
  //   setSearchValueObject({
  //     firstName: `*${searchValue.trim()}*`,
  //     lastName: `*${searchValue.trim()}*`,
  //     phone: `*${searchValue.trim()}*`,
  //     email: `*${searchValue.trim()}*`,
  //     name: `*${searchValue.trim()}*`,
  //     siren: `*${searchValue.trim()}*`,
  //   });
  // }, [searchValue]);

  const onChangePage: PaginationProps['onChange'] = async (page: number, pageSize: number) => {
    setPagination({
      page: page,
      pageSize: pageSize,
      total: pagination.total,
    });
  };

  const onSearchCreateNewAccountFields = async (
    changedValues: {
      [key: string]: string;
    },
    allFields: {
      [key: string]: string;
    },
  ) => {
    if (changedValues?.clientType) return;
    const blockedFields = clientType === CONTACT_TYPES.BUSINESS ? ['email', 'lastName', 'phone', 'firstName'] : [];
    if (Object.keys(changedValues).some((field) => blockedFields.includes(field))) {
      return;
    }
    setSelectCompany(false);
    debouncedSearch(allFields);
  };

  const debounce = <T extends (...args: { [key: string]: string }[]) => void>(
    func: T,
    delay: number,
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: ReturnType<typeof setTimeout>;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  const debouncedSearch = useCallback(
    debounce(async (allFields: { [key: string]: string }) => {
      setIsLoading(true);
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const searchFields: any = {};
        for (const key in allFields) {
          if (key != 'clientType') {
            if (
              Object.prototype.hasOwnProperty?.call(allFields, key) &&
              allFields[key]?.trim() !== '' &&
              allFields[key] !== null &&
              allFields[key] !== undefined
            ) {
              searchFields[key] = `*${allFields[key].trim()}*`;
            }
          }
        }
        searchFields['clientType'] = CONTACT_TYPES.BUSINESS;
        setSearchValueObject(searchFields);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    }, 500),
    [],
  );

  const columns = [
    {
      width: '17%',
      title: 'Compte',
      dataIndex: 'name',
      render: (_: string, record: DuplicateClient) => {
        const clientTypeTag =
          record.client_type === CONTACT_TYPES.BUSINESS ? (
            <Tag color='geekblue' key={record.client_type}>
              Société
            </Tag>
          ) : record.client_type === CONTACT_TYPES.INDIVIDUEL ? (
            <Tag color='green' key={record.client_type}>
              Particulier
            </Tag>
          ) : null;
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {clientTypeTag}
              <Text>{record.name}</Text>
              <Text>{record.siren}</Text>
              {record.billing_addresses
                ?.filter((billingAddress: Address) => billingAddress.formattedAddress)
                .slice(0, 10)
                .map((billingAddress: Address) => (
                  <Text key={billingAddress.id}>{billingAddress.formattedAddress}</Text>
                ))}
              <Text>{record.sales}</Text>
            </Space>
          </div>
        );
      },
    },
    {
      width: '33%',
      title: 'Contacts',
      dataIndex: 'zoho_book_contact_persons',
      render: (_: string, record: DuplicateClient) => {
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {record.contact_persons?.map((clientContactPerson: ClientContactPerson) => (
                <Text key={clientContactPerson.id}>
                  {clientContactPerson.firstName ? clientContactPerson.firstName : ''}{' '}
                  {clientContactPerson.lastName ? clientContactPerson.lastName : ''} -{' '}
                  {clientContactPerson.phone ? clientContactPerson.phone : ''} -{' '}
                  {clientContactPerson.email ? clientContactPerson.email : ''}
                </Text>
              ))}
            </Space>
          </div>
        );
      },
    },
    {
      width: '25%',
      title: 'Chantier',
      dataIndex: 'address',
      render: (_: string, record: DuplicateClient) => {
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {record.site_addresses
                ?.filter((billingAddress: Address) => billingAddress.formattedAddress)
                .slice(0, 10)
                .map((site_addresses: Address) => (
                  <Text key={site_addresses.id}>
                    {site_addresses.formattedAddress ? site_addresses.formattedAddress : ''}
                  </Text>
                ))}
              <Text>{record.sales}</Text>
            </Space>
          </div>
        );
      },
    },
    {
      width: '25%',
      title: 'Infos Compte',
      dataIndex: 'cf_en_compte',
      style: { background: '#fafafa' },
      render: (
        _: string,
        record: DuplicateClient & {
          enCompte: string;
          typeDeCompte: string;
          encoursDispo: string;
          encoursAut: string;
          estimates: string;
          estimatesM: string;
          sales: string;
          invoices: string;
          orders: string;
        },
      ) => {
        {
          let objectTypeDeCompte = null;
          if (record.typeDeCompte === 'Client actif') {
            objectTypeDeCompte = (
              <Tag color='blue' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          } else if (record.typeDeCompte === 'Prospect') {
            objectTypeDeCompte = (
              <Tag color='green' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          } else if (record.typeDeCompte === 'Client dormant') {
            objectTypeDeCompte = (
              <Tag color='orange' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          }
          const objectEnCompte =
            record.enCompte === 'En compte' ? (
              <Tag color='blue' key={record.enCompte}>
                {record.enCompte}
              </Tag>
            ) : record.enCompte === 'Paiement requis' ? (
              <Tag color='orange' key={record.enCompte}>
                {record.enCompte}
              </Tag>
            ) : null;
          const encoursDispoValue = parseFloat(record.encoursDispo);
          const encoursAutValue = parseFloat(record.encoursAut);

          const encoursDispoStyle = {
            color: encoursDispoValue < 0 ? 'red' : 'inherit',
            fontWeight: encoursDispoValue < 0 ? 'bold' : 'normal',
          };
          const encoursAutStyle = {
            color: encoursAutValue < 0 ? 'red' : 'inherit',
            fontWeight: encoursAutValue < 0 ? 'bold' : 'normal',
          };
          return (
            <div style={{ minWidth: '50px' }}>
              <div>
                {objectTypeDeCompte} {record.client_type === CONTACT_TYPES.BUSINESS ? objectEnCompte : ''}
              </div>
              <div className='mt-4'>
                <Space direction='vertical' align='baseline'>
                  {record.client_type === CONTACT_TYPES.BUSINESS && record.typeDeCompte !== 'Prospect' ? (
                    <>
                      <Text style={encoursDispoStyle}>
                        Encours dispo:{' '}
                        {record.encoursDispo ? `${frenchCurrencyFormat(Number(record.encoursDispo).toFixed(2))}` : 0} €
                      </Text>
                      <Text style={encoursAutStyle}>
                        Encours autorisé:{' '}
                        {record.encoursAut ? `${frenchCurrencyFormat(Number(record.encoursAut).toFixed(2))}` : 0} €
                      </Text>
                    </>
                  ) : (
                    ''
                  )}
                  <Text>Devis (m-1): {record.estimatesM}</Text>
                  {record.typeDeCompte !== 'Prospect' && (
                    <Text>
                      Facturée année: {record.sales ? `${frenchCurrencyFormat(Number(record.sales).toFixed(2))}` : 0} €
                    </Text>
                  )}
                  <Text>Nb devis: {record.estimates}</Text>
                  {record.typeDeCompte !== 'Prospect' && <Text>Nb commande: {record.invoices}</Text>}
                  {record.typeDeCompte !== 'Prospect' && <Text>Nb facture: {record.orders}</Text>}
                </Space>
              </div>
            </div>
          );
        }
      },
    },
  ] as TableProps<DuplicateClient>['columns'];

  return (
    <>
      <Modal
        zIndex={2000}
        className='client-search-modal'
        title='Contact Dupliqué'
        open={open}
        onCancel={handleCancel}
        centered
        width='90%'
        footer={[]}
      >
        <Flex vertical justify='center'>
          <Flex
            justify='center'
            style={{
              height: 'auto',
              transition: 'height 0.3s ease-in-out',
              overflow: 'hidden',
            }}
          >
            <section className='my-4 section client-chantier px-4'>
              <Form
                layout='horizontal'
                form={form}
                className='mb-2'
                style={{ minHeight: '50px' }}
                onValuesChange={(changedValues, allValues) => {
                  onSearchCreateNewAccountFields(changedValues, allValues);
                }}
                initialValues={{
                  clientType: CONTACT_TYPES.BUSINESS,
                }}
              >
                <Row gutter={80}>
                  <Col>
                    <Form.Item
                      name='name'
                      label='Société'
                      rules={[{ required: true, message: 'Ce champ est obligatoire!' }]}
                    >
                      <Input />
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </section>
          </Flex>
          {showModalPaper && dataCompany ? (
            <>
              <Divider className='horizontal-bar my-4' />
              <CompanySearch company={dataCompany} onSelectCompany={onSelectCompany} isLoading={isLoading} />
            </>
          ) : (
            <>
              {showClientTable && dataSource.length > 0 && (
                <>
                  <Divider className='horizontal-bar my-4' />
                  <Typography.Title level={3} style={{ textAlign: 'center', fontStyle: 'italic' }}>
                    Clients / Prospects
                  </Typography.Title>
                  <SortableContext items={dataSource.map((item) => item.id)} strategy={verticalListSortingStrategy}>
                    <Table
                      className='search-table'
                      tableLayout='fixed'
                      scroll={{ x: '100%' }}
                      rowKey={'uuid'}
                      dataSource={dataSource}
                      columns={columns}
                      onRow={(record) => ({
                        onClick: () => handleRowClick(record),
                      })}
                      rowClassName='hover-cursor-pointer'
                      loading={isLoading}
                      pagination={false}
                    />
                  </SortableContext>
                  {pagination.total > 0 && (
                    <Flex justify='space-between' className='my-4'>
                      <Text className='font-italic font-weight-light' role='status' aria-live='polite'>
                        {"Affichage de l'élement"} {(pagination?.page - 1) * pagination?.pageSize + 1} à{' '}
                        {pagination?.pageSize * pagination?.page > pagination?.total
                          ? pagination?.total
                          : pagination?.pageSize * pagination?.page}{' '}
                        sur {pagination?.total} éléments
                      </Text>
                      <Pagination
                        defaultCurrent={1}
                        total={pagination.total}
                        onChange={onChangePage}
                        current={pagination.page}
                      />
                    </Flex>
                  )}
                </>
              )}
            </>
          )}
          <Flex
            style={{
              height: '40px',
              overflow: 'hidden',
            }}
            className='button_create_cancel_search_client'
          >
            <Flex style={{ margin: '0 auto' }}>
              <Button
                key='annuler'
                onClick={() => {
                  form.resetFields();
                  setDataSource([]);
                  setClientType(CONTACT_TYPES.BUSINESS);
                  handleCancel();
                }}
                className='ant-modal-content__btn-color-dark mr-4'
              >
                {`Ce n'est pas une société en doublon, je continue mon devis`}
              </Button>
            </Flex>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
};
export default DuplicateClient;
