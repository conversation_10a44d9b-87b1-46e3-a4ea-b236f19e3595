/* eslint-disable @typescript-eslint/no-unused-vars */
import { Row, Col, Form, Input, Card, Space, FormInstance } from 'antd';
import { WarningOutlined } from '@ant-design/icons';
import { useEffect } from 'react';
import { Quotation, ClientContact } from '../../models';
import { DocumentTypes } from 'types';

const { TextArea } = Input;

const Logistique = ({
  type,
  form,
  data,
  contactData,
  disabled,
}: {
  type?: DocumentTypes;
  form: FormInstance;
  data?: Quotation;
  contactData: ClientContact | null | undefined;
  disabled?: boolean;
}) => {
  const { document } = data ?? {};
  useEffect(() => {
    form.setFieldsValue({
      objectDuDocument: document?.objectDuDocument ?? '',
    });
  }, [document]);
  return (
    <>
      <section className='mt-2 section logistique'>
        <Row gutter={[24, 0]}>
          <Col xs={24} sm={24} md={12} lg={12} xl={10} className='mb-6'>
            <Space direction='vertical' style={{ width: '100%' }} className='logistique__alert'>
              <WarningOutlined
                style={{
                  color: '#BD2D2C',
                  fontWeight: '600px',
                  fontSize: '32px',
                }}
              />
              <strong>
                Encours disponible:
                {contactData?.encoursDispo
                  ? ' ' + contactData.encoursDispo.toString().replace('€', '').replace(/\./g, ' ')
                  : ' 0 '}{' '}
                €
              </strong>
            </Space>
            <Form.Item
              label='Object du document'
              initialValue={document && document?.objectDuDocument}
              name='objectDuDocument'
              rules={[
                {
                  validator: (_, value) => {
                    if (value && /[<>]/.test(value)) {
                      return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <TextArea
                disabled={disabled}
                className='logistique__document-textarea'
                placeholder='Des détails à rajouter concernant cette commande ?'
                allowClear
                autoSize={{ minRows: 6, maxRows: 6 }}
                value={document && document?.objectDuDocument}
              />
            </Form.Item>
          </Col>
          <Col xs={24} sm={24} md={12} lg={12} xl={14} className='mb-6'>
            <Row gutter={[24, 0]} className=''>
              <Col xs={24} sm={24} md={12} lg={12} xl={12} className='mb-6 '>
                <Card bordered={false} className='logistique__card' title='Demande commerciale'>
                  {document && document?.demandeCommerciale && document.demandeCommerciale != 'null'
                    ? document.demandeCommerciale
                        ?.split('\n')
                        ?.map((subStr: string, index: number) => <p key={index}>{subStr}</p>)
                    : ''}
                </Card>
              </Col>
              <Col xs={24} sm={24} md={12} lg={12} xl={12} className='mb-6 '>
                <Card bordered={false} className='logistique__card' title='Réponse logistique'>
                  {document && document?.responseLogistique && document.responseLogistique != 'null'
                    ? document.responseLogistique
                        ?.split('\n')
                        ?.map((subStr: string, index: number) => <p key={index}>{subStr}</p>)
                    : ''}
                </Card>
              </Col>
            </Row>
          </Col>
        </Row>
      </section>
    </>
  );
};
export default Logistique;
