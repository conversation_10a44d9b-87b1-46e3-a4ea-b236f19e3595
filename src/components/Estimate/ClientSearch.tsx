import { useEffect, useState, useCallback } from 'react';
import {
  Button,
  Space,
  Modal,
  Table,
  Pagination,
  PaginationProps,
  Typography,
  Tag,
  Form,
  Col,
  Row,
  Select,
  Input,
  Radio,
  RadioChangeEvent,
  Divider,
  Flex,
  TableProps,
  message,
} from 'antd';
import { SortableContext, verticalListSortingStrategy } from '@dnd-kit/sortable';
import { toast } from 'react-toastify';
import CompanySearch, { CompanySearchData } from './CompanySearch';
import { CLIENT_SEARCHING_MESSAGE, CONTACT_TYPES, LIST_SERVICE, ZOHO_SYNC_STATUS } from 'utils/constant';
import SelectWithGoogleSuggestion from '../Common/SelectWithGoogleSuggestion';
import { Address } from 'types';
import { clientContactService, pappersService } from 'services';
import { ClientContact, ClientContactPerson, Company, Sale } from 'models';
import { AxiosResponse } from 'axios';
import type { SearchProps } from 'antd/es/input/Search';
import useDebounce from 'hooks/useDebounce';
import { v4 as uuidv4 } from 'uuid';
import { frenchCurrencyFormat } from 'utils';

const { Text } = Typography;
type EditableTableProps = Parameters<typeof Table>[0];
export type ColumnTypes = Exclude<EditableTableProps['columns'], undefined>;

// interface Deal {
//   id: string;
//   adresse_compl_te: string;
//   deal_status?: string;
//   deal_value?: number;
//   start_date?: Date;
//   end_date?: Date;
//   client_id?: string;
// }

interface ClientSearch {
  id: number;
  contact_id: number;
  crm_contact_id: string;
  name: string;
  siren: string;
  billing_addresses: Address[];
  site_addresses: Address[];
  client_type: string;
  sales?: string;
  contact_persons: ClientContactPerson[];
}

interface ClientSearchProps {
  open: boolean;
  onCreateContact: () => void;
  setOpen: (value: boolean) => void;
  onSelectClient: (value: ClientSearch) => void;
  setChangedClient: (value: ClientSearch | null) => void;
  lastModifyById?: string | null;
  /* eslint-disable-next-line */
  getClientContact: Function;
  resetDataFieldFormClientChantier: () => void;
  lastModifySale?: Sale;
}

interface SearchParams {
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  name?: string;
  siren?: string;
  service?: string;
  address_search?: string;
}

const ClientSearch = (props: ClientSearchProps) => {
  const {
    open,
    onCreateContact,
    setOpen,
    onSelectClient,
    setChangedClient,
    getClientContact,
    resetDataFieldFormClientChantier,
    lastModifySale,
  } = props;
  let clientTypeCurrent = CONTACT_TYPES.BUSINESS;
  const [dataSource, setDataSource] = useState<ClientSearch[]>([]);
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [isLoadingCompanyPappers, setIsLoadingCompanyPappers] = useState<boolean>(false);
  const [searchValue, setSearchValue] = useState<string>('');
  const [form] = Form.useForm();
  const [clientType, setClientType] = useState<string>(clientTypeCurrent);
  const [isSubmitCreateNewAccountOrContact, setIsSubmitCreateNewAccountOrContact] = useState<boolean>(false);
  const [pagination, setPagination] = useState<{
    page: number;
    pageSize: number;
    total: number;
  }>({
    page: 1,
    pageSize: 10,
    total: 0,
  });
  const [searchValueObject, setSearchValueObject] = useState<SearchParams>();
  const [isSimilarPapperName, setIsSimilarPapperName] = useState<boolean>(false);
  const [dataCompany, setDataCompany] = useState<CompanySearchData | null>(null);
  const [showModalPaper, setShowModalPaper] = useState<boolean>(false);
  const [showClientTable, setShowClientTable] = useState<boolean>(false);
  const [isUpdateAnything, setIsUpdateAnything] = useState<boolean>(false);
  const [selectCompany, setSelectCompany] = useState<boolean>(false);
  const [step2, setStep2] = useState<boolean>(false);
  const [selectAddressFacturation, setSelectAddressFacturation] = useState<Address>({});
  const [addressFacturation, setAddressFacturation] = useState<Address[]>([]);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [companyDetail, setCompanyDetail] = useState<Company | null>(null);
  const [lastFailedName, setLastFailedName] = useState<string | null>(null);
  const [lastFailedSiren, setLastFailedSiren] = useState<string | null>(null);
  const initialValues = () => {
    setSearchValueObject({});
    setSearchValue('');
    setDataSource([]);
    setClientType(CONTACT_TYPES.BUSINESS);
    setPagination({
      page: 1,
      pageSize: 10,
      total: 0,
    });
    form.resetFields();
    form.setFieldValue('address_facturation', null);
    setChangedClient(null);
  };

  const onChangeClientType = (e: RadioChangeEvent) => {
    setSelectCompany(false);
    setClientType(e.target.value);
    clientTypeCurrent = e.target.value;
  };

  const onSelectCompany = async (value: object) => {
    try {
      if (value) {
        setIsLoading(true);
        const values = form.getFieldsValue();
        const dataCompany = value as Company;
        const dataCompanyDetail: Company | null = await pappersService.pappersGetCompanyDetail({
          siren: dataCompany?.siren,
        });
        const papperAddress = dataCompanyDetail?.etablissements[0];
        const address: Address = {
          address_id: uuidv4(),
          formattedAddress: [
            papperAddress?.adresse_ligne_1,
            [papperAddress?.code_postal, papperAddress?.ville].filter(Boolean).join(' '),
          ]
            .filter(Boolean)
            .join(', '),
          address: papperAddress?.adresse_ligne_1,
          postalcode: papperAddress?.code_postal,
          city: papperAddress?.ville,
          country: papperAddress?.pays,
          latitude: papperAddress?.latitude ?? '',
          longitude: papperAddress?.longitude ?? '',
        };
        setCompanyDetail(dataCompanyDetail);
        form.setFieldsValue({
          name: dataCompanyDetail?.nom_entreprise,
          siren: dataCompanyDetail?.siren,
          address_facturation: papperAddress?.adresse_ligne_1,
        });
        setAddressFacturation([address]);
        setSelectAddressFacturation(address);
        setSearchValueObject({
          ...searchValueObject,
          name: dataCompanyDetail?.nom_entreprise,
          siren: dataCompanyDetail?.siren,
          address_search: papperAddress?.adresse_ligne_1,
        });
        setIsSimilarPapperName(values.name.toLowerCase() === dataCompanyDetail?.nom_entreprise.toLowerCase());
        setSelectCompany(true);
        setDataCompany(null);
        setShowModalPaper(false);
        setIsUpdateAnything(false);
      }
      setIsLoading(false);
    } catch (error) {
      console.error(error);
      setIsLoading(false);
    }
  };

  const handleSelectFacturationAddress = async (address: Address) => {
    console.log(address);
    setSelectCompany(false);
    setIsUpdateAnything(true);
    setSelectAddressFacturation(address);
    const existingAddress = addressFacturation.find((a) => a.address_id === address.address_id);
    if (!existingAddress) {
      setAddressFacturation((prevAddress) => [...prevAddress, address]);
    }
    setSearchValueObject({
      ...searchValueObject,
      address_search: address.formattedAddress,
    });
  };

  const checkZohoSyncStatus = async (contactId: number | undefined): Promise<void> => {
    if (contactId) {
      return new Promise((resolve, reject) => {
        let attempts = 0;
        const maxAttempts = 10;

        const checkStatus = async () => {
          try {
            if (attempts >= maxAttempts) {
              toast.error(
                "Un problème de synchronisation est survenu lors de la création du client sur ZOHO. Veuillez contacter l'administrateur pour obtenir de l'aide.",
              );
              resolve();
              return;
            }

            attempts++;
            const response = await clientContactService.findContact(contactId.toString(), {});

            if (response.zohoSyncStatus === ZOHO_SYNC_STATUS.IN_PROGRESS || response.zohoSyncStatus === null) {
              setTimeout(checkStatus, 7000);
            } else if (response.zohoSyncStatus === ZOHO_SYNC_STATUS.FAILED) {
              toast.error(
                "Un problème de synchronisation est survenu lors de la création du client sur ZOHO. Veuillez contacter l'administrateur pour obtenir de l'aide.",
              );
              resolve();
            } else if (response.zohoSyncStatus === 'success') {
              resolve();
            }
          } catch (error) {
            console.error('Error checking sync status:', error);
            reject(error);
          }
        };

        checkStatus();
      });
    }
  };

  const handleCreateNewContactOrAccount = async () => {
    try {
      const vaild = await form.validateFields();
      if (!vaild) return;
      setIsLoading(true);
      setIsSubmitCreateNewAccountOrContact(true);
      const values = form.getFieldsValue();
      let submitData: ClientContact | ClientContactPerson = {
        firstName: values.firstName ?? '',
        lastName: values.lastName ?? '',
        crmOwnerId: lastModifySale?.crmUserId ?? '',
        crmOwnerName: lastModifySale?.name ?? '',
        phone: values.phone,
        email: values.email,
        clientType: clientType,
      };
      if (selectAddressFacturation?.address) {
        submitData = {
          ...submitData,
          billingAddress: {
            formattedAddress: selectAddressFacturation?.formattedAddress,
            address: selectAddressFacturation?.address,
            city: selectAddressFacturation?.city ?? '',
            country: selectAddressFacturation?.country ?? '',
            postalCode: selectAddressFacturation?.postalCode
              ? selectAddressFacturation?.postalCode
              : selectAddressFacturation?.postalcode || '',
            latitude: selectAddressFacturation?.latitude ?? '',
            longitude: selectAddressFacturation?.longitude ?? '',
          },
        };
      }
      if (clientType === CONTACT_TYPES.BUSINESS) {
        submitData = {
          ...submitData,
          name: values.name,
          service: values.service,
          siren: values.siren,
          enCompte: 'Paiement requis',
          companyDetail: companyDetail,
        };
      } else {
        submitData = {
          ...submitData,
          name: (values.firstName || '') + ' ' + (values.lastName || ''),
          service: values.service,
          siren: values.siren,
          enCompte: 'Paiement requis',
          companyDetail: companyDetail,
          modeDeRGlement1: 'Carte bancaire, Virement bancaire, Chèque (ordre : Ecodrop)',
        };
      }
      await clientContactService
        .createAccountContact({
          ...submitData,
          zohoApiClientId: lastModifySale?.zohoApiClientId,
          zohoApiConnection: lastModifySale?.zohoApiConnection,
        })
        .then(async (response: AxiosResponse<ClientContact | ClientContactPerson>) => {
          if (response) {
            onCreateContact();
            setOpen(false);
            toast.success('Succès');
            handleCancel();
            resetDataFieldFormClientChantier();
            const clientContactId = (await response)?.id;
            await getClientContact(clientContactId);
            await checkZohoSyncStatus(clientContactId);
          }
        })
        .catch((error) => {
          console.error('error:', error);
          toast.error(
            `Erreur innatendue à la création du compte / contact, merci d'alerter l'équipe technique: <EMAIL>`,
          );
          throw error;
        });
      setIsLoading(false);
      setIsSubmitCreateNewAccountOrContact(false);
      setCompanyDetail(null);
      setStep2(false);
      setSelectCompany(false);
    } catch (error) {
      setIsLoading(false);
      setIsSubmitCreateNewAccountOrContact(false);
      setCompanyDetail(null);
      setStep2(false);
      setSelectCompany(false);
      console.error('Validation error:', error);
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const searchCompany = async (type?: string) => {
    try {
      if (!searchValueObject?.name) {
        message.destroy();
        message.warning(CLIENT_SEARCHING_MESSAGE.ENTER_REQUIREMENT);
      }
      if (searchValueObject?.name && clientType === CONTACT_TYPES.BUSINESS) {
        setIsLoadingCompanyPappers(true);
        message.destroy();
        message.loading(CLIENT_SEARCHING_MESSAGE.CUSTOMER_SEARCHING);
        const listCompanyInformation: CompanySearchData | null = await pappersService.pappersSearchListCompany({
          company_name: searchValueObject?.name,
          code_postal: selectAddressFacturation?.zip ? selectAddressFacturation?.zip : '',
        });
        if (listCompanyInformation) {
          setDataCompany(listCompanyInformation);
          switch (type) {
            case 'Nouvelle recherche':
              setShowModalPaper(true);
              setStep2(true);
              setIsUpdateAnything(false);
              break;
            default:
              if (listCompanyInformation?.list_company?.resultats?.length > 0) {
                setShowModalPaper(true);
                setStep2(true);
                setIsUpdateAnything(false);
              } else {
                setShowModalPaper(false);
                message.destroy();
                // message.loading(CLIENT_SEARCHING_MESSAGE.CLIENT_NOT_FOUND);
                handleCreateNewContactOrAccount();
              }
          }
        }
      } else if (clientType === CONTACT_TYPES.INDIVIDUEL) {
        message.destroy();
        message.loading(CLIENT_SEARCHING_MESSAGE.CUSTOMER_SEARCHING);
        handleCreateNewContactOrAccount();
      }
    } catch (error) {
      console.error(error);
      setIsLoadingCompanyPappers(false);
    } finally {
      setIsLoadingCompanyPappers(false);
    }
  };

  const handleCancel = () => {
    setSelectCompany(false);
    setStep2(false);
    setOpen(false);
    setSelectAddressFacturation({});
  };

  const handleRowClick = (record: ClientSearch) => {
    setSelectCompany(false);
    setStep2(false);
    setIsLoading(true);
    onSelectClient(record);
  };

  const onSearchSearchField: SearchProps['onSearch'] = async (value) => {
    if (value.trim() !== '') {
      setIsLoading(true);
      try {
        setSearchValueObject({
          firstName: `*${searchValue.trim()}*`,
          lastName: `*${searchValue.trim()}*`,
          phone: `*${searchValue.trim()}*`,
          email: `*${searchValue.trim()}*`,
        });
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    }
  };

  const handleAutoCreateAccountContact = async () => {
    const vaild = await form.validateFields();
    if (!vaild) return;
    setIsSubmitCreateNewAccountOrContact(true);
    const values = form.getFieldsValue();
    let submitData: ClientContact | ClientContactPerson = {
      ...searchValueObject,
      firstName: values.firstName ?? '',
      lastName: values.lastName ?? '',
      crmOwnerId: lastModifySale?.crmUserId ?? '',
      crmOwnerName: lastModifySale?.name ?? '',
      clientType: clientType,
      phone: values.phone,
      email: values.email,
    };
    if (selectAddressFacturation?.address) {
      submitData = {
        ...submitData,
        billingAddress: {
          formattedAddress: selectAddressFacturation?.formattedAddress,
          address: selectAddressFacturation?.address,
          city: selectAddressFacturation?.city ?? '',
          country: selectAddressFacturation?.country ?? '',
          postalCode: selectAddressFacturation?.postalCode
            ? selectAddressFacturation?.postalCode
            : selectAddressFacturation?.postalcode || '',
          latitude: selectAddressFacturation?.latitude ?? '',
          longitude: selectAddressFacturation?.longitude ?? '',
        },
      };
    }
    if (clientType === CONTACT_TYPES.BUSINESS) {
      submitData = {
        ...submitData,
        name: values.name,
        service: values.service,
        siren: values.siren,
        enCompte: 'Paiement requis',
        companyDetail: companyDetail,
      };
    } else {
      submitData = {
        ...submitData,
        name: (values.firstName || '') + ' ' + (values.lastName || ''),
        service: values.service,
        siren: values.siren,
        enCompte: 'Paiement requis',
        companyDetail: companyDetail,
        modeDeRGlement1: 'Carte bancaire, Virement bancaire, Chèque (ordre : Ecodrop)',
      };
    }
    await clientContactService
      .createAccountContact({
        ...submitData,
        zohoApiClientId: lastModifySale?.zohoApiClientId,
        zohoApiConnection: lastModifySale?.zohoApiConnection,
      })
      .then(async (response: AxiosResponse<ClientContact | ClientContactPerson>) => {
        if (response) {
          setOpen(false);
          setIsSubmitCreateNewAccountOrContact(false);
          toast.success('Succès');
          handleCancel();
          resetDataFieldFormClientChantier();
          setIsSimilarPapperName(false);
          await getClientContact((await response)?.id);
        }
      })
      .catch((error) => {
        console.error('error:', error);
        setIsSubmitCreateNewAccountOrContact(false);
        setIsSimilarPapperName(false);
        throw error;
      });
  };

  const searchClientWithParams = useDebounce(async () => {
    try {
      setIsLoading(true);
      setIsUpdateAnything(true);
      setShowModalPaper(false);
      setShowClientTable(true);
      const requestParam = {
        ...searchValueObject,
        page: pagination.page.toString(),
        limit: pagination.pageSize.toString(),
        include: 'ContactPersons',
        orderBy: 'clientType,asc',
      };
      const valuesSearch = form.getFieldsValue();
      const { firstName, lastName, phone, email, address_facturation, name, siren } = valuesSearch;
      const shouldShowTableResultSearch = [
        firstName,
        lastName,
        phone,
        email,
        address_facturation,
        clientTypeCurrent === CONTACT_TYPES.BUSINESS && (name || siren),
      ].some((value) => value !== undefined && value?.toString().trim() !== '' && value !== null);
      if (clientTypeCurrent === CONTACT_TYPES.BUSINESS && !isSimilarPapperName) {
        // Prevent search if adding characters to a previously failed search
        if (lastFailedName && name?.startsWith(lastFailedName) && name.length >= lastFailedName.length) {
          setIsLoading(false);
          return;
        }
        if (lastFailedSiren && siren?.startsWith(lastFailedSiren) && siren.length >= lastFailedSiren.length) {
          setIsLoading(false);
          return;
        }
      }
      if (shouldShowTableResultSearch && !isSimilarPapperName) {
        message.destroy();
        message.loading(CLIENT_SEARCHING_MESSAGE.SEARCHING);
        const clients = await clientContactService.getContacts(requestParam);
        message.destroy();
        if (clients.count === 0) {
          // message.warning(CLIENT_SEARCHING_MESSAGE.NO_EXISTING);
          if (clientTypeCurrent === CONTACT_TYPES.BUSINESS) {
            if (name) setLastFailedName(name);
            if (siren) setLastFailedSiren(siren);
          }
        } else if (clientTypeCurrent === CONTACT_TYPES.BUSINESS) {
          setLastFailedName(null);
          setLastFailedSiren(null);
        }
        setPagination({
          ...pagination,
          total: clients?.count,
        });
        if (clients?.count === 0 && selectCompany) {
          setDataSource([]);
          setIsLoading(false);
          await handleAutoCreateAccountContact();
        } else {
          const dataRows: ClientSearch[] = [];
          clients.rows.forEach((client: ClientContact) => {
            dataRows.push({
              ...client,
              id: client.id ?? 0,
              contact_id: client?.id ?? 0,
              crm_contact_id: client?.crmContactId ?? '',
              name: client?.name ?? '',
              siren: client?.siren ?? '',
              billing_addresses:
                client.ContactAddresses?.map((contactAddress) =>
                  contactAddress.Address && contactAddress.addressType == 'billing' ? contactAddress.Address : [],
                ) ?? [],
              site_addresses:
                client.ContactAddresses?.map((contactAddress) =>
                  contactAddress.Address && contactAddress.addressType == 'site' ? contactAddress.Address : [],
                ) ?? [],
              client_type: client?.clientType ?? '',
              sales: client?.sales ?? '',
              contact_persons: client?.ContactPersons ?? [],
            });
          });
          setDataSource(dataRows);
          console.log(dataRows, 'dataRows');
        }
      } else if (isSimilarPapperName) {
        setDataSource([]);
        setIsLoading(false);
        await handleAutoCreateAccountContact();
      } else {
        setDataSource([]);
        setPagination({
          page: 1,
          pageSize: 10,
          total: 0,
        });
      }
      setIsLoading(false);
    } catch (error) {
      console.log(error);
      setIsLoading(false);
    }
  }, 150);

  useEffect(() => {
    if (open) {
      initialValues();
      onSearchSearchField?.(searchValue);
    }
  }, [open]);

  useEffect(() => {
    if (searchValueObject && Object.keys(searchValueObject).length > 0) {
      searchClientWithParams();
    }
  }, [searchValueObject, pagination.page, pagination.pageSize]);

  // useEffect(() => {
  //   setSearchValueObject({
  //     firstName: `*${searchValue.trim()}*`,
  //     lastName: `*${searchValue.trim()}*`,
  //     phone: `*${searchValue.trim()}*`,
  //     email: `*${searchValue.trim()}*`,
  //     name: `*${searchValue.trim()}*`,
  //     siren: `*${searchValue.trim()}*`,
  //   });
  // }, [searchValue]);

  const onChangePage: PaginationProps['onChange'] = async (page: number, pageSize: number) => {
    setPagination({
      page: page,
      pageSize: pageSize,
      total: pagination.total,
    });
  };

  const onSearchCreateNewAccountFields = async (
    changedValues: {
      [key: string]: string;
    },
    allFields: {
      [key: string]: string;
    },
  ) => {
    if (changedValues?.clientType) return;
    const blockedFields = clientType === CONTACT_TYPES.BUSINESS ? ['email', 'lastName', 'phone', 'firstName'] : [];
    if (Object.keys(changedValues).some((field) => blockedFields.includes(field))) {
      return;
    }
    setSelectCompany(false);
    debouncedSearch(allFields);
  };

  const debounce = <T extends (...args: { [key: string]: string }[]) => void>(
    func: T,
    delay: number,
  ): ((...args: Parameters<T>) => void) => {
    let timeoutId: ReturnType<typeof setTimeout>;
    return (...args: Parameters<T>) => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        func(...args);
      }, delay);
    };
  };

  const debouncedSearch = useCallback(
    debounce(async (allFields: { [key: string]: string }) => {
      setIsLoading(true);
      try {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        const searchFields: any = {};
        for (const key in allFields) {
          if (key != 'clientType') {
            if (
              Object.prototype.hasOwnProperty?.call(allFields, key) &&
              allFields[key]?.trim() !== '' &&
              allFields[key] !== null &&
              allFields[key] !== undefined
            ) {
              searchFields[key] = `*${allFields[key].trim()}*`;
            }
          }
        }
        setSearchValueObject(searchFields);
        setIsLoading(false);
      } catch (error) {
        console.error(error);
        setIsLoading(false);
      }
    }, 500),
    [],
  );

  const columns = [
    {
      width: '17%',
      title: 'Compte',
      dataIndex: 'name',
      render: (_: string, record: ClientSearch) => {
        const clientTypeTag =
          record.client_type === CONTACT_TYPES.BUSINESS ? (
            <Tag color='geekblue' key={record.client_type}>
              Société
            </Tag>
          ) : record.client_type === CONTACT_TYPES.INDIVIDUEL ? (
            <Tag color='green' key={record.client_type}>
              Particulier
            </Tag>
          ) : null;
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {clientTypeTag}
              <Text>{record.name}</Text>
              <Text>{record.siren}</Text>
              {record.billing_addresses
                ?.filter((billingAddress: Address) => billingAddress.formattedAddress)
                .slice(0, 10)
                .map((billingAddress: Address) => (
                  <Text key={billingAddress.id}>{billingAddress.formattedAddress}</Text>
                ))}
              <Text>{record.sales}</Text>
            </Space>
          </div>
        );
      },
    },
    {
      width: '33%',
      title: 'Contacts',
      dataIndex: 'zoho_book_contact_persons',
      render: (_: string, record: ClientSearch) => {
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {record.contact_persons?.map((clientContactPerson: ClientContactPerson) => (
                <Text key={clientContactPerson.id}>
                  {clientContactPerson.firstName ? clientContactPerson.firstName : ''}{' '}
                  {clientContactPerson.lastName ? clientContactPerson.lastName : ''} -{' '}
                  {clientContactPerson.phone ? clientContactPerson.phone : ''} -{' '}
                  {clientContactPerson.email ? clientContactPerson.email : ''}
                </Text>
              ))}
            </Space>
          </div>
        );
      },
    },
    {
      width: '25%',
      title: 'Chantier',
      dataIndex: 'address',
      render: (_: string, record: ClientSearch) => {
        return (
          <div style={{ minWidth: '50px' }}>
            <Space direction='vertical' align='baseline'>
              {record.site_addresses
                ?.filter((billingAddress: Address) => billingAddress.formattedAddress)
                .slice(0, 10)
                .map((site_addresses: Address) => (
                  <Text key={site_addresses.id}>
                    {site_addresses.formattedAddress ? site_addresses.formattedAddress : ''}
                  </Text>
                ))}
              <Text>{record.sales}</Text>
            </Space>
          </div>
        );
      },
    },
    {
      width: '25%',
      title: 'Infos Compte',
      dataIndex: 'cf_en_compte',
      style: { background: '#fafafa' },
      render: (
        _: string,
        record: ClientSearch & {
          enCompte: string;
          typeDeCompte: string;
          encoursDispo: string;
          encoursAut: string;
          estimates: string;
          estimatesM: string;
          sales: string;
          invoices: string;
          orders: string;
        },
      ) => {
        {
          let objectTypeDeCompte = null;
          if (record.typeDeCompte === 'Client actif') {
            objectTypeDeCompte = (
              <Tag color='blue' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          } else if (record.typeDeCompte === 'Prospect') {
            objectTypeDeCompte = (
              <Tag color='green' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          } else if (record.typeDeCompte === 'Client dormant') {
            objectTypeDeCompte = (
              <Tag color='orange' key={record.typeDeCompte}>
                {record.typeDeCompte}
              </Tag>
            );
          }
          const objectEnCompte =
            record.enCompte === 'En compte' ? (
              <Tag color='blue' key={record.enCompte}>
                {record.enCompte}
              </Tag>
            ) : record.enCompte === 'Paiement requis' ? (
              <Tag color='orange' key={record.enCompte}>
                {record.enCompte}
              </Tag>
            ) : null;
          const encoursDispoValue = parseFloat(record.encoursDispo);
          const encoursAutValue = parseFloat(record.encoursAut);

          const encoursDispoStyle = {
            color: encoursDispoValue < 0 ? 'red' : 'inherit',
            fontWeight: encoursDispoValue < 0 ? 'bold' : 'normal',
          };
          const encoursAutStyle = {
            color: encoursAutValue < 0 ? 'red' : 'inherit',
            fontWeight: encoursAutValue < 0 ? 'bold' : 'normal',
          };
          return (
            <div style={{ minWidth: '50px' }}>
              <div>
                {objectTypeDeCompte} {record.client_type === CONTACT_TYPES.BUSINESS ? objectEnCompte : ''}
              </div>
              <div className='mt-4'>
                <Space direction='vertical' align='baseline'>
                  {record.client_type === CONTACT_TYPES.BUSINESS && record.typeDeCompte !== 'Prospect' ? (
                    <>
                      <Text style={encoursDispoStyle}>
                        Encours dispo:{' '}
                        {record.encoursDispo ? `${frenchCurrencyFormat(Number(record.encoursDispo).toFixed(2))}` : 0} €
                      </Text>
                      <Text style={encoursAutStyle}>
                        Encours autorisé:{' '}
                        {record.encoursAut ? `${frenchCurrencyFormat(Number(record.encoursAut).toFixed(2))}` : 0} €
                      </Text>
                    </>
                  ) : (
                    ''
                  )}
                  <Text>Devis (m-1): {record.estimatesM}</Text>
                  {record.typeDeCompte !== 'Prospect' && (
                    <Text>
                      Facturée année: {record.sales ? `${frenchCurrencyFormat(Number(record.sales).toFixed(2))}` : 0} €
                    </Text>
                  )}
                  <Text>Nb devis: {record.estimates}</Text>
                  {record.typeDeCompte !== 'Prospect' && <Text>Nb commande: {record.invoices}</Text>}
                  {record.typeDeCompte !== 'Prospect' && <Text>Nb facture: {record.orders}</Text>}
                </Space>
              </div>
            </div>
          );
        }
      },
    },
  ] as TableProps<ClientSearch>['columns'];

  return (
    <>
      <Modal
        className='client-search-modal'
        title='Gestion de compte'
        open={open}
        onCancel={handleCancel}
        centered
        width='90%'
        footer={[]}
        maskClosable={false}
      >
        <Flex vertical justify='center'>
          <Flex
            justify='center'
            style={{
              height: 'auto',
              transition: 'height 0.3s ease-in-out',
              overflow: 'hidden',
            }}
          >
            <section className='my-4 section client-chantier px-4'>
              <Form
                layout='horizontal'
                form={form}
                className='mb-2'
                style={{ minHeight: '240px' }}
                onValuesChange={(changedValues, allValues) => {
                  onSearchCreateNewAccountFields(changedValues, allValues);
                }}
                initialValues={{
                  clientType: CONTACT_TYPES.BUSINESS,
                }}
              >
                <Row gutter={[16, 0]}>
                  <Form.Item label='Type de client' name='clientType' rules={[{ required: true }]}>
                    <Radio.Group onChange={onChangeClientType} value={clientType}>
                      <Radio value={CONTACT_TYPES.BUSINESS}> Professionnel </Radio>
                      <Radio value={CONTACT_TYPES.INDIVIDUEL}> Particulier </Radio>
                    </Radio.Group>
                  </Form.Item>
                </Row>
                <Row gutter={80}>
                  <Col>
                    {clientType === CONTACT_TYPES.BUSINESS && (
                      <Form.Item
                        name='name'
                        label='Société'
                        rules={[{ required: true, message: 'Ce champ est obligatoire!' }]}
                      >
                        <Input />
                      </Form.Item>
                    )}
                    <Form.Item
                      name='lastName'
                      label='Nom'
                      rules={[{ required: true, message: 'Ce champ est obligatoire!' }]}
                    >
                      <Input />
                    </Form.Item>
                    <Form.Item
                      name='phone'
                      label='Téléphone'
                      rules={[{ required: true, message: 'Ce champ est obligatoire!' }]}
                    >
                      <Input />
                    </Form.Item>
                    <Form.Item name='address_facturation' label='Addresse facturation'>
                      <SelectWithGoogleSuggestion
                        defaultValue={selectAddressFacturation}
                        options={addressFacturation ?? []}
                        onSelect={handleSelectFacturationAddress}
                      />
                    </Form.Item>
                  </Col>
                  <Col>
                    {clientType === CONTACT_TYPES.BUSINESS && (
                      <Form.Item
                        name='siren'
                        label='SIREN'
                        // rules={[{ required: true, message: 'Ce champ est obligatoire!' }]}
                      >
                        <Input />
                      </Form.Item>
                    )}
                    <Form.Item name='firstName' label='Prénom'>
                      <Input />
                    </Form.Item>
                    <Form.Item
                      name='email'
                      label='Email'
                      rules={[
                        { required: true, message: 'Ce champ est obligatoire!' },
                        {
                          pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                          message: 'Veuillez entrer une adresse email valide!',
                        },
                      ]}
                    >
                      <Input />
                    </Form.Item>
                    {clientType === CONTACT_TYPES.BUSINESS && (
                      <Form.Item name='service' label='Service'>
                        <Select
                          options={LIST_SERVICE?.map((option) => ({
                            value: option.value,
                            label: option.name,
                          }))}
                        />
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Form>
            </section>
          </Flex>
          {showModalPaper && dataCompany ? (
            <>
              <Divider className='horizontal-bar my-4' />
              <CompanySearch company={dataCompany} onSelectCompany={onSelectCompany} isLoading={isLoading} />
            </>
          ) : (
            <>
              {showClientTable && dataSource.length > 0 && (
                <>
                  <Divider className='horizontal-bar my-4' />
                  <Typography.Title level={3} style={{ textAlign: 'center', fontStyle: 'italic' }}>
                    Clients / Prospects
                  </Typography.Title>
                  <SortableContext items={dataSource.map((item) => item.id)} strategy={verticalListSortingStrategy}>
                    <Table
                      className='search-table'
                      tableLayout='fixed'
                      scroll={{ x: '100%' }}
                      rowKey={'uuid'}
                      dataSource={dataSource}
                      columns={columns}
                      onRow={(record) => ({
                        onClick: () => handleRowClick(record),
                      })}
                      rowClassName='hover-cursor-pointer'
                      loading={isLoading}
                      pagination={false}
                    />
                  </SortableContext>
                  {pagination.total > 0 && (
                    <Flex justify='space-between' className='my-4'>
                      <Text className='font-italic font-weight-light' role='status' aria-live='polite'>
                        {"Affichage de l'élement"} {(pagination?.page - 1) * pagination?.pageSize + 1} à{' '}
                        {pagination?.pageSize * pagination?.page > pagination?.total
                          ? pagination?.total
                          : pagination?.pageSize * pagination?.page}{' '}
                        sur {pagination?.total} éléments
                      </Text>
                      <Pagination
                        defaultCurrent={1}
                        total={pagination.total}
                        onChange={onChangePage}
                        current={pagination.page}
                      />
                    </Flex>
                  )}
                </>
              )}
            </>
          )}
          <Flex
            style={{
              height: '40px',
              overflow: 'hidden',
            }}
            className='button_create_cancel_search_client'
          >
            <Flex style={{ margin: '0 auto' }}>
              <Button
                key='annuler'
                onClick={() => {
                  form.resetFields();
                  setStep2(false);
                  setDataSource([]);
                  setClientType(CONTACT_TYPES.BUSINESS);
                  handleCancel();
                }}
                className='ant-modal-content__cancel-btn mr-4'
              >
                Annuler
              </Button>
              {showModalPaper || step2 || clientType === CONTACT_TYPES.INDIVIDUEL ? (
                <>
                  {clientType === CONTACT_TYPES.INDIVIDUEL ? (
                    <Button
                      className='client-search__add-btn'
                      onClick={() => {
                        handleCreateNewContactOrAccount();
                      }}
                      loading={isSubmitCreateNewAccountOrContact}
                    >
                      Créer le contact
                    </Button>
                  ) : (
                    <Button
                      className='client-search__add-btn'
                      onClick={() => {
                        handleCreateNewContactOrAccount();
                      }}
                      loading={isSubmitCreateNewAccountOrContact}
                    >
                      Créer
                    </Button>
                  )}
                </>
              ) : (
                <Button
                  className='client-search__add-btn'
                  onClick={() => {
                    setShowClientTable(false);
                    searchCompany();
                  }}
                  disabled={isLoadingCompanyPappers}
                  loading={isLoadingCompanyPappers}
                >
                  Vérifier l’existence de la société sur Pappers
                </Button>
              )}
              {isUpdateAnything && step2 && (
                <Button
                  key='nouvelle recherche'
                  onClick={() => searchCompany('Nouvelle recherche')}
                  className='client-search__search-btn ml-4'
                >
                  Nouvelle recherche
                </Button>
              )}
            </Flex>
          </Flex>
        </Flex>
      </Modal>
    </>
  );
};
export default ClientSearch;
