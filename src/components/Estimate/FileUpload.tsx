import { UploadFile, UploadProps, Upload, Button } from 'antd';
import { UploadOutlined } from '@ant-design/icons';
import { useEffect, useState } from 'react';
import fileService from 'services/file.service';
import { documentService } from 'services';
import { FILE_UPLOAD_TYPES } from 'utils/constant';

export interface UploadFileCustom extends UploadFile {
  id?: number;
  status_file: string;
  file_name: string;
  keyFile: string;
  fileUrl: string;
  documentId?: string | number;
  dataBase64?: string | null;
}
export default function FileUpload({
  fileListProp,
  setFileListProp,
  salesorderId,
  isCopyForm,
}: {
  fileListProp: UploadFile[];
  setFileListProp: (fileList: UploadFileCustom[]) => void;
  salesorderId?: string | number;
  isCopyForm?: boolean;
}) {
  const [fileList, setFileList] = useState<UploadFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  // const convertBase64 = (file: File) => {
  //   return new Promise((resolve, reject) => {
  //     const fileReader = new FileReader();
  //     fileReader.readAsDataURL(file);
  //     fileReader.onload = () => {
  //       resolve(fileReader.result);
  //     };
  //     fileReader.onerror = (error) => {
  //       reject(error);
  //     };
  //   });
  // };

  const handleChange: UploadProps['onChange'] = async ({ fileList: newFileList }) => {
    setIsLoading(true);
    if (newFileList.length > 0) {
      const itemEnd = newFileList[newFileList.length - 1] as UploadFileCustom;
      if (itemEnd) {
        let indexNameFile = 0;
        if (newFileList.length > 1) {
          const itemGetIndex = newFileList[newFileList.length - 2] as UploadFileCustom;
          try {
            indexNameFile = parseInt(itemGetIndex.name.split('_')[2]) + 1;
          } catch (e) {
            console.log(e);
          }

          if (!indexNameFile) {
            indexNameFile = 0;
          }
        }
        itemEnd.status = 'done';
        itemEnd.file_name = 'BDC_Client_' + indexNameFile + '.pdf';
        itemEnd.status_file = 'NEW';
        const response = await fileService.uploadFileAdvanced(
          itemEnd.originFileObj as File,
          itemEnd.file_name,
          'application/pdf',
          10,
        );
        itemEnd.url = response.keyFile;
        itemEnd.name = response.fileUrl;
        // itemEnd.dataBase64 = itemEnd.originFileObj ? ((await convertBase64(itemEnd.originFileObj)) as string) : null;
        // Call API upload file
        const file_upload = [];
        file_upload.push(itemEnd);
        // check update order
        if (salesorderId && !isCopyForm) {
          try {
            const data = await documentService.uploadFileToSalesorder(
              {
                ...itemEnd,
                name: itemEnd.file_name,
                status_file: 'CREATE',
                type: FILE_UPLOAD_TYPES.BDC_CLIENT,
              },
              salesorderId,
            );
            newFileList[newFileList.length - 1] = {
              ...data,
              status_file: 'UPDATE',
            };
          } catch (error) {
            console.log(error);
          }
        }
      }
    }
    const dataListFile: UploadFileCustom[] = [];
    for (let index = 0; index < newFileList.length; index++) {
      const item = newFileList[index] as UploadFileCustom;
      dataListFile.push({
        ...item,
        uid: item.uid,
        name: item.file_name ? item.file_name.replace('.pdf', '') : item?.name?.replace('.pdf', ''),
        status_file: item.status_file == 'UPDATE' ? 'UPDATE' : 'NEW',
      });
    }
    setFileListProp(dataListFile);
    setIsLoading(false);
  };

  useEffect(() => {
    setFileList(fileListProp);
  }, [fileListProp]);
  const uploadButton = (
    <Button
      loading={isLoading}
      disabled={isLoading}
      className='ant-btn-white bnt-upload-file'
      icon={<UploadOutlined />}
    ></Button>
  );
  return (
    <>
      <Upload
        customRequest={({ file }) => console.log(file)}
        accept='.pdf'
        listType='picture'
        fileList={fileList}
        className='upload-list-inline'
        onChange={handleChange}
      >
        {uploadButton}
      </Upload>
    </>
  );
}
