import { Form, InputNumber, Select, Space } from 'antd';
import { RuleObject } from 'antd/es/form';
import { ProductLineDevis } from 'models';
import { CURRENCIES, DOCUMENT_STATUSES } from 'utils/constant';

interface DiscountCellProps {
  record: ProductLineDevis;
  onChangeDiscount: (discount: number) => void;
  onChangeDiscountUnit: (discountUnit: string) => void;
  status?: string;
}

const DiscountCell = (props: DiscountCellProps) => {
  const { record, onChangeDiscount, onChangeDiscountUnit, status } = props;
  if (record.creationType !== 'header') {
    return (
      <div className='text-center'>
        <Space direction='horizontal' align='baseline' className='space-remise'>
          <div className='remise-hover-input-and-select'>
            <Form.Item
              name={['lineItems', `${record.uuid}`, 'discount']}
              initialValue={record.discount || 0}
              rules={[
                { required: true },
                () => ({
                  validator(_: RuleObject, value) {
                    if (value === undefined || value === null || value === '') {
                      return Promise.reject();
                    }
                    const parsed = parseFloat(value);
                    if (isNaN(parsed)) {
                      return Promise.reject('Discount must be a number');
                    }
                    if (parsed < 0) {
                      return Promise.reject('Discount cannot be negative');
                    }
                    return Promise.resolve();
                  },
                }),
              ]}
            >
              <InputNumber
                className='text-right'
                formatter={(value) =>
                  value!
                    .toString()
                    .replace(/\s/g, '')
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                    .replace('.', ',')
                }
                controls={false}
                parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                onChange={(e) => onChangeDiscount(parseFloat(e || '0'))}
                disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
              />
            </Form.Item>
            <Form.Item
              name={['lineItems', `${record.uuid}`, 'discountUnit']}
              initialValue={record.discountUnit || CURRENCIES[0].value}
            >
              <Select
                className='unit-remise text-right'
                listItemHeight={32}
                options={CURRENCIES}
                onChange={(value) => onChangeDiscountUnit(value)}
                disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
              />
            </Form.Item>
          </div>
        </Space>
      </div>
    );
  }
  return <></>;
};

export default DiscountCell;
