import { DeleteOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, DatePicker, Form, FormInstance, Input, InputNumber, Row, Select, Spin, Typography } from 'antd';
import {
  Price,
  PriceFamily,
  ProductLineDevis,
  Option,
  OptionType,
  PriceType,
  PriceOption,
  DocumentProductLinePrestation,
  ProductTypeUnit,
} from 'models';
import frFR from 'antd/es/date-picker/locale/fr_FR';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import { dateFormat } from 'types';
import { v4 as uuidv4 } from 'uuid';
import { frenchCurrencyFormat } from 'utils';
import { DOCUMENT_STATUSES, DOCUMENT_TYPES, PRICE_TYPE_LOGICS, PRICE_TYPES, TARIF_TYPE } from 'utils/constant';
import { Fragment } from 'react';
const { Text } = Typography;

dayjs.locale('fr');

export interface PrestationDateLine {
  uuid: string;
  date: Date | null;
  timeSlotId: number | null;
  subOptionId: number | null;
}

export interface ParameterTarifLine {
  price: Price & { priceMargin?: number | null; validSP?: number | null; buyingPrice?: number | null };
  priceOptions: PriceOption[];
  value?: number;
}
export interface OptionPrestationType {
  value: number | undefined;
  label: string | undefined;
}
interface PrestationCellProps {
  form: FormInstance;
  record: ProductLineDevis;
  productTypeId?: number | null;
  priceFamilyId?: number | null;
  productTypeUnitId?: number;
  options: Option[];
  priceFamilies: PriceFamily[];
  loading: boolean;
  priceTypes: PriceType[];
  timeSlotOptionType?: OptionType;
  optionOptionType?: OptionType;
  onSelectPriceFamily: (priceFamilyId: number) => void;
  prestationDateList: DocumentProductLinePrestation[];
  onChangePrestationOptions: (uuid?: string | number) => void;
  onAddPrestationDateLine: (prestationDateItem: DocumentProductLinePrestation) => void;
  onRemovePrestationDateLine: (uuid?: string | number) => void;
  parameterTarifs: ParameterTarifLine[];
  unitPrice: number;
  onDateChange: (date: dayjs.Dayjs | null, uuid?: string | number) => void;
  onChangeTimeSlot: (timeSlot: string, uuid?: string | number) => void;
  onChangeSubOption: (subOptionLabel: string, uuid?: string | number) => void;
  status?: string;
  documentType?: string;
  productTypeUnits?: ProductTypeUnit[];
  isCatalog?: boolean | null;
}

interface FormValues {
  lineItems: {
    [key: string]: {
      prestation: {
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        tarifItems: any;
      };
    };
  };
}

const PrestationCell = (props: PrestationCellProps) => {
  const {
    form,
    record,
    productTypeId,
    priceFamilyId,
    productTypeUnitId,
    options,
    priceFamilies,
    loading,
    timeSlotOptionType,
    optionOptionType,
    onSelectPriceFamily,
    prestationDateList,
    onChangePrestationOptions,
    priceTypes,
    onAddPrestationDateLine,
    onRemovePrestationDateLine,
    parameterTarifs,
    unitPrice,
    onDateChange,
    status,
    documentType,
    productTypeUnits,
    onChangeTimeSlot,
    onChangeSubOption,
    isCatalog,
  } = props;
  const formValuesWatch = Form.useWatch<FormValues>([], form);
  if ((record.creationType === 'product' || record.creationType === 'multi-product') && record.productTypeId) {
    const timeSlots = options.filter(
      (option) => option.productTypeId === productTypeId && option.optionTypeId === timeSlotOptionType?.id,
    );
    const optionList = options.filter(
      (option) => option.productTypeId === productTypeId && option.optionTypeId === optionOptionType?.id,
    );
    const isPrestationOptionList = optionList.filter((option) => option.isPrestationOption);
    const notPrestationOptionList = optionList.filter((option) => !option.isPrestationOption);
    const selectedPriceFamilies = priceFamilies.filter((option) => option.productTypeId === productTypeId);
    return (
      <Spin spinning={loading}>
        {timeSlots.length > 0 && !!record?.quantity && record?.quantity > 0 && (
          <>
            <Row gutter={[16, 0]}>
              <Col xs={{ span: isPrestationOptionList?.length > 0 ? 7 : 11 }} className='text-align-center pa-1'>
                <Text strong italic>
                  Date
                </Text>
              </Col>
              <Col
                xs={{ span: isPrestationOptionList?.length > 0 ? (prestationDateList.length === 1 ? 10 : 9) : 11 }}
                className='text-align-center pa-1'
              >
                <Text strong italic>
                  Creneaux Horaires
                </Text>
              </Col>
              {isPrestationOptionList?.length > 0 && (
                <Col xs={{ span: 7 }} className='text-align-center pa-1'>
                  <Text strong italic>
                    Type
                  </Text>
                </Col>
              )}
              {prestationDateList.length > 1 && <Col xs={{ span: 1 }}></Col>}
            </Row>
            {prestationDateList.map((prestationDateItem, indexPrestationDateLine) => (
              <Fragment key={`${prestationDateItem?.id}-${indexPrestationDateLine}`}>
                {generatePrestationDateLine(
                  form,
                  record,
                  timeSlots,
                  isPrestationOptionList,
                  prestationDateItem,
                  prestationDateList,
                  onRemovePrestationDateLine,
                  onDateChange,
                  onChangeTimeSlot,
                  onChangeSubOption,
                  status,
                  documentType,
                  indexPrestationDateLine,
                )}
              </Fragment>
            ))}
            <Row gutter={[16, 0]} className='mt-2'>
              <Col xs={{ span: 24 }} className='text-align-center pa-1'>
                <Button
                  size='large'
                  type='primary'
                  className={`btn-add__creation-button`}
                  style={{
                    width: 'auto',
                    height: '32px',
                    borderRadius: '6px',
                  }}
                  onClick={() =>
                    onAddPrestationDateLine({
                      uuid: uuidv4(),
                      prestationDate: null,
                      timeSlotId: null,
                      subOptionId: null,
                      isNewLine: true,
                    })
                  }
                  disabled={
                    !(prestationDateList.length + 1 <= Number(record?.quantity ?? 0)) ||
                    (status !== undefined
                      ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                      : undefined)
                  }
                >
                  <PlusCircleOutlined className='btn-add__creation-icon' />
                  <span className='btn-add__creation-text'>Ajouter Date</span>
                </Button>
              </Col>
            </Row>
          </>
        )}
        {generateIsNotPrestationItemLines(form, record, notPrestationOptionList, onChangePrestationOptions, status)}
        {generateTarifSection(
          form,
          record,
          isCatalog,
          selectedPriceFamilies,
          priceTypes,
          onSelectPriceFamily,
          parameterTarifs,
          unitPrice,
          priceFamilyId,
          productTypeUnitId,
          status,
          productTypeUnits,
          formValuesWatch,
        )}
      </Spin>
    );
  }
  return <></>;
};

const generatePrestationDateLine = (
  form: FormInstance,
  record: ProductLineDevis,
  timeSlots: Option[],
  isPrestationOptionList: Option[],
  prestationDateItem: DocumentProductLinePrestation,
  prestationDateList: DocumentProductLinePrestation[],
  onRemovePrestationDateLine: (uuid?: string | number, type?: string) => void,
  onDateChange: (date: dayjs.Dayjs | null, uuid?: string | number) => void,
  onChangeTimeSlot: (timeSlot: string, uuid?: string | number) => void,
  onChangeSubOption: (subOptionLabel: string, uuid?: string | number) => void,
  status?: string,
  documentType?: string,
  indexPrestationDateLine?: number,
) => {
  const onChangeOption = (
    value: string | number,
    option: OptionPrestationType | OptionPrestationType[],
    name: string,
  ) => {
    if (value) {
      form?.setFieldValue(
        [
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'prestationDateLine',
          `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
          `${name}`,
        ],
        (option as OptionPrestationType).label,
      );
    }
  };
  const handleChangeTimeSlot = (option: OptionPrestationType | OptionPrestationType[], uuid?: string | number) => {
    onChangeTimeSlot((option as OptionPrestationType)?.label ?? '', uuid);
  };
  const handleChangeSubOption = (option: OptionPrestationType | OptionPrestationType[], uuid?: string | number) => {
    onChangeSubOption((option as OptionPrestationType)?.label ?? '', uuid);
  };
  return (
    <Row gutter={[16, 0]}>
      <Col xs={{ span: isPrestationOptionList?.length > 0 ? 7 : 11 }} className='text-align-center pa-1'>
        <Form.Item
          className='mb-0'
          name={[
            'lineItems',
            `${record?.uuid}`,
            'prestation',
            'prestationDateLine',
            `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
            'prestationDate',
          ]}
          initialValue={prestationDateItem?.prestationDate && dayjs(prestationDateItem?.prestationDate, dateFormat)}
          rules={[{ required: documentType === DOCUMENT_TYPES.ORDER && indexPrestationDateLine === 0 }]}
        >
          <DatePicker
            defaultValue={dayjs(prestationDateItem?.prestationDate ?? '', dateFormat)}
            placeholder='Select date'
            style={{ width: '100%' }}
            format={'DD/MM/YYYY'}
            locale={frFR}
            onChange={(date) => onDateChange(date, prestationDateItem?.id ?? prestationDateItem?.uuid)}
            disabled={
              status !== undefined
                ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                : undefined
            }
          />
        </Form.Item>
      </Col>
      <Col
        xs={{ span: isPrestationOptionList?.length > 0 ? (prestationDateList.length === 1 ? 10 : 8) : 11 }}
        className='text-align-center pa-1'
      >
        <Form.Item
          className='mb-0'
          name={[
            'lineItems',
            `${record?.uuid}`,
            'prestation',
            'prestationDateLine',
            `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
            'optionId',
          ]}
          initialValue={prestationDateItem?.optionId}
          rules={[{ required: documentType === DOCUMENT_TYPES.ORDER && indexPrestationDateLine === 0 }]}
        >
          <Select
            placeholder='Sélectionner'
            showSearch={false}
            options={timeSlots.map((timeSlot) => ({
              value: timeSlot.id,
              label: timeSlot.name,
            }))}
            onChange={(value, option) => {
              onChangeOption(value, option, 'optionLabel');
              handleChangeSubOption(option, prestationDateItem?.id ?? prestationDateItem?.uuid);
            }}
            disabled={
              status !== undefined
                ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                : undefined
            }
          />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={[
            'lineItems',
            `${record?.uuid}`,
            'prestation',
            'prestationDateLine',
            `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
            'optionLabel',
          ]}
          initialValue={prestationDateItem?.optionLabel}
        ></Form.Item>
        <Form.Item
          className='hidden'
          name={[
            'lineItems',
            `${record?.uuid}`,
            'prestation',
            'prestationDateLine',
            `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
            'isNewLine',
          ]}
          initialValue={prestationDateItem?.isNewLine ? prestationDateItem?.isNewLine : false}
        ></Form.Item>
      </Col>
      {isPrestationOptionList.length > 0 && (
        <Col xs={{ span: 7 }} className='text-align-center pa-1'>
          {isPrestationOptionList.map((option, index) => (
            <Fragment key={`${option?.id}-${index}`}>
              <Form.Item
                className='mb-0'
                name={[
                  'lineItems',
                  `${record?.uuid}`,
                  'prestation',
                  'prestationDateLine',
                  `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
                  'subOptionId',
                ]}
                initialValue={prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionId}
                rules={[{ required: documentType === DOCUMENT_TYPES.ORDER && indexPrestationDateLine === 0 }]}
              >
                <Select
                  placeholder='Sélectionner'
                  showSearch={false}
                  options={option?.SubOptions?.map((subOption) => ({
                    value: subOption.id,
                    label: subOption.name,
                  }))}
                  onChange={(value, option) => {
                    onChangeOption(value, option, 'subOptionLabel');
                    handleChangeTimeSlot(option, prestationDateItem?.id ?? prestationDateItem?.uuid);
                  }}
                  disabled={
                    status !== undefined
                      ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                      : undefined
                  }
                />
              </Form.Item>
              <Form.Item
                className='hidden'
                name={[
                  'lineItems',
                  `${record?.uuid}`,
                  'prestation',
                  'prestationDateLine',
                  `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
                  'subOptionLabel',
                ]}
                initialValue={prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel}
              ></Form.Item>
              <Form.Item
                className='hidden'
                name={[
                  'lineItems',
                  `${record?.uuid}`,
                  'prestation',
                  'prestationDateLine',
                  `${prestationDateItem?.id ?? prestationDateItem.uuid}`,
                  'documentProductLinePrestationStatusId',
                ]}
                initialValue={prestationDateItem?.DocumentProductLinePrestationStatus?.id}
              ></Form.Item>
            </Fragment>
          ))}
        </Col>
      )}
      {prestationDateList.length > 1 && (
        <Col xs={{ span: 2 }} className='text-align-center pa-1'>
          <Button
            hidden={prestationDateList.length === 1}
            type='link'
            icon={<DeleteOutlined style={{ fontSize: '16px', color: '#FF4D4F' }} />}
            onClick={() =>
              onRemovePrestationDateLine(
                prestationDateItem.id ?? prestationDateItem.uuid,
                prestationDateItem.id ? 'update' : 'create',
              )
            }
            disabled={
              status !== undefined
                ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                : undefined
            }
          />
        </Col>
      )}
    </Row>
  );
};

const generateIsNotPrestationItemLines = (
  form: FormInstance,
  record: ProductLineDevis,
  notPrestationOptionList: Option[],
  onChangePrestationOption: () => void,
  status?: string,
) => {
  const onChangeOption = (
    value: string | number,
    option: OptionPrestationType | OptionPrestationType[],
    name: string,
    id: number | string,
  ) => {
    if (value) {
      onChangePrestationOption();
      form?.setFieldValue(
        ['lineItems', `${record?.uuid}`, 'prestation', 'productLineSubOption', `${id}`, `${name}`],
        (option as OptionPrestationType).label,
      );
    }
  };
  return (
    <section className='section product-prestation-group-product mt-4'>
      {notPrestationOptionList.map((item, index) => (
        <Row key={`${item.id}-${index}`} gutter={[16, 0]}>
          <Col xs={{ span: 18 }}>
            <Form.Item
              label={item.name}
              name={[
                'lineItems',
                `${record?.uuid}`,
                'prestation',
                'productLineSubOption',
                `${item?.id}`,
                'subOptionId',
              ]}
              initialValue={item?.SubOptions?.find((subOption) => subOption.setByDefault)?.id}
              rules={[{ required: true }]}
            >
              <Select
                style={{ width: '150px' }}
                placeholder='Sélectionner'
                showSearch={false}
                options={item?.SubOptions?.filter((i) => i.isActive)?.map((subOption) => ({
                  value: subOption.id,
                  label: subOption.name,
                }))}
                onChange={(value, option) => onChangeOption(value, option, 'subOptionLabel', item?.id)}
                disabled={
                  status !== undefined
                    ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                    : undefined
                }
              />
            </Form.Item>
            <Form.Item
              className='hidden'
              name={[
                'lineItems',
                `${record?.uuid}`,
                'prestation',
                'productLineSubOption',
                `${item?.id}`,
                'optionLabel',
              ]}
              initialValue={item?.name}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={[
                'lineItems',
                `${record?.uuid}`,
                'prestation',
                'productLineSubOption',
                `${item?.id}`,
                'subOptionLabel',
              ]}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={[
                'lineItems',
                `${record?.uuid}`,
                'prestation',
                'productLineSubOption',
                `${item?.id}`,
                'documentProductLineId',
              ]}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'productLineSubOption', `${item?.id}`, 'id']}
            ></Form.Item>
          </Col>
        </Row>
      ))}
    </section>
  );
};

// eslint-disable-next-line @typescript-eslint/no-explicit-any
const debounce = <T extends (...args: any[]) => void>(fn: T, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};

const generateTarifSection = (
  form: FormInstance,
  record: ProductLineDevis,
  isCatalog: boolean | null | undefined,
  priceFamilies: PriceFamily[],
  priceTypes: PriceType[],
  onSelectPriceFamily: (priceFamilyId: number) => void,
  parameterTarifs: ParameterTarifLine[],
  unitPrice: number,
  priceFamilyId?: number | null,
  productTypeUnitId?: number,
  status?: string,
  productTypeUnits?: ProductTypeUnit[],
  formValuesWatch?: FormValues,
) => {
  let content: JSX.Element = <></>;
  const selectedPriceFamily = priceFamilies.find((priceFamily) => priceFamily.id === priceFamilyId);
  const selectedProductTypeUnit = productTypeUnits?.find((productTypeUnit) => productTypeUnit.id === productTypeUnitId);
  const selectionPriceType = priceTypes?.find((priceType) => priceType.key === PRICE_TYPES.selection);
  const numberPriceType = priceTypes?.find((priceType) => priceType.key === PRICE_TYPES.numberInput);
  const priceFamilyPriceMargin = priceFamilyId
    ? formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']?.[`${priceFamilyId}`]?.[
        'priceFamilyPriceMargin'
      ]
    : formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']?.['priceFamilyPriceMargin'];
  const totalBuyingPrice =
    formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']['buyingPrice'];
  const onChangeOption = (value: Option, option: OptionPrestationType | OptionPrestationType[], name: string[]) => {
    if (value) {
      form?.setFieldValue(name, (option as OptionPrestationType).label);
    }
  };

  const handlePriceChange = debounce((value: string | null) => {
    if (
      value &&
      (selectedPriceFamily?.key === TARIF_TYPE.SEMIFORFAIT ||
        selectedPriceFamily?.key === TARIF_TYPE.FORFAIT ||
        priceFamilyId === null) &&
      isCatalog === false &&
      record?.priceFamilyBuyingPrice
    ) {
      const cleanValue = parseFloat(value.toString());
      const cleanBuyingPrice = parseFloat(`${record?.priceFamilyBuyingPrice}` || '0');
      const margin = (cleanValue - cleanBuyingPrice) / cleanValue;
      if (priceFamilyId) {
        form.setFieldValue(
          ['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', `${priceFamilyId}`, 'priceFamilyPriceMargin'],
          margin,
        );
      } else {
        form.setFieldValue(
          ['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyPriceMargin'],
          margin,
        );
      }
    }
  }, 500);

  const handlePriceTarifChange = debounce((value: number | string | null, price: Price) => {
    const buyingPrice = form.getFieldValue([
      'lineItems',
      `${record?.uuid}`,
      'prestation',
      'tarifItems',
      'productLinePrices',
      `${price?.id}`,
      'buyingPrice',
    ]);
    if (value && selectedPriceFamily?.key === TARIF_TYPE.SEMIFORFAIT && isCatalog === false && buyingPrice) {
      const cleanValue = parseFloat(value.toString());
      const cleanBuyingPrice = buyingPrice;
      const margin = (cleanValue - cleanBuyingPrice) / cleanValue;
      form.setFieldValue(
        [
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          `${price?.id}`,
          'priceMargin',
        ],
        margin,
      );
    }
  }, 500);

  if (priceFamilies.length > 0) {
    content = (
      <>
        <Row gutter={[16, 0]}>
          <Col xs={{ span: 18 }}>
            <Form.Item
              label='Type du tarif'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyId']}
              rules={[{ required: true }]}
              initialValue={priceFamilyId}
            >
              <Select
                style={{ width: '200px' }}
                placeholder='Sélectionner'
                showSearch={false}
                onChange={(value) => onSelectPriceFamily(Number(value))}
                options={priceFamilies.map((priceFamily) => ({
                  value: priceFamily.id,
                  label: priceFamily.name,
                }))}
                disabled={
                  priceFamilies.length === 1 ||
                  (status !== undefined
                    ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                    : undefined)
                }
              />
            </Form.Item>
          </Col>
        </Row>
        {priceFamilyId && (
          <Row gutter={[16, 0]}>
            <Col xs={{ span: 18 }}>
              <Form.Item
                label={selectedPriceFamily?.labelName ?? ''}
                labelCol={{ span: 16 }}
                wrapperCol={{ span: 8 }}
                className='long-label-item'
              >
                <Form.Item
                  name={[
                    'lineItems',
                    `${record?.uuid}`,
                    'prestation',
                    'tarifItems',
                    `${priceFamilyId}`,
                    'priceFamilyValue',
                  ]}
                  rules={[{ required: true }]}
                  noStyle
                >
                  <InputNumber
                    className='input-prix-unitaire'
                    style={{ width: '140px' }}
                    suffix='€'
                    formatter={(value) =>
                      value!
                        .toString()
                        .replace(/\s/g, '')
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                        .replace('.', ',')
                    }
                    parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                    onChange={handlePriceChange}
                    disabled={
                      status !== undefined
                        ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                        : undefined
                    }
                  />
                </Form.Item>
                <p className='flex whitespace-nowrap mt-2' style={{ minWidth: '195px' }}>
                  {typeof priceFamilyPriceMargin === 'number'
                    ? `(${parseFloat(`${(priceFamilyPriceMargin * 100).toFixed(2)}`)}% - ${record?.priceFamilyValidSP} prestataires)`
                    : ''}
                </p>
              </Form.Item>
              <PriceFamilyHiddenFields
                record={record}
                priceFamilyId={priceFamilyId}
                selectedPriceFamily={selectedPriceFamily}
              />
            </Col>
          </Row>
        )}
        {parameterTarifs.map((tarif, indexTarif) => (
          <Fragment key={`${tarif?.price?.id}-${indexTarif}`}>
            <Row gutter={[16, 0]} className='mt-4'>
              <Col xs={{ span: 18 }}>
                <Form.Item
                  colon={false}
                  labelCol={{ style: { fontWeight: 'bold', height: '30px' } }}
                  label={
                    tarif.price.name +
                    `${tarif.price?.PriceTypeLogic?.key === PRICE_TYPE_LOGICS.indicative_purpose ? ' (Informatif)' : ''}`
                  }
                ></Form.Item>
              </Col>
            </Row>
            {tarif?.priceOptions?.map((priceOption, indexPriceOption) => (
              <Fragment key={`${priceOption?.id}-${indexPriceOption}`}>
                <Row gutter={[16, 0]}>
                  <Col xs={{ span: 18 }}>
                    {priceOption.priceTypeId === selectionPriceType?.id ? (
                      <>
                        <Form.Item
                          label={priceOption.name}
                          labelCol={{ span: 16 }}
                          wrapperCol={{ span: 8 }}
                          name={[
                            'lineItems',
                            `${record?.uuid}`,
                            'prestation',
                            'tarifItems',
                            'productLinePrices',
                            'priceOption',
                            `${priceOption?.id}`,
                            'priceSubOptionId',
                          ]}
                          rules={[{ required: true }]}
                        >
                          <Select
                            placeholder='Sélectionner'
                            className='select-price-sub-options long-label-item'
                            showSearch={false}
                            style={{ width: '140px' }}
                            options={priceOption?.PriceSubOptions?.map((priceSubOption) => ({
                              value: priceSubOption.id,
                              label: priceSubOption.name,
                            }))}
                            disabled={
                              status !== undefined
                                ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                                : undefined
                            }
                            onChange={(value, option) =>
                              onChangeOption(value, option, [
                                'lineItems',
                                `${record?.uuid}`,
                                'prestation',
                                'tarifItems',
                                'productLinePrices',
                                'priceOption',
                                `${priceOption?.id}`,
                                'priceSubOptionLabel',
                              ])
                            }
                          />
                        </Form.Item>
                        <Form.Item
                          className='hidden'
                          name={[
                            'lineItems',
                            `${record?.uuid}`,
                            'prestation',
                            'tarifItems',
                            'productLinePrices',
                            'priceOption',
                            `${priceOption?.id}`,
                            'priceSubOptionLabel',
                          ]}
                        ></Form.Item>
                      </>
                    ) : (
                      <Form.Item
                        label={priceOption.name}
                        labelCol={{ span: 16 }}
                        wrapperCol={{ span: 8 }}
                        name={[
                          'lineItems',
                          `${record?.uuid}`,
                          'prestation',
                          'tarifItems',
                          'productLinePrices',
                          'priceOption',
                          `${priceOption?.id}`,
                          'priceOptionValue',
                        ]}
                        rules={[{ required: true }]}
                        initialValue={priceOption.defaultValue}
                      >
                        {priceOption?.priceTypeId === numberPriceType?.id ? (
                          <InputNumber
                            className='input-prix-unitaire long-label-item'
                            style={{ width: '140px' }}
                            suffix={priceOption.unit}
                            formatter={(value) =>
                              value!
                                .toString()
                                .replace(/\s/g, '')
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                                .replace('.', ',')
                            }
                            parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                            disabled={
                              status !== undefined
                                ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                                : undefined
                            }
                          />
                        ) : (
                          <Input
                            className='input-text-prix-unitaire long-label-item'
                            style={{ width: '140px' }}
                            suffix={priceOption.unit}
                          />
                        )}
                      </Form.Item>
                    )}
                    <TarifPriceOptionHiddenFields priceOption={priceOption} record={record} tarif={tarif} />
                  </Col>
                </Row>
              </Fragment>
            ))}
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 18 }}>
                <Form.Item label={tarif.price.labelName} labelCol={{ span: 16 }} wrapperCol={{ span: 8 }}>
                  <Form.Item
                    name={[
                      'lineItems',
                      `${record?.uuid}`,
                      'prestation',
                      'tarifItems',
                      'productLinePrices',
                      `${tarif?.price?.id}`,
                      'priceValue',
                    ]}
                    rules={[{ required: true }]}
                    initialValue={
                      tarif.price?.PriceTypeLogic?.key === PRICE_TYPE_LOGICS.indicative_purpose
                        ? tarif.price.defaultPrice
                        : undefined
                    }
                    noStyle
                  >
                    <InputNumber
                      className='input-prix-unitaire long-label-item'
                      style={{ width: '140px' }}
                      suffix='€'
                      formatter={(value) =>
                        value!
                          .toString()
                          .replace(/\s/g, '')
                          .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                          .replace('.', ',')
                      }
                      parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                      disabled={
                        status !== undefined
                          ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                          : undefined
                      }
                      onChange={(value) => handlePriceTarifChange(value, tarif.price)}
                    />
                  </Form.Item>
                  <p className='flex whitespace-nowrap mt-2' style={{ minWidth: '195px' }}>
                    {tarif.price?.PriceTypeLogic?.key !== PRICE_TYPE_LOGICS.indicative_purpose &&
                    formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']?.[
                      'productLinePrices'
                    ]?.[`${tarif?.price?.id}`]?.['priceMargin'] !== null
                      ? `(${parseFloat((parseFloat(formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']?.['productLinePrices']?.[`${tarif?.price?.id}`]?.['priceMargin']) * 100).toFixed(2))}% - ${formValuesWatch?.['lineItems']?.[`${record?.uuid}`]?.['prestation']?.['tarifItems']?.['productLinePrices']?.[`${tarif?.price?.id}`]?.['validSP']} prestataires)`
                      : ''}
                  </p>
                </Form.Item>
                <TarifPriceHiddenFields tarif={tarif} record={record} />
              </Col>
            </Row>
            <Form.Item
              className='hidden'
              name={[
                'lineItems',
                `${record?.uuid}`,
                'prestation',
                'tarifItems',
                'productLinePrices',
                `${tarif?.price?.id}`,
                'priceId',
              ]}
              initialValue={tarif?.price?.id}
            ></Form.Item>
          </Fragment>
        ))}
      </>
    );
  } else {
    content = (
      <>
        <Row gutter={[16, 0]}>
          <Col xs={{ span: 18 }}>
            <Form.Item
              label='Prix'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyValue']}
              rules={[{ required: true }]}
            >
              <InputNumber
                className='input-prix-unitaire'
                style={{ width: '100px' }}
                suffix='€'
                onChange={handlePriceChange}
                formatter={(value) =>
                  value!
                    .toString()
                    .replace(/\s/g, '')
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                    .replace('.', ',')
                }
                parser={(value) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                disabled={
                  status !== undefined
                    ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                    : undefined
                }
              />
            </Form.Item>
            <Form.Item label=' ' colon={false}>
              <div className='whitespace-nowrap'>
                {typeof priceFamilyPriceMargin === 'number'
                  ? `(${parseFloat(`${(priceFamilyPriceMargin * 100).toFixed(2)}`)}% - ${record?.priceFamilyValidSP} prestataires)`
                  : ''}
              </div>
            </Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'linePriceFamilyId']}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyValidSP']}
              initialValue={record?.priceFamilyValidSP}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyBuyingPrice']}
              initialValue={record?.priceFamilyBuyingPrice}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'priceFamilyPriceMargin']}
              initialValue={record?.priceFamilyPriceMargin}
            ></Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'buyingPrice']}
              initialValue={record?.buyingPrice}
            ></Form.Item>
          </Col>
        </Row>
      </>
    );
  }

  return (
    <div>
      <Row gutter={[16, 0]} className='mt-4'>
        <Col xs={{ span: 24 }} className='text-align-center pa-1 pl-3 pr-3'>
          <div className='text-tarif'>
            <Text strong italic>
              Tarif
            </Text>
          </div>
        </Col>
      </Row>
      <section className='section product-prestation-group-price mt-2 mb-12'>
        {content}
        <div className='input-line-under w-100 mt-4'></div>
        <Row gutter={[16, 0]}>
          <Col xs={{ span: 18 }}>
            <Form.Item
              label='Prix Unitaire'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'prixUnitaire']}
              className='total-price'
            >
              <div className='flex justify-between'>
                <Text strong> {`${frenchCurrencyFormat(unitPrice.toFixed(2))}`} € </Text>
                <Text className='ml-2'>
                  {totalBuyingPrice
                    ? `(${parseFloat((((unitPrice - totalBuyingPrice) / unitPrice) * 100).toFixed(2))}%)`
                    : ''}
                </Text>
              </div>
            </Form.Item>
            <Form.Item
              className='hidden'
              name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', 'buyingPrice']}
            ></Form.Item>
            <Form.Item
              label='Unité'
              name={['lineItems', `${record?.uuid}`, 'productTypeUnitId']}
              className='total-price'
              initialValue={
                selectedProductTypeUnit?.id
                  ? selectedProductTypeUnit?.id
                  : record?.product?.ProductType?.ProductTypeUnit?.id
              }
            >
              <Select
                placeholder='Sélectionner'
                style={{ width: 140 }}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={productTypeUnits?.map((productTypeUnit) => ({
                  value: productTypeUnit.id,
                  label: productTypeUnit.name,
                }))}
                onChange={(value, option) =>
                  onChangeOption(value, option, ['lineItems', `${record?.uuid}`, 'productTypeUnitLabel'])
                }
                disabled={
                  status !== undefined
                    ? status !== DOCUMENT_STATUSES.SENT.key && status !== DOCUMENT_STATUSES.DRAFT.key
                    : undefined
                }
              />
            </Form.Item>
            <Form.Item
              className='hidden'
              label='Unitaire'
              name={['lineItems', `${record?.uuid}`, 'productTypeUnitLabel']}
              initialValue={
                selectedProductTypeUnit?.name
                  ? selectedProductTypeUnit.name
                  : record?.product?.ProductType?.ProductTypeUnit?.name
              }
            ></Form.Item>
          </Col>
        </Row>
      </section>
    </div>
  );
};

const PriceFamilyHiddenFields = ({
  record,
  priceFamilyId,
  selectedPriceFamily,
}: {
  record: ProductLineDevis;
  priceFamilyId: number;
  selectedPriceFamily?: PriceFamily;
}) => {
  return (
    <>
      <Form.Item
        className='hidden'
        name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', `${priceFamilyId}`, 'linePriceFamilyId']}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', `${priceFamilyId}`, 'priceFamilyValidSP']}
        initialValue={record?.priceFamilyValidSP}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          `${priceFamilyId}`,
          'priceFamilyBuyingPrice',
        ]}
        initialValue={record?.priceFamilyBuyingPrice}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          `${priceFamilyId}`,
          'priceFamilyPriceMargin',
        ]}
        initialValue={record?.priceFamilyPriceMargin}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={['lineItems', `${record?.uuid}`, 'prestation', 'tarifItems', `${priceFamilyId}`, 'buyingPrice']}
        initialValue={record?.buyingPrice}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={['lineItems', `${record?.uuid}`, 'priceFamilyLabel']}
        initialValue={selectedPriceFamily?.labelName}
      ></Form.Item>
    </>
  );
};

const TarifPriceHiddenFields = ({ tarif, record }: { tarif: ParameterTarifLine; record: ProductLineDevis }) => {
  return (
    <>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          `${tarif?.price?.id}`,
          'priceLabel',
        ]}
        initialValue={tarif.price.labelName}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          `${tarif?.price?.id}`,
          'id',
        ]}
      ></Form.Item>
      {tarif.price?.PriceTypeLogic?.key !== PRICE_TYPE_LOGICS.indicative_purpose && (
        <>
          <Form.Item
            className='hidden'
            name={[
              'lineItems',
              `${record?.uuid}`,
              'prestation',
              'tarifItems',
              'productLinePrices',
              `${tarif?.price?.id}`,
              'priceMargin',
            ]}
            initialValue={tarif.price.priceMargin}
          ></Form.Item>
          <Form.Item
            className='hidden'
            name={[
              'lineItems',
              `${record?.uuid}`,
              'prestation',
              'tarifItems',
              'productLinePrices',
              `${tarif?.price?.id}`,
              'buyingPrice',
            ]}
            initialValue={tarif.price.buyingPrice}
          ></Form.Item>
          <Form.Item
            className='hidden'
            name={[
              'lineItems',
              `${record?.uuid}`,
              'prestation',
              'tarifItems',
              'productLinePrices',
              `${tarif?.price?.id}`,
              'validSP',
            ]}
            initialValue={tarif.price.validSP}
          ></Form.Item>
        </>
      )}
    </>
  );
};

const TarifPriceOptionHiddenFields = ({
  priceOption,
  record,
  tarif,
}: {
  priceOption: PriceOption;
  record: ProductLineDevis;
  tarif: ParameterTarifLine;
}) => {
  return (
    <>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          'priceOption',
          `${priceOption?.id}`,
          'priceOptionLabel',
        ]}
        initialValue={priceOption.name}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          'priceOption',
          `${priceOption?.id}`,
          'priceOptionId',
        ]}
        initialValue={priceOption.id}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          'priceOption',
          `${priceOption?.id}`,
          'priceId',
        ]}
        initialValue={tarif?.price?.id}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.uuid}`,
          'prestation',
          'tarifItems',
          'productLinePrices',
          'priceOption',
          `${priceOption?.id}`,
          'id',
        ]}
      ></Form.Item>
    </>
  );
};

export default PrestationCell;
