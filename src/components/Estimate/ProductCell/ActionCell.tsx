import { DeleteOutlined } from '@ant-design/icons';
import { Button } from 'antd';
import { ProductLineDevis } from 'models';
import { DOCUMENT_STATUSES } from '../../../utils/constant';

interface ActionCellProps {
  record: ProductLineDevis;
  onDelete: (uuid?: string) => void;
  status?: string;
}

const ActionCell = (props: ActionCellProps) => {
  const { record, onDelete, status } = props;
  return (
    <div>
      <Button
        type='link'
        icon={<DeleteOutlined style={{ fontSize: '16px', color: '#FF4D4F' }} />}
        className='datatable__action-destroy-button'
        onClick={() => onDelete(record.uuid)}
        disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
      />
    </div>
  );
  return <></>;
};

export default ActionCell;
