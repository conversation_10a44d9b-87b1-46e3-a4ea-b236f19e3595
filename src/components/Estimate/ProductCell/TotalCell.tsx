import { CheckOutlined, CloseOutlined } from '@ant-design/icons';
import { Form, Space, Switch, Tooltip, Typography } from 'antd';
import { ProductLineDevis } from 'models';
import { frenchCurrencyFormat } from 'utils';
import { DOCUMENT_STATUSES } from '../../../utils/constant';
const { Text } = Typography;

interface TotalCellProps {
  record: ProductLineDevis;
  total: number;
  onSwitchValue: (toggled: boolean, uuid?: string) => void;
  status?: string;
}

const TotalCell = (props: TotalCellProps) => {
  const { record, total, onSwitchValue, status } = props;
  if (record.creationType !== 'header' && record.creationType !== 'header-multi-product') {
    return (
      <div style={{ minWidth: '50px' }} className='text-center'>
        <Space direction='vertical' align='baseline'>
          <Form.Item name={['lineItems', `${record?.uuid}`, 'total']}>
            <Text style={{ lineHeight: '32px' }} strong>
              {`${frenchCurrencyFormat(total.toFixed(2))} €`}
            </Text>
          </Form.Item>
          <Form.Item name={['lineItems', `${record?.uuid}`, 'isSetTotalZero']} initialValue={record?.isSetTotalZero}>
            <Tooltip placement='bottom' title='Mis à 0'>
              <Switch
                defaultChecked={record?.isSetTotalZero}
                checkedChildren={<CheckOutlined />}
                unCheckedChildren={<CloseOutlined />}
                onChange={(e) => {
                  onSwitchValue(e.valueOf(), record?.uuid);
                }}
                disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
              />
            </Tooltip>
          </Form.Item>
        </Space>
      </div>
    );
  }
  return <></>;
};

export default TotalCell;
