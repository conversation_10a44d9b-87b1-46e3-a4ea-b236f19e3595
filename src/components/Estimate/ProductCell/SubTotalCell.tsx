import { Form, Space, Typography } from 'antd';
import { ProductLineDevis } from 'models';
import { frenchCurrencyFormat } from 'utils';
const { Text } = Typography;

interface SubTotalCellProps {
  record: ProductLineDevis;
  subTotal: number;
}

const SubTotalCell = (props: SubTotalCellProps) => {
  const { record, subTotal } = props;
  if (record.creationType !== 'header' && record.creationType !== 'header-multi-product') {
    return (
      <div style={{ minWidth: '100px' }} className='text-right'>
        <Form.Item name={['lineItems', `${record?.uuid}`, 'totalBeforeDiscount']}>
          <Space direction='horizontal' align='baseline'>
            <Text style={{ lineHeight: '32px' }}>{`${frenchCurrencyFormat(subTotal.toFixed(2))} €`}</Text>
          </Space>
        </Form.Item>
      </div>
    );
  }
  return <></>;
};

export default SubTotalCell;
