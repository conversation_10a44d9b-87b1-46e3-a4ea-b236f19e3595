import { Col, Form, FormInstance, Row } from 'antd';
import {
  OptionType,
  ProductLineDevis,
  Product,
  PriceType,
  DocumentProductLine,
  DocumentProductLinePrestation,
  DocumentProductLineSubOption,
  PriceFamily,
  ProductComposition,
} from 'models';
import { FC, useEffect, useMemo, useState, forwardRef, useImperativeHandle, useContext } from 'react';
import { OPTION_TYPES, PRICE_MARGIN, PRICE_TYPE_LOGICS, PRICE_TYPES, TARIF_TYPE } from 'utils/constant';
import PrestationCell, { ParameterTarifLine } from './PrestationCell';
import ProductNameCell from './ProductNameCell';
import QuantityCell from './QuantityCell';
import DiscountCell from './DiscountCell';
import { UrbanCenterZoneIds } from 'types';
import dayjs from 'dayjs';
import TotalCell from './TotalCell';
import SubTotalCell from './SubTotalCell';
import ActionCell from './ActionCell';
import { catalogPriceService, productService } from 'services';
import { useAppSelector } from 'store';
import { selectProductTypeUnit, selectProductTypeUnitLoading } from 'store/slices/product.slices';
import { v4 as uuidv4 } from 'uuid';
import { CatalogPriceZonesType, DocumentTableRowContext } from '../ProductRow';
import SortHandle from 'components/Common/SortHandle';
import { useSortable } from '@dnd-kit/sortable';
import { ProductLineDevisType } from 'models/product_lines_devis';
interface EditableCellProps {
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof object;
  form: FormInstance;
  isDocumentCatalog: boolean | null;
  dataList: {
    products: Product[];
    optionTypes: OptionType[];
    priceTypes: PriceType[];
    urbanCenterZoneIds: UrbanCenterZoneIds;
  };
  formtype?: 'estimate' | 'order';
  updateDataSource: (data: ProductLineDevis, uuid?: string) => void;
  dataSource?: ProductLineDevis[];
  disabled?: boolean;
  status?: string;
  documentType?: string;
  record: ProductLineDevis & DocumentProductLine;
  type?: string;
  onDelete: (uuid?: string) => void;
  setDataSource: (data: ProductLineDevis[] | DocumentProductLine) => void;
}

export interface ProductCellRef {
  refreshCatalogPrice: (args: {
    zoneIds: UrbanCenterZoneIds;
    isChangePrice?: boolean;
    isDocumentCatalog: boolean | null;
  }) => Promise<void>;
}

const ProductCell: FC<EditableCellProps> = forwardRef<ProductCellRef, EditableCellProps>(
  (
    {
      editable,
      children,
      dataIndex,
      dataList,
      updateDataSource,
      form,
      record,
      onDelete,
      status,
      isDocumentCatalog,
      documentType,
      dataSource,
      setDataSource,
      ...restProps
    },
    ref,
  ) => {
    const {
      options,
      priceFamilies,
      catalogPriceZones,
      catalogPriceZonesLoading,
      priceFamiliesLoading,
      optionsLoading,
      fetchPriceFamilies,
      fetchOptions,
      setCatalogPriceZones,
      setCatalogPriceZonesLoading,
      productTypeReguls,
      productTypeRegulsLoading,
      fetchProductTypeReguls,
      parameterTarifs,
      setParameterTarifs,
      prices,
      pricesLoading,
      fetchPrices,
    } = useContext(DocumentTableRowContext);
    const [onLoadingSelectProduct, setOnLoadingSelectProduct] = useState<boolean>(false);
    const formValuesWatch = Form.useWatch([], form);
    const isCatalog = record?.isCatalog;
    const cellDataSource = useMemo(() => {
      if (
        record?.DocumentProductLinePrestations &&
        record.DocumentProductLinePrestations.length > 0 &&
        (!record.prestationDateList || record.prestationDateList?.length === 0)
      ) {
        record.prestationDateList = [...record.DocumentProductLinePrestations];
      }
      return record;
    }, [record]);
    const productTypeUnitId = cellDataSource?.productTypeUnitId;
    const optionTypes = dataList?.optionTypes;
    const qtyPriceType = dataList?.priceTypes?.find((priceType) => priceType.key === PRICE_TYPES.numberInput);
    const timeSlotOptionType = optionTypes?.find((i) => i.key === OPTION_TYPES.timeSlot);
    const optionOptionType = optionTypes?.find((i) => i.key === OPTION_TYPES.option);
    const [parameterPrestationOptions, setParameterPrestationOptions] = useState<DocumentProductLineSubOption[]>([]);

    const prestationValues = formValuesWatch?.['lineItems']?.[`${cellDataSource?.uuid}`]?.['prestation'];
    const tarifItems = prestationValues?.['tarifItems']?.['productLinePrices'];

    const [logComment, setLogComment] = useState<string>(cellDataSource?.logComment || '');
    const productTypeUnits = useAppSelector(selectProductTypeUnit);
    const productTypeUnitsLoading = useAppSelector(selectProductTypeUnitLoading);
    const { listeners, setActivatorNodeRef } = useSortable({
      id: cellDataSource?.uuid || '',
    });
    useEffect(() => {
      if (dataIndex === 'name') {
        const formLogComment = form.getFieldValue(['lineItems', `${cellDataSource?.uuid}`, 'logComment']);
        if (formLogComment !== logComment) {
          setLogComment(formLogComment);
        }
      }
    }, [formValuesWatch]);

    useEffect(() => {
      if ((priceFamilies?.length || (record?.productId && !record?.priceFamilyId)) && dataIndex === 'prestation') {
        const priceFamilyId = cellDataSource?.priceFamilyId as number;
        const newParameterTarifs = generateParameterTarifs({
          priceFamilyId,
          resetPriceMargin: cellDataSource?.id ? false : true,
          catalogPriceZones,
        });
        setParameterTarifs(newParameterTarifs);
        if (cellDataSource && !cellDataSource?.id) {
          const isCatalogValue = isCatalog !== undefined ? isCatalog : null;
          const priceMargin = getPriceMargin({ priceFamilyId, catalogPriceZones, isCatalog: isCatalogValue });
          setParameterTarifsValue({
            parameterTarifs: newParameterTarifs,
            priceFamilyId,
            options: priceMargin,
            catalogPriceZones,
            isCatalog: isCatalogValue,
          });
        }
        if (!cellDataSource?.id) {
          autoSelectPriceFamily({ priceFamilies, catalogPriceZones });
        }
      }
    }, [priceFamilies, prices]);

    const autoSelectPriceFamily = (args: {
      priceFamilies: PriceFamily[];
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog?: boolean | null;
    }) => {
      if (!updateDataSource) {
        return;
      }
      const { priceFamilies, catalogPriceZones } = args;
      const _isCatalog = args.isCatalog !== undefined ? args.isCatalog : isCatalog;
      let matchingPriceFamily = priceFamilies?.find(
        (priceFamily) =>
          priceFamily.key === catalogPriceZones?.tarifType &&
          priceFamily.productTypeId === cellDataSource?.productTypeId,
      );
      // if the tarif type is semiforfait and there is no semiforfait price family, we try to find a forfait price family
      if (
        catalogPriceZones?.tarifType === TARIF_TYPE.SEMIFORFAIT &&
        !catalogPriceZones?.semiforfait?.length &&
        catalogPriceZones?.forfait?.length
      ) {
        matchingPriceFamily = priceFamilies?.find(
          (priceFamily) =>
            priceFamily.key === TARIF_TYPE.FORFAIT && priceFamily.productTypeId === cellDataSource?.productTypeId,
        );
      }
      // select the first price family if there is only one price family
      if (!matchingPriceFamily && priceFamilies?.length === 1 && !cellDataSource?.id) {
        matchingPriceFamily = priceFamilies[0];
      }
      if (matchingPriceFamily?.id) {
        onSelectPriceFamily({
          priceFamilyId: matchingPriceFamily.id,
          catalogPriceZones,
          isCatalog: _isCatalog !== undefined ? _isCatalog : isDocumentCatalog,
        });
      } else {
        updateDataSource({ priceFamilyId: null }, cellDataSource?.uuid);
        setParameterTarifs([]);
        form.setFieldValue(['lineItems', cellDataSource?.uuid, 'prestation', 'tarifItems', 'priceFamilyId'], null);
      }
      if (cellDataSource?.priceFamilyId === null && !priceFamilies?.length && !parameterTarifs?.length) {
        const isCatalogValue = _isCatalog !== undefined ? _isCatalog : null;
        const priceMargin = getPriceMargin({ priceFamilyId: null, catalogPriceZones, isCatalog: isCatalogValue });
        setParameterTarifsValue({
          parameterTarifs: [],
          priceFamilyId: null,
          options: priceMargin,
          catalogPriceZones,
          isCatalog: isCatalogValue,
        });
      }
    };

    useEffect(() => {
      if (cellDataSource?.productId && dataIndex === 'prestation') {
        getCatalogPriceZone({
          zoneIds: dataList?.urbanCenterZoneIds,
          productId: cellDataSource?.productId,
          isAutoSelectPriceFamily: cellDataSource?.id ? false : true,
        });
        fetchOptions({
          'productTypeId[]': JSON.stringify([cellDataSource?.productTypeId]),
          include: 'SubOptions',
        });
        fetchPriceFamilies({
          'productTypeId[]': JSON.stringify([cellDataSource?.productTypeId]),
          include: 'Prices.PriceOptions|Prices.PriceTypeLogic',
        });
      }
    }, [cellDataSource?.productId]);

    useEffect(() => {
      if (priceFamilies?.length && dataIndex === 'prestation') {
        fetchPrices({
          'priceFamilyId[]': JSON.stringify(priceFamilies?.map((priceFamily) => priceFamily.id)),
          limit: 'unlimited',
          include: 'PriceOptions.PriceType|PriceTypeLogic',
        });
      }
    }, [priceFamilies?.length]);

    useEffect(() => {
      if (dataIndex === 'name' && cellDataSource?.productTypeRegulId) {
        fetchProductTypeReguls({
          'productId[]': JSON.stringify(cellDataSource?.productTypeRegulId),
          limit: 'unlimited',
          include: 'ProductType|ProductType.ProductTypeUnit',
        });
      }
    }, [cellDataSource?.productTypeRegulId]);

    const getCatalogPriceZone = async (args: {
      zoneIds: UrbanCenterZoneIds;
      productId?: number | null;
      isAutoSelectPriceFamily?: boolean;
      isRefreshPriceFamily?: boolean;
      isCatalog?: boolean | null;
    }) => {
      const { zoneIds, productId, isAutoSelectPriceFamily = false, isRefreshPriceFamily = true } = args;
      const _isCatalog = args.isCatalog !== undefined ? args.isCatalog : isCatalog;
      const newCatalogPriceZones: CatalogPriceZonesType = {
        tarifType: null,
        forfait: [],
        semiforfait: [],
      };
      if (productId) {
        if (zoneIds?.regionCatalogZoneIds?.length || zoneIds?.serviceProviderZoneIds?.length) {
          try {
            setCatalogPriceZonesLoading(true);
            const catalogPriceZoneResponse = await catalogPriceService.getCatalogPriceZones({
              regionCatalogZoneIds: zoneIds?.regionCatalogZoneIds,
              serviceProviderZoneIds: zoneIds?.serviceProviderZoneIds,
              serviceProviderZones: zoneIds?.serviceProviderZones?.map((zone) => ({
                id: zone.id,
                name: zone.name,
                minDistance: zone.minDistance,
                maxDistance: zone.maxDistance,
              })),
              productIds: [productId],
            });
            newCatalogPriceZones.tarifType = catalogPriceZoneResponse.tarifType;
            newCatalogPriceZones.countServiceProvider = catalogPriceZoneResponse.countServiceProvider;
            newCatalogPriceZones.countServiceProviderForfait = catalogPriceZoneResponse.countServiceProviderForfait;
            newCatalogPriceZones.forfait = catalogPriceZoneResponse.forfait ?? [];
            newCatalogPriceZones.semiforfait = catalogPriceZoneResponse.semiforfait ?? [];
            setCatalogPriceZones(newCatalogPriceZones);
            const priceFamilyId = cellDataSource?.priceFamilyId || null;
            if (isRefreshPriceFamily) {
              const isCatalogValue = _isCatalog !== undefined ? _isCatalog : null;
              if (priceFamilies?.length === 0 && cellDataSource?.priceFamilyId === null) {
                if (cellDataSource && !cellDataSource?.id) {
                  const priceMargin = getPriceMargin({
                    priceFamilyId,
                    catalogPriceZones: newCatalogPriceZones,
                    isCatalog: isCatalogValue,
                  });
                  setParameterTarifsValue({
                    parameterTarifs: [],
                    priceFamilyId,
                    options: priceMargin,
                    catalogPriceZones: newCatalogPriceZones,
                    isCatalog: isCatalogValue,
                  });
                }
                if (!cellDataSource?.id) {
                  autoSelectPriceFamily({
                    priceFamilies,
                    catalogPriceZones: newCatalogPriceZones,
                    isCatalog: isCatalogValue,
                  });
                }
              } else {
                fetchPriceFamilies({
                  'productTypeId[]': JSON.stringify([cellDataSource?.productTypeId]),
                  include: 'Prices.PriceOptions|Prices.PriceTypeLogic',
                });
              }
            }
            if (isAutoSelectPriceFamily) {
              autoSelectPriceFamily({ priceFamilies, catalogPriceZones: newCatalogPriceZones, isCatalog: _isCatalog });
            }
          } catch (ex) {
            console.log(ex);
          } finally {
            setCatalogPriceZonesLoading(false);
          }
        } else {
          setCatalogPriceZones({ tarifType: null, forfait: [], semiforfait: [] });
        }
      }
      return newCatalogPriceZones;
    };

    useImperativeHandle(ref, () => ({
      refreshCatalogPrice,
    }));

    const refreshCatalogPrice = async (args: {
      zoneIds: UrbanCenterZoneIds;
      isChangePrice?: boolean;
      isDocumentCatalog: boolean | null;
    }) => {
      const { zoneIds, isChangePrice, isDocumentCatalog } = args;
      // if the product is General Price (priceFamilyId is null), we don't need to refresh the price family to avoid the trigger from the useEffect
      const updatedCatalogPriceZones = await getCatalogPriceZone({
        zoneIds,
        isAutoSelectPriceFamily: isChangePrice,
        productId: cellDataSource?.productId,
        isRefreshPriceFamily: isChangePrice && priceFamilies?.length !== 0 ? true : false,
        isCatalog: isDocumentCatalog,
      });
      if (isChangePrice) {
        autoSelectPriceFamily({
          priceFamilies,
          catalogPriceZones: updatedCatalogPriceZones,
          isCatalog: isDocumentCatalog,
        });
      }
    };

    const handleCalculateUnitPrix = (priceValue: number, buyingPrice: number) => {
      let unitPriceTotal = priceValue;
      let buyingPriceTotal = buyingPrice;
      if (parameterTarifs.length > 0) {
        const calculateParameterTarifs = parameterTarifs?.filter(
          (tarif) => tarif.price?.PriceTypeLogic?.key === PRICE_TYPE_LOGICS.calculate,
        );
        calculateParameterTarifs.forEach((tarif) => {
          const priceOptionValue = tarif?.value || 0;
          const priceOptionBuyingPrice = form.getFieldValue([
            'lineItems',
            `${cellDataSource?.uuid}`,
            'prestation',
            'tarifItems',
            'productLinePrices',
            `${tarif.price.id}`,
            'buyingPrice',
          ]);
          const calculatePriceOptions = tarif?.priceOptions?.filter(
            (priceOption) => priceOption?.priceTypeId === qtyPriceType?.id,
          );
          let priceOptionValueTotal = 0;
          let priceOptionBuyingPriceTotal = 0;
          // Calculate the product of defaultValue for all priceOptions in this tarif
          if (calculatePriceOptions.length > 1) {
            priceOptionValueTotal = calculatePriceOptions.reduce((accumulator, priceOption, index) => {
              const unitValue = priceOption?.defaultValue ? Number(priceOption.defaultValue) : 0;
              if (index === 0) {
                return unitValue;
              } else {
                return accumulator * unitValue;
              }
            }, 1);
          } else if (calculatePriceOptions.length === 1) {
            const unitValue = calculatePriceOptions[0]?.defaultValue
              ? Number(calculatePriceOptions[0].defaultValue)
              : 0;
            priceOptionValueTotal = unitValue;
            priceOptionBuyingPriceTotal = unitValue;
          }
          // Multiply this product with the tarif's value
          priceOptionValueTotal *= priceOptionValue;
          priceOptionBuyingPriceTotal *= priceOptionBuyingPrice ? parseFloat(priceOptionBuyingPrice) : 0;
          // Add this tarif's calculated value to the total
          unitPriceTotal += priceOptionValueTotal;
          buyingPriceTotal += priceOptionBuyingPriceTotal;
        });
      }
      if (
        `${form.getFieldValue(['lineItems', `${cellDataSource?.uuid}`, 'prestation', 'tarifItems', 'prixUnitaire'])}` !==
        `${unitPriceTotal}`
      ) {
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              prestation: {
                tarifItems: {
                  prixUnitaire: unitPriceTotal,
                  buyingPrice: buyingPriceTotal,
                },
              },
            },
          },
        });
      }
      return unitPriceTotal;
    };

    const unitPrice = useMemo(() => {
      if (!cellDataSource?.uuid || !formValuesWatch) return 0;
      const lineItem = formValuesWatch?.['lineItems']?.[`${cellDataSource?.uuid}`]?.['prestation']?.['tarifItems'];
      const priceValue = lineItem
        ? cellDataSource?.priceFamilyId
          ? lineItem[`${cellDataSource?.priceFamilyId}`]?.['priceFamilyValue'] || 0
          : lineItem?.['priceFamilyValue'] || 0
        : null;
      const buyingPrice = lineItem
        ? cellDataSource?.priceFamilyId
          ? lineItem[`${cellDataSource?.priceFamilyId}`]?.['priceFamilyBuyingPrice'] || 0
          : lineItem?.['priceFamilyBuyingPrice'] || 0
        : null;
      return handleCalculateUnitPrix(
        priceValue ? parseFloat(priceValue) : 0,
        buyingPrice ? parseFloat(buyingPrice) : 0,
      );
    }, [formValuesWatch, parameterTarifs]);

    const subTotal = useMemo(() => {
      if (cellDataSource?.isSetTotalZero) return 0;
      return cellDataSource?.quantity ? cellDataSource.quantity * unitPrice : 0;
    }, [cellDataSource?.quantity, unitPrice, cellDataSource?.isSetTotalZero]);

    const total = useMemo(() => {
      let totalValue = subTotal;
      if (cellDataSource?.isSetTotalZero) totalValue = 0;
      if (cellDataSource?.discount) {
        const discountAmount =
          cellDataSource.discountUnit === '€' ? cellDataSource.discount : (subTotal * cellDataSource.discount) / 100;
        totalValue -= discountAmount;
      }
      form?.setFieldsValue({
        lineItems: {
          [`${cellDataSource?.uuid}`]: {
            totalBeforeDiscount: subTotal,
            total: totalValue,
          },
        },
      });
      return totalValue;
    }, [
      cellDataSource?.quantity,
      unitPrice,
      subTotal,
      cellDataSource?.discount,
      cellDataSource?.discountUnit,
      cellDataSource?.product0,
    ]);

    useEffect(() => {
      handleUpdateTotalPrice(total);
    }, [total]);

    const generateParameterTarifs = (args: {
      priceFamilyId: number;
      resetPriceMargin: boolean;
      catalogPriceZones: CatalogPriceZonesType;
    }) => {
      const { priceFamilyId, resetPriceMargin, catalogPriceZones } = args;
      // const prices = priceFamilies?.find((priceFamily) => priceFamily.id === priceFamilyId)?.Prices ?? [];
      const filteredPrices = prices?.filter((price) => price.priceFamilyId === priceFamilyId);
      const isCatalogValue = isCatalog !== undefined ? isCatalog : null;
      const priceMargin = getPriceMargin({ priceFamilyId, catalogPriceZones, isCatalog: isCatalogValue });

      const newParameterTarifs = filteredPrices?.map((price) => ({
        price: {
          ...price,
          ...(resetPriceMargin ? priceMargin : {}),
        },
        value: parameterTarifs.find((p) => p.price.id === price.id)?.value ?? 0,
        priceOptions: price?.PriceOptions
          ? price.PriceOptions.map((priceOption) => {
              const existingPriceOption = parameterTarifs
                .find((p) => p.price.id === price.id)
                ?.priceOptions.find((p) => p.id === priceOption.id);
              return {
                ...priceOption,
                defaultValue: existingPriceOption?.defaultValue
                  ? existingPriceOption?.defaultValue
                  : priceOption?.defaultValue || '',
              };
            })
          : [],
      }));
      return newParameterTarifs;
    };

    const setParameterTarifsValue = (args: {
      parameterTarifs: ParameterTarifLine[];
      priceFamilyId: number | null;
      options: {
        priceMargin: number | null;
        validSP: number | null;
      };
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog: boolean | null;
    }) => {
      const { parameterTarifs, priceFamilyId, options, catalogPriceZones, isCatalog } = args;
      const marginRate = 1 - (options?.priceMargin || 0);

      const selectedCatalogPriceZones = selectCatalogPriceZones({
        priceFamilyId,
        catalogPriceZones,
        isCatalog,
      });
      if (!priceFamilyId && parameterTarifs?.length === 0) {
        const isEmptyCatalogPriceZones = !selectedCatalogPriceZones?.length;
        const priceValue = selectedCatalogPriceZones?.[0]?.priceValue ?? '0';
        const shouldMarginPrice = options.priceMargin !== null && !isEmptyCatalogPriceZones;

        // Calculate values based on conditions
        const buyingPrice = shouldMarginPrice ? parseFloat(priceValue) : null;
        const priceFamilyValue = shouldMarginPrice ? (parseFloat(priceValue) / marginRate).toFixed(2) : priceValue;
        // Common price family values
        const priceFamilyFormFields = {
          priceFamilyPriceMargin: isEmptyCatalogPriceZones ? null : options.priceMargin,
          priceFamilyValidSP: isEmptyCatalogPriceZones ? null : options.validSP,
          priceFamilyBuyingPrice: buyingPrice,
        };

        updateDataSource(priceFamilyFormFields, cellDataSource?.uuid);
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              prestation: {
                tarifItems: {
                  ...priceFamilyFormFields,
                  priceFamilyValue,
                  buyingPrice,
                },
              },
            },
          },
        });
      } else {
        parameterTarifs.forEach((tarif) => {
          const catalogPriceZone = selectedCatalogPriceZones?.find(
            (item) =>
              (item.priceFamilyId === tarif.price.priceFamilyId &&
                item.priceId === tarif.price.id &&
                item.priceOptionId === null) ||
              (item.DestCatalogPrice?.priceFamilyId === tarif.price.priceFamilyId &&
                item.DestCatalogPrice?.priceId === tarif.price.id &&
                item.DestCatalogPrice?.priceOptionId === null),
          );
          form.setFieldsValue({
            lineItems: {
              [`${cellDataSource?.uuid}`]: {
                prestation: {
                  tarifItems: {
                    productLinePrices: {
                      [`${tarif.price.id}`]: {
                        ...(tarif.price?.PriceTypeLogic?.key !== PRICE_TYPE_LOGICS.indicative_purpose
                          ? {
                              priceValue:
                                (parseFloat(catalogPriceZone?.priceValue || '0') / marginRate).toFixed(2) ||
                                tarif.value ||
                                0,
                              buyingPrice:
                                options?.priceMargin !== null ? catalogPriceZone?.priceValue || tarif.value : null,
                              priceMargin: options?.priceMargin,
                              validSP: options?.validSP,
                            }
                          : {
                              priceValue: tarif.value || tarif.price.defaultPrice,
                              buyingPrice: null,
                              priceMargin: null,
                              validSP: null,
                            }),
                      },
                    },
                  },
                },
              },
            },
          });
        });
      }
    };

    const getPriceMargin = (args: {
      priceFamilyId: number | null;
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog: boolean | null;
    }) => {
      const { priceFamilyId, catalogPriceZones, isCatalog } = args;
      const result: {
        priceMargin: number | null;
        validSP: number | null;
      } = {
        priceMargin: null,
        validSP: null,
      };
      const priceFamily = priceFamilies?.find((priceFamily) => priceFamily.id === priceFamilyId);
      if (
        ((priceFamily?.key === TARIF_TYPE.SEMIFORFAIT && catalogPriceZones?.semiforfait?.length) ||
          (priceFamily?.key === TARIF_TYPE.FORFAIT && catalogPriceZones?.forfait?.length) ||
          priceFamilyId === null) &&
        isCatalog === false
      ) {
        result.priceMargin = PRICE_MARGIN;
        result.validSP =
          priceFamily?.key === TARIF_TYPE.FORFAIT
            ? catalogPriceZones?.countServiceProviderForfait || 0
            : catalogPriceZones?.countServiceProvider || 0;
      }
      return result;
    };

    const mapCatalogPriceZones = async (args: {
      priceFamilyId: number;
      priceMargin: { priceMargin: number | null; validSP: number | null };
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog: boolean | null;
    }) => {
      const { priceFamilyId, priceMargin, catalogPriceZones, isCatalog } = args;
      const marginRate = 1 - parseFloat(`${priceMargin.priceMargin || '0'}`);
      const updatedPriceFamily = priceFamilies?.find((priceFamily) => priceFamily.id === priceFamilyId);
      if (updatedPriceFamily) {
        const newParameterTarifs = generateParameterTarifs({
          priceFamilyId: updatedPriceFamily.id,
          resetPriceMargin: false,
          catalogPriceZones,
        });
        setParameterTarifs(newParameterTarifs);
        setParameterTarifsValue({
          parameterTarifs: newParameterTarifs,
          priceFamilyId: updatedPriceFamily.id,
          options: priceMargin,
          catalogPriceZones,
          isCatalog,
        });
        const newCatalogPriceZones =
          updatedPriceFamily.key === TARIF_TYPE.FORFAIT ? catalogPriceZones?.forfait : catalogPriceZones?.semiforfait;
        const selectedPriceFamily = newCatalogPriceZones?.find(
          (item) =>
            (item.priceFamilyId === updatedPriceFamily.id && item.priceId === null && item.priceOptionId === null) ||
            (item.DestCatalogPrice?.priceFamilyId === updatedPriceFamily.id &&
              item.DestCatalogPrice?.priceId === null &&
              item.DestCatalogPrice.priceOptionId === null),
        );
        setLogComment(selectedPriceFamily?.comment || '');
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              logComment: selectedPriceFamily?.comment,
            },
          },
        });
        let dataPriceFamilyValue: string | number = 0;
        if (selectedPriceFamily?.priceValue) {
          dataPriceFamilyValue = (parseFloat(selectedPriceFamily.priceValue) / marginRate).toFixed(2);
        }
        // const priceMargin = getPriceMargin({ priceFamilyId: updatedPriceFamily.id, catalogPriceZones, isCatalog });
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              prestation: {
                tarifItems: {
                  [`${updatedPriceFamily.id}`]: {
                    priceFamilyValue: dataPriceFamilyValue,
                    priceFamilyBuyingPrice: priceMargin.priceMargin !== null ? selectedPriceFamily?.priceValue : null,
                    priceFamilyPriceMargin: priceMargin.priceMargin,
                    priceFamilyValidSP: priceMargin.validSP,
                  },
                },
              },
            },
          },
        });
      } else {
        const priceFamilyValue = catalogPriceZones?.forfait?.find(
          (item) =>
            (item.priceFamilyId === null && item.priceId === null && item.priceOptionId === null) ||
            (item.DestCatalogPrice?.priceFamilyId === null &&
              item.DestCatalogPrice?.priceId === null &&
              item.DestCatalogPrice?.priceOptionId === null),
        );
        let dataPriceFamilyValue: string | number = 0;
        if (priceFamilyValue?.priceValue) {
          dataPriceFamilyValue = priceFamilyValue?.priceValue;
        }
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              logComment: priceFamilyValue?.comment,
            },
          },
        });
        form.setFieldsValue({
          lineItems: {
            [`${cellDataSource?.uuid}`]: {
              prestation: {
                tarifItems: { priceFamilyValue: dataPriceFamilyValue },
              },
            },
          },
        });
      }
    };

    useEffect(() => {
      if (prestationValues?.productLineSubOption) {
        handleChangeParameterPrestationSubOptionValue();
      }
      if (tarifItems && typeof tarifItems === 'object') {
        const tarifEntries = Object.entries(tarifItems).filter(
          ([key, value]) => !isNaN(Number(key)) && typeof value === 'object',
        ) as [
          string,
          object & {
            priceId: number;
            priceValue: number;
            priceLabel: string;
            id: string;
          },
        ][];

        tarifEntries.forEach(([id, { priceId, priceValue }]) => {
          if (priceId) {
            handleChangeParameterTarifValue(parseInt(id), undefined, priceValue, undefined);
          }
        });

        if (tarifItems.priceOption) {
          const priceOptionEntries = Object.entries(tarifItems.priceOption).filter(
            ([key, value]) => !isNaN(Number(key)) && typeof value === 'object',
          ) as [
            string,
            object & {
              priceOptionId: number;
              priceOptionValue: string;
              priceOptionLabel: string;
              priceId: number;
              priceSubOptionId: string;
            },
          ][];

          priceOptionEntries.forEach(([priceOptionId, { priceOptionValue, priceSubOptionId }]) => {
            if (priceOptionId) {
              handleChangeParameterTarifValue(
                undefined,
                parseInt(priceOptionId),
                undefined,
                priceOptionValue,
                priceSubOptionId,
              );
            }
          });
        }
      }
    }, [formValuesWatch, prestationValues, tarifItems, cellDataSource?.uuid]);

    useEffect(() => {
      if (cellDataSource?.priceFamilyValue) {
        if (cellDataSource?.priceFamilyIdOriginal) {
          form.setFieldsValue({
            lineItems: {
              [`${cellDataSource?.uuid}`]: {
                prestation: {
                  tarifItems: {
                    [`${cellDataSource?.priceFamilyIdOriginal}`]: {
                      priceFamilyValue: cellDataSource?.priceFamilyValue ?? 0,
                      linePriceFamilyId: cellDataSource?.linePriceFamilyId,
                    },
                  },
                },
              },
            },
          });
        } else if (cellDataSource?.linePriceFamilyId) {
          form.setFieldsValue({
            lineItems: {
              [`${cellDataSource?.uuid}`]: {
                prestation: {
                  tarifItems: {
                    priceFamilyValue: cellDataSource?.priceFamilyValue ?? 0,
                    linePriceFamilyId: cellDataSource?.linePriceFamilyId,
                  },
                },
              },
            },
          });
        }
      }
      if (cellDataSource?.listPriceOptions) {
        cellDataSource?.listPriceOptions.forEach((priceOption) => {
          form.setFieldsValue({
            lineItems: {
              [`${cellDataSource?.uuid}`]: {
                prestation: {
                  tarifItems: {
                    productLinePrices: {
                      priceOption: {
                        [`${priceOption.priceOptionId}`]: {
                          priceOptionValue: priceOption.value,
                          priceSubOptionId: priceOption.priceSubOptionId,
                          priceSubOptionLabel: priceOption.priceSubOptionLabel,
                          id: priceOption.id,
                        },
                      },
                      [`${priceOption?.priceId}`]: {
                        priceValue: priceOption.priceValue,
                        priceMargin: priceOption.priceMargin,
                        buyingPrice: priceOption.buyingPrice,
                        validSP: priceOption.validSP,
                        id: priceOption.linePriceId,
                      },
                    },
                  },
                },
              },
            },
          });
        });
      }
    }, []);

    useEffect(() => {
      if (options) {
        const optionList = options?.filter(
          (option) =>
            option.productTypeId === cellDataSource?.productTypeId && option.optionTypeId === optionOptionType?.id,
        );
        const notPrestationOptionList = optionList?.filter((option) => !option.isPrestationOption);
        if (notPrestationOptionList) {
          notPrestationOptionList?.forEach((item) => {
            item?.SubOptions?.forEach((subOption) => {
              cellDataSource?.DocumentProductLineSubOptions?.forEach((i) => {
                if (i.subOptionId === subOption.id) {
                  form.setFieldsValue({
                    lineItems: {
                      [`${cellDataSource?.uuid}`]: {
                        prestation: {
                          productLineSubOption: {
                            [`${item?.id}`]: {
                              subOptionId: i.subOptionId,
                              subOptionLabel: i.subOptionLabel,
                              documentProductLineId: i.documentProductLineId,
                              id: i.id,
                            },
                          },
                        },
                      },
                    },
                  });
                }
              });
            });
          });
        }
      }
    }, [options]);

    const handleDateChange = (date: dayjs.Dayjs | null, uuid?: string | number) => {
      updateDataSource(
        {
          prestationDateList: cellDataSource?.prestationDateList?.map((item) =>
            item.uuid === uuid || item?.id === uuid
              ? { ...item, prestationDate: date ? date.format('YYYY-MM-DD') : null }
              : item,
          ),
        },
        cellDataSource?.uuid,
      );
    };
    const handleTimeSlotChange = (timeSlot: string, uuid?: string | number) => {
      updateDataSource(
        {
          prestationDateList: cellDataSource?.prestationDateList?.map((item) =>
            item.uuid === uuid || item?.id === uuid ? { ...item, subOptionLabel: timeSlot } : item,
          ),
        },
        cellDataSource?.uuid,
      );
    };
    const handleSubOptionChange = (subOptionLabel: string, uuid?: string | number) => {
      updateDataSource(
        {
          prestationDateList: cellDataSource?.prestationDateList?.map((item) =>
            item.uuid === uuid || item?.id === uuid ? { ...item, optionLabel: subOptionLabel } : item,
          ),
        },
        cellDataSource?.uuid,
      );
    };

    const handleAddPrestationDateLine = (prestationDateItem: DocumentProductLinePrestation) => {
      updateDataSource(
        { prestationDateList: [...(cellDataSource?.prestationDateList ?? []), prestationDateItem] },
        cellDataSource?.uuid,
      );
    };

    const handleRemovePrestationDateLine = (uuid?: string | number, type?: string) => {
      if (type === 'update') {
        updateDataSource(
          { prestationDateList: cellDataSource?.prestationDateList?.filter((item) => item.id !== uuid) },
          cellDataSource?.uuid,
        );
      } else if (type === 'create') {
        updateDataSource(
          { prestationDateList: cellDataSource?.prestationDateList?.filter((item) => item.uuid !== uuid) },
          cellDataSource?.uuid,
        );
      }
    };

    const handleUpdateTotalPrice = (newTotal: number) => {
      if (updateDataSource) updateDataSource({ total: newTotal }, cellDataSource?.uuid);
    };
    const getProductTypeRegulId = (description: string) => {
      try {
        if (description && description.length > 0) {
          const regex = /\[Prix-Regul-(\d+)\]/g;
          const matches = description.match(regex);

          if (matches) {
            const numbers = [
              ...new Set(
                matches.map((match) => {
                  const number = match.replace('[Prix-Regul-', '').replace(']', '');
                  return parseInt(number);
                }),
              ),
            ];
            return numbers;
          }
        }
      } catch (ex) {
        console.log(ex);
      }
      return undefined;
    };
    const createNewRow = (
      uuid: string,
      index: number,
      product: Product,
      rowProduct: DocumentProductLine[],
      creationType?: string,
    ) => {
      const prestationDateItem = {
        uuid: uuidv4(),
        prestationDate: null,
        timeSlotId: null,
        subOptionId: null,
        isNewLine: true,
      };
      const newRow: ProductLineDevis = {
        uuid: uuid,
        lineOrderNumber: index,
        creationType: creationType,
        total: 0,
        quantity: 1,
        discount: 0,
        discountUnit: '%',
        productTypeId: product?.productTypeId,
        description: product?.description,
        product,
        productId: product.id,
        isCatalog: isDocumentCatalog,
        parentKey: product?.parentKey,
        mainProductId: product?.mainProductId?.toString(),
        productTypeRegulId: getProductTypeRegulId(product?.description),
        prestationDateList: [...(cellDataSource?.prestationDateList ?? []), prestationDateItem],
        productNameForClient: product?.name,
      };
      newRow.total = total;
      rowProduct.push(newRow);
    };
    const handleChangeDataSwap = (index: ProductLineDevis, changeIndex: number) => {
      form.setFieldsValue({
        lineItems: {
          [`${index?.id && index?.creationType === 'header' ? index?.id : index?.uuid}`]: {
            lineOrderNumber: changeIndex,
          },
        },
      });
    };
    const handleAddNewRowForMultiProduct = (creationType: ProductLineDevisType, parentKey?: string) => {
      if (!parentKey) return;

      const prevDataSource = [...(dataSource || [])];
      const parentProductIndex = prevDataSource.findIndex((item) => item.uuid === parentKey);
      if (parentProductIndex === -1) return;

      const parentProduct = prevDataSource[parentProductIndex];
      const children = prevDataSource.filter((item) => item.parentKey === parentKey);

      let insertIndex = parentProductIndex + 1;
      if (children.length > 0) {
        const lastChildIndex = prevDataSource.findIndex((item) => item.uuid === children[children.length - 1].uuid);
        insertIndex = lastChildIndex + 1;
      }

      const lineOrderNumber =
        children.length > 0
          ? (Number(children[children.length - 1].lineOrderNumber) || 0) + 1
          : (Number(parentProduct.lineOrderNumber) || 0) + 1;

      const newRow: ProductLineDevis = {
        uuid: uuidv4(),
        creationType,
        total: 0,
        quantity: 1,
        discount: 0,
        isCatalog: isDocumentCatalog,
        description: '',
        lineOrderNumber,
        parentKey,
        mainProductId: parentProduct?.product?.id?.toString(),
      };
      //handle add new row with case update
      const parentProductLine = prevDataSource.find(
        (item) => item.parentKey === parentKey && item?.mainProductId && item?.parentProductLineId,
      );
      if (parentProductLine) {
        newRow.mainProductId = parentProductLine?.mainProductId;
        newRow.parentProductLineId = parentProductLine?.parentProductLineId;
      }
      prevDataSource.splice(insertIndex, 0, newRow);
      prevDataSource.map((item, index) => {
        handleChangeDataSwap(item, index);
      });

      const newDataSource = prevDataSource.map((item, index) => ({
        ...item,
        lineOrderNumber: index,
      }));
      setDataSource(newDataSource);
    };
    const onSelectProductSubProduct = async (productId: number) => {
      try {
        const products = await productService?.getProducts({
          id: productId,
          include: 'ProductType.ProductTypeUnit|ProductType.ProductTypeIntervention',
        });
        const product = products?.rows?.[0];
        const prestationDateItem = {
          uuid: uuidv4(),
          prestationDate: null,
          timeSlotId: null,
          subOptionId: null,
          isNewLine: true,
        };
        updateDataSource(
          {
            product: product,
            productNameForClient: product?.name,
            productTypeId: product.productTypeId,
            productId: product.id,
            isCatalog: isDocumentCatalog,
            productTypeRegulId: getProductTypeRegulId(product.description),
            prestationDateList: [...(cellDataSource?.prestationDateList ?? []), prestationDateItem],
          },
          cellDataSource?.uuid,
        );
      } catch (error) {
        console.log('onSelectProduct: ', error);
      }
    };
    const onSelectProduct = async (productId: number) => {
      try {
        setOnLoadingSelectProduct(true);
        let newProduct = await productService.getOneMultiProduct(productId, {
          include: 'MainProducts.SubProduct|ProductType.ProductTypeUnit|ProductType.ProductTypeIntervention',
        });
        const subProducts =
          newProduct?.MainProducts?.sort(
            (a: ProductComposition, b: ProductComposition) => (a?.sortOrder || 0) - (b?.sortOrder || 0),
          )?.flatMap((i: ProductComposition) => i.SubProduct) || [];
        if (subProducts.length > 0) {
          newProduct = {
            ...newProduct,
            SubProduct: subProducts,
          };
        }
        const rowChildProduct: ProductLineDevis[] = [];
        if (newProduct.ProductType?.isMultiProduct && newProduct.MainProducts) {
          form.setFieldValue([`line_items`, record.uuid], undefined);
          const newDataSource = dataSource?.filter((i) => i.uuid !== record.uuid);
          const headerProduct: ProductLineDevis = {
            uuid: uuidv4(),
            lineOrderNumber: (record?.lineOrderNumber || 0) + 1,
            creationType: 'header-multi-product',
            productNameForClient: newProduct?.name,
            product: newProduct,
            headerName: newProduct?.name,
          };
          rowChildProduct.push(headerProduct);
          newProduct.SubProduct?.forEach((item: Product, index: number) => {
            const productChild = {
              ...item,
              ProductType: {
                ...newProduct?.ProductType,
              },
              parentKey: headerProduct?.uuid,
              mainProductId: headerProduct?.product?.id?.toString(),
            };
            const uuid = uuidv4();
            createNewRow(
              uuid,
              (record.lineOrderNumber || 0) + (index + 1) + 1,
              productChild,
              rowChildProduct,
              'multi-product',
            );
          });
          setDataSource([...(newDataSource || []), ...rowChildProduct]);
        } else if (dataSource) {
          const lastIndex = dataSource && dataSource?.length > 0 ? dataSource?.length - 2 : 0;
          const lastElement = dataSource?.[lastIndex];
          const findProduct = dataSource?.find((i) => i.uuid === lastElement?.uuid);
          if (findProduct?.parentKey) {
            form.setFieldValue([`line_items`, record.uuid], undefined);
            dataSource = dataSource?.filter((i) => i.uuid !== record.uuid);
            const separatorProduct: ProductLineDevis = {
              uuid: record.uuid,
              lineOrderNumber: record.lineOrderNumber,
              creationType: 'header',
              headerName: createSeparator(),
            };
            rowChildProduct.push(separatorProduct);
            const uuid = uuidv4();
            createNewRow(uuid, (record.lineOrderNumber || 0) + 1, newProduct, rowChildProduct, 'product');
            setDataSource([...(dataSource || []), ...rowChildProduct]);
          } else {
            const prestationDateItem = {
              uuid: uuidv4(),
              prestationDate: null,
              timeSlotId: null,
              subOptionId: null,
              isNewLine: true,
            };
            updateDataSource(
              {
                product: newProduct,
                productNameForClient: newProduct?.name,
                productTypeId: newProduct.productTypeId,
                productId: newProduct.id,
                isCatalog: isDocumentCatalog,
                productTypeRegulId: getProductTypeRegulId(newProduct.description),
                prestationDateList: [...(cellDataSource?.prestationDateList ?? []), prestationDateItem],
              },
              cellDataSource?.uuid,
            );
          }
        }
      } catch (error) {
        console.log('onSelectProduct: ', error);
      } finally {
        setOnLoadingSelectProduct(false);
      }
    };
    const createSeparator = () => {
      const separatorContent = '***';
      const separatorLength = 30;
      const separatorName = separatorContent.repeat(separatorLength);
      const numberSeparator =
        (dataSource && dataSource?.filter((i) => i.headerName?.includes(separatorName))?.length) || 0;
      return separatorName + '*'.repeat(numberSeparator);
    };
    const onChangeDiscount = (discount: number) => {
      updateDataSource({ discount }, cellDataSource?.uuid);
    };

    const onChangeDiscountUnit = (discountUnit: string) => {
      updateDataSource({ discountUnit }, cellDataSource?.uuid);
    };
    const selectCatalogPriceZones = (args: {
      priceFamilyId: number | null;
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog: boolean | null;
    }) => {
      const { priceFamilyId, catalogPriceZones, isCatalog } = args;
      const selectedPriceFamily = priceFamilies?.find((priceFamily) => priceFamily.id === priceFamilyId);
      const isForfaitType = selectedPriceFamily?.key === TARIF_TYPE.FORFAIT;
      const shouldUseForfait = isForfaitType || (priceFamilyId === null && isCatalog);
      return shouldUseForfait ? catalogPriceZones?.forfait : (catalogPriceZones?.semiforfait ?? []);
    };

    const onSelectPriceFamily = (args: {
      priceFamilyId: number;
      catalogPriceZones: CatalogPriceZonesType;
      isCatalog: boolean | null;
    }) => {
      const { priceFamilyId, catalogPriceZones, isCatalog } = args;
      const updatedPriceFamilyId = priceFamilyId;
      const selectedPriceFamily = priceFamilies?.find((priceFamily) => priceFamily.id === updatedPriceFamilyId);
      form.setFieldValue(
        ['lineItems', cellDataSource?.uuid, 'prestation', 'tarifItems', 'priceFamilyId'],
        updatedPriceFamilyId,
      );
      // update isCatalog when the user select a new price family
      const priceMarginData = getPriceMargin({
        priceFamilyId: updatedPriceFamilyId,
        catalogPriceZones,
        isCatalog,
      });
      const priceMargin = priceMarginData.priceMargin;
      const validSP = priceMarginData.validSP;

      const newCatalogPriceZones = selectCatalogPriceZones({
        priceFamilyId: updatedPriceFamilyId,
        catalogPriceZones,
        isCatalog,
      });
      const selectedPrice = newCatalogPriceZones?.find(
        (item) =>
          (item.priceFamilyId === updatedPriceFamilyId && item.priceId === null && item.priceOptionId === null) ||
          (item.DestCatalogPrice?.priceFamilyId === updatedPriceFamilyId &&
            item.DestCatalogPrice?.priceId === null &&
            item.DestCatalogPrice.priceOptionId === null),
      );
      updateDataSource(
        {
          isCatalog,
          priceFamilyId: updatedPriceFamilyId,
          priceFamilyPriceMargin: priceMargin,
          priceFamilyValidSP: validSP,
          priceFamilyBuyingPrice: priceMargin !== null ? parseFloat(selectedPrice?.priceValue ?? '0') : null,
        },
        cellDataSource?.uuid,
      );
      form.setFieldsValue({
        lineItems: {
          [`${cellDataSource?.uuid}`]: {
            prestation: {
              tarifItems: {
                [`${updatedPriceFamilyId}`]: {
                  priceFamilyLabel: selectedPriceFamily?.labelName,
                  linePriceFamilyId: cellDataSource?.linePriceFamilyId,
                },
              },
            },
          },
        },
      });
      mapCatalogPriceZones({
        priceFamilyId: updatedPriceFamilyId,
        priceMargin: { priceMargin: priceMargin, validSP: validSP },
        catalogPriceZones,
        isCatalog: isDocumentCatalog,
      });
    };

    const onChangeQuantity = (quantity: number) => {
      let newPrestationDateList = cellDataSource?.prestationDateList ?? [];
      if (quantity > 0) {
        newPrestationDateList = cellDataSource?.prestationDateList?.slice(0, quantity) ?? [];
      }
      updateDataSource(
        {
          quantity,
          prestationDateList: [...newPrestationDateList],
        },
        cellDataSource?.uuid,
      );
    };
    const handleChangeParameterPrestationSubOptionValue = () => {
      const values = Object.values(prestationValues.productLineSubOption) as DocumentProductLineSubOption[];
      const keys = Object.keys(prestationValues.productLineSubOption);

      // Map over the values array to update each element
      const updatedValues = values.map((value, index) => {
        const matchingOption = options.find((item) => item.id === parseInt(keys[index], 10));

        if (matchingOption) {
          const matchingSubOption = matchingOption?.SubOptions?.find((subOption) => subOption.id === value.subOptionId);

          if (matchingSubOption) {
            value.subOptionLabel = matchingSubOption.name;
            value.isShowOnClientView = matchingSubOption.showOnClientView;
          }
        }

        return value; // Return the updated element
      });

      setParameterPrestationOptions(updatedValues); // Pass the updated array
    };

    const handleChangeParameterTarifValue = (
      priceId?: number,
      priceOptionId?: number,
      priceValue?: number,
      priceOptionValue?: string,
      priceSubOptionId?: string,
    ) => {
      setParameterTarifs((prevList: ParameterTarifLine[]) =>
        prevList.map((item) => ({
          ...item,
          ...(priceOptionId
            ? {
                priceOptions: item.priceOptions.map((priceOption) => {
                  if (priceOption.id === priceOptionId) {
                    return {
                      ...priceOption,
                      defaultValue: priceSubOptionId
                        ? priceSubOptionId
                        : priceOptionValue && priceOptionValue != ''
                          ? priceOptionValue
                          : '0',
                    };
                  }
                  return priceOption;
                }),
              }
            : {}),
          ...(priceId && item.price.id === priceId ? { value: priceValue } : {}),
        })),
      );
    };

    const handleSwitchValue = (toggled: boolean, uuid?: string) => {
      updateDataSource({ isSetTotalZero: toggled }, uuid);
      form.setFieldsValue({
        lineItems: {
          [`${uuid}`]: {
            isSetTotalZero: toggled,
          },
        },
      });
    };
    let childrenNode = <></>;
    switch (dataIndex) {
      case 'id':
        childrenNode = (
          <>
            {cellDataSource?.creationType === 'multi-product' ? (
              <></>
            ) : (
              <>
                <SortHandle setActivatorNodeRef={setActivatorNodeRef} listeners={listeners} />
              </>
            )}
          </>
        );
        break;
      case 'name':
        childrenNode = (
          <>
            {cellDataSource?.creationType === 'multi-product' ? (
              <>
                <Row>
                  <Col span={2} style={{ paddingTop: '5px' }}>
                    <SortHandle setActivatorNodeRef={setActivatorNodeRef} listeners={listeners} />
                  </Col>
                  <Col span={22}>
                    <ProductNameCell
                      documentType={documentType}
                      form={form}
                      isCatalog={isCatalog}
                      record={cellDataSource}
                      dataList={dataList}
                      productTypeReguls={productTypeReguls}
                      onSelectProduct={onSelectProduct}
                      onSelectProductSubProduct={onSelectProductSubProduct}
                      prestationDateList={cellDataSource?.prestationDateList ?? []}
                      prestationSubOptions={parameterPrestationOptions}
                      parameterTarifs={parameterTarifs}
                      status={status}
                      logComment={logComment}
                      handleAddNewRowForMultiProduct={handleAddNewRowForMultiProduct}
                    />
                  </Col>
                </Row>
              </>
            ) : (
              <>
                <ProductNameCell
                  documentType={documentType}
                  form={form}
                  isCatalog={isCatalog}
                  record={cellDataSource}
                  dataList={dataList}
                  productTypeReguls={productTypeReguls}
                  onSelectProduct={onSelectProduct}
                  prestationDateList={cellDataSource?.prestationDateList ?? []}
                  prestationSubOptions={parameterPrestationOptions}
                  parameterTarifs={parameterTarifs}
                  status={status}
                  logComment={logComment}
                  handleAddNewRowForMultiProduct={handleAddNewRowForMultiProduct}
                />
              </>
            )}
          </>
        );
        break;
      case 'prestation':
        childrenNode = (
          <PrestationCell
            form={form}
            record={cellDataSource}
            options={options}
            isCatalog={isCatalog}
            priceFamilies={priceFamilies ?? []}
            loading={
              catalogPriceZonesLoading ||
              priceFamiliesLoading === 'pending' ||
              optionsLoading === 'pending' ||
              productTypeUnitsLoading === 'pending' ||
              productTypeRegulsLoading === 'pending' ||
              pricesLoading === 'pending' ||
              onLoadingSelectProduct
            }
            priceTypes={dataList.priceTypes}
            productTypeId={cellDataSource?.productTypeId}
            priceFamilyId={cellDataSource?.priceFamilyId}
            productTypeUnitId={productTypeUnitId}
            timeSlotOptionType={timeSlotOptionType}
            optionOptionType={optionOptionType}
            onSelectPriceFamily={(value) =>
              onSelectPriceFamily({
                priceFamilyId: value,
                catalogPriceZones,
                isCatalog: isDocumentCatalog,
              })
            }
            prestationDateList={cellDataSource?.prestationDateList ?? []}
            onChangePrestationOptions={handleChangeParameterPrestationSubOptionValue}
            onAddPrestationDateLine={handleAddPrestationDateLine}
            onRemovePrestationDateLine={handleRemovePrestationDateLine}
            parameterTarifs={parameterTarifs}
            unitPrice={unitPrice}
            onDateChange={handleDateChange}
            status={status}
            documentType={documentType}
            productTypeUnits={productTypeUnits}
            onChangeTimeSlot={handleTimeSlotChange}
            onChangeSubOption={handleSubOptionChange}
          />
        );
        break;
      case 'quantity':
        childrenNode = <QuantityCell record={cellDataSource} onChangeQuantity={onChangeQuantity} status={status} />;
        break;
      case 'sub-total':
        childrenNode = <SubTotalCell record={cellDataSource} subTotal={subTotal} />;
        break;
      case 'discount':
        childrenNode = (
          <DiscountCell
            record={cellDataSource}
            onChangeDiscount={onChangeDiscount}
            onChangeDiscountUnit={onChangeDiscountUnit}
            status={status}
          />
        );
        break;
      case 'total':
        childrenNode = (
          <TotalCell record={cellDataSource} total={total} onSwitchValue={handleSwitchValue} status={status} />
        );
        break;
      case 'action':
        childrenNode = <ActionCell record={cellDataSource} onDelete={onDelete} status={status} />;
        break;
      default:
        childrenNode = <></>;
    }
    let childNode = children;
    if (editable) {
      childNode = childrenNode;
    }
    return (
      <td
        {...restProps}
        style={cellDataSource?.creationType === `header-multi-product` ? { backgroundColor: '#fafafa' } : {}}
      >
        {childNode}
      </td>
    );
  },
);

ProductCell.displayName = 'ProductCell';
export default ProductCell;
