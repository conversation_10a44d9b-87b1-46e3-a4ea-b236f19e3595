import { PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, Form, FormInstance, Input, Row, Select, Typography } from 'antd';
import {
  DocumentProductLinePrestation,
  Product,
  ProductLineDevis,
  ProductTypeRegul,
  DocumentProductLineSubOption,
} from 'models';
import { Fragment, useEffect, useState } from 'react';
import { checkHeaderNameDuplicates, duplicates, formatDate, productFilter } from 'utils';
import { ParameterTarifLine } from './PrestationCell';
import { DOCUMENT_STATUSES, DOCUMENT_TYPES, PRICE_TYPE_LOGICS, PRICE_TYPES } from '../../../utils/constant';
import { ProductLineDevisType } from 'models/product_lines_devis';
import { productService } from 'services';
import { useDebounce } from 'hooks';
import { toast } from 'react-toastify';
import { ParameterTarifItem } from 'components/Common';
import CopyDataTextDocument from 'components/Common/CopyDataText';
const { Text } = Typography;
const { TextArea } = Input;

interface ProductNameCellProps {
  form: FormInstance;
  record: ProductLineDevis;
  productTypeReguls: ProductTypeRegul[];
  dataList?: { products: Product[] };
  onSelectProduct?: (value: number) => void;
  onSelectProductSubProduct?: (value: number) => void;
  prestationDateList: DocumentProductLinePrestation[];
  prestationSubOptions: DocumentProductLineSubOption[];
  parameterTarifs: ParameterTarifLine[];
  status?: string;
  documentType?: string;
  logComment?: string;
  isCatalog?: boolean | null;
  handleAddNewRowForMultiProduct: (creationType: ProductLineDevisType, parentKey?: string) => void;
}

const ProductNameCell = (props: ProductNameCellProps) => {
  const {
    form,
    record,
    onSelectProduct,
    prestationDateList,
    prestationSubOptions,
    parameterTarifs,
    status,
    documentType,
    logComment,
    productTypeReguls,
    isCatalog,
    handleAddNewRowForMultiProduct,
    onSelectProductSubProduct,
  } = props;
  // const product = dataList?.products?.find((products) => products.id === record?.product?.id);
  const [product, setProduct] = useState<Product>();
  const [listProduct, setListProduct] = useState<Product[]>();
  const [listSubProduct, setListSubProduct] = useState<Product[]>();
  const [productNameForClient, setProductNameForClient] = useState<string>(
    (record?.productNameForClient ? record?.productNameForClient : product?.name) ?? '',
  );
  const [searchLoading, setSearchLoading] = useState<boolean>(false);
  const [searchSubProductLoading, setSearchSubProductLoading] = useState<boolean>(false);
  useEffect(() => {
    let description = record?.description ? record?.description : product?.description;
    record?.productTypeRegulId?.forEach((item) => {
      const listProductTypeReguls = productTypeReguls?.filter(
        (obj) => obj.productId === item && obj.productTypeId == product?.productTypeId,
      );
      // Check description and productTypeRegul
      if (description && listProductTypeReguls && listProductTypeReguls?.length > 0) {
        form.setFieldValue(['lineItems', `${record?.uuid}`, 'description'], description);
        listProductTypeReguls?.forEach((item) => {
          const textReplace = `[Prix-Regul-${item.productId}](Regul)`;
          const dataReplace = `${parseFloat(item.defaultPrice)} €`;
          description = description?.replaceAll(textReplace, dataReplace);
        });
      }
    });
    form.setFieldValue(
      ['lineItems', `${record?.uuid}`, 'descriptionWithParameter'],
      record?.descriptionWithParameter ?? description,
    );
  }, [productTypeReguls, record?.description, product?.description]);
  useEffect(() => {
    let prestationVueClient = '';
    prestationSubOptions.forEach((prestationSubOption) => {
      if (
        prestationSubOption &&
        !(!prestationSubOption.isShowOnClientView && documentType === DOCUMENT_TYPES.QUOTATION)
      ) {
        prestationVueClient +=
          '\n' + prestationSubOption.optionLabel + ': ' + prestationSubOption.subOptionLabel + '\n';
      }
    });
    if (prestationDateList.length > 0 && !prestationDateList.every((item) => item.prestationDate === null)) {
      prestationVueClient += '\nDate prestation\n';
      for (let i = 0; i < prestationDateList.length; i++) {
        const prestationDateItem = prestationDateList[i];
        if (prestationDateItem?.prestationDate) {
          prestationVueClient +=
            '#' + (i + 1) + ' - ' + formatDate(prestationDateItem?.prestationDate?.toString() ?? '');
        }
        if (prestationDateItem?.optionLabel) {
          prestationVueClient += ' - ' + prestationDateItem?.optionLabel;
        }
        if (prestationDateItem?.subOptionLabel) {
          prestationVueClient += ' - ' + prestationDateItem?.subOptionLabel;
        } else if (prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel) {
          prestationVueClient +=
            ' - ' + prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel;
        }
        prestationVueClient += '\n';
      }
    }
    for (let i = 0; i < parameterTarifs.length; i++) {
      const parameterTarif = parameterTarifs[i];
      const priceLogicKey = parameterTarif?.price?.PriceTypeLogic?.key;
      // Skip if the logic is "indicative_purpose" and all numberInputs have defaultValue = '0'
      const hasValidNumberInput = parameterTarif?.priceOptions?.some(
        (priceOption) => priceOption?.PriceType?.key === PRICE_TYPES.numberInput && priceOption?.defaultValue !== '0',
      );

      const shouldDisplay =
        priceLogicKey !== PRICE_TYPE_LOGICS.indicative_purpose ||
        (priceLogicKey === PRICE_TYPE_LOGICS.indicative_purpose && hasValidNumberInput);
      if (shouldDisplay) {
        // Add the main price name
        prestationVueClient += '\n' + parameterTarif?.price?.name + '\n';
        if (parameterTarif?.priceOptions) {
          for (let j = 0; j < parameterTarif.priceOptions.length; j++) {
            const priceOption = parameterTarif.priceOptions[j];
            const isZeroIndicativeNumberInput =
              priceLogicKey === PRICE_TYPE_LOGICS.indicative_purpose &&
              priceOption?.PriceType?.key === PRICE_TYPES.numberInput &&
              priceOption?.defaultValue === '0';
            // Skip if it's an indicative purpose with default value 0
            if (!isZeroIndicativeNumberInput) {
              prestationVueClient += '  ' + priceOption?.name + ': ';
              // Check if the option has sub-options and display the name of the selected one
              if (priceOption?.PriceSubOptions && priceOption?.PriceSubOptions?.length > 0) {
                const selectedSubOption = priceOption?.PriceSubOptions?.find(
                  (subOption) => subOption?.id === Number(priceOption?.defaultValue),
                );
                prestationVueClient += selectedSubOption?.name ?? '';
              } else {
                // Otherwise, just display the default value
                prestationVueClient += priceOption?.defaultValue + ' ';
              }
              // Append the unit if it exists
              prestationVueClient += (priceOption?.unit || '') + '\n';
            }
          }
          // Display the price label with value
          prestationVueClient += '  ' + parameterTarif?.price?.labelName + ': ' + parameterTarif?.value + '€/ ';
          const firstOption = parameterTarif?.priceOptions[0];
          // Append unit/sub-option name for the first price option
          if (firstOption?.PriceSubOptions && firstOption?.PriceSubOptions?.length > 0) {
            const selectedSubOption = firstOption?.PriceSubOptions?.find(
              (subOption) => subOption?.id === Number(firstOption?.defaultValue),
            );
            prestationVueClient += selectedSubOption?.name ?? '';
          } else {
            prestationVueClient += firstOption?.unit + '\n';
          }
        }
      }
    }

    form.setFieldValue(['lineItems', `${record?.uuid}`, 'prestationVueClient'], prestationVueClient);
    if (record?.creationType === 'header-multi-product') {
      form.setFieldValue(['lineItems', `${record?.uuid}`, 'headerName'], productNameForClient);
    }
  }, [productNameForClient, prestationDateList, parameterTarifs, prestationSubOptions]);
  let content: JSX.Element | null = null;
  useEffect(() => {
    setProduct(record?.product);
    setProductNameForClient(record?.productNameForClient ?? '');
    form.setFieldValue(['lineItems', `${record?.uuid}`, 'productNameForClient'], record?.productNameForClient ?? '');
  }, [record?.product?.id]);
  useEffect(() => {
    initData();
  }, []);
  const initData = async () => {
    try {
      const [products, subProducts] = await Promise.all([
        productService.getProducts({
          limit: '10',
          orderBy: 'frequencyCount,desc|name', // order by popularity
          exclude: 0,
          isActive: 1,
          isVisible: 1,
        }),
        productService.getProducts({
          limit: '10',
          orderBy: 'frequencyCount,desc|name', // order by popularity
          exclude: 0,
          isActive: 1,
          isVisible: 1,
        }),
      ]);
      setListSubProduct(products?.rows || []);
      setListProduct(subProducts?.rows || []);
    } catch (error) {
      console.log('error on initData: ', error);
    }
  };
  const onSearchProduct = useDebounce(async (value: string) => {
    try {
      setSearchLoading(true);
      const data = await productService.getMultiProduct({
        name: value,
        limit: 'unlimited',
        orderBy: 'frequencyCount,desc|name', // order by popularity
        exclude: 0,
        isActive: 1,
        isVisible: 1,
      });
      setListProduct(data?.rows || []);
    } catch (error) {
      console.log('error on search product: ', error);
    } finally {
      setSearchLoading(false);
    }
  }, 500);
  const onSearchSubProduct = useDebounce(async (value: string) => {
    try {
      setSearchSubProductLoading(true);
      const data = await productService.getProducts({
        name: value,
        limit: 'unlimited',
        orderBy: 'frequencyCount,desc|name', // order by popularity
        exclude: 0,
        isActive: 1,
        isVisible: 1,
      });
      setListSubProduct(data?.rows || []);
    } catch (error) {
      console.log('error on search sub product: ', error);
    } finally {
      setSearchSubProductLoading(false);
    }
  }, 500);
  const onChangeHeaderName = (value: string) => {
    try {
      checkHeaderNameDuplicates(form, value);
      setProductNameForClient(value);
    } catch (error) {
      console.log('onChangeHeaderName: ', error);
    }
  };
  const handleCopyProductName = () => {
    const productName = record?.product?.name?.toString();
    if (productName !== undefined && productName.length > 0) {
      navigator.clipboard.writeText(productName ? productName : '');
      toast.success(<div>Copié dans le presse-papier</div>);
    }
  };
  const validator = (uuid?: string) => {
    try {
      const values = form.getFieldsValue() || {};
      const duplicatedUuids = duplicates(values?.lineItems || {});
      if (duplicatedUuids && duplicatedUuids?.size > 0 && uuid && duplicatedUuids?.has(uuid)) {
        return Promise.reject(
          new Error('Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.'),
        );
      }
      return Promise.resolve();
    } catch (error) {
      console.log('validator: ', error);
    }
  };
  // new product
  if (record.creationType === 'product' && !record.productTypeId) {
    content = (
      <Form.Item rules={[{ required: true }]} name={['lineItems', `${record.uuid}`, 'item_id']}>
        <Select
          placeholder='Sélectionner'
          optionFilterProp='children'
          onChange={onSelectProduct}
          showSearch
          filterOption={(input, option) => productFilter(input, option)}
          options={listProduct
            ?.filter((product) => product.isVisible)
            .map((product) => ({
              value: product.id,
              label: product.name,
            }))}
          disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          onSearch={onSearchProduct}
          loading={searchLoading}
        />
      </Form.Item>
    );
  } else if (!record.productTypeId && record?.creationType === 'multi-product') {
    content = (
      <Form.Item rules={[{ required: true }]} name={['lineItems', `${record.uuid}`, 'item_id']}>
        <Select
          placeholder='Sélectionner'
          optionFilterProp='children'
          onChange={onSelectProductSubProduct}
          showSearch
          filterOption={(input, option) => productFilter(input, option)}
          options={listSubProduct
            ?.filter((product) => product.isVisible)
            .map((product) => ({
              value: product.id,
              label: product.name,
            }))}
          disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          onSearch={onSearchSubProduct}
          loading={searchSubProductLoading}
        />
      </Form.Item>
    );
  }
  // if normal product
  if (record.productTypeId && record?.creationType === 'product') {
    content = (
      <>
        <Row gutter={[16, 0]} className='copy-row'>
          <Col xs={{ span: 22 }}>
            <Form.Item
              label='Référence interne'
              className='mb-2'
              name={['lineItems', `${record?.uuid}`, 'name']}
              initialValue={record?.product?.name}
              rules={[
                {
                  validator: (_, value) => {
                    if (value && /[<>]/.test(value)) {
                      return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                    }
                    return Promise.resolve();
                  },
                },
              ]}
            >
              <Text>
                <Text underline strong>
                  {record?.product?.name}
                </Text>
              </Text>
            </Form.Item>
          </Col>
          <Col className='prodct-cell__display-right' xs={{ span: 2 }}>
            <CopyDataTextDocument
              text={record?.product?.name || ''}
              className='copy-icon-outline'
              onClick={handleCopyProductName}
              styleMode='form'
            />
          </Col>
        </Row>
        <Form.Item
          label='Libellé pour client'
          name={['lineItems', `${record?.uuid}`, 'productNameForClient']}
          initialValue={productNameForClient ? productNameForClient : record?.product?.name}
          rules={[
            { required: true, message: 'Ce champ est requis' },
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
          className='mb-4'
        >
          <Input
            placeholder='Référence interne'
            onChange={(e) => onChangeHeaderName(e.target.value)}
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item
          name={['lineItems', `${record?.uuid}`, 'descriptionWithParameter']}
          className='mb-4'
          initialValue={record?.descriptionWithParameter}
          rules={[
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <TextArea
            autoSize={{ minRows: 1, maxRows: 22 }}
            className='logistique__document-textarea textareaProduct'
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item name={['lineItems', `${record?.uuid}`, 'description']} className='hidden'></Form.Item>
        <Form.Item className='mb-2'>
          <Text>
            <Text strong>Commentaire LOG</Text>
          </Text>
        </Form.Item>
        <Form.Item name={['lineItems', `${record?.uuid}`, 'logComment']} className='mb-2 ml-4'>
          <Text>{logComment}</Text>
        </Form.Item>
        <Form.Item className='mb-2'>
          <Text>
            <Text strong>Prestation (Vue Client)</Text>
          </Text>
        </Form.Item>
        <Form.Item name='informationClientView' className='mb-2 prestation-detail'>
          <div className='mb-6'>
            {prestationSubOptions?.length > 0 &&
              prestationSubOptions.map(
                (prestationSubOption, index) =>
                  !(!prestationSubOption.isShowOnClientView && documentType === DOCUMENT_TYPES.QUOTATION) && (
                    <div className='mb-4' key={index}>
                      <Text>{prestationSubOption.optionLabel}</Text>:<Text> {prestationSubOption.subOptionLabel}</Text>
                    </div>
                  ),
              )}
          </div>

          {prestationDateList.length > 0 && !prestationDateList.every((item) => item.prestationDate === null) && (
            <div className='mb-6'>
              <div className='mb-2'>
                <Text strong>Date prestation</Text>
              </div>
              {prestationDateList.map((prestationDateItem, index) => (
                <Fragment key={prestationDateItem?.id}>
                  {prestationDateItem?.prestationDate && (
                    <div key={index} className='mb-2'>
                      <Text>
                        #{index + 1} - {formatDate(prestationDateItem?.prestationDate?.toString() ?? '')}
                        {prestationDateItem?.optionLabel && ' - ' + prestationDateItem?.optionLabel}
                        {prestationDateItem?.subOptionLabel
                          ? ' - ' + prestationDateItem?.subOptionLabel
                          : prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel &&
                            ' - ' + prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel}
                      </Text>
                    </div>
                  )}
                </Fragment>
              ))}
            </div>
          )}

          {parameterTarifs?.map((parameterTarif) => (
            <ParameterTarifItem key={parameterTarif?.price?.id} parameterTarif={parameterTarif} />
          ))}
        </Form.Item>
        <Form.Item className='hidden' name={['lineItems', `${record?.uuid}`, 'prestationVueClient']}>
          <Input placeholder='Référence interne' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'productId']}
          initialValue={record?.product?.id}
        >
          <Input
            placeholder='Référence interne'
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item className='hidden' name={['lineItems', `${record?.uuid}`, 'isCatalog']} initialValue={isCatalog}>
          <Input placeholder='Référence interne' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'interventionId']}
          initialValue={
            record?.product?.ProductType?.interventionId && record?.product?.ProductType?.interventionId > 0
              ? record?.product?.ProductType?.interventionId
              : record?.interventionId
          }
        >
          <Input placeholder='interventionId' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'interventionKey']}
          initialValue={
            record?.product?.ProductType?.ProductTypeIntervention?.key &&
            record?.product?.ProductType?.ProductTypeIntervention?.key.length > 0
              ? record?.product?.ProductType?.ProductTypeIntervention?.key
              : record?.interventionKey
          }
        >
          <Input placeholder='interventionKey' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'booksProductId']}
          initialValue={record?.product?.booksProductId}
        ></Form.Item>
        <Form.Item name={['lineItems', `${record.uuid}`, 'id']} initialValue={record.id} className='hidden'></Form.Item>
      </>
    );
  } else if (record.creationType === 'header') {
    // header
    content = (
      <>
        <Form.Item
          className='w-full'
          name={['lineItems', `${record.uuid}`, 'headerName']}
          initialValue={record.headerName}
          rules={[
            { required: true },
            { validator: () => validator(record.uuid) },
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <Input
            onChange={(e) => {
              checkHeaderNameDuplicates(form, e.target.value);
            }}
            placeholder='Titre'
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'booksProductLineHeaderId']}
          initialValue={record.booksProductLineHeaderId}
          className='hidden'
        ></Form.Item>
      </>
    );
  } else if (record?.creationType === 'header-multi-product') {
    content = (
      <div>
        <Form.Item
          label='Référence interne'
          className='w-full mb-2'
          name={['lineItems', `${record?.uuid}`, 'name']}
          initialValue={record?.product?.name}
        >
          <Text>
            <Text underline strong>
              {record?.product?.name}
            </Text>
          </Text>
        </Form.Item>
        <Form.Item
          label='Libellé pour client'
          name={['lineItems', `${record?.uuid}`, 'productNameForClient']}
          initialValue={productNameForClient ? productNameForClient : record?.product?.name}
          rules={[
            { required: true, message: 'Ce champ est requis' },
            { validator: () => validator(record.uuid) },
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
          className='mb-4'
        >
          <Input
            placeholder='Référence interne'
            onChange={(e) => onChangeHeaderName(e.target.value)}
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <div className='btn-add__creation'>
          <Button
            size='large'
            type='primary'
            className={`btn-add__creation-button`}
            onClick={() => handleAddNewRowForMultiProduct('multi-product', record?.uuid)}
            style={{
              width: 'auto',
              height: '32px',
              borderRadius: '6px',
            }}
          >
            <PlusCircleOutlined className='btn-add__creation-icon' />
            <span className='btn-add__creation-text'>Ajouter un sous produit</span>
          </Button>
        </div>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'booksProductLineHeaderId']}
          initialValue={record.booksProductLineHeaderId}
          className='hidden'
        ></Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'productId']}
          initialValue={record?.product?.id}
          className='hidden'
        ></Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'headerName']}
          initialValue={record?.product?.name}
          className='hidden'
        ></Form.Item>
      </div>
    );
  } else if (record.productTypeId && record?.creationType === 'multi-product') {
    content = (
      <div>
        <Row gutter={[16, 0]} className='copy-row'>
          <Col xs={{ span: 22 }}>
            <Form.Item
              label='Référence interne'
              className='mb-2'
              name={['lineItems', `${record?.uuid}`, 'name']}
              initialValue={record?.product?.name}
            >
              <Text>
                <Text underline strong>
                  {record?.product?.name}
                </Text>
              </Text>
            </Form.Item>
          </Col>
          <Col className='prodct-cell__display-right' xs={{ span: 2 }}>
            <CopyDataTextDocument
              text={record?.product?.name || ''}
              className='copy-icon-outline'
              onClick={handleCopyProductName}
              styleMode='form'
            />
          </Col>
        </Row>
        <Form.Item
          label='Libellé pour client'
          name={['lineItems', `${record?.uuid}`, 'productNameForClient']}
          initialValue={productNameForClient ? productNameForClient : record?.product?.name}
          rules={[
            { required: true, message: 'Ce champ est requis' },
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
          className='mb-4'
        >
          <Input
            placeholder='Référence interne'
            onChange={(e) => setProductNameForClient(e.target.value)}
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item
          name={['lineItems', `${record?.uuid}`, 'descriptionWithParameter']}
          className='mb-4'
          initialValue={record?.descriptionWithParameter}
          rules={[
            {
              validator: (_, value) => {
                if (value && /[<>]/.test(value)) {
                  return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                }
                return Promise.resolve();
              },
            },
          ]}
        >
          <TextArea
            autoSize={{ minRows: 1, maxRows: 22 }}
            className='logistique__document-textarea textareaProduct'
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item name={['lineItems', `${record?.uuid}`, 'description']} className='hidden'></Form.Item>
        <Form.Item className='mb-2'>
          <Text>
            <Text strong>Commentaire LOG</Text>
          </Text>
        </Form.Item>
        <Form.Item name={['lineItems', `${record?.uuid}`, 'logComment']} className='mb-2 ml-4'>
          <Text>{logComment}</Text>
        </Form.Item>
        <Form.Item className='mb-2'>
          <Text>
            <Text strong>Prestation (Vue Client)</Text>
          </Text>
        </Form.Item>
        <Form.Item name='informationClientView' className='mb-2 prestation-detail'>
          <div className='mb-6'>
            {prestationSubOptions?.length > 0 &&
              prestationSubOptions.map(
                (prestationSubOption, index) =>
                  !(!prestationSubOption.isShowOnClientView && documentType === DOCUMENT_TYPES.QUOTATION) && (
                    <div className='mb-4' key={index}>
                      <Text>{prestationSubOption.optionLabel}</Text>:<Text> {prestationSubOption.subOptionLabel}</Text>
                    </div>
                  ),
              )}
          </div>

          {prestationDateList.length > 0 && !prestationDateList.every((item) => item.prestationDate === null) && (
            <div className='mb-6'>
              <div className='mb-2'>
                <Text strong>Date prestation</Text>
              </div>
              {prestationDateList.map((prestationDateItem, index) => (
                <Fragment key={prestationDateItem?.id}>
                  {prestationDateItem?.prestationDate && (
                    <div key={index} className='mb-2'>
                      <Text>
                        #{index + 1} - {formatDate(prestationDateItem?.prestationDate?.toString() ?? '')}
                        {prestationDateItem?.optionLabel && ' - ' + prestationDateItem?.optionLabel}
                        {prestationDateItem?.subOptionLabel
                          ? ' - ' + prestationDateItem?.subOptionLabel
                          : prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel &&
                            ' - ' + prestationDateItem?.DocumentProductLinePrestationSubOptions?.[0]?.subOptionLabel}
                      </Text>
                    </div>
                  )}
                </Fragment>
              ))}
            </div>
          )}

          {parameterTarifs?.map((parameterTarif) => (
            <ParameterTarifItem key={parameterTarif?.price?.id} parameterTarif={parameterTarif} />
          ))}
        </Form.Item>
        <Form.Item className='hidden' name={['lineItems', `${record?.uuid}`, 'prestationVueClient']}>
          <Input placeholder='Référence interne' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'productId']}
          initialValue={record?.product?.id}
        >
          <Input
            placeholder='Référence interne'
            disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
          />
        </Form.Item>
        <Form.Item className='hidden' name={['lineItems', `${record?.uuid}`, 'isCatalog']} initialValue={isCatalog}>
          <Input placeholder='Référence interne' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'interventionId']}
          initialValue={
            record?.product?.ProductType?.interventionId && record?.product?.ProductType?.interventionId > 0
              ? record?.product?.ProductType?.interventionId
              : record?.interventionId
          }
        >
          <Input placeholder='interventionId' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'interventionKey']}
          initialValue={
            record?.product?.ProductType?.ProductTypeIntervention?.key &&
            record?.product?.ProductType?.ProductTypeIntervention?.key.length > 0
              ? record?.product?.ProductType?.ProductTypeIntervention?.key
              : record?.interventionKey
          }
        >
          <Input placeholder='interventionKey' />
        </Form.Item>
        <Form.Item
          className='hidden'
          name={['lineItems', `${record?.uuid}`, 'booksProductId']}
          initialValue={record?.product?.booksProductId}
        ></Form.Item>
        <Form.Item name={['lineItems', `${record.uuid}`, 'id']} initialValue={record.id} className='hidden'></Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'mainProductId']}
          initialValue={record?.mainProductId}
          className='hidden'
        ></Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'parentKey']}
          initialValue={record?.parentKey}
          className='hidden'
        ></Form.Item>
        <Form.Item
          name={['lineItems', `${record.uuid}`, 'parentProductLineId']}
          initialValue={record?.parentProductLineId}
          className='hidden'
        ></Form.Item>
      </div>
    );
  }

  return (
    <div style={{ minWidth: '350px', maxWidth: '95%' }}>
      {content}
      <Form.Item
        name={['lineItems', `${record.uuid}`, 'lineOrderNumber']}
        initialValue={record.lineOrderNumber}
        className='hidden'
        rules={[
          {
            validator: (_, value) => {
              if (value && /[<>]/.test(value)) {
                return Promise.reject('Les caractères < et > ne sont pas autorisés.');
              }
              return Promise.resolve();
            },
          },
        ]}
      >
        <Input
          placeholder='Titre'
          disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
        />
      </Form.Item>
      <Form.Item
        name={['lineItems', `${record.uuid}`, 'creationType']}
        initialValue={record?.creationType}
        className='hidden'
      ></Form.Item>
    </div>
  );
};

export default ProductNameCell;
