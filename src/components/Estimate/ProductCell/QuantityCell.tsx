import { Form, InputNumber, Space } from 'antd';
import { RuleObject } from 'antd/es/form';
import { ProductLineDevis } from 'models';
import { DOCUMENT_STATUSES } from '../../../utils/constant';

interface QuantityCellProps {
  record: ProductLineDevis;
  onChangeQuantity: (quantity: number) => void;
  status?: string;
}

const QuantityCell = (props: QuantityCellProps) => {
  const { record, onChangeQuantity, status } = props;
  if (record.creationType !== 'header' && record.creationType !== 'header-multi-product') {
    return (
      <div>
        <Space>
          <Form.Item
            name={['lineItems', `${record.uuid}`, 'quantity']}
            rules={[
              { required: true },
              () => ({
                validator(_: RuleObject, value) {
                  if (isNaN(value) || parseFloat(value) < 0) {
                    return Promise.reject();
                  }
                  return Promise.resolve();
                },
              }),
            ]}
            initialValue={record.quantity}
          >
            <InputNumber
              className='text-right'
              style={{ width: '70px' }}
              formatter={(value) =>
                value!
                  .toString()
                  .replace(/\s/g, '')
                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                  .replace('.', ',')
              }
              controls={false}
              parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
              onChange={(e) => onChangeQuantity(parseFloat(e || '0'))}
              disabled={status !== undefined ? status !== DOCUMENT_STATUSES.DRAFT.key : undefined}
            />
          </Form.Item>
        </Space>
      </div>
    );
  }
  return <></>;
};

export default QuantityCell;
