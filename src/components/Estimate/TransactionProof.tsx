import { useEffect, useState, Dispatch, SetStateAction } from 'react';
import { Modal, Button, Upload, Form } from 'antd';
import { DeleteOutlined, FilePdfOutlined, InboxOutlined } from '@ant-design/icons';
import { TRANSACTION_PROOF } from 'utils/constant';
import fileService from 'services/file.service';
import { toast } from 'react-toastify';
import { UploadFileCustom } from './FileUpload';

const { Dragger } = Upload;

interface TransactionProofProps {
  isOpenModal: boolean;
  setIsOpenModal: Dispatch<SetStateAction<boolean>>;
  onSubmit: (file: UploadFileCustom) => void;
}

const TransactionProof = ({ isOpenModal, setIsOpenModal, onSubmit }: TransactionProofProps) => {
  const [selectedFile, setSelectedFile] = useState<UploadFileCustom | null>(null);
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [file, setFile] = useState<File | null>(null);
  const [fileType, setFileType] = useState<string>('');

  useEffect(() => {
    if (isOpenModal) {
      form.resetFields();
      setFile(null);
      setSelectedFile(null);
    }
  }, [isOpenModal]);

  const handleSubmit = async () => {
    if (!selectedFile || !file) return;
    try {
      setIsLoading(true);
      const res = await fileService.uploadFileAdvanced(file, selectedFile?.file_name, fileType, 10);
      onSubmit({
        ...selectedFile,
        keyFile: res.keyFile,
        url: res.keyFile,
      });
      setIsLoading(false);
      setIsOpenModal(false);
    } catch (error) {
      toast.error('Erreur');
      setIsLoading(false);
      console.log(error);
    }
  };

  const onBeforeUpload = async (uploadFile: File) => {
    const isJpgOrPng =
      uploadFile.type === 'image/jpeg' || uploadFile.type === 'image/png' || uploadFile.type === 'application/pdf';
    if (!isJpgOrPng) {
      toast.error('You can only upload JPG/PNG/PDF file!');
      return false;
    }
    setFile(uploadFile);
    setFileType(uploadFile.type);
    return false;
  };

  return (
    <div>
      {isOpenModal && (
        <Modal
          zIndex={1000}
          title=''
          open={isOpenModal}
          onOk={() => setIsOpenModal(false)}
          onCancel={() => setIsOpenModal(false)}
          maskClosable={false}
          centered
          footer={[
            <Button key='annuler' onClick={() => setIsOpenModal(false)} className='ant-modal-content__cancel-btn'>
              Annuler
            </Button>,
            <Button
              key='ajouter'
              type='primary'
              loading={isLoading}
              onClick={handleSubmit}
              className='ant-modal-content__add-btn'
            >
              Télécharger
            </Button>,
          ]}
        >
          <Form form={form} layout='vertical'>
            <Form.Item>
              <div style={{ marginTop: '70px' }}>
                <Dragger
                  name='file'
                  multiple={false}
                  disabled={false}
                  beforeUpload={onBeforeUpload}
                  accept='image/jpeg,image/gif,image/png,application/pdf,image/x-eps'
                  showUploadList={false}
                  onChange={async (info) => {
                    if (info.fileList.length > 0) {
                      const fileItem = info.fileList[info.fileList.length - 1];
                      const fileType = fileItem.originFileObj?.name.split('.').pop();
                      const file: UploadFileCustom = {
                        ...fileItem,
                        file_name: TRANSACTION_PROOF + '.' + fileType!,
                        status_file: 'NEW',
                        keyFile: '',
                        fileUrl: '',
                      };
                      setSelectedFile(file);
                    }
                  }}
                >
                  <p className='ant-upload-drag-icon'>
                    <InboxOutlined style={{ color: '#A6C84D' }} />
                  </p>
                  <p className='ant-upload-text'>Virement, besoin de la preuve de virement</p>
                  <p className='ant-upload-hint'>choisissez un fichier ou faites-le glisser et déposez-le ici</p>
                </Dragger>
                {selectedFile && (
                  <div style={{ paddingTop: 10 }}>
                    <FilePdfOutlined style={{ color: '#95C515' }} />
                    <span className='ml-1 mr-2' style={{ color: '#95C515' }}>
                      {selectedFile.file_name}
                    </span>
                    <DeleteOutlined onClick={() => setSelectedFile(null)} style={{ cursor: 'pointer', color: 'red' }} />
                  </div>
                )}
              </div>
            </Form.Item>
          </Form>
        </Modal>
      )}
    </div>
  );
};

export default TransactionProof;
