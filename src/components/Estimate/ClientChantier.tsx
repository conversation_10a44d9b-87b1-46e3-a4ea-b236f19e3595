import { Row, Col, Form, Input, Select, Divider, Spin, FormInstance, Switch, Typography } from 'antd';
import {
  DeleteTwoTone,
  PlusCircleOutlined,
  CheckOutlined,
  CloseOutlined,
  SearchOutlined,
  FilePdfOutlined,
  DeleteOutlined,
  LoadingOutlined,
} from '@ant-design/icons';
import { ChangeEvent, Dispatch, SetStateAction, useEffect, useState } from 'react';
import {
  ADDRESS_TYPE,
  CONTACT_TYPES,
  CREATE_ACCOUNT,
  DOCUMENT_FILE_UPLOADS_TYPE,
  DOCUMENT_STATUSES,
  DOCUMENT_TYPES,
} from 'utils/constant';
import ClientSearch from './ClientSearch';
import SelectWithGoogleSuggestion from 'components/Common/SelectWithGoogleSuggestion';
import { convertAddress, getFullAddress, mapAddressArray } from 'utils';
import { Address } from 'types';
import { useAppSelector } from 'store';
import { selectSales } from 'store/slices/sale.slices';
import { Client<PERSON>ontact, ClientContactPerson, DocumentCCContact, Quotation, Sale } from 'models';
import { selectClientContact } from 'store/slices/client_contact.slice';
import { selectClientContactPerson, selectClientContactPersons } from 'store/slices/client_contact_person.slice';
import { toast } from 'react-toastify';
import { useCountryRegions, useCountryRegionsQuery, useDebounce } from 'hooks';
import axios from 'axios';
import CreateClientContact from './CreateClientContact';
import ChangementAddressModal from 'components/Common/ChangementAddressModal';
import TransactionProof from './TransactionProof';
import FileUpload, { UploadFileCustom } from 'components/Estimate/FileUpload';
import { clientContactService, documentService } from 'services';
import DuplicateClient from './DuplicateClient';
import CopyDataTextDocument from 'components/Common/CopyDataText';
type CCContact = string[];

const ClientChantier = ({
  documentType,
  form,
  data,
  setData,
  lastModifyById,
  referentId,
  getClientContact,
  isSaveDraft = false,
  isSubmitted,
  isOpenTransactionModal = false,
  setIsOpenTransactionModal = () => null,
  onDataProofFile,
  disabled,
  onChangeAddressChantier,
  isCopyForm = false,
  isCreateForm,
  isTransForm = false,
  createAccount,
  isOpenModalDuplicateClient,
  setIsOpenModalDuplicateClient = () => null,
  zcrm_contact_id,
}: {
  documentType?: string;
  form: FormInstance;
  disabled?: boolean;
  data: Quotation;
  /* eslint-disable-next-line */
  setData: Function;
  lastModifyById?: string | null;
  referentId?: string | null;
  getClientContact: (contact_id?: string) => Promise<void>;
  isSaveDraft?: boolean;
  isSubmitted?: boolean;
  isOpenTransactionModal?: boolean;
  setIsOpenTransactionModal?: Dispatch<SetStateAction<boolean>>;
  onDataProofFile?: (file: UploadFileCustom) => void;
  onChangeAddressChantier?: (address: Address, isChangePrice?: boolean) => void;
  isCopyForm?: boolean;
  isCreateForm?: boolean;
  isTransForm?: boolean;
  createAccount?: string;
  isOpenModalDuplicateClient?: number;
  setIsOpenModalDuplicateClient?: Dispatch<SetStateAction<number>>;
  zcrm_contact_id?: string | null;
}) => {
  const [isOpenModal, setIsOpenModal] = useState(false);
  const sales = useAppSelector(selectSales);
  const contact = useAppSelector(selectClientContact);
  const clientContactPersons = useAppSelector(selectClientContactPersons);
  const clientContactPerson = useAppSelector(selectClientContactPerson);
  const { document } = data;
  // const [isCheckedPaymentClient] = useState<boolean>(false); // QBO-1054
  const [isOpenModalSearchClient, setIsOpenModalSearchClient] = useState(false);
  const [isLoadingModal, setIsLoadingModal] = useState(false);
  const [isChangeClient, setIsChangeClient] = useState(false);
  const [isChangeAddress, setIsChangeAddress] = useState<boolean>(false);
  const [selectedAddress, setSelectedAddress] = useState<Address>({});
  const [lastModifySale, setlastModifySale] = useState<Sale>();
  const [fileList, setFileList] = useState<UploadFileCustom[]>([]);
  const [fileListDelete, setFileListDelete] = useState<UploadFileCustom[]>([]);
  const [loadingIds, setLoadingIds] = useState<(string | number | undefined)[]>([]);
  // const [changedClient, setChangedClient] = useState<ClientContactPerson | null>(null);
  const [contactPersonCreateId, setContactPersonCreateId] = useState<number | null>(null);
  const MAX_EMAIL_ALLOWED = 5;
  const cclibres =
    document?.DocumentCCLibresContacts &&
    document?.DocumentCCLibresContacts?.length > 0 &&
    document?.DocumentCCLibresContacts?.[0]?.email
      ? document?.DocumentCCLibresContacts?.[0]?.email?.split(',')
      : [];
  const [emailcc, setEmailcc] = useState<CCContact>(cclibres);
  const [countryRegionsQuery] = useCountryRegionsQuery();
  const [countryRegions] = useCountryRegions(countryRegionsQuery);

  // const addressChantier = data?.addressChantierArray?.find((item: Address) => item?.id === document?.siteAddressId);
  // useEffect(() => {
  //   setData({
  //     chantierAddress: addressChantier,
  //   });
  // }, [addressChantier]);
  useEffect(() => {
    if (createAccount == CREATE_ACCOUNT.CREATE_A_CONTACT || createAccount == CREATE_ACCOUNT.CREATE_CONTACT_ESTIMATE) {
      setIsLoadingModal(true);
      setIsOpenModalSearchClient(true);
      setIsLoadingModal(false);
    }
  }, []);
  useEffect(() => {
    const contactPersonId = getContactPersonId();
    const addressBillingArray = mapAddressArray(contact?.ContactAddresses, ADDRESS_TYPE.BILLING);
    const addressChantierArray = mapAddressArray(
      contact?.ContactAddresses?.filter((ca) => ca.contactPersonId === contactPersonId),
      ADDRESS_TYPE.SITE,
    );
    const chantierNotInArray =
      data?.chantierAddress?.formattedAddress &&
      !addressChantierArray?.some((item: Address) => item.formattedAddress === data?.chantierAddress?.formattedAddress);
    const chantierAddressWithFlag = chantierNotInArray
      ? { ...data.chantierAddress, isCreate: true }
      : data?.chantierAddress;
    if (chantierAddressWithFlag?.isCreate) delete chantierAddressWithFlag.id;
    const newAddressChantierArray = addressChantierArray
      ? chantierNotInArray
        ? [...addressChantierArray, chantierAddressWithFlag]
        : addressChantierArray
      : chantierAddressWithFlag
        ? [chantierAddressWithFlag]
        : [];
    setData({
      addressBillingArray: addressBillingArray ?? [],
      billingAddress: addressBillingArray?.[0] ?? null,
      addressChantierArray: newAddressChantierArray,
      ...(data?.chantierAddress ? { chantierAddress: chantierAddressWithFlag } : {}),
      ...(data?.isGpsAddress ? { isGpsAddress: data?.isGpsAddress } : {}),
      ...(data?.latLongGpsAddress ? { latLongGpsAddress: data?.latLongGpsAddress } : {}),
    });
    const currentChantierAddress = newAddressChantierArray?.find(
      (item: Address) => item?.address_id === data?.chantierAddress?.address_id,
    );
    if (data?.chantierAddress && currentChantierAddress) {
      form.setFieldValue('siteAddressFull', currentChantierAddress?.formattedAddress);
      setData({
        chantierAddress: currentChantierAddress,
      });
    }

    form.setFieldValue(
      'billingAddressFull',
      addressBillingArray && addressBillingArray?.length > 0 ? addressBillingArray[0]?.formattedAddress : '',
    );
    if (contact?.clientType === CONTACT_TYPES.BUSINESS) {
      if (contact?.name && contactPersonId) {
        form.setFieldsValue({
          customerName: contact?.name,
          contactPersons: contactPersonId,
          contactSurPlace:
            contact.id === document?.contactId ? document?.contactSurPlace : contact?.ContactPersons?.[0]?.phone,
        });
      }
    } else {
      form.setFieldsValue({ contactPersons: null });
      if (contact?.ContactPersons && contact.ContactPersons.length > 0) {
        const customerName = contact?.ContactPersons?.[0]
          ? [contact?.ContactPersons?.[0]?.firstName, contact?.ContactPersons?.[0]?.lastName].filter(Boolean).join(' ')
          : '';
        form.setFieldsValue({
          customerName: customerName,
          contactSurPlace:
            contact.id === document?.contactId ? document?.contactSurPlace : contact?.ContactPersons?.[0]?.phone,
        });
      } else {
        form.setFieldsValue({ customerName: '', contactSurPlace: '' });
      }
    }
    if (isChangeClient) {
      setChangeClientValue();
    }
    if (contact && isCreateForm) {
      setIsLoadingModal(true);
      getListClientContactDuplicateName(contact);
      setIsLoadingModal(false);
      console.log(contact);
    }
  }, [contact, data?.clientContact, data?.isGpsAddress, data?.latLongGpsAddress]);

  useEffect(() => {
    setData({
      fileUpload: [...fileList, ...fileListDelete],
    });
  }, [fileList, fileListDelete]);

  useEffect(() => {
    // set data to vendeur and referent
    if (sales?.length > 0) {
      const lastModifySale = document
        ? sales?.find((i: Sale) => i.id === document?.vendeur)
        : sales?.find((i: Sale) => i.crmUserId === lastModifyById);
      setlastModifySale(lastModifySale);
      const referentSale = document
        ? sales?.find((i: Sale) => i.id === document?.referent)
        : sales?.find((i: Sale) => i.crmUserId === referentId);

      form.setFieldsValue({
        vendeur: lastModifySale?.id,
        referent: referentSale?.id,
      });
    }

    if (document) {
      const contactPersonId = getContactPersonId();
      const address = data?.addressBillingArray?.find((item: Address) => item?.id === document?.billingAddressId);
      const addressChantierArray = mapAddressArray(
        contact?.ContactAddresses?.filter((ca) => ca.contactPersonId === contactPersonId),
        ADDRESS_TYPE.SITE,
      );
      const addressChantier = addressChantierArray?.find((item: Address) => item?.id === document?.siteAddressId);
      if (document?.contactId === contact?.id) {
        setData({
          billingAddress: address,
          chantierAddress: addressChantier,
          isGpsAddress: document?.siteAddressIsGps,
          gpsAddress: addressChantier?.formattedAddress,
          latLongGpsAddress:
            document?.siteAddressLatitude && document?.siteAddressLongitude
              ? [document?.siteAddressLatitude, document?.siteAddressLongitude].join(',')
              : '',
          isAddressChantierTemporary: document?.isAddressChantierTemporary,
        });

        form.setFieldsValue({
          email: cclibres,
          bdcClient: document?.bdcClient,
          contactSurPlace: document?.contactSurPlace,
          billingAddressFull: document?.billingAddressFull,
          siteAddressFull: document?.siteAddressFull,
        });
        if (document?.DocumentCCContacts) {
          form.setFieldsValue({
            ccContactPersons: document?.DocumentCCContacts.map((item: DocumentCCContact) => item.contactPersonId),
          });
        }
      }
      if (
        !(isCopyForm && documentType === DOCUMENT_TYPES.ORDER) &&
        document?.DocumentFileUploads &&
        document.DocumentFileUploads.length > 0
      ) {
        const dataListFile: UploadFileCustom[] = [];
        // Set data list file
        document?.DocumentFileUploads?.map((item) => {
          if (item.type == DOCUMENT_FILE_UPLOADS_TYPE.BDC_CLIENT) {
            dataListFile.push({
              ...item,
              uid: item.id?.toString() ?? '',
              name: item.name.replace('.pdf', ''),
              status_file: 'UPDATE',
            });
          }
        });
        dataListFile.sort((a, b) => a.name.localeCompare(b.name));
        setFileList(dataListFile);
      }
    }
  }, [document, sales, contact]);
  useEffect(() => {
    if (contact?.clientType === CONTACT_TYPES.BUSINESS) {
      const contactPersonId = getContactPersonId();
      form.setFieldValue('contactPersons', contactPersonId);
    }
  }, [clientContactPersons, clientContactPerson, document?.contactPersonId]);

  const getContactPersonId = () => {
    let contactPersonId;
    if (contactPersonCreateId && clientContactPersons.find((obj) => obj?.id === contactPersonCreateId)) {
      contactPersonId = contactPersonCreateId;
    } else if (
      document?.contactPersonId &&
      clientContactPersons.find((contact) => contact?.id === document?.contactPersonId)
    ) {
      contactPersonId = document?.contactPersonId;
    } else if (clientContactPerson) {
      contactPersonId = clientContactPerson.id;
    } else {
      if (zcrm_contact_id != null) {
        const contactPersons = clientContactPersons.find(
          (contactPerson) => contactPerson?.crmContactId === zcrm_contact_id,
        );
        contactPersonId = contactPersons ? contactPersons.id : null;
      }
      if (contactPersonId == null) {
        contactPersonId = clientContactPersons[0]?.id;
      }
    }
    return contactPersonId;
  };
  const getListClientContactDuplicateName = async (contact: ClientContact) => {
    if (contact?.clientType == CONTACT_TYPES.BUSINESS) {
      // Get list contact duplicate name
      const listContact = await clientContactService.getContacts({
        name: `*${contact.name}*`,
        clientType: CONTACT_TYPES.BUSINESS,
        include: 'ContactPersons',
      });
      const listDocumentOfContact = await documentService.getDocuments({
        contactId: contact?.id,
      });
      if (listContact?.rows?.length > 1 && listDocumentOfContact?.count == 0 && isOpenModalDuplicateClient == -1) {
        setIsOpenModalDuplicateClient(1);
      }
    }
  };
  const isEnableActionClientContact = () => {
    if (
      (isCreateForm && isTransForm && isCopyForm == false) ||
      (!isCreateForm &&
        ((documentType === DOCUMENT_TYPES.ORDER &&
          data.document?.DocumentStatus?.key === DOCUMENT_STATUSES.CONFIRMED.key) ||
          (documentType === DOCUMENT_TYPES.QUOTATION &&
            (data.document?.DocumentStatus?.key === DOCUMENT_STATUSES.SENT.key ||
              data.document?.DocumentStatus?.key === DOCUMENT_STATUSES.ACCEPTED.key))))
    ) {
      return false;
    }
    return true;
  };
  const resetDataFieldFormClientChantier = () => {
    form.setFieldsValue({
      bdcClient: null,
      contactPersons: null,
      contactSurPlace: null,
      billingAddressFull: null,
      siteAddressFull: null,
      ccContactPersons: [],
      email: [],
    });
  };
  const handleDownloadFile = async (item: UploadFileCustom) => {
    // Check update order
    if (item.status_file === 'UPDATE') {
      fetch(process.env.REACT_APP_PATH_FILES_S3 + `/${item?.url}`)
        .then((response) => response.blob())
        .then((blob) => {
          const link = window.document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = item.name;
          link.click();
        });
    }
  };

  const handleDeleteFile = async (item: UploadFileCustom) => {
    try {
      if (item.status_file === 'NEW') {
        const updatedFileList = fileList.filter((file) => file.uid !== item.uid);
        setFileList(updatedFileList);
      } else if (item.status_file === 'UPDATE') {
        const fileDelete = fileList.find((file) => file.uid == item.uid);
        if (fileDelete) {
          fileDelete.status_file = 'DELETE';
          if (item?.id) {
            setLoadingIds((prev) => [...prev, item?.id]);
            await documentService.deleteFileToSalesorder(item?.id);
          }
          setFileListDelete([...fileListDelete, fileDelete]);
          const updatedFileList = fileList.filter((file) => file.uid !== item.uid);
          setFileList(updatedFileList);
        }
      }
    } catch (error) {
      console.log(error);
    } finally {
      setLoadingIds((prev) => prev.filter((id) => id !== item?.id));
    }
  };

  const setChangeClientValue = async () => {
    resetDataFieldFormClientChantier();
    // await handlChangeSwitchGPS(false);
    setIsChangeClient(false);
  };
  const handleChangeClient = () => {};
  const handleCopyDataAccount = () => {
    const accountValue = form.getFieldValue('vendeur');
    const accountName = sales.find((s) => s.id == accountValue)?.name;
    handleCopyData(accountName);
  };
  const handleCopyDataSalesPerson = () => {
    const salesPersonValue = form.getFieldValue('referent');
    const salesPersonName = sales.find((s) => s.id == salesPersonValue)?.name;
    handleCopyData(salesPersonName);
  };
  const handleCopyData = (dataCopy: string | undefined) => {
    dataCopy = dataCopy?.toString();
    if (dataCopy !== undefined && dataCopy.length > 0) {
      navigator.clipboard.writeText(dataCopy ? dataCopy : '');
      toast.success(<div>Copié dans le presse-papier</div>);
    }
  };
  const handleShowPopUpSearchClient = () => {
    if (isEnableActionClientContact()) {
      setIsLoadingModal(true);
      setIsOpenModalSearchClient(true);
      setIsLoadingModal(false);
    }
  };

  const handleCopyDataFollowFormName = (FieldNameForm: string) => {
    handleCopyData(form.getFieldValue(FieldNameForm));
  };
  const handleChangeReferentClient = (contactPersonId: number) => {
    const addressChantierArray = mapAddressArray(
      contact?.ContactAddresses?.filter((ca) => ca.contactPersonId === contactPersonId),
      ADDRESS_TYPE.SITE,
    );
    form.setFieldValue('siteAddressFull', '');
    const chantierNotInArray =
      data?.chantierAddress?.formattedAddress &&
      !addressChantierArray?.some((item: Address) => item.formattedAddress === data?.chantierAddress?.formattedAddress);
    const chantierAddressWithFlag = chantierNotInArray
      ? { ...data.chantierAddress, isCreate: true }
      : data?.chantierAddress;
    if (chantierAddressWithFlag?.isCreate) delete chantierAddressWithFlag.id;
    setData({
      addressChantierArray: addressChantierArray
        ? chantierNotInArray
          ? [...addressChantierArray, chantierAddressWithFlag]
          : addressChantierArray
        : chantierAddressWithFlag
          ? [chantierAddressWithFlag]
          : [],
      ...(data?.chantierAddress ? { chantierAddress: chantierAddressWithFlag } : {}),
      ...(data?.isGpsAddress ? { isGpsAddress: data?.isGpsAddress } : {}),
      ...(data?.latLongGpsAddress ? { latLongGpsAddress: data?.latLongGpsAddress } : {}),
    });
  };
  const handleCopyDataContactPersons = () => {
    const contactPersonsValue = form.getFieldValue('contactPersons');
    const contactPerson = clientContactPersons?.find((option: ClientContactPerson) => option.id == contactPersonsValue);
    const contactPersonsString = contactPerson
      ? [
          [contactPerson.firstName, contactPerson.lastName].filter(Boolean).join(' '),
          contact?.clientType === CONTACT_TYPES.BUSINESS ? contactPerson.email : '',
        ]
          .filter(Boolean)
          .join(' - ')
      : '';
    handleCopyData(contactPersonsString);
  };
  const handleChangeCCEmail = (value: CCContact) => {
    if (value.length > MAX_EMAIL_ALLOWED) {
      toast.error(<div>Les cc gratuits ont atteint leur quantité maximale</div>);
    } else if (value.length === 0 || value.length <= cclibres?.length) {
      setEmailcc(cclibres);
    } else {
      const limitedEmail = value?.slice(0, MAX_EMAIL_ALLOWED).map((option: string) => option);
      setEmailcc(limitedEmail);
    }
  };
  const handleContactChantierChange = (e: ChangeEvent<HTMLInputElement>) => {
    const inputValue = e.target.value;
    const formattedInput = inputValue.replace(/(\d{2}(?=\d))/g, '$1 ');

    form.setFieldValue('contactSurPlace', formattedInput);
  };
  const handleSelectClient = async (record: ClientSearch) => {
    if (record.crm_contact_id !== contact?.crmContactId) {
      setIsLoadingModal(true);
      setIsChangeClient(true);
      // setChangedClient(record);
      await getClientContact(record.contact_id.toString());
      setIsLoadingModal(false);
    }
    setIsOpenModalSearchClient(false);
  };
  const handleSelectClientDuplicate = async (record: ClientSearch) => {
    if (record.crm_contact_id !== contact?.crmContactId) {
      setIsLoadingModal(true);
      setIsChangeClient(true);
      // setChangedClient(record);
      await getClientContact(record.contact_id.toString());
      setIsLoadingModal(false);
    }
    setIsOpenModalDuplicateClient(0);
  };
  const handleSelectFacturationAddress = async (address: Address) => {
    form.setFieldValue('billingAddressFull', address.fullAddress);
    setData({ billingAddress: address });
  };
  const handleSelectChantierAddress = async (address: Address) => {
    const siteAddressFull = form.getFieldValue('siteAddressFull');
    const countryRegion = address?.countryRegion;
    const countryRegionId = countryRegions.find((cr) => cr.name === countryRegion)?.id;
    address.countryRegionId = countryRegionId;
    if (siteAddressFull !== address.fullAddress) {
      if (siteAddressFull) {
        setIsChangeAddress(true);
        setSelectedAddress(address);
        confirmChangeAddressChantier(address);
      } else {
        if (data?.chantierAddress && data?.addressChantierArray && data?.addressChantierArray?.length > 0) {
          setIsChangeAddress(true);
        }
        form.setFieldValue('siteAddressFull', address);
        setData({ chantierAddress: address });
        handleChangeSwitchAddressTemporary(!address.isCompleted);
        if (onChangeAddressChantier) {
          onChangeAddressChantier(address, true);
        }
      }
    }
  };
  const onCreateContact = () => {
    setIsOpenModalDuplicateClient(0);
  };
  const cancelChangePriceWhenChangeAddressChantier = () => {
    setIsChangeAddress(false);
    if (onChangeAddressChantier) {
      onChangeAddressChantier(selectedAddress, false);
    }
  };
  const confirmPriceWhenChangeAddressChantier = () => {
    setIsChangeAddress(false);
    if (onChangeAddressChantier) {
      onChangeAddressChantier(selectedAddress, true);
    }
  };
  const confirmChangeAddressChantier = (address: Address) => {
    form.setFieldValue('siteAddressFull', address?.fullAddress);
    setData({ chantierAddress: address });
    handleChangeSwitchAddressTemporary(!address.isCompleted);
  };
  const handlChangeGPS = (inputValue: string) => {
    form.setFieldValue('latLongGpsAddress', inputValue);
    setData({ latLongGpsAddress: inputValue });
    fetchChangeGPS(inputValue);
  };
  const handlChangeSwitchGPS = async (isGpsAddress: boolean) => {
    setData({ isGpsAddress: isGpsAddress });
    // if (!isGpsAddress) {
    //   setData({ latLongGpsAddress: '' });
    //   setData({ gpsAddress: '' });
    // }
  };
  const fetchChangeGPS = useDebounce(async (inputValue: string) => {
    if (!inputValue || inputValue.length === 0) {
      return;
    }
    const [lat, long] = inputValue.split(', ');
    try {
      const mapsResponse = await axios.get('https://maps.googleapis.com/maps/api/geocode/json', {
        params: {
          key: process.env.REACT_APP_GOOGLE_MAPS_API_KEY,
          latlng: `${lat},${long}`,
          language: 'fr',
        },
      });
      const locations = mapsResponse?.data?.results[0];
      const newAddress = convertAddress(locations) as Address;
      newAddress.isCreate = true;
      // setData({ billingAddress: newAddress });
      setData({ gpsAddress: newAddress?.fullAddress });
      // Find full address in contact_address
      const contactAddressOld = data?.addressChantierArray?.find(
        (i: Address) => i.formattedAddress === newAddress?.formattedAddress,
      );
      if (!contactAddressOld) {
        const newChantierAddress: Address = { ...newAddress };
        newChantierAddress.isCreate = true;
        newChantierAddress.fullAddress = newAddress.fullAddress?.toUpperCase();
        newChantierAddress.address = newAddress.address;
        newChantierAddress.postalcode = newAddress.zip;
        newChantierAddress.city = newAddress.city;
        newChantierAddress.longitude = newAddress.longitude;
        newChantierAddress.latitude = newAddress.latitude;
        newChantierAddress.formatted_address = newAddress.fullAddress;
        const countryRegion = newAddress?.countryRegion;
        const countryRegionId = countryRegions.find((cr) => cr.name === countryRegion)?.id;
        newChantierAddress.countryRegionId = countryRegionId;
        setData({ chantierAddress: { ...newChantierAddress, address_id: locations?.place_id } });
        setSelectedAddress(newChantierAddress);
        if (data?.chantierAddress && data?.chantierAddress?.formattedAddress != newChantierAddress?.formattedAddress) {
          setIsChangeAddress(true);
        } else if (onChangeAddressChantier) {
          onChangeAddressChantier(newChantierAddress, true);
        }
      } else {
        setData({ chantierAddress: { ...contactAddressOld, address_id: locations?.place_id } });
        setSelectedAddress(contactAddressOld);
        if (data?.chantierAddress && data?.chantierAddress?.formattedAddress != contactAddressOld?.formattedAddress) {
          setIsChangeAddress(true);
        } else if (onChangeAddressChantier) {
          onChangeAddressChantier(contactAddressOld, true);
        }
      }
    } catch (error) {
      console.log(error);
      // setData({ gpsAddress: '' });
      // setData({ billingAddress: {} });
      // setData({ chantierAddress: {} });
    }
  }, 1000);
  const handleChangeSwitchAddressTemporary = (isAddressChantierTemporary: boolean) => {
    setData({ isAddressChantierTemporary: isAddressChantierTemporary });
  };

  const submitFileProof = (file: UploadFileCustom) => {
    onDataProofFile!(file);
  };

  return (
    <section className='mt-16 section client-chantier'>
      <CreateClientContact
        lastModifySale={lastModifySale}
        isOpenModal={isOpenModal}
        setIsOpenModal={setIsOpenModal}
        sales={sales}
        getClientContact={getClientContact}
        setContactPersonCreateId={setContactPersonCreateId}
      />
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 12 }}>
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              <Form.Item label='Vendeur' name='vendeur' rules={[{ required: true }]}>
                <Select
                  placeholder='Sélectionner'
                  showSearch
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={sales?.map((option: Sale) => ({
                    value: option.id,
                    label: option.name,
                  }))}
                  disabled={disabled}
                />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 4 }}>
              <CopyDataTextDocument
                text={sales?.find((s) => s.id === form.getFieldValue('vendeur'))?.name || ''}
                className='copy-icon-outline'
                onClick={handleCopyDataAccount}
                styleMode='form'
              />
            </Col>
          </Row>
        </Col>
        <Col xs={{ span: 12 }}>
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              <Form.Item label='Référent' name='referent' rules={[{ required: true }]}>
                <Select
                  placeholder='Sélectionner'
                  showSearch
                  listItemHeight={32}
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={sales?.map((option: Sale) => ({
                    value: option.id,
                    label: option.name,
                  }))}
                  disabled={disabled}
                />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 4 }}>
              <CopyDataTextDocument
                text={sales?.find((s) => s.id === form.getFieldValue('referent'))?.name || ''}
                className='copy-icon-outline'
                onClick={handleCopyDataSalesPerson}
                styleMode='form'
              />
            </Col>
          </Row>
        </Col>
      </Row>
      <Divider className='horizontal-bar' />
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 12 }}>
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              {/*Hide the checkbox in scope of ticket QBO-1054*/}
              {/*{documentType === 'quotation' ? (*/}
              {/*  <Form.Item*/}
              {/*    label='Client'*/}
              {/*    tooltip={{*/}
              {/*      title: 'Generer lien de paiement',*/}
              {/*      color: '#E0EDC2',*/}
              {/*      icon: <Checkbox checked={isCheckedPaymentClient} onChange={onChange}></Checkbox>,*/}
              {/*    }}*/}
              {/*    name='customerName'*/}
              {/*    rules={[{ required: true }]}*/}
              {/*  >*/}
              {/*    <Input disabled={true} />*/}
              {/*  </Form.Item>*/}
              {/*) : (*/}
              <Form.Item label='Client' name='customerName' rules={[{ required: true }]}>
                <Input disabled={true} />
              </Form.Item>
              {/*)}*/}
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
              <SearchOutlined
                disabled={!isEnableActionClientContact()}
                onClick={() => handleShowPopUpSearchClient()}
                style={
                  isEnableActionClientContact()
                    ? { fontSize: 24, color: '#95C515' }
                    : { fontSize: 24, color: '#d9d9d9', cursor: 'not-allowed' }
                }
              />
              <CopyDataTextDocument
                text={form.getFieldValue('customerName') || ''}
                className='copy-icon-outline-margin'
                onClick={() => handleCopyDataFollowFormName('customerName')}
                styleMode='form'
              />
            </Col>
          </Row>
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              <Form.Item
                label='Référent Client'
                name='contactPersons'
                // initialValue={data?.contactPersons?.[0]}
                rules={[
                  {
                    required: contact?.clientType === CONTACT_TYPES.BUSINESS,
                  },
                ]}
              >
                <Select
                  placeholder='Sélectionner'
                  disabled={contact?.clientType !== CONTACT_TYPES.BUSINESS || disabled}
                  onChange={handleChangeReferentClient}
                  showSearch
                  listItemHeight={32}
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={clientContactPersons?.map((option: ClientContactPerson) => ({
                    value: option.id,
                    label: [
                      [option.firstName, option.lastName].filter((i) => i).join(' '),
                      contact?.clientType === CONTACT_TYPES.BUSINESS ? option.email : '',
                    ]
                      .filter((i) => i)
                      .join(' - '),
                  }))}
                />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
              <PlusCircleOutlined
                onClick={() => isEnableActionClientContact() && setIsOpenModal(true)}
                disabled={!isEnableActionClientContact()}
                style={
                  isEnableActionClientContact()
                    ? { fontSize: 24, color: '#95C515' }
                    : { fontSize: 24, color: '#d9d9d9', cursor: 'not-allowed' }
                }
              />
              <CopyDataTextDocument
                text={
                  (() => {
                    const contactPersonsValue = form.getFieldValue('contactPersons');
                    const contactPerson = clientContactPersons?.find(
                      (option: ClientContactPerson) => option.id == contactPersonsValue,
                    );
                    return contactPerson
                      ? [
                          [contactPerson.firstName, contactPerson.lastName].filter(Boolean).join(' '),
                          contact?.clientType === CONTACT_TYPES.BUSINESS ? contactPerson.email : '',
                        ]
                          .filter(Boolean)
                          .join(' - ')
                      : '';
                  })() || ''
                }
                className='copy-icon-outline-margin'
                onClick={handleCopyDataContactPersons}
                styleMode='form'
              />
            </Col>
          </Row>
          <Row gutter={[16, 0]}>
            <Col xs={{ span: 18 }}>
              <Form.Item label='CC' name='ccContactPersons'>
                <Select
                  placeholder='Sélectionner'
                  showSearch
                  mode='multiple'
                  listItemHeight={32}
                  disabled={contact?.clientType !== 'business' || disabled}
                  removeIcon={<DeleteTwoTone style={{ paddingLeft: 4, fontSize: 12 }} twoToneColor='#ff4d4f' />}
                  filterOption={(input, option) =>
                    ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                  }
                  options={clientContactPersons?.map((option: ClientContactPerson) => ({
                    value: option.id,
                    label: [[option.firstName, option.lastName].filter((i) => i).join(' '), option.email]
                      .filter((i) => i)
                      .join(' - '),
                  }))}
                />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
              <PlusCircleOutlined
                onClick={() => isEnableActionClientContact() && setIsOpenModal(true)}
                disabled={!isEnableActionClientContact()}
                style={
                  isEnableActionClientContact()
                    ? { fontSize: 24, color: '#95C515' }
                    : { fontSize: 24, color: '#d9d9d9', cursor: 'not-allowed' }
                }
              />
            </Col>
          </Row>
          {documentType === DOCUMENT_TYPES.QUOTATION ? (
            <Row gutter={[16, 0]} className='copy-row'>
              <Col xs={{ span: 18 }}>
                <Form.Item label='CC libres (limité à 5)' name='email' initialValue={emailcc}>
                  <Select
                    placeholder='Sélectionner'
                    disabled={disabled}
                    showSearch
                    mode='tags'
                    listItemHeight={32}
                    removeIcon={<DeleteTwoTone style={{ paddingLeft: 4, fontSize: 12 }} twoToneColor='#ff4d4f' />}
                    tokenSeparators={[',']}
                    onChange={handleChangeCCEmail}
                    defaultValue={emailcc}
                    options={emailcc?.map((option: string) => ({
                      value: option,
                      label: option,
                    }))}
                  />
                </Form.Item>
              </Col>
              <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
                <CopyDataTextDocument
                  text={form.getFieldValue('email') || ''}
                  className='copy-icon-outline'
                  onClick={() => handleCopyDataFollowFormName('email')}
                  styleMode='form'
                />
              </Col>
            </Row>
          ) : (
            <Row gutter={[16, 0]} className='copy-row'>
              <Col xs={{ span: 18 }}>
                <Form.Item
                  label='Adresse facturation'
                  name='billingAddressFull'
                  rules={[
                    {
                      required: documentType === 'order',
                      message: 'Adresse facturation is required',
                      validateTrigger: 'onSubmit',
                    },
                  ]}
                >
                  <SelectWithGoogleSuggestion
                    defaultValue={data?.billingAddress}
                    disabled={disabled}
                    data={data}
                    options={data?.addressBillingArray ?? []}
                    onSelect={handleSelectFacturationAddress}
                  />
                </Form.Item>
              </Col>
              <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
                <CopyDataTextDocument
                  text={getFullAddress(data?.billingAddress) || ''}
                  className='copy-icon-outline'
                  onClick={() => handleCopyData(getFullAddress(data?.billingAddress))}
                  styleMode='form'
                />
              </Col>
            </Row>
          )}
        </Col>
        <Col style={{ borderLeft: '1px solid #ccc' }} xs={{ span: 12 }}>
          {documentType === DOCUMENT_TYPES.ORDER && (
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 18 }}>
                {contact?.typeBdc && contact?.typeBdc !== 'Aucun' && (
                  <Form.Item>
                    <Typography.Title level={5} className='ma-0'>{`Client ${contact?.typeBdc}`}</Typography.Title>
                  </Form.Item>
                )}
              </Col>
            </Row>
          )}
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              <Form.Item
                label='Reference BDC client'
                name='bdcClient'
                initialValue={document?.bdcClient}
                rules={[
                  {
                    validator: (_, value) => {
                      if (value && /[<>]/.test(value)) {
                        return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input
                  placeholder='Reference BDC client'
                  disabled={
                    disabled &&
                    ((documentType === DOCUMENT_TYPES.ORDER &&
                      data.document?.DocumentStatus?.key !== DOCUMENT_STATUSES.CONFIRMED.key) ||
                      documentType === DOCUMENT_TYPES.QUOTATION)
                  }
                />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0, paddingRight: 0 }} xs={{ span: 1 }}>
              <CopyDataTextDocument
                text={form.getFieldValue('bdcClient') || ''}
                className='copy-icon-outline'
                onClick={() => handleCopyDataFollowFormName('bdcClient')}
                styleMode='form'
              />
            </Col>
            <Col xs={{ span: 4 }} className='pt-4 client-chantier-upload'>
              {documentType === DOCUMENT_TYPES.ORDER && (
                <FileUpload
                  fileListProp={fileList}
                  setFileListProp={setFileList}
                  salesorderId={data.document?.id}
                  isCopyForm={isCopyForm}
                />
              )}
            </Col>
          </Row>
          {fileList?.length > 0 && (
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 18 }}>
                <Form.Item>
                  <Row gutter={[16, 0]}>
                    {fileList?.map((item, index) => (
                      <Col key={index} xs={{ span: 12 }}>
                        <span style={{ whiteSpace: 'nowrap' }}>
                          <a onClick={() => handleDownloadFile(item)}>
                            <FilePdfOutlined style={{ color: '#95C515' }} />
                            <span className='ml-1 mr-1' style={{ color: '#95C515' }}>
                              {item.name}
                            </span>
                          </a>
                          {loadingIds.includes(item?.id) ? (
                            <LoadingOutlined />
                          ) : (
                            <DeleteOutlined
                              onClick={() => handleDeleteFile(item)}
                              style={{ cursor: 'pointer', color: 'red' }}
                            />
                          )}
                        </span>
                      </Col>
                    ))}
                  </Row>
                </Form.Item>
              </Col>
            </Row>
          )}
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              <Form.Item
                label='Contact sur place'
                initialValue={document?.contactSurPlace}
                name='contactSurPlace'
                rules={[
                  { required: documentType === DOCUMENT_TYPES.ORDER },
                  () => ({
                    validator(_, value) {
                      if (!value && documentType === DOCUMENT_TYPES.ORDER) return Promise.reject();
                      const pattern = /^(\+\d{1,3}[- ]?)?[\d\s]+$/;
                      const valid = pattern.test(value);
                      if (!valid && documentType === DOCUMENT_TYPES.ORDER) return Promise.reject();
                      return Promise.resolve();
                    },
                  }),
                  {
                    validator: (_, value) => {
                      if (value && /[<>]/.test(value)) {
                        return Promise.reject('Les caractères < et > ne sont pas autorisés.');
                      }
                      return Promise.resolve();
                    },
                  },
                ]}
              >
                <Input disabled={disabled} placeholder='Contact sur place' onChange={handleContactChantierChange} />
              </Form.Item>
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
              <CopyDataTextDocument
                text={form.getFieldValue('contactSurPlace') || ''}
                className='copy-icon-outline'
                onClick={() => handleCopyDataFollowFormName('contactSurPlace')}
                styleMode='form'
              />
            </Col>
          </Row>
          {documentType === DOCUMENT_TYPES.QUOTATION && (
            <Row gutter={[16, 0]} className='copy-row'>
              <Col xs={{ span: 18 }}>
                <Form.Item label='Adresse facturation' name='billingAddressFull'>
                  <SelectWithGoogleSuggestion
                    defaultValue={data?.billingAddress}
                    disabled={disabled}
                    data={data}
                    options={data?.addressBillingArray ?? []}
                    onSelect={handleSelectFacturationAddress}
                  />
                </Form.Item>
              </Col>
              <Col style={{ paddingTop: 20, paddingLeft: 0 }} xs={{ span: 6 }}>
                <CopyDataTextDocument
                  text={getFullAddress(data?.billingAddress) || ''}
                  className='copy-icon-outline'
                  onClick={() => handleCopyData(getFullAddress(data?.billingAddress))}
                  styleMode='form'
                />
              </Col>
            </Row>
          )}
          <Row gutter={[16, 0]} className='copy-row'>
            <Col xs={{ span: 18 }}>
              {data?.isGpsAddress ? (
                <Form.Item
                  label='Adresse Chantier'
                  initialValue={data?.latLongGpsAddress}
                  rules={[
                    { required: data?.isGpsAddress && !isSaveDraft },
                    () => ({
                      validator() {
                        const invalid =
                          data?.isGpsAddress &&
                          !isSaveDraft &&
                          (!data?.gpsAddress || data?.gpsAddress === '' || data?.gpsAddress.trim().length === 0);
                        if (invalid) return Promise.reject();
                        return Promise.resolve();
                      },
                    }),
                  ]}
                  validateStatus={
                    isSubmitted &&
                    (!data?.gpsAddress || data?.gpsAddress == '' || data?.gpsAddress.trim().length == 0) &&
                    !isSaveDraft
                      ? 'error'
                      : ''
                  }
                  name={['latLongGpsAddress']}
                >
                  <Input
                    onChange={(e) => handlChangeGPS(e.target.value)}
                    value={data?.latLongGpsAddress}
                    disabled={disabled}
                  />
                  <div>{data?.gpsAddress}</div>
                </Form.Item>
              ) : (
                <Form.Item
                  label='Adresse Chantier'
                  name='siteAddressFull'
                  initialValue={data?.chantierAddress?.address}
                  rules={[
                    {
                      required: true,
                      message: 'Chantier address is required',
                      validateTrigger: 'onSubmit',
                      validator: () => {
                        const { city, address, postalcode } = (data?.chantierAddress as Address) || {};
                        const isInvalid =
                          (documentType === DOCUMENT_TYPES.ORDER && (!city || !postalcode || !address)) ||
                          (!isSaveDraft && (!city || !postalcode));
                        return isInvalid ? Promise.reject() : Promise.resolve();
                      },
                    },
                  ]}
                >
                  <SelectWithGoogleSuggestion
                    defaultValue={data?.chantierAddress}
                    disabled={disabled}
                    autoConvertAddress
                    options={data?.addressChantierArray ?? []}
                    onSelect={handleSelectChantierAddress}
                  />
                </Form.Item>
              )}
            </Col>
            <Col style={{ paddingTop: 20, paddingLeft: 0, paddingRight: 0 }} xs={{ span: 1 }}>
              <CopyDataTextDocument
                text={getFullAddress(data?.chantierAddress) || ''}
                className='copy-icon-outline'
                onClick={() => handleCopyData(getFullAddress(data?.chantierAddress))}
                styleMode='form'
              />
            </Col>
            <Col xs={{ span: 4 }} className='client-chantier-gps'>
              <Form.Item style={{ justifyContent: 'nomal', position: 'absolute' }} initialValue={data?.isGpsAddress}>
                <Switch
                  disabled={disabled}
                  checkedChildren={<CheckOutlined />}
                  unCheckedChildren={<CloseOutlined />}
                  checked={data?.isGpsAddress}
                  defaultChecked={data?.isGpsAddress}
                  onChange={(e) => {
                    handlChangeSwitchGPS(e.valueOf());
                  }}
                />
                <label style={{ position: 'absolute', marginTop: '3px', marginLeft: '5px' }}>
                  <b>GPS</b>
                </label>
              </Form.Item>
            </Col>
          </Row>
          {!data?.isGpsAddress && (
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 18 }}>
                <Form.Item className='address-temporaire'>
                  <Switch
                    disabled={disabled}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    checked={data?.isAddressChantierTemporary}
                    defaultChecked={data?.isAddressChantierTemporary}
                    onChange={(e) => {
                      handleChangeSwitchAddressTemporary(e.valueOf());
                    }}
                  />
                  <label style={{ marginLeft: '5px' }}>Adresse temporaire, doit être validée à la commande</label>
                </Form.Item>
              </Col>
            </Row>
          )}
        </Col>
      </Row>
      <TransactionProof
        isOpenModal={isOpenTransactionModal}
        setIsOpenModal={setIsOpenTransactionModal}
        onSubmit={submitFileProof}
      />
      <Spin spinning={isLoadingModal}>
        <ClientSearch
          onCreateContact={onCreateContact}
          lastModifySale={lastModifySale}
          open={isOpenModalSearchClient}
          setOpen={setIsOpenModalSearchClient}
          onSelectClient={handleSelectClient}
          setChangedClient={handleChangeClient}
          getClientContact={getClientContact}
          resetDataFieldFormClientChantier={resetDataFieldFormClientChantier}
        />
      </Spin>
      <DuplicateClient
        contact={contact}
        lastModifySale={lastModifySale}
        open={isOpenModalDuplicateClient == 1}
        setOpen={setIsOpenModalDuplicateClient}
        onSelectClient={handleSelectClientDuplicate}
        setChangedClient={handleChangeClient}
        getClientContact={getClientContact}
        resetDataFieldFormClientChantier={resetDataFieldFormClientChantier}
      />
      <ChangementAddressModal
        isOpenModal={isChangeAddress}
        onCancel={cancelChangePriceWhenChangeAddressChantier}
        onConfirmation={confirmPriceWhenChangeAddressChantier}
      />
    </section>
  );
};
export default ClientChantier;
