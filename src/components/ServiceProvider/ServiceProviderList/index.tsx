import { DeleteOutlined, DownOutlined, EyeOutlined, FormOutlined, SyncOutlined } from '@ant-design/icons';
import { Button, TableColumnType as ColumnType, Dropdown, Space, Table, Tag, Spin, Divider } from 'antd';
import { ServiceProvider } from 'models';
import { useEffect, useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ScaleLoader } from 'react-spinners';
import { toast } from 'react-toastify';
import { boService, serviceProviderService } from 'services';
import { Loading } from 'types';
import { ServiceProviderParams } from 'pages/ServiceProvider';
import { ButtonSync } from 'components/Common';
import InfiniteScroll from 'react-infinite-scroll-component';
import { LoadingOutlined } from '@ant-design/icons';

const ServiceProviderList = ({
  type,
  dataSource,
  dataSourceLoading,
  query,
  total,
  fetchServiceProviderList,
  loadMoreData,
  hasMore,
}: {
  type: string | 'benneur' | 'camionneur' | 'pup';
  dataSource: ServiceProvider[];
  dataSourceLoading: Loading;
  query: ServiceProviderParams;
  total: number;
  fetchServiceProviderList: (isLoadMore: boolean, serviceTypeKey: string, query: ServiceProviderParams) => void;
  loadMoreData: () => void;
  hasMore: boolean;
}) => {
  const navigate = useNavigate();
  const [defaultColumns, setDefaultColumns] = useState<ColumnType<ServiceProvider>[]>([]);
  const [isAllowedLoading, setIsAllowedLoading] = useState<boolean>(true);

  const deleteServiceProvider = async (serviceProvider: ServiceProvider, filter: ServiceProviderParams) => {
    try {
      await boService.deleteSpFromQBO({
        id: serviceProvider?.id,
        boId: serviceProvider?.boId,
        type: serviceProvider?.ServiceTypes?.[0]?.key,
      });
      await fetchServiceProviderList(false, type, filter);
      toast.success('Succès');
    } catch (error) {
      console.error(error);
      toast.error((error as Error)?.message);
    }
  };

  const reactivateServiceProvider = async (serviceProviderId: number, filter: ServiceProviderParams) => {
    try {
      await serviceProviderService.reactivateServiceProvider(serviceProviderId);
      setIsAllowedLoading(false);
      await fetchServiceProviderList(false, type, filter);
      toast.success('Succès');
      setIsAllowedLoading(true);
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const fetchServiceProvider = async () => {
    try {
      await fetchServiceProviderList(false, type, query);
    } catch (error) {
      console.error(error);
      toast.error((error as Error)?.message);
    }
  };

  useEffect(() => {
    let columns: ColumnType<ServiceProvider>[];
    switch (type) {
      case 'benneur':
        columns = [
          {
            title: 'Nom de société',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Nom de code',
            dataIndex: 'supplierCode',
            key: 'supplierCode',
            width: 220,
            render: (text: string | boolean) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Téléphone',
            dataIndex: 'phone',
            key: 'phone',
            width: 200,
            render: (text: string | boolean) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Email',
            dataIndex: 'email',
            key: 'email',
            width: 200,
            render: (text: string | boolean) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'N° de Récépissé',
            dataIndex: 'receiptNumber',
            key: 'receiptNumber',
            width: 200,
            render: (text: string | boolean) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Déchèterie',
            dataIndex: 'wasteCenterNumber',
            key: 'wasteCenterNumber',
            width: 160,
            render: (_: string | boolean, record: ServiceProvider) => (
              <p className='datatable__item'>
                {record.WasteCenters?.filter((wasteCenter) => wasteCenter.isActive === true).length}
              </p>
            ),
          },
          {
            title: <div className='service-provider-list__sync_bo_field'>Sync BO statut</div>,
            key: 'isSynced',
            width: 100,
            render: (_: unknown, record: ServiceProvider) => {
              const { isSynced, enableSync } = record;
              return enableSync ? (
                isSynced ? (
                  <div className='service-provider-list__sync_bo'>
                    <Button type='link' className='service-provider-list__sync_bo__button' disabled={true}>
                      BO synchronisé
                    </Button>
                  </div>
                ) : (
                  <div className='service-provider-list__sync_bo'>
                    <ButtonSync serviceProvider={record} getServiceProvider={fetchServiceProvider} />
                  </div>
                )
              ) : (
                <div className=''></div>
              );
            },
          },
          {
            title: 'Statut',
            dataIndex: 'isActive',
            key: 'isActive',
            width: 200,
            render: (status: boolean) => (status ? <Tag color='green'>Actif</Tag> : <Tag color='red'>Inactif</Tag>),
          },
          {
            title: 'Actions',
            key: 'action',
            width: 150,
            render: (_: unknown, record: ServiceProvider) => (
              <Dropdown
                menu={{
                  items: [
                    {
                      label: 'Connexion',
                      key: '1',
                      icon: <EyeOutlined />,
                    },
                    {
                      label: 'Modifier',
                      key: '2',
                      icon: <FormOutlined />,
                      onClick: () => navigate(`/prestataires/${type}/${record.id}`),
                    },
                    {
                      label: record.isActive ? 'Inactif' : 'Actif',
                      key: '3',
                      icon: <SyncOutlined />,
                      onClick: () => reactivateServiceProvider(record.id, query),
                    },
                    {
                      label: 'Supprimer',
                      key: '4',
                      icon: <DeleteOutlined />,
                      disabled: (!record.isSynced && record?.boId) || record?.isActive ? true : false,
                      onClick: () => deleteServiceProvider(record, query),
                    },
                  ],
                  onClick: (e) => console.log('click', e),
                }}
                className='service-provider-list__actions-dropdown'
              >
                <Button>
                  <Space>
                    Action
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            ),
          },
        ];
        break;
      case 'camionneur':
        columns = [
          {
            title: 'Nom de société',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Nom de code',
            dataIndex: 'supplierCode',
            key: 'supplierCode',
            width: 220,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Téléphone',
            dataIndex: 'phone',
            key: 'phone',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Email',
            dataIndex: 'email',
            key: 'email',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'N° Récépissé',
            dataIndex: 'receiptNumber',
            key: 'receiptNumber',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: <div className='service-provider-list__sync_bo_field'>Sync BO statut</div>,
            key: 'isSynced',
            width: 100,
            render: (_: unknown, record: ServiceProvider) => {
              const { isSynced, enableSync } = record;
              return enableSync ? (
                isSynced ? (
                  <div className='service-provider-list__sync_bo'>
                    <Button type='link' className='service-provider-list__sync_bo__button' disabled={true}>
                      BO synchronisé
                    </Button>
                  </div>
                ) : (
                  <div className='service-provider-list__sync_bo'>
                    <ButtonSync serviceProvider={record} getServiceProvider={fetchServiceProvider} />
                  </div>
                )
              ) : (
                <div className=''></div>
              );
            },
          },
          {
            title: 'Statut',
            dataIndex: 'isActive',
            key: 'isActive',
            width: 200,
            render: (status: boolean) => (status ? <Tag color='green'>Actif</Tag> : <Tag color='red'>Inactif</Tag>),
          },
          {
            title: 'Actions',
            key: 'action',
            width: 150,
            render: (_: unknown, record: ServiceProvider) => (
              <Dropdown
                menu={{
                  items: [
                    {
                      label: 'Modifier',
                      key: '1',
                      icon: <FormOutlined />,
                      onClick: () => navigate(`/prestataires/${type}/${record.id}`),
                    },
                    {
                      label: record.isActive ? 'Inactif' : 'Actif',
                      key: '2',
                      icon: <SyncOutlined />,
                      onClick: () => reactivateServiceProvider(record.id, query),
                    },
                    {
                      label: 'Supprimer',
                      key: '3',
                      icon: <DeleteOutlined />,
                      disabled: !record.isSynced ? true : false,
                      onClick: () => deleteServiceProvider(record, query),
                    },
                  ],
                  onClick: (e) => console.log('click', e),
                }}
                className='service-provider-list__actions-dropdown'
              >
                <Button>
                  <Space>
                    Action
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            ),
          },
        ];
        break;
      case 'pup':
        columns = [
          {
            title: 'Nom déchèterie',
            dataIndex: 'name',
            key: 'name',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Nom de code',
            dataIndex: 'supplierCode',
            key: 'supplierCode',
            width: 220,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Ville',
            dataIndex: 'city',
            key: 'city',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Téléphone',
            dataIndex: 'phone',
            key: 'phone',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Email',
            dataIndex: 'email',
            key: 'email',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Statut',
            dataIndex: 'isActive',
            key: 'isActive',
            width: 200,
            render: (status: boolean) => (status ? <Tag color='green'>Actif</Tag> : <Tag color='red'>Inactif</Tag>),
          },
          {
            title: 'Actions',
            key: 'action',
            width: 150,
            render: (_: unknown, record: ServiceProvider) => (
              <Dropdown
                menu={{
                  items: [
                    {
                      label: 'Connexion',
                      key: '1',
                      icon: <EyeOutlined />,
                    },
                    {
                      label: record.isActive ? 'Inactif' : 'Actif',
                      key: '2',
                      icon: <SyncOutlined />,
                      onClick: () => reactivateServiceProvider(record.id, query),
                    },
                    {
                      label: 'Modifier',
                      key: '3',
                      icon: <FormOutlined />,
                      onClick: () => navigate(`/prestataires/${type}/${record.id}`),
                    },
                    {
                      label: 'Supprimer',
                      key: '4',
                      icon: <DeleteOutlined />,
                      disabled: !record.isActive ? false : true,
                      onClick: () => deleteServiceProvider(record, query),
                    },
                  ],
                  onClick: (e) => console.log('click', e),
                }}
                className='service-provider-list__actions-dropdown'
              >
                <Button>
                  <Space>
                    Action
                    <DownOutlined />
                  </Space>
                </Button>
              </Dropdown>
            ),
          },
        ];
        break;
      default:
        columns = [];
        break;
    }
    setDefaultColumns(columns);
  }, [query]);

  console.log('dataSource.length total', dataSource.length, 'total: ', total);
  const LoaderRow = () => (
    <div className='datatable-loader'>
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  );

  return (
    <>
      <InfiniteScroll
        dataLength={dataSource.length}
        next={loadMoreData}
        hasMore={hasMore}
        loader={<LoaderRow />}
        endMessage={<Divider plain>Vous avez atteint la fin du flux</Divider>}
      >
        <Table
          columns={defaultColumns}
          dataSource={dataSource}
          pagination={false}
          className='service-provider-list__datatable'
          loading={{
            indicator: (
              <ScaleLoader
                color='#A6C84D'
                cssOverride={{
                  display: 'inline-block !important',
                  margin: '0 auto',
                  left: 0,
                  height: '100%',
                  width: '100%',
                }}
                aria-label='Loading Spinner'
                data-testid='loader'
              />
            ),
            spinning: dataSourceLoading === 'pending' && isAllowedLoading,
          }}
        />
      </InfiniteScroll>
    </>
  );
};

export default ServiceProviderList;
