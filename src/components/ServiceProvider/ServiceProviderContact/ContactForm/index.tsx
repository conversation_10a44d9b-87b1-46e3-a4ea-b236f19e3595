import { CloseOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, Input, Row, Select, Typography } from 'antd';
import { CheckButton, TrashButton } from 'components/Common';
import { useMergeState } from 'hooks';
import { Contact } from 'models';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { contactService } from 'services';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchContactFunctions, selectContactFunctions } from 'store/slices/contact.slices';
import { EllipsisAndTooltip } from 'utils/ellipsisAndTooltip';
import { validateEmail, validatePhoneNumber } from 'utils/validation';
const { Text } = Typography;
const { Option } = Select;
const ContactForm = ({
  contact,
  action,
  serviceProviderId,
  handleShowCreateForm,
  fetchContactList,
  getServiceProvider,
}: {
  contact?: Contact;
  serviceProviderId: number;
  action: 'create' | 'update';
  handleShowCreateForm: (value: boolean) => void;
  fetchContactList: () => void;
  getServiceProvider: () => void;
}) => {
  const dispatch = useAppDispatch();
  const [isEditing, setIsEditing] = useState<boolean>(action === 'create' ? true : false);
  const [isSubmited, setIsSubmited] = useState<boolean>(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [selectedContact, setSelectedContact] = useMergeState<Contact | undefined>(contact);
  const [inputValid, setInputValid] = useState<{
    emailValid: boolean;
    phoneValid: boolean;
  }>({
    emailValid: true,
    phoneValid: true,
  });
  const contactFunctions = useAppSelector(selectContactFunctions);

  const handleEditingMode = (value: boolean) => {
    setIsEditing(value);
  };

  const handleCancelEdit = () => {
    handleEditingMode(false);
    setSelectedContact(contact);
  };

  const getContactFunctions = async () => {
    await dispatch(fetchContactFunctions()).unwrap();
  };

  useEffect(() => {
    getContactFunctions();
  }, []);
  // Refresh data on change event
  const refreshData = async () => {
    await Promise.all([fetchContactList(), getServiceProvider()]);
  };
  const handleSubmitContact = async () => {
    setIsSubmited(true);
    try {
      if (
        !selectedContact?.name ||
        !selectedContact?.phone ||
        !selectedContact?.email ||
        !inputValid.emailValid ||
        !inputValid.phoneValid
      )
        return;
      setIsSubmiting(true);
      if (action === 'create') {
        const submitData = {
          ...selectedContact,
          serviceProviderId,
        };
        await contactService.createContact(submitData);
      } else {
        await contactService.updateContact(selectedContact?.id, selectedContact);
      }
      await refreshData();
      setIsSubmiting(false);
      handleShowCreateForm(false);
      setIsEditing(false);
      toast.success('Succès');
      setIsSubmited(false);
    } catch (error) {
      console.log(error);
      setIsSubmiting(false);
      toast.error('Erreur');
    }
  };

  const handleDeleteContact = async () => {
    if (!selectedContact?.id) return;
    try {
      setDeleteLoading(true);
      await contactService.deleteContact(selectedContact?.id);
      await refreshData();
      setDeleteLoading(false);
    } catch (error) {
      console.log(error);
      setDeleteLoading(false);
      toast.error('Erreur');
    }
  };

  const handleChangeContactFunction = async (value: number) => {
    setSelectedContact({ contactFunctionId: value });
    if (!isEditing) {
      if (!selectedContact?.id) return;
      try {
        const submitData = {
          ...selectedContact,
          contactFunctionId: value,
        };
        await contactService.updateContact(selectedContact?.id, submitData);
        await fetchContactList();
      } catch (error) {
        console.log(error);
        toast.error('Erreur');
      }
    }
  };

  const handleEmailChange = (email: string) => {
    setSelectedContact({ ...selectedContact, email });
    setInputValid({
      ...inputValid,
      emailValid: validateEmail(email),
    });
  };

  const handlePhoneChange = (phone: string) => {
    setSelectedContact({ ...selectedContact, phone });
    setInputValid({
      ...inputValid,
      phoneValid: validatePhoneNumber(phone),
    });
  };

  return (
    <div className='service-provider-contact__form'>
      <Row gutter={[12, 12]} style={{ width: '100%', justifyContent: 'space-between' }}>
        <Col lg={22} md={20} sm={20} xs={18}>
          <Row>
            <Col
              lg={6}
              md={24}
              sm={24}
              style={{
                display: 'flex',
                flexDirection: 'row',
                alignItems: 'center',
                gap: '8px',
                lineHeight: '22px !important',
              }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-contact__label'>Nom du contact:</Text>
              {action === 'update' && !isEditing ? (
                <Text className='service-provider-contact__name-text'>
                  <EllipsisAndTooltip>{contact?.name}</EllipsisAndTooltip>
                </Text>
              ) : (
                <Input
                  placeholder='Input'
                  className='service-provider-contact__input'
                  value={selectedContact?.name}
                  style={{
                    borderColor: isSubmited && !selectedContact?.name ? 'red' : '',
                  }}
                  onChange={(e) => setSelectedContact({ name: e.target.value })}
                  onPressEnter={handleSubmitContact}
                />
              )}
            </Col>
            <Col
              lg={6}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-contact__label'>Téléphone:</Text>
              {action === 'update' && !isEditing ? (
                <Text className='service-provider-contact__mobile-text'>
                  <EllipsisAndTooltip>{contact?.phone}</EllipsisAndTooltip>
                </Text>
              ) : (
                <Input
                  placeholder='Input'
                  value={selectedContact?.phone}
                  className='service-provider-contact__input'
                  style={{
                    borderColor: (isSubmited && !selectedContact?.phone) || !inputValid.phoneValid ? 'red' : '',
                  }}
                  onChange={(e) => handlePhoneChange(e.target.value)}
                  onPressEnter={handleSubmitContact}
                />
              )}
            </Col>
            <Col
              lg={6}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-contact__label'>Email:</Text>
              {action === 'update' && !isEditing ? (
                <Text className='service-provider-contact__email-text'>
                  <EllipsisAndTooltip>{contact?.email}</EllipsisAndTooltip>
                </Text>
              ) : (
                <Input
                  placeholder='Input'
                  value={selectedContact?.email}
                  className='service-provider-contact__input'
                  style={{
                    borderColor: (isSubmited && !selectedContact?.email) || !inputValid.emailValid ? 'red' : '',
                  }}
                  onChange={(e) => handleEmailChange(e.target.value)}
                  onPressEnter={handleSubmitContact}
                />
              )}
            </Col>
            <Col
              lg={6}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <Text className='service-provider-contact__label'>Fontion:</Text>
              <Select
                placeholder='Sélectionner'
                value={selectedContact?.contactFunctionId}
                className='service-provider-contact__function-selection'
                onChange={(value) =>
                  action === 'create'
                    ? setSelectedContact({ contactFunctionId: value })
                    : handleChangeContactFunction(value)
                }
              >
                {contactFunctions.map((contactFunction) => (
                  <Option key={contactFunction.id} value={contactFunction.id}>
                    {contactFunction.name}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
        </Col>
        <Col
          lg={2}
          md={4}
          sm={4}
          xs={6}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
            justifyContent: 'flex-end',
          }}
        >
          {action === 'update' &&
            (isEditing ? (
              <>
                <CheckButton className='check-button' loading={isSubmiting} onClick={handleSubmitContact} />
                <CloseOutlined
                  className='service-provider-contact__update-cancel-icon cancel-button'
                  onClick={handleCancelEdit}
                />
              </>
            ) : (
              <>
                <EditOutlined
                  className='service-provider-contact__edit-icon edit-icon'
                  onClick={() => handleEditingMode(true)}
                />
                <TrashButton
                  className='service-provider-contact__trash-icon trash-button'
                  loading={deleteLoading}
                  onClick={handleDeleteContact}
                />
              </>
            ))}
          {action === 'create' && (
            <>
              <Button
                type='text'
                size='small'
                className='service-provider-contact__ajouter-btn'
                loading={isSubmiting}
                onClick={handleSubmitContact}
              >
                <PlusCircleOutlined className='service-provider-contact__add-icon' />
                Ajouter
              </Button>
              <CloseOutlined className='cancel-button' onClick={() => handleShowCreateForm(false)} />
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};
export default ContactForm;
