import { ButtonAdd, OptionCard } from 'components/Common';
import { useEffect, useState } from 'react';
import ContactForm from './ContactForm';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchContacts, selectContacts } from 'store/slices/contact.slices';
import { useParams } from 'react-router-dom';

const ServiceProviderContact = ({ getServiceProvider }: { getServiceProvider: () => void }) => {
  const dispatch = useAppDispatch();
  const params = useParams();
  const serviceProviderId = parseInt(params.serviceProviderId as string);
  const [isShowCreateForm, setIsShowCreateForm] = useState<boolean>(false);
  const contacts = useAppSelector(selectContacts);

  const handleShowCreateForm = (value: boolean) => {
    setIsShowCreateForm(value);
  };

  const getContacts = async () => {
    await dispatch(
      fetchContacts({
        isActive: 1,
        serviceProviderId,
      }),
    ).unwrap();
  };

  useEffect(() => {
    getContacts();
  }, []);

  return (
    <OptionCard title='Informations Contact' otherStyles={{ marginTop: '30px' }}>
      {contacts.map((contact) => (
        <div key={contact.id}>
          <ContactForm
            contact={contact}
            action='update'
            serviceProviderId={serviceProviderId}
            handleShowCreateForm={handleShowCreateForm}
            fetchContactList={getContacts}
            getServiceProvider={getServiceProvider}
          />
          <hr className='service-provider-contact__end-line' />
        </div>
      ))}
      {isShowCreateForm && (
        <div>
          <ContactForm
            action='create'
            serviceProviderId={serviceProviderId}
            handleShowCreateForm={handleShowCreateForm}
            fetchContactList={getContacts}
            getServiceProvider={getServiceProvider}
          />
          <hr className='service-provider-contact__end-line' />
        </div>
      )}
      {!isShowCreateForm && (
        <ButtonAdd
          otherStyles={{
            height: '32px',
            borderRadius: '6px',
          }}
          handleClick={() => handleShowCreateForm(true)}
        >
          Ajouter un contact
        </ButtonAdd>
      )}
    </OptionCard>
  );
};

export default ServiceProviderContact;
