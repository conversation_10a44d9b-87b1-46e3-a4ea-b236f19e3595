import { useEffect, useState } from 'react';
import { useParams, useSearchParams } from 'react-router-dom';
import { Button, Form, Select, Spin, Switch } from 'antd';
import { toast } from 'react-toastify';
import { ServiceProvider, UrbanCenter, Year } from 'models';
import { urbanCenterService, yearService, catalogPriceLineService } from 'services';
import { CopyOutlined } from '@ant-design/icons';
import {
  YearPriceCopyModal,
  CopyConfirmationModal,
  UrbanCenterModal,
  ButtonAdd,
  CatalogPriceTab,
  OptionCard,
} from 'components/Common';
import { useUrbanCenters, useUrbanCentersQuery, useYearQuery, useYears } from 'hooks';

const { Option } = Select;

const ServiceProviderTarification = ({
  serviceProvider,
}: {
  typeKey: string;
  serviceProvider: ServiceProvider | null;
}) => {
  const [form] = Form.useForm();
  const [isOpenModal, setIsOpenModal] = useState(false);
  const params = useParams();
  const currentYear = new Date().getFullYear();
  const serviceProviderId = parseInt(params.serviceProviderId as string);
  const [urbanCentersQuery] = useUrbanCentersQuery({
    serviceProviderId,
    isActive: 1,
  });
  const [urbanCenters, refreshUrbanCenters, loadingUrbanCenters] = useUrbanCenters(urbanCentersQuery);
  const [searchParams, setSearchParams] = useSearchParams();
  const [selectedYearId, setSelectedYearId] = useState(parseInt(searchParams.get('yearId') as string));
  const urbanCenterId = parseInt(searchParams.get('urbanCenterId') as string);
  const [yearsQuery] = useYearQuery({
    isActive: 1,
  });
  const [years] = useYears(yearsQuery);
  const currentYearData = years.find((year) => parseInt(year.year as unknown as string) === currentYear);
  const [selectedYear, setSelectedYear] = useState<Year | null>(null);
  const [isOpenCopyConfirmation, setIsOpenCopyConfirmation] = useState<boolean>(false);
  const [isShowCopyPriceModal, setIsShowCopyPriceModal] = useState<boolean>(false);
  const [isDuplicatingYearPrice, setIsDuplicatingYearPrice] = useState<boolean>(false);
  const [isYearPriceActive, setIsYearPriceActive] = useState<boolean>(false);
  const [isShowPrixActive, setIsShowPrixActive] = useState<boolean>(true);
  const isLoading = loadingUrbanCenters === 'pending';
  useEffect(() => {
    handleShowPriceActive();
    handleShowYearPriceActivate();
  }, [selectedYearId, urbanCenters, form]);

  useEffect(() => {
    if (currentYearData?.id) {
      form.setFieldsValue({
        year: currentYearData.id,
      });
      setSearchParams({ yearId: `${currentYearData?.id}` });
      setSelectedYearId(currentYearData?.id);
    }
  }, [years, currentYearData]);

  const handleShowModal = (value: boolean) => {
    setIsOpenModal(value);
  };

  const handleSubmit = async (data: UrbanCenter) => {
    try {
      const submitData = {
        ...data,
        serviceProviderId,
        serviceProviderName: serviceProvider?.name,
      };
      const response = await urbanCenterService.createUrbanCenterWithoutRegion(submitData);
      Promise.all([refreshUrbanCenters()]);
      setSearchParams({ yearId: `${selectedYearId}`, urbanCenterId: response?.id });
      setIsOpenModal(false);
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const handleChangeYear = async (yearId: number) => {
    const year = years.find((year) => year.id === yearId);
    if (year) {
      setSearchParams({ yearId: `${year.id}` });
      if (urbanCenterId) {
        setSearchParams({ yearId: `${year.id}`, urbanCenterId: `${urbanCenterId}` });
      }
      setSelectedYearId(yearId);
    }
  };

  const handleActiveYearPrice = async () => {
    if (!selectedYearId) return;
    try {
      const urbanCenterIds = urbanCenters?.map((urbanCenter) => urbanCenter.id);
      setIsYearPriceActive(!isYearPriceActive);
      const submitData = {
        urbanCenterIds,
        isVisible: !isYearPriceActive,
      };
      await yearService.activateYearPrice(selectedYearId, submitData);
      Promise.all([refreshUrbanCenters()]);
      toast.success('Succès');
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const handleShowCopyConfirmation = (isOpen: boolean) => {
    setIsOpenCopyConfirmation(isOpen);
  };

  const handleShowCopyPriceModal = (isOpen: boolean) => {
    setIsShowCopyPriceModal(isOpen);
  };

  const handleSelectYear = (year: Year) => {
    setSelectedYear(year);
  };

  const handleSubmitYearPriceDuplicates = async () => {
    if (!selectedYear) return;
    try {
      setIsDuplicatingYearPrice(true);
      const dataSubmit = {
        duplicatedYearId: selectedYearId,
        urbanCenterIds: urbanCenters.map((urbanCenter) => urbanCenter.id),
        serviceProviderId: serviceProviderId,
      };
      await yearService.duplicateYearPrice(selectedYear.id, dataSubmit);
      setIsDuplicatingYearPrice(false);
      Promise.all([refreshUrbanCenters()]);
      toast.success('Succès');
      handleShowCopyConfirmation(false);
    } catch (error) {
      console.error(error);
      setIsDuplicatingYearPrice(false);
      toast.error('Erreur');
    }
  };

  const handleShowYearPriceActivate = async () => {
    try {
      const catalogPriceLineNumbers = await fetchCatalogPriceLineNumbers();
      if (catalogPriceLineNumbers) {
        form.setFieldsValue({
          isVisible: catalogPriceLineNumbers > 0,
        });
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const fetchCatalogPriceLineNumbers = async () => {
    try {
      const urbanCenterIds = urbanCenters.map((urbanCenter) => urbanCenter.id) as number[];
      const catalogPriceLineNumbers = await catalogPriceLineService.countCatalogPriceLines({
        yearId: selectedYearId,
        'urbanCenterIds[]': `[${urbanCenterIds.join(',')}]`,
        serviceProviderId,
        isVisible: 1,
      });
      setIsYearPriceActive(catalogPriceLineNumbers > 0);
      return catalogPriceLineNumbers;
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const handleShowPriceActive = async () => {
    try {
      const urbanCenterIds = urbanCenters.map((urbanCenter) => urbanCenter.id) as number[];
      const catalogPriceLineNumbers = await catalogPriceLineService.countCatalogPriceLines({
        yearId: selectedYearId,
        'urbanCenterIds[]': `[${urbanCenterIds.join(',')}]`,
        serviceProviderId,
      });
      setIsShowPrixActive(catalogPriceLineNumbers > 0);
      handleShowYearPriceActivate();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  return (
    <Spin style={{ color: '#A6C84D' }} spinning={loadingUrbanCenters === 'pending'}>
      <OptionCard
        title='Tarification'
        otherStyles={{
          marginTop: '30px',
          marginBottom: '30px',
        }}
      >
        <Form name='catalog-price' form={form} autoComplete='off'>
          <div
            style={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
            }}
          >
            <div style={{ display: 'flex' }}>
              <Form.Item
                name='year'
                initialValue={selectedYearId}
                rules={[{ required: true, message: '' }]}
                style={{ marginBottom: 20, width: '100%' }}
              >
                <Select placeholder='Année' className='region-product-list__zone-selection' onChange={handleChangeYear}>
                  {years.map((year) => (
                    <Option key={year.id} value={year.id}>
                      {year.year}
                    </Option>
                  ))}
                </Select>
              </Form.Item>
              {urbanCenters && urbanCenters?.length > 0 && isShowPrixActive && (
                <Form.Item name='isVisible' label='Activer le prix de cette année' initialValue={isYearPriceActive}>
                  <Switch
                    checked={isYearPriceActive}
                    size='small'
                    className='option__option-attributes-switch-default'
                    onChange={handleActiveYearPrice}
                  />
                </Form.Item>
              )}
            </div>
            {urbanCenters?.length > 0 && (
              <Button
                type='link'
                disabled={isYearPriceActive && isShowPrixActive}
                icon={
                  <CopyOutlined
                    style={{ fontSize: 24, color: '#95C515' }}
                    onClick={() => handleShowCopyPriceModal(true)}
                  />
                }
              />
            )}
            {isShowCopyPriceModal && (
              <YearPriceCopyModal
                yearList={years.filter((year) => year.id !== selectedYearId)}
                isOpenModal={isShowCopyPriceModal}
                onCancel={() => handleShowCopyPriceModal(false)}
                onShowCopyConfirmation={handleShowCopyConfirmation}
                onSelectYear={handleSelectYear}
              />
            )}
            {isOpenCopyConfirmation && (
              <CopyConfirmationModal
                isOpenModal={isOpenCopyConfirmation}
                form={form}
                selectedYear={years?.find((year) => year.id === selectedYearId)}
                onCancel={() => handleShowCopyConfirmation(false)}
                onSubmit={handleSubmitYearPriceDuplicates}
                isLoading={isDuplicatingYearPrice}
              />
            )}
          </div>
        </Form>
        {isLoading ? (
          <Spin
            spinning={true}
            style={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              marginTop: '100px',
            }}
          >
            <div style={{ height: 200 }}></div>
          </Spin>
        ) : (
          <>
            {urbanCenters.length > 0 ? (
              <CatalogPriceTab
                showDuplicateProduct={true}
                urbanCenters={urbanCenters}
                serviceProviderId={serviceProviderId}
                serviceProvider={serviceProvider}
                refreshUrbanCenters={refreshUrbanCenters}
                isLoading={isLoading}
                fetchCatalogPriceLinesByUrbanCenterIds={handleShowPriceActive}
                selectedYearId={selectedYearId}
                fetchCatalogPriceLineNumbers={fetchCatalogPriceLineNumbers}
              />
            ) : (
              <div className='service-provider__contact'>
                <ButtonAdd
                  otherStyles={{
                    height: '32px',
                    borderRadius: '6px',
                    marginTop: '43px',
                  }}
                  handleClick={() => handleShowModal(true)}
                >
                  Ajouter un centre urbain
                </ButtonAdd>
              </div>
            )}
          </>
        )}
        <UrbanCenterModal
          action='create'
          isOpenModal={isOpenModal}
          onCancel={() => setIsOpenModal(false)}
          onSubmit={handleSubmit}
        />
      </OptionCard>
    </Spin>
  );
};

export default ServiceProviderTarification;
