import { Checkbox, Col, Form, Input, Row } from 'antd';
import { useEffect, useState } from 'react';
import { ServiceProvider } from 'models';
import { AddressInput, ButtonEditSave, ColorPickerInput, OptionCard } from 'components/Common';
import { convertAddress, isValidUrl } from 'utils';
import { Address } from 'types';
import { serviceProviderService } from 'services';
import { toast } from 'react-toastify';
import { SERVICE_TYPES } from 'utils/constant';
import { EllipsisAndTooltip } from 'utils/ellipsisAndTooltip';
import { useCountryRegions, useCountryRegionsQuery } from 'hooks';
import { AxiosError } from 'axios';

interface ServiceProviderInfoProps {
  typeKey: string;
  serviceProvider: ServiceProvider | null;
  getServiceProvider: () => void;
}

const ServiceProviderInfo = (props: ServiceProviderInfoProps) => {
  const { typeKey, serviceProvider, getServiceProvider } = props;
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(true);
  const [isSubmited, setIsSubmited] = useState(false);
  const [addressDetail, setAddressDetail] = useState<Address | null>(null);
  const [countryRegionsQuery] = useCountryRegionsQuery();
  const [countryRegions] = useCountryRegions(countryRegionsQuery);
  const [nameExistsError, setNameExistsError] = useState(false);
  const [showValidateStatus, setShowValidateStatus] = useState(false);

  const handleEdit = () => {
    setIsEditing(true);
    form.setFieldsValue(defaultFormValues);
    setAddressDetail({ formattedAddress: serviceProvider?.formattedAddress || '' });
  };

  useEffect(() => {
    // form.setFieldsValue(defaultFormValues);
    setAddressDetail({ formattedAddress: serviceProvider?.formattedAddress || '' });
  }, [serviceProvider]);

  const defaultFormValues = {
    ...serviceProvider,
    colorCode: serviceProvider?.colorCode ?? '#ffffff',
    colorFormat: serviceProvider?.colorFormat ?? '#000000',
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setIsSubmited(false);
    setIsLoading(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    setIsSubmited(true);
    setShowValidateStatus(true);
    setNameExistsError(false);
    const vaild = await form.validateFields();
    if (vaild) {
      try {
        setIsLoading(true);
        const countryRegion = addressDetail?.countryRegion;
        const countryRegionId = countryRegions.find((cr) => cr.name === countryRegion)?.id;
        const values = { ...form.getFieldsValue(), ...addressDetail, countryRegionId };
        if (values.colorCode) {
          values.colorCode = typeof values.colorCode === 'string' ? values.colorCode : values.colorCode.toHexString();
        }
        if (values.colorFormat) {
          values.colorFormat =
            typeof values.colorFormat === 'string' ? values.colorFormat : values.colorFormat.toHexString();
        }

        await serviceProviderService.updateServiceProvider(serviceProvider?.id as number, values);
        await getServiceProvider();
        toast.success('Succès');
        setIsEditing(false);
        setIsLoading(false);
        setIsSubmited(false);
      } catch (error) {
        setIsLoading(false);
        console.error(error);
        const axiosError = error as AxiosError;
        if (axiosError.response?.status === 409) {
          setNameExistsError(true);
          toast.error('Le nom du fournisseur de services existe déjà');
        } else {
          toast.error('Erreur');
        }
      }
    }
  };

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleSelectAddress = async (place: any) => {
    const addressDetail = convertAddress(place);
    form.setFieldValue('formattedAddress', addressDetail?.formattedAddress);
    setAddressDetail(addressDetail);
  };

  const handleChangeAddress = () => {
    if (addressDetail) {
      setAddressDetail(null);
    }
    form.setFieldValue('formattedAddress', null);
  };

  const fichierField = (
    <Col xl={8} lg={12} md={24}>
      <Form.Item label='Fichiers' name='fichiers'>
        {isEditing ? (
          <Input onPressEnter={handleSubmit} />
        ) : (
          <EllipsisAndTooltip>
            {serviceProvider?.fichiers && isValidUrl(serviceProvider?.fichiers) ? (
              <a
                href={serviceProvider?.fichiers}
                target='_blank'
                rel='noopener noreferrer'
                style={{
                  color: '#1677FF',
                }}
              >
                {serviceProvider?.fichiers}
              </a>
            ) : (
              <span>{serviceProvider?.fichiers}</span>
            )}
          </EllipsisAndTooltip>
        )}
      </Form.Item>
    </Col>
  );

  return (
    <OptionCard title='Informations Société' otherStyles={{ marginTop: '30px' }}>
      <div className='service-provider__info'>
        <Form
          name='service-provider-info'
          labelCol={{ lg: 11, md: 11 }}
          form={form}
          initialValues={defaultFormValues}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
        >
          <Row gutter={[12, 12]}>
            <Col lg={22} md={22} sm={20} xs={18}>
              <Row>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item
                    label='Nom de la société'
                    name='name'
                    rules={[{ required: true }]}
                    validateStatus={isSubmited && nameExistsError && showValidateStatus ? 'error' : undefined}
                  >
                    {isEditing ? (
                      <Input
                        onPressEnter={handleSubmit}
                        onChange={() => {
                          setShowValidateStatus(false);
                        }}
                      />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.name}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item
                    label='Adresse'
                    name='formattedAddress'
                    validateStatus={isSubmited && !addressDetail?.formattedAddress ? 'error' : 'validating'}
                    rules={[{ required: true }]}
                  >
                    {isEditing ? (
                      <AddressInput
                        onSelect={handleSelectAddress}
                        onChange={handleChangeAddress}
                        defaultAddress={addressDetail?.formattedAddress ?? serviceProvider?.formattedAddress}
                      />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.formattedAddress}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Nom de code' name='supplierCode' rules={[{ required: true }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.supplierCode}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='SIREN' name='siren'>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.siren}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='SIRET' name='siret'>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.siret}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item
                    label='Téléphone'
                    name='phone'
                    rules={[
                      { required: true },
                      () => ({
                        validator(_, value) {
                          if (!value) return Promise.reject();
                          const pattern = /^\d+\.?\d*$/;
                          const valid = pattern.test(value);
                          if (!valid) return Promise.reject();
                          return Promise.resolve();
                        },
                      }),
                    ]}
                  >
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.phone}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col lg={2} md={2} sm={4} xs={6} style={{ textAlign: 'center' }}>
              <ButtonEditSave
                isEditing={isEditing}
                loading={isLoading}
                onEdit={handleEdit}
                onSubmit={handleSubmit}
                onCancel={handleCancelEdit}
              />
            </Col>
          </Row>
          <Row gutter={[12, 12]}>
            <Col lg={22} md={22} sm={20} xs={18}>
              <Row>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Forme juridique' name='legalForm'>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.legalForm}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Email' name='email' rules={[{ required: true, type: 'email' }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{serviceProvider?.email}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                {typeKey !== SERVICE_TYPES.pup && (
                  <Col xl={8} lg={12} md={24}>
                    <Form.Item label='N° de Récepissé' name='receiptNumber'>
                      {isEditing ? (
                        <Input onPressEnter={handleSubmit} />
                      ) : (
                        <EllipsisAndTooltip>{serviceProvider?.receiptNumber}</EllipsisAndTooltip>
                      )}
                    </Form.Item>
                  </Col>
                )}
                {typeKey === SERVICE_TYPES.pup && (
                  <Col xl={8} lg={12} md={24}>
                    <Form.Item label='Collecte' name='collecte' valuePropName='checked'>
                      <Checkbox disabled={!isEditing} />
                    </Form.Item>
                  </Col>
                )}
              </Row>
            </Col>
          </Row>
          {typeKey === SERVICE_TYPES.pup && (
            <Col lg={22} md={22} sm={20} xs={18}>
              <div className='fichier-field'>{fichierField}</div>
            </Col>
          )}
          {typeKey !== SERVICE_TYPES.pup && (
            <Row gutter={[12, 12]}>
              <Col lg={22} md={22} sm={20} xs={18}>
                <Row>
                  <Col span={8}>
                    <Form.Item label='Couleur' name='colorCode' style={{ marginBottom: 0 }}>
                      <Form.Item
                        name='colorCode'
                        style={{
                          display: 'inline-block',
                          width: 'calc(50% - 15px)',
                        }}
                      >
                        <ColorPickerInput
                          customText='Texte'
                          disabled={!isEditing}
                          readonlyValue={serviceProvider?.colorCode ?? '#ffffff'}
                        />
                      </Form.Item>

                      <Form.Item
                        label=''
                        name='colorFormat'
                        style={{
                          display: 'inline-block',
                          width: 'calc(50% + 15px)',
                        }}
                      >
                        <ColorPickerInput
                          customText='Arrière-plan'
                          disabled={!isEditing}
                          readonlyValue={serviceProvider?.colorFormat ?? '#000000'}
                        />
                      </Form.Item>
                    </Form.Item>
                  </Col>
                  {fichierField}
                </Row>
              </Col>
            </Row>
          )}
        </Form>
      </div>
    </OptionCard>
  );
};

export default ServiceProviderInfo;
