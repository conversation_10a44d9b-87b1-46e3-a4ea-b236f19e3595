import { Col, Form, Input, Row, Select, Switch } from 'antd';
import { useEffect, useState } from 'react';
import { ServiceProvider, ServiceProviderBankAccount } from 'models';
import { AddressInput, ButtonEdit, OptionCard } from 'components/Common';
import { convertAddress } from 'utils';
import { Address } from 'types';
import { serviceProviderService } from 'services';
import { toast } from 'react-toastify';
import mangopayImg from 'assets/images/mangopay.png';
import { MP_COUNTRIES, MP_PAYOUTS } from 'utils/constant';
import { EllipsisAndTooltip } from 'utils/ellipsisAndTooltip';
const { Option } = Select;

interface ServiceProviderBankAccountProps {
  serviceProvider: ServiceProvider | null;
  serviceProviderId: number;
}

const ServiceProviderBankInfo = (props: ServiceProviderBankAccountProps) => {
  const { serviceProviderId } = props;
  const [form] = Form.useForm();
  const [isLoading, setIsLoading] = useState(false);
  const [isEditing, setIsEditing] = useState(false);
  const [isSubmited, setIsSubmited] = useState(false);
  const [addressDetail, setAddressDetail] = useState<Address | null>(null);
  const [isConnectedMP, setIsConnectedMP] = useState(false);
  const [bankAccount, setBankAccount] = useState<ServiceProviderBankAccount>({
    serviceProviderId,
    isConnectedMP: false,
  });

  const getBankAccount = async () => {
    const response = await serviceProviderService.getBankAccount({
      serviceProviderId,
      limit: 'unlimited',
    });
    if (response.rows.length) {
      setBankAccount(response.rows[0]);
      setIsConnectedMP(response.rows[0].isConnectedMP as boolean);
    }
  };

  useEffect(() => {
    getBankAccount();
  }, []);

  const handleEdit = () => {
    setIsEditing(true);
    form.setFieldsValue(bankAccount);
    setAddressDetail({ formattedAddress: bankAccount?.formattedAddress });
  };

  const handleCancelEdit = () => {
    setIsEditing(false);
    setIsSubmited(false);
    setIsLoading(false);
    setIsConnectedMP(false);
    form.resetFields();
  };

  const handleSubmit = async () => {
    setIsSubmited(true);
    const vaild = await form.validateFields();
    if (vaild) {
      try {
        setIsLoading(true);
        const values = {
          ...form.getFieldsValue(),
          isConnectedMP,
          serviceProviderId,
          ...addressDetail,
        };
        let response;
        if (bankAccount?.id) {
          response = await serviceProviderService.updateBankAccount(bankAccount?.id as number, values);
        } else {
          response = await serviceProviderService.createBankAccount(values);
        }
        setBankAccount(response);
        setIsConnectedMP(response.isConnectedMP as boolean);
        toast.success('Succès');
        setIsEditing(false);
        setIsLoading(false);
        setIsSubmited(false);
      } catch (error) {
        console.log(error);
        setIsLoading(false);
        toast.error('Erreur');
      }
    }
  };

  const handleSelectAddress = async (place: object) => {
    const addressDetail = convertAddress(place);
    form.setFieldValue('formattedAddress', addressDetail?.formattedAddress);
    setAddressDetail(addressDetail);
  };

  const handleChangeAddress = () => {
    if (addressDetail) {
      setAddressDetail(null);
    }
    form.setFieldValue('formattedAddress', null);
  };

  const handleIsConnectedMP = (checked: boolean) => {
    setIsConnectedMP(checked);
    if (!isEditing) {
      form.setFieldsValue(bankAccount);
      setAddressDetail({ formattedAddress: bankAccount?.formattedAddress });
      setIsEditing(true);
    }
    if (!form.getFieldValue('isPayoutAuto')) {
      form.setFieldValue('isPayoutAuto', MP_PAYOUTS[0].value);
    }
    if (!form.getFieldValue('countryMP')) {
      form.setFieldValue('countryMP', MP_COUNTRIES[0].value);
    }
    if (isSubmited) {
      setTimeout(() => {
        form.validateFields();
      });
    }
  };

  const payoutAuto = MP_PAYOUTS.find((i) => i.value === bankAccount?.isPayoutAuto);

  return (
    <OptionCard
      title='Informations Bancaires'
      otherStyles={{
        marginTop: '30px',
      }}
    >
      <div className='service-provider__bank-account'>
        <Form
          name='service-provider-bank-account'
          labelCol={{ lg: 12, md: 12 }}
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
        >
          <Row gutter={[12, 12]}>
            <Col lg={22} md={22} sm={20} xs={18}>
              <Row>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Banque' name='bankName' rules={[{ required: isConnectedMP }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.bankName}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='N° de compte / IBAN' name='IBAN'>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.IBAN}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Code BIC' name='BIC' rules={[{ required: isConnectedMP }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.BIC}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Nom du titulaire' name='ownerName' rules={[{ required: isConnectedMP }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.ownerName}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Adresse' name='formattedAddress' rules={[{ required: isConnectedMP }]}>
                    {isEditing ? (
                      <AddressInput
                        onSelect={handleSelectAddress}
                        onChange={handleChangeAddress}
                        defaultAddress={addressDetail?.formattedAddress}
                      />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.formattedAddress}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Région' name='region' rules={[{ required: isConnectedMP }]}>
                    {isEditing ? (
                      <Input onPressEnter={handleSubmit} />
                    ) : (
                      <EllipsisAndTooltip>{bankAccount?.region}</EllipsisAndTooltip>
                    )}
                  </Form.Item>
                </Col>
                <Col xl={8} lg={12} md={24}>
                  <Form.Item label='Connexion MangoPay' name='isConnectedMP'>
                    <Switch
                      size='small'
                      checked={isConnectedMP}
                      className='option__option-attributes-switch-default'
                      onChange={handleIsConnectedMP}
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Col>
            <Col lg={2} md={2} sm={4} xs={6} style={{ textAlign: 'center' }}>
              <ButtonEdit
                isEditing={isEditing}
                loading={isLoading}
                onEdit={handleEdit}
                onSubmit={handleSubmit}
                onCancel={handleCancelEdit}
              />
            </Col>
          </Row>
          {isConnectedMP && (
            <Row gutter={[12, 12]} className='mangopay-block'>
              <Col xl={6} lg={12} sm={22} xs={24}>
                <Form.Item label='Pays' name='countryMP' rules={[{ required: true }]}>
                  {isEditing ? (
                    <Select placeholder='Sélectionner'>
                      {MP_COUNTRIES.map((option) => (
                        <Option key={option.key} value={option.value}>
                          {option.name}
                        </Option>
                      ))}
                    </Select>
                  ) : (
                    <EllipsisAndTooltip>{bankAccount?.countryMP}</EllipsisAndTooltip>
                  )}
                </Form.Item>
              </Col>
              <Col xl={8} lg={12} sm={22} xs={24}>
                <Form.Item label='Option de paiement' name='isPayoutAuto' rules={[{ required: true }]}>
                  {isEditing ? (
                    <Select placeholder='Sélectionner'>
                      {MP_PAYOUTS.map((option) => (
                        <Option key={option.key} value={option.value}>
                          {option.name}
                        </Option>
                      ))}
                    </Select>
                  ) : payoutAuto ? (
                    payoutAuto?.name
                  ) : (
                    ''
                  )}
                </Form.Item>
              </Col>
              <Col xl={5} lg={12} sm={22} xs={24}>
                <Form.Item label='Status'>{/* <Tag color='processing'>REGULAR</Tag> */}</Form.Item>
              </Col>
              <Col xl={5} lg={12} sm={24} xs={24}>
                <div className='mangopay-img-container'>
                  <img src={mangopayImg} className='mangopay-img' alt='mangopay' />
                </div>
              </Col>
            </Row>
          )}
        </Form>
      </div>
    </OptionCard>
  );
};

export default ServiceProviderBankInfo;
