import { Button, Input, Modal, Space, Typography } from 'antd';
import { ServiceType } from 'models';
const { Text } = Typography;

const CreateProviderModal = ({
  visible,
  loading,
  serviceType,
  name,
  handleOk,
  handleCancel,
  setName,
}: {
  visible: boolean;
  loading: boolean;
  name: string;
  serviceType?: ServiceType;
  handleOk: () => void;
  handleCancel: () => void;
  setName: (value: string) => void;
}) => {
  return (
    <Modal
      title={`AJOUTER UN ${serviceType?.name?.toUpperCase()}`}
      open={visible}
      onOk={handleOk}
      onCancel={handleCancel}
      centered
      footer={[
        <Button
          key='annuler'
          onClick={handleCancel}
          className='ant-modal-content__cancel-btn'
        >
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          onClick={handleOk}
          className='ant-modal-content__add-btn'
          loading={loading}
        >
          Ajouter
        </Button>,
      ]}
    >
      <Space direction='vertical'>
        <Text className='ant-modal-content__title-of-product-label'>
          Nom de la société
        </Text>
        <Input
          placeholder='Input'
          value={name}
          className='ant-modal-content__title-of-product-input'
          onChange={(e) => setName(e.target.value)}
        />
      </Space>
    </Modal>
  );
};

export default CreateProviderModal;
