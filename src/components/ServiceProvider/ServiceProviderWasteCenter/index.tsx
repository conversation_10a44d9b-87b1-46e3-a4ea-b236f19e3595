import { ButtonAdd, OptionCard } from 'components/Common';
import { useEffect, useState } from 'react';
import WasteCenterForm from './WasteCenterForm';
import { wasteCenterService } from 'services';
import { ServiceProvider, WasteCenter } from 'models';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchWasteTypes, selectWasteTypes } from 'store/slices/service_provider.slices';
import { Row, Space } from 'antd';
import ValuationRatesModal from './ValuationRatesModal';

interface ServiceProviderWasteCenterProps {
  serviceProvider: ServiceProvider | null;
  serviceProviderId: number;
  getServiceProvider: () => void;
}

const ServiceProviderWasteCenter = (props: ServiceProviderWasteCenterProps) => {
  const { serviceProviderId, getServiceProvider } = props;
  const dispatch = useAppDispatch();
  const [wasteCenters, setWasteCenters] = useState<WasteCenter[]>([]);
  const [isShowCreateForm, setIsShowCreateForm] = useState<boolean>(false);
  const [isShowAddValuationRatesForm, setIsShowAddValuationRatesForm] = useState<boolean>(false);
  const wasteTypes = useAppSelector(selectWasteTypes);
  const wasteCenterIds = wasteCenters.map((wasteCenter) => wasteCenter.id);

  const handleShowCreateForm = (value: boolean) => {
    setIsShowCreateForm(value);
  };

  const handleShowAddValuationRates = (value: boolean) => {
    setIsShowAddValuationRatesForm(value);
  };

  const getWasteCenters = async () => {
    const { rows } = await wasteCenterService.getWasteCenters({
      serviceProviderId,
      limit: 'unlimited',
      include: 'WasteTypes',
      isActive: 1,
    });
    setWasteCenters(rows);
  };

  const getWasteTypes = async () => {
    await dispatch(fetchWasteTypes());
  };

  useEffect(() => {
    getWasteCenters();
    getWasteTypes();
  }, []);

  return (
    <OptionCard title='Déchèterie' otherStyles={{ marginTop: '30px' }}>
      {isShowAddValuationRatesForm && (
        <ValuationRatesModal
          isOpenModal={isShowAddValuationRatesForm}
          handleCancel={() => handleShowAddValuationRates(false)}
          wasteCenterIds={wasteCenterIds}
          serviceProviderId={serviceProviderId}
          getServiceProvider={getServiceProvider}
        />
      )}
      {wasteCenters.map((wasteCenter) => (
        <div key={wasteCenter?.id}>
          <WasteCenterForm
            serviceProviderId={serviceProviderId}
            action='update'
            wasteTypes={wasteTypes}
            wasteCenter={wasteCenter}
            handleShowCreateForm={handleShowCreateForm}
            fetchWasteCenterList={getWasteCenters}
            getServiceProvider={props.getServiceProvider}
          />
          <hr className='service-provider-waste-center__end-line' />
        </div>
      ))}
      {isShowCreateForm && (
        <div>
          <WasteCenterForm
            serviceProviderId={serviceProviderId}
            action='create'
            wasteTypes={wasteTypes}
            handleShowCreateForm={handleShowCreateForm}
            fetchWasteCenterList={getWasteCenters}
            getServiceProvider={props.getServiceProvider}
          />
          <hr className='service-provider-contact__end-line' />
        </div>
      )}
      <Row justify='space-between' className='mt-8'>
        <Space direction='horizontal' align='start' className='quotation__button-block'>
          {!isShowCreateForm && (
            <ButtonAdd
              otherStyles={{
                height: '32px',
                borderRadius: '6px',
                marginTop: '43px',
              }}
              handleClick={() => handleShowCreateForm(true)}
            >
              Ajouter une déchèterie
            </ButtonAdd>
          )}
          {wasteCenters.length >= 1 && (
            <ButtonAdd
              otherStyles={{
                height: '32px',
                borderRadius: '6px',
                marginTop: '43px',
              }}
              handleClick={() => handleShowAddValuationRates(true)}
            >
              Ajouter les taux de valorisation
            </ButtonAdd>
          )}
        </Space>
      </Row>
    </OptionCard>
  );
};

export default ServiceProviderWasteCenter;
