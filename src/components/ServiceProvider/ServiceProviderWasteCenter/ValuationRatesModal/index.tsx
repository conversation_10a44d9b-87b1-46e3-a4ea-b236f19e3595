import { Button, Form, Modal } from 'antd';
import { useEffect, useState } from 'react';
import ValuationRatesTable from './ValuationRatesTable';
import { useWasteCenterTypes, useWasteCenterTypesQuery } from 'hooks';
import { useAppSelector } from 'store';
import { selectWasteCenterTypesTotal } from 'store/slices/waste_center_type.slices';
import { toast } from 'react-toastify';
import { wasteCenterTypeService } from 'services';

interface ValuationRatesModalProps {
  isOpenModal: boolean;
  handleCancel: () => void;
  wasteCenterIds: number[];
  getServiceProvider: () => void;
  serviceProviderId: number;
}

const ValuationRatesModal = (props: ValuationRatesModalProps) => {
  const { isOpenModal, handleCancel, wasteCenterIds, getServiceProvider, serviceProviderId } = props;
  const [form] = Form.useForm();
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [page, setPage] = useState<number>(1);
  const [limit, setLimit] = useState<number>(10);
  const [wasteCenterTypesQuery] = useWasteCenterTypesQuery({
    'wasteCenterId[]': JSON.stringify(wasteCenterIds),
    include: 'WasteCenter|WasteType',
    page: page,
    limit: limit,
  });
  const [wasteCenterTypes, wasteCenterTypesLoading, fetchWasteCenterTypes] = useWasteCenterTypes(wasteCenterTypesQuery);
  const wasteCenterTypesTotal = useAppSelector(selectWasteCenterTypesTotal);

  useEffect(() => {
    fetchWasteCenterTypes({
      'wasteCenterId[]': JSON.stringify(wasteCenterIds),
      include: 'WasteCenter|WasteType',
      page: page,
      limit: limit,
    });
  }, [isOpenModal, wasteCenterIds, page, limit]);

  const handleSubmit = async () => {
    try {
      const valid = await form.validateFields();
      if (valid) {
        setIsSubmiting(true);
        const values = form.getFieldsValue(true);
        const valueRates = Object.keys(values.valueRates).map((key) => {
          const item = { ...values.valueRates[key], id: parseInt(key) };
          return item;
        });
        console.log('valueRates: ', valueRates);
        const updatedValueRates = wasteCenterTypes.map((wasteCenterType) => {
          const updatedRate = valueRates.find((rate) => rate.id === wasteCenterType.id);
          if (updatedRate) {
            return {
              ...wasteCenterType,
              ...updatedRate,
            };
          }
          return wasteCenterType;
        });
        const submitData = {
          wasteCenterTypes: updatedValueRates,
          serviceProviderId: serviceProviderId,
        };
        await wasteCenterTypeService.multipleWasteCenterTypeUpdates(submitData);
        await Promise.all([
          fetchWasteCenterTypes({
            'wasteCenterId[]': JSON.stringify(wasteCenterIds),
            include: 'WasteCenter|WasteType',
            page: page,
            limit: limit,
          }),
          getServiceProvider(),
        ]);
        setIsSubmiting(false);
        toast.success('Succès');
        handleCancel();
      }
    } catch (error) {
      setIsSubmiting(false);
      console.log(error);
      toast.error('Erreur');
    }
  };
  return (
    <Modal
      title='Ajouter les taux de valorisation'
      open={isOpenModal}
      maskClosable={false}
      onCancel={handleCancel}
      className='duplicate-produits-modal'
      width='75%'
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={handleSubmit}
          loading={isSubmiting}
        >
          Valider
        </Button>,
      ]}
    >
      <Form layout='horizontal' form={form} style={{ width: '100%' }}>
        <ValuationRatesTable
          form={form}
          wasteCenterTypesDataList={wasteCenterTypes}
          wasteCenterTypesLoading={wasteCenterTypesLoading}
          wasteCenterTypesTotal={wasteCenterTypesTotal}
          page={page}
          limit={limit}
          setPage={setPage}
          setLimit={setLimit}
        />
      </Form>
    </Modal>
  );
};

export default ValuationRatesModal;
