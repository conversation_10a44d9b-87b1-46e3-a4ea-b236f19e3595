import { Form, FormInstance, Input, InputRef, message } from 'antd';
import { WasteCenterType } from 'models';
import React, { FormEvent, useEffect, useRef, useState } from 'react';

export type ValuationRates = WasteCenterType & {
  rowSpan?: number;
  isLast?: boolean;
  isFirst?: boolean;
  isDisplay?: boolean;
};

const EditableContext = React.createContext<FormInstance | null>(null);

export const EditableValuationRatesRow = (props: React.HTMLAttributes<HTMLTableRowElement>) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditableValuationRatesCellProps {
  form: FormInstance;
  title: string;
  editable: boolean;
  children: React.ReactNode;
  inputType: string;
  required: boolean;
  dataIndex: keyof ValuationRates;
  record: ValuationRates;
  className?: string;
}

export const EditableValuationRatesCell: React.FC<EditableValuationRatesCellProps> = ({
  form,
  editable,
  children,
  dataIndex,
  record,
  ...restProps
}) => {
  const fieldName = dataIndex;
  const [value, setValue] = useState(record && record[fieldName]);
  const inputRef = useRef<InputRef>(null);

  useEffect(() => {
    if (record) {
      form.setFieldsValue({
        valueRates: {
          [`${record?.id}`]: {
            [`${fieldName}`]: record[fieldName],
          },
        },
      });
    }
  }, [record]);

  const save = async (e: FormEvent<HTMLInputElement>) => {
    try {
      const inputElement = e.target as HTMLInputElement;
      const { value } = inputElement;
      try {
        form.setFieldsValue({
          valueRates: {
            [`${record?.id}`]: {
              [`${fieldName}`]: value,
            },
          },
        });
      } catch (error) {
        console.error(error);
        message.error('Error updating field value');
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  let childNode = children;
  if (editable) {
    childNode = (
      <Form.Item
        style={{ margin: 0 }}
        name={['valueRates', record?.id, fieldName]}
        initialValue={value}
        rules={[
          () => ({
            validator(_, value: string | number) {
              if (isNaN(value as number)) {
                return Promise.reject();
              }
              return Promise.resolve();
            },
          }),
        ]}
      >
        <Input
          style={{ height: '30px' }}
          ref={inputRef}
          onChangeCapture={(e) => save(e)}
          onBlur={(e) => save(e)}
          suffix={'%'}
          onChange={(e) => setValue(parseFloat(e.target.value))}
        />
      </Form.Item>
    );
  }

  const className = restProps?.className;

  return (
    <td {...restProps} className={`${className} ant-table-cell ${record?.isLast === false ? 'non-bottom-border' : ''}`}>
      {childNode}
    </td>
  );
};
