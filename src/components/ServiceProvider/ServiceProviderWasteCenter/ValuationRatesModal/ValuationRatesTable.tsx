import { FormInstance, Pagination, Table, TableProps } from 'antd';
import { EditableValuationRatesCell, EditableValuationRatesRow, ValuationRates } from './ValuationRatesCellTable';
import { WasteCenter, WasteCenterType, WasteType } from 'models';
import { Loading } from 'types';
import { useMemo } from 'react';

interface ValuationRatesTableProps {
  form: FormInstance;
  wasteCenterTypesDataList: WasteCenterType[];
  wasteCenterTypesLoading: Loading;
  wasteCenterTypesTotal: number;
  page: number;
  limit: number;
  setPage: (page: number) => void;
  setLimit: (limit: number) => void;
}

const ValuationRatesTable = (props: ValuationRatesTableProps) => {
  const {
    form,
    wasteCenterTypesDataList,
    wasteCenterTypesLoading,
    wasteCenterTypesTotal,
    page,
    limit,
    setPage,
    setLimit,
  } = props;
  const dataSource = useMemo(() => {
    return wasteCenterTypesDataList?.map((wasteCenterType) => ({
      ...wasteCenterType,
      key: wasteCenterType.id.toString(),
      rowSpan: 1,
    }));
  }, [wasteCenterTypesDataList]) as ValuationRates[];
  const defaultColumns = [
    {
      title: 'Exutoire',
      dataIndex: 'WasteCenter',
      width: 240,
      onCell: (record: ValuationRates) => ({
        rowSpan: record.rowSpan,
      }),
      render: (value: WasteCenter) => <span>{value?.name ?? ''}</span>,
    },
    {
      title: 'Type de déchet',
      dataIndex: 'WasteType',
      width: 200,
      onCell: (record: ValuationRates) => ({
        rowSpan: record.rowSpan,
      }),
      render: (value: WasteType) => <span>{value?.name ?? ''}</span>,
    },
    {
      title: 'Taux de Réutilisation',
      dataIndex: 'tauxDeReutilisation',
      editable: true,
      width: 140,
    },
    {
      title: 'Taux de Recyclage',
      dataIndex: 'tauxDeRecyclage',
      editable: true,
      width: 140,
    },
    {
      title: 'Taux de Valorisation énergétique',
      dataIndex: 'tauxDeValoEnerge',
      editable: true,
      width: 140,
    },
    {
      title: "Taux d'Elimination",
      dataIndex: 'tauxElimination',
      editable: true,
      width: 140,
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: ValuationRates) => ({
        form,
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
      }),
    };
  }) as TableProps<ValuationRates>['columns'];

  return (
    <section className='section quotation' style={{ width: '100%' }}>
      <Table
        bordered
        className='valuation-rates-table'
        components={{
          body: {
            row: EditableValuationRatesRow,
            cell: EditableValuationRatesCell,
          },
        }}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
        loading={wasteCenterTypesLoading === 'pending' ? true : false}
      />
      <div className='pagination__pagination-items'>
        <span className='pagination__number-total'>Total {wasteCenterTypesTotal ?? 0} items</span>
        <Pagination
          showSizeChanger
          total={wasteCenterTypesTotal}
          current={page}
          pageSize={limit}
          onChange={(_page, _limit) => {
            setPage(_page);
            setLimit(_limit);
          }}
          className='pagination'
        />
      </div>
    </section>
  );
};

export default ValuationRatesTable;
