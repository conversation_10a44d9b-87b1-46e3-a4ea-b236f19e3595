import { CloseOutlined, EditOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { Button, Col, Input, Radio, Row, Select, Tag, Typography } from 'antd';
import { AddressInput, CheckButton, TrashButton } from 'components/Common';
import { useMergeState } from 'hooks';
import { WasteCenter, WasteType } from 'models';
import { useEffect, useState } from 'react';
import { toast } from 'react-toastify';
import { serviceProviderService, wasteCenterService } from 'services';
import { Address } from 'types';
import { convertAddress } from 'utils';
import { EllipsisAndTooltip } from 'utils/ellipsisAndTooltip';
const { Option } = Select;
const { Text } = Typography;

interface RecordType {
  label: string | undefined;
  value: number;
}

const WasteCenterForm = ({
  serviceProviderId,
  wasteCenter,
  wasteTypes,
  action,
  handleShowCreateForm,
  fetchWasteCenterList,
  getServiceProvider,
}: {
  serviceProviderId: number;
  wasteCenter?: WasteCenter;
  wasteTypes: WasteType[];
  action: 'create' | 'update';
  handleShowCreateForm: (value: boolean) => void;
  fetchWasteCenterList: () => void;
  getServiceProvider: () => void;
}) => {
  const [selectedAll, setSelectedAll] = useState<boolean>(false);
  const [wasteTypeIds, setWasteTypeIds] = useState<(number | string)[]>(
    (wasteCenter?.WasteTypes as WasteType[])?.map((i) => i.id),
  );
  const [deleteLoading, setDeleteLoading] = useState<boolean>(false);
  const [isSubmited, setIsSubmited] = useState<boolean>(false);
  const [isSubmiting, setIsSubmiting] = useState<boolean>(false);
  const [selectedWasteCenter, setSelectedWasteCenter] = useMergeState<WasteCenter | undefined>(wasteCenter);
  const [addressDetail, setAddressDetail] = useState<Address | null>({
    city: wasteCenter?.city,
    postalcode: wasteCenter?.postalCode,
    country: wasteCenter?.country,
    address: wasteCenter?.address,
    formattedAddress: wasteCenter?.formattedAddress,
  });
  const [isEditing, setIsEditing] = useState<boolean>(action === 'create' ? true : false);

  const handleEditingMode = (value: boolean) => {
    setIsEditing(value);
  };

  const handleCancelEdit = async () => {
    handleEditingMode(false);
    setSelectedWasteCenter(wasteCenter);
    setAddressDetail({
      city: wasteCenter?.city,
      postalcode: wasteCenter?.postalCode,
      country: wasteCenter?.country,
      address: wasteCenter?.address,
      formattedAddress: wasteCenter?.formattedAddress,
    });
    setWasteTypeIds((wasteCenter?.WasteTypes as WasteType[])?.map((i) => i.id));
    await getServiceProvider();
  };

  const toggleSelectAll = () => {
    if (selectedAll) {
      if (action === 'create') {
        setWasteTypeIds([]);
      } else {
        handleChangeWasteCenter([]);
      }
    } else {
      const allValues = wasteTypes.map((option) => option.id);
      if (action === 'create') {
        setWasteTypeIds(allValues);
      } else {
        handleChangeWasteCenter(allValues);
      }
    }
    setSelectedAll(!selectedAll);
  };

  /* eslint-disable-next-line */
  const filterOption = (inputValue: string, option: RecordType | any) =>
    option?.label && option.label.indexOf(inputValue) > -1;

  const handleSelectAddress = (place: Address) => {
    const addressDetail = convertAddress(place);
    setAddressDetail(addressDetail);
  };

  const handleChangeAddress = () => {
    if (addressDetail) {
      setAddressDetail(null);
    }
  };

  useEffect(() => {
    setSelectedWasteCenter(wasteCenter);
  }, [wasteCenter]);

  useEffect(() => {
    setWasteTypeIds((wasteCenter?.WasteTypes as WasteType[])?.map((i) => i.id));
  }, [wasteCenter?.WasteTypes]);

  // refresh waste center list and service provider
  const refreshData = async () => {
    await Promise.all([fetchWasteCenterList(), getServiceProvider()]);
  };
  const handleDeactivateWasteCenter = async () => {
    if (!selectedWasteCenter?.id) return;
    try {
      setDeleteLoading(true);
      await wasteCenterService.deactivateWasteCenter(selectedWasteCenter?.id, {
        serviceProviderId,
      });
      await refreshData();
      setDeleteLoading(false);
      toast.success('Succès');
    } catch (error) {
      setDeleteLoading(false);
      toast.error('Erreur');
      console.log(error);
    }
  };

  const handleSetDefaultWasteCenter = async (serviceProviderId: number, wasteCenterId?: number) => {
    if (!wasteCenterId) return;
    try {
      await serviceProviderService.setDefaultWasteCenter(serviceProviderId, wasteCenterId);
      await refreshData();
      toast.success('Succès');
    } catch (error) {
      setIsSubmiting(false);
      toast.error('Erreur');
      console.log(error);
    }
  };

  const handleChangeWasteCenter = async (values: (number | string)[]) => {
    setWasteTypeIds(values);
    if (!isEditing) {
      if (!selectedWasteCenter?.id) return;
      try {
        const submitData = {
          ...selectedWasteCenter,
          ...addressDetail,
          serviceProviderId,
          wasteTypeIds: values,
          postalCode: addressDetail?.postalcode,
        };
        await wasteCenterService.updateWasteCenter(selectedWasteCenter?.id, submitData);
        await refreshData();
      } catch (error) {
        toast.error('Erreur');
        console.log(error);
      }
    }
  };

  const handleSubmitWasteCenter = async () => {
    setIsSubmited(true);
    try {
      if (
        !selectedWasteCenter?.name ||
        !selectedWasteCenter?.siren ||
        !addressDetail?.address ||
        !(wasteTypeIds.length > 0)
      )
        return;
      setIsSubmiting(true);
      const submitData = {
        ...selectedWasteCenter,
        ...addressDetail,
        serviceProviderId,
        wasteTypeIds,
        postalCode: addressDetail?.postalcode,
      };
      if (action === 'create') {
        await wasteCenterService.createWasteCenter(submitData);
      } else {
        if (!wasteCenter?.id) return;
        await wasteCenterService.updateWasteCenter(wasteCenter?.id, submitData);
      }
      await refreshData();
      setIsSubmiting(false);
      handleShowCreateForm(false);
      setIsEditing(false);
      toast.success('Succès');
      setIsSubmited(false);
    } catch (error) {
      setIsSubmiting(false);
      toast.error('Erreur');
      console.log(error);
    }
  };

  return (
    <div className='service-provider-waste-center__form' style={{ width: '100%' }}>
      <Row gutter={[12, 12]} style={{ width: '100%', justifyContent: 'space-between' }}>
        <Col lg={22} md={20} sm={20} xs={18}>
          <Row>
            <Col
              lg={4}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              {action === 'update' && (
                <Radio
                  checked={wasteCenter?.isPrimary}
                  onClick={() => handleSetDefaultWasteCenter(serviceProviderId, wasteCenter?.id)}
                  className='service-provider-waste-center__default-radio'
                ></Radio>
              )}
              <span className='red-star'>*</span>
              <Text className='service-provider-waste-center__label'>Nom:</Text>
              {action === 'update' && !isEditing ? (
                <EllipsisAndTooltip>{wasteCenter?.name}</EllipsisAndTooltip>
              ) : (
                <Input
                  placeholder='Input'
                  className='service-provider-waste-center__input'
                  value={selectedWasteCenter?.name}
                  style={{
                    // width: '140px',
                    borderColor: isSubmited && !selectedWasteCenter?.name ? 'red' : '',
                  }}
                  onChange={(e) => setSelectedWasteCenter({ name: e.target.value })}
                  onPressEnter={handleSubmitWasteCenter}
                />
              )}
            </Col>
            <Col
              offset={1}
              lg={4}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-waste-center__label'>SIREN:</Text>
              {action === 'update' && !isEditing ? (
                <EllipsisAndTooltip>{wasteCenter?.siren}</EllipsisAndTooltip>
              ) : (
                <Input
                  placeholder='Input'
                  className='service-provider-waste-center-siren__input'
                  value={selectedWasteCenter?.siren}
                  style={{
                    // width: '140px',
                    borderColor: isSubmited && !selectedWasteCenter?.siren ? 'red' : '',
                  }}
                  onChange={(e) => setSelectedWasteCenter({ siren: e.target.value })}
                  onPressEnter={handleSubmitWasteCenter}
                />
              )}
            </Col>
            <Col
              offset={1}
              lg={5}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-waste-center__label'>Adresse:</Text>
              {action === 'update' && !isEditing ? (
                <EllipsisAndTooltip>{wasteCenter?.formattedAddress}</EllipsisAndTooltip>
              ) : (
                <AddressInput
                  onSelect={handleSelectAddress}
                  onChange={handleChangeAddress}
                  defaultAddress={addressDetail?.formattedAddress}
                  otherStyles={{
                    // minWidth: '280px',
                    borderColor: isSubmited && !addressDetail?.formattedAddress ? 'red' : '',
                  }}
                />
              )}
            </Col>
            <Col
              offset={1}
              lg={7}
              md={24}
              sm={24}
              style={{ display: 'flex', flexDirection: 'row', alignItems: 'center', gap: '8px' }}
            >
              <span className='red-star'>*</span>
              <Text className='service-provider-waste-center__label'>Type de déchets:</Text>
              <Select
                placeholder='Sélectionner'
                className={`service-provider-waste-center__waste-type-selection ${
                  isSubmited && isEditing && !(wasteTypeIds?.length > 0)
                    ? 'service-provider-waste-center__waste-type-selection-error'
                    : ''
                }`}
                mode='multiple'
                showSearch={false}
                value={wasteTypeIds}
                onChange={(newValue: (number | string)[]) => {
                  if (newValue.includes('select-all')) {
                    toggleSelectAll();
                    return;
                  } else if (newValue.includes('clear-all')) {
                    if (action === 'create') {
                      setWasteTypeIds([]);
                    } else {
                      handleChangeWasteCenter([]);
                    }
                    setSelectedAll(false);
                    return;
                  }
                  if (action === 'create') {
                    setWasteTypeIds(newValue);
                  } else {
                    handleChangeWasteCenter(newValue);
                  }
                  setSelectedAll(newValue.length === wasteTypes.length);
                }}
                maxTagCount='responsive'
                filterOption={filterOption}
                tagRender={(props) => (
                  <Tag
                    style={{
                      background: 'rgba(0, 0, 0, 0.04)',
                      border: '1px solid rgba(0, 0, 0, 0.06)',
                      borderRadius: 4,
                      display: 'inline-block',
                      padding: '0px 8px',
                      marginTop: '2px',
                      marginBottom: '2px',
                      marginRight: '2px',
                      cursor: 'pointer',
                    }}
                    onClick={(e) => {
                      e.stopPropagation();
                    }}
                    onMouseDown={(event: React.MouseEvent<HTMLSpanElement>) => {
                      event.preventDefault();
                      event.stopPropagation();
                    }}
                  >
                    {props.label}
                  </Tag>
                )}
              >
                {[
                  {
                    name: 'Tout sélectionner',
                    id: 'select-all',
                    style: {
                      color: '#95C515',
                      fontWeight: '600px',
                    },
                  },
                  {
                    name: 'Tout désélectionner',
                    id: 'clear-all',
                    style: {
                      color: '#FF4D4F',
                      fontWeight: '600px',
                    },
                  },
                  ...wasteTypes,
                ].map((option) => (
                  <Option
                    key={option.id}
                    value={option.id}
                    id={option.id}
                    /* eslint-disable-next-line */
                    style={(option as any)?.style}
                  >
                    {option.name}
                  </Option>
                ))}
              </Select>
            </Col>
          </Row>
        </Col>
        <Col
          lg={2}
          md={4}
          sm={4}
          xs={6}
          style={{
            display: 'flex',
            flexDirection: 'row',
            alignItems: 'center',
            gap: '8px',
            justifyContent: 'flex-end',
          }}
        >
          {action === 'update' &&
            (isEditing ? (
              <>
                <CheckButton className='check-button' loading={isSubmiting} onClick={handleSubmitWasteCenter} />
                <CloseOutlined
                  className='service-provider-contact__update-cancel-icon cancel-button'
                  onClick={handleCancelEdit}
                />
              </>
            ) : (
              <>
                <EditOutlined
                  className='service-provider-contact__edit-icon edit-icon'
                  onClick={() => handleEditingMode(true)}
                />
                <TrashButton
                  className='service-provider-contact__trash-icon trash-button'
                  loading={deleteLoading}
                  onClick={handleDeactivateWasteCenter}
                />
              </>
            ))}
          {action === 'create' && (
            <>
              <Button
                type='text'
                size='small'
                className='service-provider-waste-center__ajouter-btn'
                loading={isSubmiting}
                onClick={handleSubmitWasteCenter}
              >
                <PlusCircleOutlined className='service-provider-waste-center__add-icon' />
                Ajouter
              </Button>
              <CloseOutlined className='cancel-button' onClick={() => handleShowCreateForm(false)} />
            </>
          )}
        </Col>
      </Row>
    </div>
  );
};
export default WasteCenterForm;
