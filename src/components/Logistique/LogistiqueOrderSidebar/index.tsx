import { Card, Tag, Divider, Spin } from 'antd';
import { DocumentStatus, Documents } from '../../../models';
import { DEFAULT_TAX, DOCUMENT_STATUSES } from '../../../utils/constant';
import dayjs from 'dayjs';
import { frenchCurrencyFormat } from '../../../utils';
import { useState } from 'react';
import InfiniteScroll from 'react-infinite-scroll-component';
import { LoadingOutlined } from '@ant-design/icons';

const LogistiqueOrderSidebar = ({
  orders,
  handleSelectOrder,
  selectedDocument,
  loadMoreData,
  hasMore,
}: {
  orders: Documents[];
  handleSelectOrder: (order: Documents) => void;
  selectedDocument: Documents | null;
  loadMoreData: () => void;
  hasMore: boolean;
}) => {
  const [taxPercentage] = useState(DEFAULT_TAX);
  const onSelectOrder = (order: Documents) => {
    handleSelectOrder(order);
  };

  const calculateTotalPrice = (order: Documents) => {
    const totalBeforeTax =
      order.DocumentProductLines?.reduce((total, line) => {
        return total + Number(line.total || 0);
      }, 0) || 0;

    const totalAfterTax = totalBeforeTax + totalBeforeTax * (taxPercentage / 100);

    return frenchCurrencyFormat(totalAfterTax.toFixed(2));
  };

  const getStatus = (documentStatus: DocumentStatus) => {
    let rs = <></>;
    if (documentStatus?.status === DOCUMENT_STATUSES.A_TRAITER.status) {
      rs = <Tag color={DOCUMENT_STATUSES.A_TRAITER.color}>{documentStatus?.name}</Tag>;
    } else if (documentStatus?.status === DOCUMENT_STATUSES.PRISE_EN_COMPTE.status) {
      rs = <Tag color={DOCUMENT_STATUSES.PRISE_EN_COMPTE.color}>{documentStatus?.name}</Tag>;
    } else if (documentStatus?.status === DOCUMENT_STATUSES.TRAITEE.status) {
      rs = <Tag color={DOCUMENT_STATUSES.TRAITEE.color}>{documentStatus?.name}</Tag>;
    }
    return rs;
  };

  const LoaderRow = () => (
    <div className='datatable-loader'>
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  );

  return (
    <div
      id='scrollableSidebar'
      style={{
        height: '100%',
        overflow: 'auto',
        padding: '0 16px',
      }}
    >
      <InfiniteScroll
        dataLength={orders.length}
        next={loadMoreData}
        hasMore={hasMore}
        loader={<LoaderRow />}
        endMessage={<Divider plain>Vous avez atteint la fin du flux</Divider>}
        scrollableTarget='scrollableSidebar'
        scrollThreshold='90%'
      >
        <div className='order-sidebar'>
          {orders.map((order) => (
            <Card
              key={order.id}
              className={`order-card ${selectedDocument?.id === order.id ? 'order-card--selected' : ''}`}
              onClick={() => onSelectOrder(order)}
            >
              <div className='order-card__header'>
                <h3>{order.Contact?.name}</h3>
                <strong>{calculateTotalPrice(order)}€</strong>
              </div>
              <p className='datatable__item'>{dayjs(order.createdAt).format('DD/MM/YYYY')}</p>
              <p>{order.cdeZoho}</p>
              <p>
                {order.siteAddressPostalCode} {order.siteAddressCity ? `- ${order.siteAddressCity}` : ''}
              </p>
              {order.DocumentStatus ? getStatus(order.DocumentStatus) : null}
            </Card>
          ))}
        </div>
      </InfiniteScroll>
    </div>
  );
};

export default LogistiqueOrderSidebar;
