import { Button, Form, DatePicker, Modal, Select, Space } from 'antd';
import dayjs from 'dayjs';
import {
  useCountryRegions,
  useCountryRegionsQuery,
  useDocumentStatuses,
  useDocumentStatusesQuery,
  useProductTypeInterventions,
  useProductTypeInterventionsQuery,
} from 'hooks';
import { useEffect, useState } from 'react';
import { QueryParams } from 'types';
import { DOCUMENT_STATUSES } from 'utils/constant';
import frFR from 'antd/es/date-picker/locale/fr_FR';
const { RangePicker } = DatePicker;

const FilterLogistiqueModal = ({
  visible,
  handleCancel,
  query,
  onQueryChange,
}: {
  visible: boolean;
  handleCancel: () => void;
  query: QueryParams;
  /* eslint-disable-next-line */
  onQueryChange: Function;
}) => {
  const [form] = Form.useForm();
  const [isSubmited] = useState(false);
  const [isSubmiting] = useState<boolean>(false);
  const [selectedProductTypeId] = useState<number | null>(null);
  const [productTypeInterventionsQuery] = useProductTypeInterventionsQuery({
    orderBy: 'name,asc',
  });
  const [productTypeInterventions, productTypeInterventionsLoading] =
    useProductTypeInterventions(productTypeInterventionsQuery);
  const [documentStatusesQuery] = useDocumentStatusesQuery({
    orderBy: 'name,asc',
    'status[]': `["${DOCUMENT_STATUSES.A_TRAITER.status}", "${DOCUMENT_STATUSES.PRISE_EN_COMPTE.status}", "${DOCUMENT_STATUSES.TRAITEE.status}"]`,
  });
  const [documentStatuses, documentStatusesLoading] = useDocumentStatuses(documentStatusesQuery);
  const [countryRegionsQuery] = useCountryRegionsQuery({
    orderBy: 'name,asc',
  });
  const [countryRegions, countryRegionsLoading] = useCountryRegions(countryRegionsQuery);

  useEffect(() => {
    const createdAtGte = query['createdAt[gte]'] ? dayjs(query['createdAt[gte]'].split(' ')[0], 'YYYY-MM-DD') : null;
    const createdAtLte = query['createdAt[lte]'] ? dayjs(query['createdAt[lte]'].split(' ')[0], 'YYYY-MM-DD') : null;
    form.setFieldsValue({
      countryRegionId: query.countryRegionId ? Number(query.countryRegionId) : null,
      documentTypeId: query.documentTypeId ? Number(query.documentTypeId) : null,
      documentStatusId: query.documentStatusId ? Number(query.documentStatusId) : null,
      createdAt: createdAtGte && createdAtLte ? [createdAtGte, createdAtLte] : undefined,
    });
  }, [visible]);

  const handleSubmit = async () => {
    try {
      const values = await form.validateFields();
      const dateRange = {
        'createdAt[gte]':
          values.createdAt && values.createdAt[0]
            ? dayjs(values.createdAt[0]).format('YYYY-MM-DD') + ' 00:00:00'
            : undefined,
        'createdAt[lte]':
          values.createdAt && values.createdAt[1]
            ? dayjs(values.createdAt[1]).format('YYYY-MM-DD') + ' 23:59:59'
            : undefined,
      };
      delete values.createdAt;
      onQueryChange({
        ...values,
        ...dateRange,
      });
      handleCancel();
    } catch (error) {
      console.error('Validation failed:', error);
    }
  };

  return (
    <Modal
      title='FILTRER LES COMMANDES'
      open={visible}
      maskClosable={false}
      onCancel={handleCancel}
      className='adding-produits-modal'
      width={520}
      centered
      footer={[
        <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
          Annuler
        </Button>,
        <Button
          key='ajouter'
          type='primary'
          className='ant-modal-content__add-btn'
          onClick={handleSubmit}
          loading={isSubmiting}
        >
          Valider
        </Button>,
      ]}
    >
      <Space direction='vertical' className='adding-produits-modal__body'>
        <Form
          form={form}
          validateTrigger={isSubmited ? 'onChange' : 'onSubmit'}
          autoComplete='off'
          validateMessages={{ required: '' }}
        >
          <Space direction='vertical' className='adding-produits-modal__body'>
            <Form.Item label='Date' name='createdAt' className='adding-produits-modal__form-input'>
              <RangePicker format={'DD/MM/YYYY'} locale={frFR} placeholder={['Date de début', 'Date de fin']} />
            </Form.Item>
            <Form.Item label='Région' name='countryRegionId' className='adding-produits-modal__form-input'>
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                allowClear
                value={selectedProductTypeId}
                loading={countryRegionsLoading === 'pending'}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={countryRegions?.map((countryRegion) => ({
                  value: countryRegion.id,
                  label: countryRegion.name,
                }))}
              ></Select>
            </Form.Item>
            <Form.Item label='Type de commande' name='interventionId' className='adding-produits-modal__form-input'>
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                allowClear
                value={selectedProductTypeId}
                loading={productTypeInterventionsLoading === 'pending'}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={productTypeInterventions?.map((productTypeIntervention) => ({
                  value: productTypeIntervention.id,
                  label: productTypeIntervention.name,
                }))}
              ></Select>
            </Form.Item>
            <Form.Item label='Statuts' name='documentStatusId' className='adding-produits-modal__form-input'>
              <Select
                placeholder='Sélectionner'
                className='adding-produits-modal__selection'
                allowClear
                value={selectedProductTypeId}
                loading={documentStatusesLoading === 'pending'}
                showSearch
                filterOption={(input, option) =>
                  ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                }
                options={documentStatuses?.map((documentStatus) => ({
                  value: documentStatus.id,
                  label: documentStatus.name,
                }))}
              ></Select>
            </Form.Item>
          </Space>
        </Form>
      </Space>
    </Modal>
  );
};

export default FilterLogistiqueModal;
