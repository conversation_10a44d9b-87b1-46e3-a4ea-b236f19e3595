import { Table, Tag, Layout, Row, Col, Divider, Spin } from 'antd';
import { Documents } from 'models';
import { useEffect, useState } from 'react';
import { ScaleLoader } from 'react-spinners';
import LogistiqueOrderSidebar from '../LogistiqueOrderSidebar';
import LogistiqueOrderDetail from '../LogistiqueOrderDetail';
import { Loading } from 'types';
import dayjs from 'dayjs';
import ProductTypeInterventions from 'models/product-type-interventions';
import { DOCUMENT_STATUSES } from 'utils/constant';
import InfiniteScroll from 'react-infinite-scroll-component';
import { LoadingOutlined } from '@ant-design/icons';
const { Content } = Layout;

const LogistiqueList = ({
  type,
  dataSource,
  dataSourceLoading,
  query,
  productTypeInterventions,
  handleReloadDocumentSelected,
  loadMoreData, // Add this prop to the type definition
  hasMore,
  selectedDocument,
  setSelectedDocument,
  handleReloadDocuments,
}: {
  type: string | 'nouvelles-commandes';
  dataSource: Documents[];
  dataSourceLoading: Loading;
  /* eslint-disable-next-line */
  query: any;
  /* eslint-disable-next-line */
  total: number;
  productTypeInterventions: ProductTypeInterventions[];
  handleReloadDocumentSelected: () => void;
  loadMoreData: () => void; // Add this prop to the type definition
  hasMore: boolean;
  selectedDocument: Documents | null;
  setSelectedDocument: (document: Documents) => void;
  handleReloadDocuments: () => void;
}) => {
  /* eslint-disable-next-line */
  const [defaultColumns, setDefaultColumns] = useState<any[]>([]);
  const [isShowDetail, setIsShowDetail] = useState(false);
  const [isEditingComment, setIsEditingComment] = useState(false);

  const handleSelectOrder = (document: Documents) => {
    if (document.id !== selectedDocument?.id) {
      setIsEditingComment(false);
    }
    setSelectedDocument(document);
  };

  useEffect(() => {
    /* eslint-disable-next-line */
    let columns: any[];
    switch (type) {
      case 'nouvelles-commandes':
        columns = [
          {
            title: 'Date',
            dataIndex: 'createdAt',
            key: 'createdAt',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{dayjs(text).format('DD/MM/YYYY')}</p>,
          },
          {
            title: 'N° de commande',
            dataIndex: 'cdeZoho',
            key: 'cdeZoho',
            width: 220,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Code Postal - Ville',
            dataIndex: 'siteAddressPostalCodeAndCity',
            key: 'siteAddressPostalCodeAndCity',
            width: 200,
            render: (text: string) => <p className='datatable__item'>{text}</p>,
          },
          {
            title: 'Nom du client',
            key: 'contactName',
            width: 200,
            render: (record: Documents) => <p className='datatable__item'>{record.Contact?.name}</p>,
          },
          {
            title: 'Vendeur',
            key: 'receiptNumber',
            width: 200,
            render: (record: Documents) => <p className='datatable__item'>{record.Vendeur?.name}</p>,
          },
          {
            title: 'Produit',
            key: 'product',
            width: 250,
            render: (record: Documents) => {
              let rs = '';
              if (record.DocumentProductLines) {
                const listIdIntervention = record.DocumentProductLines.map((obj) => obj.interventionId).filter(
                  (id) => id !== null && id !== undefined,
                );
                listIdIntervention.forEach((id) => {
                  const productTypeIntervention = productTypeInterventions.find((i) => i.id === id);
                  if (productTypeIntervention) {
                    rs += productTypeIntervention.name + '<br>';
                  }
                });
              }
              return <div dangerouslySetInnerHTML={{ __html: rs }} />;
            },
          },
          {
            title: 'Date de prestation',
            key: 'date_prestation',
            width: 200,
            render: (record: Documents) => {
              let rs = '';
              if (record.DocumentProductLines) {
                const prestationDates = record.DocumentProductLines.flatMap((item) =>
                  item.DocumentProductLinePrestations?.map((prestation) => prestation.prestationDate),
                );
                const minPrestationDate = Math.min(
                  ...prestationDates
                    .filter((date) => date !== null && date !== undefined)
                    .map((date) => dayjs(date).unix()),
                );
                if (dayjs(minPrestationDate * 1000).isValid()) {
                  rs = dayjs(minPrestationDate * 1000).format('DD/MM/YYYY');
                }
              }
              return <div dangerouslySetInnerHTML={{ __html: rs }} />;
            },
          },
          {
            title: 'Status',
            key: 'DocumentStatus',
            width: 150,
            render: (record: Documents) => {
              let rs = <></>;
              if (record.DocumentStatus?.status === DOCUMENT_STATUSES.A_TRAITER.status) {
                rs = <Tag color={DOCUMENT_STATUSES.A_TRAITER.color}>{record.DocumentStatus?.name}</Tag>;
              } else if (record.DocumentStatus?.status === DOCUMENT_STATUSES.PRISE_EN_COMPTE.status) {
                rs = <Tag color={DOCUMENT_STATUSES.PRISE_EN_COMPTE.color}>{record.DocumentStatus?.name}</Tag>;
              } else if (record.DocumentStatus?.status === DOCUMENT_STATUSES.TRAITEE.status) {
                rs = <Tag color={DOCUMENT_STATUSES.TRAITEE.color}>{record.DocumentStatus?.name}</Tag>;
              }
              return rs;
            },
          },
        ];
        break;
      default:
        columns = [];
        break;
    }
    setDefaultColumns(columns);
  }, [query]);
  const LoaderRow = () => (
    <div className='datatable-loader'>
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  );
  return (
    <>
      {!isShowDetail ? (
        <div>
          <InfiniteScroll
            dataLength={dataSource.length}
            next={loadMoreData}
            hasMore={hasMore}
            loader={<LoaderRow />}
            endMessage={<Divider plain>Vous avez atteint la fin du flux</Divider>}
          >
            <Table
              columns={defaultColumns}
              dataSource={dataSource}
              pagination={false}
              className='service-provider-list__datatable'
              onRow={(record) => ({
                onClick: () => {
                  setIsShowDetail(true);
                  setSelectedDocument(record);
                },
              })}
              loading={{
                indicator: (
                  <ScaleLoader
                    color='#A6C84D'
                    cssOverride={{
                      display: 'inline-block !important',
                      margin: '0 auto',
                      left: 0,
                      height: '100%',
                      width: '100%',
                    }}
                    aria-label='Loading Spinner'
                    data-testid='loader'
                  />
                ),
                spinning: dataSourceLoading === 'pending',
              }}
            />
          </InfiniteScroll>
        </div>
      ) : (
        <Layout>
          <Row gutter={16}>
            <Col
              span={6}
              style={{
                maxHeight: '100vh',
                overflowY: 'auto',
                paddingRight: '10px',
              }}
            >
              <LogistiqueOrderSidebar
                orders={dataSource}
                handleSelectOrder={handleSelectOrder}
                selectedDocument={selectedDocument}
                loadMoreData={loadMoreData}
                hasMore={hasMore}
              />
            </Col>

            <Col span={18}>
              <Content className='content'>
                <LogistiqueOrderDetail
                  setIsShowDetail={setIsShowDetail}
                  orderDetail={selectedDocument}
                  setIsEditingComment={setIsEditingComment}
                  isEditingComment={isEditingComment}
                  handleReloadDocumentSelected={handleReloadDocumentSelected}
                  handleReloadDocuments={handleReloadDocuments}
                />
              </Content>
            </Col>
          </Row>
        </Layout>
      )}
    </>
  );
};

export default LogistiqueList;
