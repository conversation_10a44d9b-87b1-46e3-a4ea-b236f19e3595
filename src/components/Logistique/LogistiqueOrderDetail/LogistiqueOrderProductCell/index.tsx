import { ProductLineDevis, DocumentProductLine } from 'models';
import { FC, useMemo, forwardRef } from 'react';
import PrestationCell from './PrestationCell';
import ProductNameCell from './ProductNameCell';
import QuantityCell from './QuantityCell';
import DiscountCell from './DiscountCell';
import { UrbanCenterZoneIds } from 'types';
import TotalCell from './TotalCell';
import SubTotalCell from './SubTotalCell';
import UnitPriceCell from './UnitPriceCell';
interface EditableCellProps {
  editable: boolean;
  children: React.ReactNode;
  dataIndex: keyof object;
  record: ProductLineDevis & DocumentProductLine;
}

export interface ProductCellRef {
  refreshCatalogPrice: (args: {
    zoneIds: UrbanCenterZoneIds;
    isChangePrice?: boolean;
    isDocumentCatalog: boolean | null;
  }) => Promise<void>;
}

export interface LogistiqueOrderCellProps {
  record: DocumentProductLine;
}

const ProductCell: FC<EditableCellProps> = forwardRef<ProductCellRef, EditableCellProps>(
  ({ editable, children, dataIndex, record, ...restProps }) => {
    const cellDataSource = useMemo(() => {
      return record;
    }, [record]);

    let childrenNode = <></>;
    switch (dataIndex) {
      case 'name':
        childrenNode = <ProductNameCell record={cellDataSource} />;
        break;
      case 'prestation':
        childrenNode = <PrestationCell record={cellDataSource} />;
        break;
      case 'unit_price':
        childrenNode = <UnitPriceCell record={cellDataSource} />;
        break;
      case 'quantity':
        childrenNode = <QuantityCell record={cellDataSource} />;
        break;
      case 'sub-total':
        childrenNode = <SubTotalCell record={cellDataSource} />;
        break;
      case 'discount':
        childrenNode = <DiscountCell record={cellDataSource} />;
        break;
      case 'total':
        childrenNode = <TotalCell record={cellDataSource} />;
        break;
      default:
        childrenNode = <></>;
    }
    let childNode = children;
    if (editable) {
      childNode = childrenNode;
    }
    return <td {...restProps}>{childNode}</td>;
  },
);

ProductCell.displayName = 'ProductCell';
export default ProductCell;
