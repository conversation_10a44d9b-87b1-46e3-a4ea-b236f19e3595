import { CopyOutlined } from '@ant-design/icons';
import { Col, Form, Input, Row, Typography } from 'antd';
import { LogistiqueOrderCellProps } from './index';
import { toast } from 'react-toastify';

const { Text } = Typography;
const { TextArea } = Input;

const ProductNameCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;

  const handleCopyProductName = () => {
    const productName = record?.productNameForClient?.toString();
    if (productName !== undefined && productName.length > 0) {
      navigator.clipboard.writeText(productName ? productName : '');
      toast.success(<div>Copié dans le presse-papier</div>);
    }
  };

  const content = (
    <>
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 22 }}>
          <Form.Item
            label='Référence interne'
            className='mb-2'
            name={['lineItems', `${record?.uuid}`, 'name']}
            initialValue={record?.productNameForClient}
          >
            <Text>
              <Text underline strong>
                {record?.productNameForClient}
              </Text>
            </Text>
          </Form.Item>
        </Col>
        <Col className='prodct-cell__display-right' xs={{ span: 2 }}>
          <CopyOutlined style={{ fontSize: 24, color: '#95C515' }} onClick={handleCopyProductName} />
        </Col>
      </Row>
      <Row gutter={[16, 0]}>
        <Text>Nom produit client</Text>
        <Input
          placeholder='Référence interne'
          value={record.productNameForClient}
          className='mb-4'
          style={{ width: '100%' }}
          readOnly
        />
        <Text>Description</Text>
        <TextArea
          autoSize={{ minRows: 3, maxRows: 22 }}
          className='mb-4'
          value={record.descriptionWithParameter}
          readOnly
        />
      </Row>
    </>
  );

  return <div style={{ minWidth: '350px', maxWidth: '95%' }}>{content}</div>;
};

export default ProductNameCell;
