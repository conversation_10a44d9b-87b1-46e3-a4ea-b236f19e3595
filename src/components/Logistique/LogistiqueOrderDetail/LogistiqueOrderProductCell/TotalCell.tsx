import { Space, Typography } from 'antd';
import { frenchCurrencyFormat } from 'utils';
import { LogistiqueOrderCellProps } from './index';
const { Text } = Typography;

const TotalCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;
  if (record.creationType !== 'header') {
    return (
      <div style={{ minWidth: '50px' }} className='text-center'>
        <Space direction='vertical' align='baseline'>
          <Text style={{ lineHeight: '32px' }} strong>
            {`${frenchCurrencyFormat(parseFloat(String(record.total || 0)).toFixed(2))} €`}
          </Text>
        </Space>
      </div>
    );
  }
  return <></>;
};

export default TotalCell;
