import { Input, Select, Space } from 'antd';
import { CopyOutlined } from '@ant-design/icons';
import { LogistiqueOrderCellProps } from './index';

const UnitPriceCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;

  console.log(record);

  return (
    <div className='text-center'>
      <Space direction='vertical' align='baseline' size='small' style={{ width: '100%' }}>
        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Input
              className='text-right'
              // disabled
              value={`${record.unitPrice || 0} €`}
              style={{
                width: '100%',
                minWidth: '80px',
                borderRadius: '6px',
              }}
              readOnly
            />
            <CopyOutlined style={{ fontSize: 24, color: '#95C515', cursor: 'pointer' }} />
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: '4px' }}>
            <Select
              // disabled
              value={record.productTypeUnitLabel}
              style={{
                width: '100%',
                minWidth: '80px',
                borderRadius: '6px',
                pointerEvents: 'none',
              }}
              options={[{ value: record.productTypeUnitLabel, label: record.productTypeUnitLabel }]}
            />
            <CopyOutlined style={{ fontSize: 24, color: '#95C515', cursor: 'pointer' }} />
          </div>
        </div>
      </Space>
    </div>
  );
};

export default UnitPriceCell;
