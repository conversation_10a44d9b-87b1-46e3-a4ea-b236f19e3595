import { Space, Tag, Typography } from 'antd';
import dayjs from 'dayjs';
import 'dayjs/locale/fr';
import { BO_LOGISTIC_ACTION } from '../../../../utils/constant';
import { LogistiqueOrderCellProps } from './index';

dayjs.locale('fr');
const { Text } = Typography;

const PrestationCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;
  return (
    <div>
      <Space align='baseline' style={{ gap: '16px' }}>
        <Text
          style={{
            lineHeight: '32px',
            marginBottom: '12px',
          }}
        >
          {typeof record.serviceProviderOrderQuantity === 'number'
            ? Math.floor(record.serviceProviderOrderQuantity)
            : parseInt(String(record.serviceProviderOrderQuantity || '0'), 10)}
        </Text>
        {(() => {
          switch (record.clientOrderProductStatus) {
            case BO_LOGISTIC_ACTION.A_TRAITER.key:
              return <Tag color='yellow'>{BO_LOGISTIC_ACTION.A_TRAITER.name}</Tag>;
            case BO_LOGISTIC_ACTION.PLANIFIEE.key:
              return <Tag color='blue'>{BO_LOGISTIC_ACTION.PLANIFIEE.name}</Tag>;
            case BO_LOGISTIC_ACTION.ANNULEE.key:
              return <Tag color='red'>{BO_LOGISTIC_ACTION.ANNULEE.name}</Tag>;
            case BO_LOGISTIC_ACTION.EN_DECHETERIE.key:
              return <Tag color='green'>{BO_LOGISTIC_ACTION.EN_DECHETERIE.name}</Tag>;
            case BO_LOGISTIC_ACTION.ATTENTE_RETOUR_PRESTA.key:
              return <Tag color='yellow-inverse'>{BO_LOGISTIC_ACTION.ATTENTE_RETOUR_PRESTA.name}</Tag>;
            case BO_LOGISTIC_ACTION.ATTENTE_RETOUR_COMMERCIAL.key:
              return <Tag color='blue-inverse'>{BO_LOGISTIC_ACTION.ATTENTE_RETOUR_COMMERCIAL.name}</Tag>;
            case BO_LOGISTIC_ACTION.SUR_CHANTIER.key:
              return <Tag color='red-inverse'>{BO_LOGISTIC_ACTION.SUR_CHANTIER.name}</Tag>;
            case BO_LOGISTIC_ACTION.KO.key:
              return <Tag color='green-inverse'>{BO_LOGISTIC_ACTION.KO.name}</Tag>;
            default:
              return null;
          }
        })()}
      </Space>
    </div>
  );
};

export default PrestationCell;
