import { Input, Select, Space } from 'antd';
import { CURRENCIES } from 'utils/constant';
import { LogistiqueOrderCellProps } from './index';

const DiscountCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;

  return (
    <div className='text-center'>
      <Space direction='horizontal' align='baseline' className='space-remise' size='small'>
        <div className='remise-hover-input-and-select' style={{ display: 'flex' }}>
          <Input className='text-right' readOnly value={record.discount || 0} style={{ width: '45px' }} />
          <Select
            className='unit-remise text-right'
            // disabled
            value={record.discountUnit || CURRENCIES[0].value}
            options={CURRENCIES}
            style={{
              minWidth: '50px',
              pointerEvents: 'none',
            }}
          />
        </div>
      </Space>
    </div>
  );
};

export default DiscountCell;
