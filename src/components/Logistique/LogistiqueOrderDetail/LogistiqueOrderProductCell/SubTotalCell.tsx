import { Space, Typography } from 'antd';
import { frenchCurrencyFormat } from 'utils';
import { LogistiqueOrderCellProps } from './index';
const { Text } = Typography;

const SubTotalCell = (props: LogistiqueOrderCellProps) => {
  const { record } = props;
  if (record.creationType !== 'header') {
    return (
      <div style={{ minWidth: '100px' }} className='text-right'>
        {/*<Form.Item name={['lineItems', `${record?.uuid}`, 'totalBeforeDiscount']}>*/}
        <Space direction='horizontal' align='baseline'>
          <Text
            style={{ lineHeight: '32px' }}
          >{`${frenchCurrencyFormat(parseFloat(String(record.totalBeforeDiscount || 0)).toFixed(2))} €`}</Text>
        </Space>
        {/*</Form.Item>*/}
      </div>
    );
  }
  return <></>;
};

export default SubTotalCell;
