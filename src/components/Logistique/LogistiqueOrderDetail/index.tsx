import { Button, Layout, Row, Col, Card, Typography, Space, Input, Divider, Table, Tag, Form, message } from 'antd';
import { CheckOutlined, CloseOutlined, EditOutlined, LoadingOutlined } from '@ant-design/icons';
import { ColumnTypes } from '../../Estimate/DevisProducts';
import { DocumentProductLine, DocumentStatus, ProductLineDevis } from '../../../models';
import { Dispatch, SetStateAction, useEffect, useMemo, useState } from 'react';
import { useAppSelector } from '../../../store';
import { selectProducts } from '../../../store/slices/product.slices';
import { frenchCurrencyFormat } from '../../../utils';
import { Documents } from 'models';
import dayjs from 'dayjs';
import OrderPrestataireModal from './OrderPrestataireModal';
import LogistiqueOrderProductRow from './LogistiqueOrderProductRow';
import LogistiqueOrderProductCell from './LogistiqueOrderProductCell';
import {
  BO_ORDER_TYPES,
  BO_TIME_TYPES,
  DEFAULT_TAX,
  LIST_FLOORS,
  LIST_TIME_FROM_00_TO_23_30,
  DOCUMENT_STATUSES,
} from '../../../utils/constant';
import { documentService, urbanCenterService, boService } from 'services';
import { OrderPrestataireLine } from './OrderPrestataireCellTable';
import { toast } from 'react-toastify';
import BigbagNouvelleCommamdeModal from '../../Common/NouvelleCommamdeModal/Bigbag';
import CamionNouvelleCommamdeModal from '../../Common/NouvelleCommamdeModal/Camion';
import BenneNouvelleCommamdeModal from '../../Common/NouvelleCommamdeModal/Benne';
import { TimeHour } from '../../../models/bo';

const { Content } = Layout;
const { Title } = Typography;
const { TextArea } = Input;

interface BoOrderFormValues {
  truckDriverName: string;
  pupId: string;
  siteAddress: string;
  pupFullAddress: string;
  pupAddress: string;
  pupZipCode: string;
  pupCity: string;
  collectDate: string;
  timeType: string;
  timePeriod?: string;
  timeHourKey?: string;
  timeHour?: TimeHour;
  depositTimeType?: string;
  depositTimeHourKey?: string;
  depositTimeHour?: TimeHour;
  depositTimePeriod?: string;
  pickupTimeType?: string;
  pickupTimePeriod?: string;
  pickupTimeHourKey?: string;
  pickupTimeHour?: TimeHour;
  pickupAction?: string;
  action: string;
  clientName: string;
  phone: string;
  cdeZoho: string;
  productTypeId: string;
  priseEnCharge: string;
  globalGarbagePricingId: string;
  quantity: number;
  garbageUnit: string;
  comment: string;
  lift?: boolean;
  floorNumber?: number;
  floorValue?: string;
  isChargementExpress?: boolean;
}

const LogistiqueOrderDetail = ({
  setIsShowDetail,
  orderDetail,
  setIsEditingComment,
  isEditingComment,
  handleReloadDocumentSelected,
  handleReloadDocuments,
}: {
  setIsEditingComment: Dispatch<SetStateAction<boolean>>;
  isEditingComment: boolean;
  setIsShowDetail: Dispatch<SetStateAction<boolean>>;
  orderDetail: Documents | null;
  handleReloadDocumentSelected: () => void;
  handleReloadDocuments: () => void;
}) => {
  const [boOrderType, setBoOrderType] = useState<string | undefined>();
  const [isShowPrestatairePopup, setIsShowPrestatairePopup] = useState<boolean>(false);
  const products = useAppSelector(selectProducts);
  const [dataSource, setDataSource] = useState<DocumentProductLine[]>(orderDetail?.DocumentProductLines || []);
  const [selectedRows, setSelectedRows] = useState<DocumentProductLine[]>([]);
  const [loading, setLoading] = useState(false);
  const [taxPercentage] = useState(DEFAULT_TAX);
  const [comment, setComment] = useState(orderDetail?.comment || '');
  const [isDisableAction, setIsDisableAction] = useState<boolean>(false);
  const [loadingSubmitComment, setLoadingSubmitComment] = useState<boolean>(false);
  const [selectedCatalogPriceLine, setSelectCatalogPriceLine] = useState<OrderPrestataireLine>();
  const [serviceProviderZoneIds, setServiceProviderZoneIds] = useState<number[]>([]);
  const subTotal = useMemo(() => {
    return dataSource.reduce((total, secondItem) => {
      return total + Number(secondItem?.total || 0);
    }, 0);
  }, [dataSource]);
  console.log(selectedRows);
  const [form] = Form.useForm();

  const selectedProductLines = useMemo(() => {
    return selectedRows.map((row) => ({
      productId: row.productId,
      ...(row?.priceFamilyId ? { priceFamilyId: row.priceFamilyId } : {}),
      ...(row?.quantity ? { quantity: row.quantity } : {}),
    }));
  }, [selectedRows]);

  useEffect(() => {
    setComment(orderDetail?.comment || '');
    setDataSource(orderDetail?.DocumentProductLines || []);
    handleCheckDisableAction();
  }, [orderDetail]);

  useEffect(() => {
    if (selectedProductLines?.length > 0) {
      getCatalogPricesZoneIds();
    }
  }, [selectedProductLines]);

  const getCatalogPricesZoneIds = async () => {
    try {
      const serviceProviderZoneIds = await getServiceProviderZoneIds(
        orderDetail?.siteAddressLatitude,
        orderDetail?.siteAddressLongitude,
      );
      console.log('serviceProviderZoneIds: ', serviceProviderZoneIds);
      setServiceProviderZoneIds(serviceProviderZoneIds);
    } catch (error) {
      console.error('An error occurred during initialization:', error);
    }
  };

  const getServiceProviderZoneIds = async (siteAddressLatitude?: string, siteAddressLongitude?: string) => {
    if (siteAddressLatitude && siteAddressLongitude) {
      const zones = await urbanCenterService.getZonesByChantierAddress(siteAddressLatitude, siteAddressLongitude);
      const serviceProviderZoneIds = zones?.serviceProviderZoneIds?.map((zone) => zone.zoneId);
      return serviceProviderZoneIds;
    }
    return [];
  };

  const handleCheckDisableAction = () => {
    const filteredDocumentProductLine = orderDetail?.DocumentProductLines?.find((item) => {
      return Number(item.serviceProviderOrderQuantity) != Number(item.quantity);
    });
    if (filteredDocumentProductLine) {
      setIsDisableAction(false);
    } else {
      setIsDisableAction(true);
    }
  };

  const handlePrestataireSubmit = async (selectedRow: OrderPrestataireLine | null) => {
    if (selectedRow) {
      setSelectCatalogPriceLine(selectedRow);
      console.log('Selected prestataire:', selectedRow);

      // if (selectedRow?.CatalogPriceLineProducts && selectedRow?.CatalogPriceLineProducts?.length > 0) {
      //   const products = await productService?.getProducts({
      //     id: selectedRow?.CatalogPriceLineProducts[0].productId,
      //     include: 'ProductType.ProductTypeUnit|ProductType.ProductTypeIntervention',
      //   });
      //   const product = products?.rows?.[0];

      //   console.log(product);

      //   setBoOrderType(product?.ProductType?.ProductTypeIntervention?.key);
      // }
      setBoOrderType('BENNE');
    }
  };

  const handleClose = () => {
    handleReloadDocuments();
    setIsShowDetail(false);
  };
  const handleCloseChangeComment = () => {
    setComment(orderDetail?.comment ?? '');
    setIsEditingComment(false);
  };
  const handleSubmitChangeComment = async () => {
    setLoadingSubmitComment(true);
    const res = await documentService.updateDocumentComment(Number(orderDetail?.id), {
      comment: comment,
    });
    console.log('res: ', res);
    if (res?.id) {
      setIsEditingComment(false);
      setLoadingSubmitComment(false);
      handleReloadDocumentSelected();
    }
  };
  /**
   * handleClickActionUpdateStatus
   */
  const handleClickActionUpdateStatus = async (status_change: string) => {
    setLoading(true);
    try {
      await documentService.updateStatusDocument(Number(orderDetail?.id), {
        document_status: status_change,
      });
      toast.success('Statut mis à jour du document dans QBO');
      handleReloadDocumentSelected();
    } catch (error) {
      console.log((error as { response: { data: { message: string } } })?.response?.data?.message);
    }
    setLoading(false);
  };
  const defaultColumns: (ColumnTypes[number] & {
    editable?: boolean;
    dataIndex: string;
  })[] = [
    {
      width: 375,
      title: 'Produit/ Description',
      dataIndex: 'name',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
    {
      width: 250,
      title: 'Prestation',
      dataIndex: 'prestation',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 0,
            },
          };
        }
      },
    },
    {
      width: 250,
      title: 'Prix unitaire',
      dataIndex: 'unit_price',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 0,
            },
          };
        }
      },
    },
    {
      width: 100,
      title: 'Quantité',
      dataIndex: 'quantity',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 0,
            },
          };
        }
      },
    },
    {
      width: 140,
      title: 'Sous-total',
      dataIndex: 'sub-total',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: { colSpan: 0 },
          };
        }
      },
    },
    {
      width: 150,
      title: 'Remise',
      dataIndex: 'discount',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 0,
            },
          };
        }
      },
    },
    {
      width: 120,
      title: 'Total',
      dataIndex: 'total',
      fixed: 'right',
      editable: true,
      render: (_, record: ProductLineDevis) => {
        if (record.creationType === 'header') {
          return {
            props: { colSpan: 0 },
          };
        }
      },
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: ProductLineDevis) => ({
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        title: col.title,
        dataSource,
        formtype: 'estimate',
        dataList: {
          products: products,
        },
      }),
    };
  });
  const rowClassName = (record: ProductLineDevis) => {
    if (record.creationType === 'header') {
      return 'quotation-title-line';
    } else {
      return '';
    }
  };
  const getStatus = (documentStatus: DocumentStatus) => {
    let rs = <></>;
    if (documentStatus?.status === DOCUMENT_STATUSES.A_TRAITER.status) {
      rs = <Tag color={DOCUMENT_STATUSES.A_TRAITER.color}>{documentStatus?.name}</Tag>;
    } else if (documentStatus?.status === DOCUMENT_STATUSES.PRISE_EN_COMPTE.status) {
      rs = <Tag color={DOCUMENT_STATUSES.PRISE_EN_COMPTE.color}>{documentStatus?.name}</Tag>;
    } else if (documentStatus?.status === DOCUMENT_STATUSES.TRAITEE.status) {
      rs = <Tag color={DOCUMENT_STATUSES.TRAITEE.color}>{documentStatus?.name}</Tag>;
    }
    return rs;
  };

  const handleShowPrestatairePopup = (isOpen: boolean) => {
    setIsShowPrestatairePopup(isOpen);
  };

  const handleCancelOrderPopup = () => {
    // setIsShowOrderPopup(false);
    setBoOrderType(undefined);
    form.resetFields();
  };

  const handleSubmitOrder = async (values: BoOrderFormValues) => {
    try {
      const valid = await form.validateFields();
      if (!valid) return;
      let response;
      let orderData;
      switch (boOrderType) {
        case BO_ORDER_TYPES.CAMION: {
          const { timeType, timePeriod, timeHourKey, ...rest } = values;

          orderData = {
            ...rest,
            ...(timeType === BO_TIME_TYPES.HOUR ? { timeHourKey } : {}),
            ...(timeType === BO_TIME_TYPES.PERIOD ? { timePeriod } : {}),
            catalogPriceLineId: selectedCatalogPriceLine?.serviceProviderId,
            floorValue: LIST_FLOORS.find((e) => e.value === values.floorNumber)?.key_equal,
            timeHour: LIST_TIME_FROM_00_TO_23_30.find((e) => e.key === values.timeHourKey),
          };

          // Call API to create
          response = await boService.submitCamionOrder(orderData);
          break;
        }
        case BO_ORDER_TYPES.BENNE: {
          const {
            pickupTimeType,
            pickupTimePeriod,
            pickupTimeHourKey,
            depositTimeType,
            depositTimePeriod,
            depositTimeHourKey,
            ...rest
          } = values;

          // Adding fields into submit data
          orderData = {
            ...rest,
            ...(pickupTimeType === BO_TIME_TYPES.HOUR ? { pickupTimeHourKey } : {}),
            ...(pickupTimeType === BO_TIME_TYPES.PERIOD ? { pickupTimePeriod } : {}),
            ...(depositTimeType === BO_TIME_TYPES.HOUR ? { depositTimeHourKey } : {}),
            ...(depositTimeType === BO_TIME_TYPES.PERIOD ? { depositTimePeriod } : {}),
            catalogPriceLineId: selectedCatalogPriceLine?.serviceProviderId,
            depositTimeHour: LIST_TIME_FROM_00_TO_23_30.find((e) => e.key === values.depositTimeHourKey),
            pickupTimeHour: LIST_TIME_FROM_00_TO_23_30.find((e) => e.key === values.pickupTimeHourKey),
            productType: boOrderType,
          };

          // Call API to create
          response = await boService.submitBenneBigbagOrder(orderData);
          break;
        }
        case BO_ORDER_TYPES.BIG_BAG: {
          // Adding fields into submit data
          const { pickupTimeType, pickupTimePeriod, pickupTimeHourKey, ...rest } = values;

          orderData = {
            ...rest,
            ...(pickupTimeType === BO_TIME_TYPES.HOUR ? { pickupTimeHourKey } : {}),
            ...(pickupTimeType === BO_TIME_TYPES.PERIOD ? { pickupTimePeriod } : {}),
            catalogPriceLineId: selectedCatalogPriceLine?.serviceProviderId,
            pickupTimeHour: LIST_TIME_FROM_00_TO_23_30.find((e) => e.key === values.pickupTimeHourKey),
            productType: boOrderType,
          };

          // Call API to create
          response = await boService.submitBenneBigbagOrder(orderData);
          break;
        }
        default:
          break;
      }

      // Handle response
      if (response) {
        message.success('Commande créée avec succès');
        handleCancelOrderPopup();
      }
    } catch (error) {
      console.error('Error creating order:', error);
      message.error('Erreur lors de la création de la commande');
    }
  };

  return (
    <div style={{ position: 'relative' }}>
      {/* Close Button */}
      <Button
        type='text'
        icon={<CloseOutlined />}
        onClick={handleClose}
        style={{
          position: 'absolute',
          top: 10,
          right: 10,
          fontSize: '16px',
          zIndex: 10,
        }}
      />
      <Layout style={{ padding: '20px', background: '#ffffff' }}>
        <Content>
          {/* Header Section */}
          <Space direction='horizontal' size={16} align='start' className=''>
            <div className='btn-add__creation'>
              <Button
                disabled={loading || orderDetail?.DocumentStatus?.status === DOCUMENT_STATUSES.PRISE_EN_COMPTE.status}
                size='large'
                type='primary'
                className={`btn-add__creation-button`}
                onClick={() => handleClickActionUpdateStatus(DOCUMENT_STATUSES.PRISE_EN_COMPTE.status)}
                style={{
                  width: 'auto',
                  height: '32px',
                  borderRadius: '6px',
                }}
              >
                <span className='btn-add__creation-text'>{DOCUMENT_STATUSES.PRISE_EN_COMPTE.name}</span>
              </Button>
            </div>
            {(orderDetail?.DocumentStatus?.status === DOCUMENT_STATUSES.PRISE_EN_COMPTE.status ||
              orderDetail?.DocumentStatus?.status === DOCUMENT_STATUSES.A_TRAITER.status) && (
              <div className='btn-add__creation'>
                <Button
                  disabled={loading}
                  size='large'
                  type='primary'
                  className={`btn-add__creation-button`}
                  onClick={() => handleClickActionUpdateStatus(DOCUMENT_STATUSES.TRAITEE.status)}
                  style={{
                    width: 'auto',
                    height: '32px',
                    borderRadius: '6px',
                  }}
                >
                  <span className='btn-add__creation-text'>{DOCUMENT_STATUSES.TRAITEE.name}</span>
                </Button>
              </div>
            )}
            {orderDetail?.DocumentStatus?.status === DOCUMENT_STATUSES.TRAITEE.status && (
              <div className='btn-add__creation'>
                <Button
                  disabled={loading}
                  size='large'
                  type='primary'
                  className={`btn-add__creation-button`}
                  onClick={() => handleClickActionUpdateStatus(DOCUMENT_STATUSES.A_TRAITER.status)}
                  style={{
                    width: 'auto',
                    height: '32px',
                    borderRadius: '6px',
                  }}
                >
                  <span className='btn-add__creation-text'>{DOCUMENT_STATUSES.A_TRAITER.name}</span>
                </Button>
              </div>
            )}
          </Space>

          <Divider />

          {/* Order Title */}
          <Title level={4} style={{ fontWeight: 'bold', marginBottom: 20 }}>
            Commande client : {orderDetail?.cdeZoho}{' '}
            {orderDetail?.Contact?.name ? `- ${orderDetail?.Contact?.name}` : ''}
            <p className='float-right'>{orderDetail?.DocumentStatus ? getStatus(orderDetail?.DocumentStatus) : null}</p>
          </Title>

          {/* Order Details Section */}
          <Card
            bordered={true}
            style={{
              marginBottom: 20,
              borderColor: '#99C24D',
              borderWidth: 1,
              borderRadius: 5,
            }}
          >
            <Row gutter={16}>
              <Col span={12}>
                <p>
                  <strong>N° de référence :</strong>{' '}
                  {orderDetail?.EstimateDocument ? orderDetail?.EstimateDocument.cdeZoho : ''}
                </p>
                <p>
                  <strong>Date de la commande :</strong>{' '}
                  {orderDetail?.createdAt ? dayjs(orderDetail?.createdAt).format('DD/MM/YYYY') : ''}
                </p>
                <p>
                  <strong>Vendeur :</strong> {orderDetail?.Vendeur?.name}
                </p>
              </Col>
              <Col span={12}>
                <p>
                  <strong>Adresse du chantier :</strong> {orderDetail?.siteAddressFull}
                </p>
                <p>
                  <strong>Code postal chantier :</strong> {orderDetail?.siteAddressPostalCode}
                </p>
                <p>
                  <strong>Ville du chantier :</strong> {orderDetail?.siteAddressCity}
                </p>
                <p>
                  <strong>Contact chantier :</strong> {orderDetail?.contactSurPlace}
                </p>
              </Col>
            </Row>
            <Row gutter={16}>
              <Col span={24}>
                <p>
                  <strong>Objet :</strong> {orderDetail?.objectDuDocument}
                </p>
              </Col>
            </Row>
          </Card>

          <Row gutter={[16, 16]}>
            <Col span={8}>
              <Card
                bordered={true}
                style={{
                  marginBottom: 20,
                  borderColor: '#99C24D',
                  borderWidth: 1,
                  borderRadius: 5,
                }}
              >
                <p style={{ fontWeight: 'bold', marginBottom: 5 }}>Commentaire :</p>
                {!isEditingComment && (
                  <TextArea
                    rows={5}
                    placeholder=''
                    value={comment}
                    readOnly
                    style={{
                      resize: 'none',
                      outline: 'none',
                      height: '163px',
                    }}
                  />
                  // <div className='custom-textarea full-width'>
                  //   <label className='custom-textarea__label'>{comment}</label>
                  // </div>
                )}
                {isEditingComment && (
                  <TextArea
                    rows={5}
                    placeholder=''
                    onChange={(e) => setComment(e.target.value)}
                    value={comment}
                    style={{
                      resize: 'none',
                      outline: 'none',
                      height: '163px',
                    }}
                  />
                )}
                {!isEditingComment ? (
                  <EditOutlined
                    className='edit-icon edit-custom-textarea-comment'
                    onClick={() => setIsEditingComment(true)}
                  />
                ) : (
                  <>
                    {loadingSubmitComment ? (
                      <LoadingOutlined className='product-description__description-loading-icon submit-edit-custom-textarea-comment' />
                    ) : (
                      <>
                        <CheckOutlined
                          className='product-description__description-apply-edit-icon check-button submit-edit-custom-textarea-comment'
                          onClick={handleSubmitChangeComment}
                        />
                        <CloseOutlined
                          className='product-description__description-cancel-icon cancel-button cancel-edit-custom-textarea-comment'
                          onClick={handleCloseChangeComment}
                        />
                      </>
                    )}
                  </>
                )}
              </Card>
            </Col>
            <Col span={8}>
              <Card
                bordered={true}
                style={{
                  marginBottom: 20,
                  borderColor: '#99C24D',
                  borderWidth: 1,
                  borderRadius: 5,
                }}
              >
                <p style={{ fontWeight: 'bold', marginBottom: 5 }}>Demande commerciale</p>
                <TextArea
                  rows={5}
                  readOnly
                  value={orderDetail?.demandeCommerciale}
                  style={{
                    resize: 'none',
                    outline: 'none',
                    height: '163px',
                  }}
                />
              </Card>
            </Col>
            <Col span={8}>
              <Card
                bordered={true}
                style={{
                  marginBottom: 20,
                  borderColor: '#99C24D',
                  borderWidth: 1,
                  borderRadius: 5,
                }}
              >
                <p style={{ fontWeight: 'bold', marginBottom: 5 }}>Réponse logistique</p>
                <TextArea
                  rows={5}
                  placeholder=''
                  readOnly
                  value={orderDetail?.responseLogistique}
                  style={{
                    resize: 'none',
                    outline: 'none',
                    height: '163px',
                  }}
                />
              </Card>
            </Col>
          </Row>

          <Table
            scroll={{ x: 1300 }}
            bordered
            rowKey={(record) => record.id}
            rowSelection={{
              type: 'checkbox',
              onChange: (selectedRowKeys, selectedRows) => {
                setSelectedRows(selectedRows);
              },
              getCheckboxProps: (record) => ({
                disabled: isShowPrestatairePopup || record.serviceProviderOrderQuantity == record.quantity,
              }),
            }}
            components={{
              body: {
                row: LogistiqueOrderProductRow,
                cell: LogistiqueOrderProductCell,
              },
            }}
            rowClassName={(record) => rowClassName(record)}
            dataSource={dataSource}
            columns={columns as ColumnTypes}
            pagination={false}
          />
          <Row justify='space-between' align='middle' style={{ marginTop: 20, display: 'flex' }}>
            <Col xl={0} xxl={8} />
            {/* Centered Button */}
            <Col xl={12} xxl={8} style={{ display: 'flex', justifyContent: 'center' }}>
              <Button
                type='primary'
                style={{ background: '#99C24D', borderColor: '#99C24D' }}
                onClick={() => handleShowPrestatairePopup(true)}
                disabled={isShowPrestatairePopup || isDisableAction || selectedProductLines.length === 0}
              >
                Gérer la commande
              </Button>
            </Col>
            {isShowPrestatairePopup && (
              <OrderPrestataireModal
                isOpenModal={isShowPrestatairePopup}
                handleCancel={() => handleShowPrestatairePopup(false)}
                handleSubmit={handlePrestataireSubmit}
                serviceProviderZoneIds={serviceProviderZoneIds}
                selectedProductLines={selectedProductLines}
              />
            )}
            {boOrderType === BO_ORDER_TYPES.BENNE && (
              <BenneNouvelleCommamdeModal
                isOpenModal={boOrderType === BO_ORDER_TYPES.BENNE}
                onCancel={handleCancelOrderPopup}
                onConfirmation={() => handleSubmitOrder(form.getFieldsValue())}
                catalogPriceLine={selectedCatalogPriceLine}
                form={form}
                client={orderDetail?.Contact ?? null}
                phone={orderDetail?.contactSurPlace ?? ''}
                orderCode={orderDetail?.cdeZoho ?? ''}
                siteAddress={orderDetail?.siteAddressFull ?? ''}
              />
            )}

            {boOrderType === BO_ORDER_TYPES.BIG_BAG && (
              <BigbagNouvelleCommamdeModal
                data={{
                  ...(orderDetail as Documents),
                  serviceProviderId: 31,
                  service: 'ECODROP',
                }}
                catalogPriceLine={selectedCatalogPriceLine}
                isOpenModal={boOrderType === BO_ORDER_TYPES.BIG_BAG}
                onCancel={handleCancelOrderPopup}
                onConfirmation={() => handleSubmitOrder(form.getFieldsValue())}
                form={form}
              />
            )}

            {boOrderType === BO_ORDER_TYPES.CAMION && (
              <CamionNouvelleCommamdeModal
                catalogPriceLine={selectedCatalogPriceLine}
                isOpenModal={boOrderType === BO_ORDER_TYPES.CAMION}
                onCancel={() => handleCancelOrderPopup()}
                onConfirmation={() => handleSubmitOrder(form.getFieldsValue())}
                form={form}
                client={orderDetail?.Contact ?? null}
                phone={orderDetail?.contactSurPlace ?? ''}
                orderCode={orderDetail?.cdeZoho ?? ''}
                siteAddress={orderDetail?.siteAddressFull ?? ''}
              />
            )}
            {/* Subtotal Block at the End */}
            <Col xl={12} xxl={8} style={{ display: 'flex', justifyContent: 'flex-end' }}>
              <Space direction='horizontal' align='end' className='quotation__subtotal-block'>
                <Card bordered={false} className='quotation__subtotal-card' style={{ width: '300px' }}>
                  <Row>
                    <Col className='quotation__subtotal-left-col' style={{ borderTopLeftRadius: '8px' }}>
                      Sous-total
                    </Col>
                    <Col className='quotation__subtotal-right-col' style={{ borderTopRightRadius: '8px' }}>
                      {`${frenchCurrencyFormat((Number(subTotal) || 0).toFixed(2))}`} €
                    </Col>
                  </Row>
                  <Row>
                    <Col className='quotation__subtotal-left-col'>
                      TVA ({`${frenchCurrencyFormat((Number(taxPercentage) || 0).toFixed(0))}`}%)
                    </Col>
                    <Col className='quotation__subtotal-right-col'>
                      {`${frenchCurrencyFormat(((Number(subTotal) || 0) * (taxPercentage / 100)).toFixed(2))}`} €
                    </Col>
                  </Row>
                  <Divider
                    style={{
                      borderBlockStart: '1px solid rgba(0, 0, 0, 0.25)',
                      margin: 0,
                    }}
                  />
                  <Row>
                    <Col className='quotation__subtotal-left-col' style={{ borderBottomLeftRadius: '8px' }}>
                      <span className='quotation__total-text'>Total</span>
                    </Col>
                    <Col className='quotation__subtotal-right-col' style={{ borderBottomRightRadius: '8px' }}>
                      <span className='quotation__total-text'>
                        {`${frenchCurrencyFormat(((Number(subTotal) || 0) + (Number(subTotal) || 0) * (taxPercentage / 100)).toFixed(2))}`}{' '}
                        €
                      </span>
                    </Col>
                  </Row>
                </Card>
              </Space>
            </Col>
          </Row>
        </Content>
      </Layout>
    </div>
  );
};

export default LogistiqueOrderDetail;
