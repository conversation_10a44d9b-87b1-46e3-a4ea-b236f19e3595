import { <PERSON><PERSON>, <PERSON>, Col, Divider, Form, Modal, Row } from 'antd';
import { ArrowsAltOutlined, CloseOutlined, MinusOutlined } from '@ant-design/icons';
import OrderPrestataireTable from './OrderPrestataireTable';
import { useEffect, useState } from 'react';
import { frenchCurrencyFormat } from 'utils';
import {
  useCatalogPriceZoneServiceProvider,
  useCatalogPriceZoneServiceProviderQuery,
} from 'hooks/catalog-price-zone-service-provider';
import { usePriceTypes, usePriceTypesQuery } from 'hooks';
import { OrderPrestataireLine } from './OrderPrestataireCellTable';
import { ServiceProvider } from 'models';
import { serviceProviderService } from 'services';
import RemoteSelect from 'components/Common/RemoteSelect';
interface OrderPrestataireModalProps {
  isOpenModal: boolean;
  handleCancel: () => void;
  handleSubmit: (selectedRow: OrderPrestataireLine | null) => void;
  serviceProviderZoneIds: number[];
  selectedProductLines: { priceFamilyId?: number; productId?: number; quantity?: number }[];
}
const OrderPrestataireModal = (props: OrderPrestataireModalProps) => {
  const { isOpenModal, handleCancel, handleSubmit, serviceProviderZoneIds, selectedProductLines } = props;
  const [form] = Form.useForm();
  const [collapsed, setCollapsed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [catalogPriceZoneServiceProviderQuery] = useCatalogPriceZoneServiceProviderQuery({
    serviceProviderZoneIds,
    products: selectedProductLines,
  });
  const [
    catalogPriceZoneServiceProviders,
    // eslint-disable-next-line @typescript-eslint/no-unused-vars
    catalogPriceZoneServiceProviderRefresh,
    catalogPriceZoneServiceProvidersLoading,
  ] = useCatalogPriceZoneServiceProvider(catalogPriceZoneServiceProviderQuery);
  const [priceTypesQuery] = usePriceTypesQuery();
  const [priceTypes] = usePriceTypes(priceTypesQuery);
  const [dataSource, setDataSource] = useState<OrderPrestataireLine[]>([]);
  const [selectedRow, setSelectedRow] = useState<OrderPrestataireLine | null>(null);
  const onSelectRow = (row: OrderPrestataireLine | null) => {
    setSelectedRow(row);
  };
  useEffect(() => {
    if (isOpenModal && dataSource.length === 0 && catalogPriceZoneServiceProviders.length > 0) {
      setDataSource(catalogPriceZoneServiceProviders);
    }
  }, [isOpenModal, catalogPriceZoneServiceProviders]);

  const fetchServiceProviders = async ({ search, page }: { search: string; page: number }) => {
    const response = await serviceProviderService.getServiceProviders({
      name: `*${search}*`,
      orderBy: 'createdAt,desc|name',
      page,
      limit: 20,
    });
    return response.rows || [];
  };
  const handleCollapse = () => {
    setCollapsed((prev) => !prev);
  };

  const fetchOneServiceProvider = async (serviceProviderId: number) => {
    const serviceProvider = await serviceProviderService.getServiceProvider(serviceProviderId, { limit: 1 });
    return serviceProvider;
  };

  const handleInputPrestataireChange = async (value: number) => {
    setIsLoading(true);
    try {
      if (!value || dataSource.some((e) => e.serviceProviderId === value)) return;
      const serviceProvider = await fetchOneServiceProvider(value);
      if (!serviceProvider) return;
      const newEntry = buildPrestataireLine(serviceProvider);
      setDataSource((prev) => [newEntry, ...prev]);
    } finally {
      setIsLoading(false);
    }
  };

  const buildPrestataireLine = (serviceProvider: ServiceProvider): OrderPrestataireLine => {
    return {
      formattedAddress: serviceProvider.address,
      postalcode: serviceProvider.postalcode,
      serviceProviderId: serviceProvider.id,
      serviceProviderName: serviceProvider.name,
      zones: selectedProductLines.map((line, index) => ({
        zoneId: line.priceFamilyId || index + 1000,
        zoneName: `Zone ${index + 1}`,
      })),
      products: selectedProductLines.map((line) => ({
        priceFamilies: [],
        priceValue: '',
        productId: line.productId || 0,
        productName: 'Benne : DIB - 10m3',
        quantity: line.quantity,
        selectedPriceFamilyId: line.priceFamilyId || 0,
      })),
    };
  };

  const handleClose = () => {
    setCollapsed(false);
    handleCancel();
  };

  useEffect(() => {
    if (isOpenModal && !collapsed) {
      const previous = document.body.style.overflow;
      document.body.style.overflow = 'hidden';
      return () => {
        document.body.style.overflow = previous;
      };
    }
  }, [isOpenModal, collapsed]);

  return (
    <Modal
      title={
        <div className={`prestataire-comparison-modal__header ${collapsed ? 'collapsed ' : ''}`}>
          <div>
            <span>CHOIX DU PRESTATAIRE</span>
          </div>
          <div style={{ display: 'flex', alignItems: 'center', gap: 4, marginLeft: '33%' }}>
            <Button
              type='text'
              icon={collapsed ? <ArrowsAltOutlined /> : <MinusOutlined />}
              onClick={(e) => {
                e.stopPropagation();
                handleCollapse();
              }}
            />
            {!collapsed && (
              <Button
                type='text'
                icon={<CloseOutlined />}
                onClick={(e) => {
                  e.stopPropagation();
                  handleClose();
                }}
              />
            )}
          </div>
        </div>
      }
      open={isOpenModal}
      maskClosable={!collapsed}
      onCancel={handleCancel}
      className={`prestataire-comparison-modal ${collapsed ? 'collapsed' : ''}`}
      wrapClassName={collapsed ? 'bottom-modal-wrap collapsed-wrap' : ''}
      centered={true}
      mask={true}
      maskStyle={collapsed ? { display: 'none' } : {}}
      getContainer={false}
      closeIcon={null}
      footer={
        collapsed
          ? null
          : [
              <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
                Annuler
              </Button>,
              <Button
                key='ajouter'
                type='primary'
                className='ant-modal-content__add-btn'
                onClick={() => handleSubmit(selectedRow)}
              >
                Confirmer
              </Button>,
            ]
      }
    >
      <Form layout='horizontal' form={form} style={{ width: '100%' }}>
        <div style={{ marginBottom: 15, maxWidth: 350 }}>
          <RemoteSelect<ServiceProvider>
            fetchData={fetchServiceProviders}
            optionKey='id'
            optionLabel='name'
            placeholder='Ajouter une Prestataire'
            onChange={handleInputPrestataireChange}
            loading={isLoading}
            excludeIds={dataSource.map((item) => item.serviceProviderId)}
            style={{ width: '100%' }}
            {...props}
          />
        </div>
        <div className={`prestataire-modal-body`} style={{ display: collapsed ? 'none' : 'block' }}>
          <OrderPrestataireTable
            form={form}
            dataSource={dataSource}
            setDataSource={setDataSource}
            isLoading={catalogPriceZoneServiceProvidersLoading === 'pending'}
            priceTypes={priceTypes}
            onSelectRow={onSelectRow}
          />
          <Card
            bordered={false}
            className='quotation__subtotal-block quotation__subtotal-card'
            style={{ width: '35%', marginTop: 15, marginBottom: 0, marginRight: 0 }}
          >
            <Row>
              <Col className='quotation__subtotal-left-col' style={{ borderTopLeftRadius: '8px' }}>
                Total prestataire
              </Col>
              <Col className='quotation__subtotal-right-col' style={{ borderTopRightRadius: '8px' }}>
                {`${frenchCurrencyFormat((Number(0) || 0).toFixed(2))}`} €
              </Col>
            </Row>
            <Row>
              <Col className='quotation__subtotal-left-col'>Total client</Col>
              <Col className='quotation__subtotal-right-col'>
                {`${frenchCurrencyFormat((Number(0) || 0).toFixed(2))}`} €
              </Col>
            </Row>
            <Divider
              style={{
                borderBlockStart: '1px solid rgba(0, 0, 0, 0.25)',
                margin: 0,
              }}
            />
            <Row>
              <Col className='quotation__subtotal-left-col' style={{ borderBottomLeftRadius: '8px' }}>
                <span className='quotation__total-text'>Marge</span>
              </Col>
              <Col className='quotation__subtotal-right-col' style={{ borderBottomRightRadius: '8px' }}>
                <span className='quotation__total-text'>{0}%</span>
              </Col>
            </Row>
          </Card>
        </div>
      </Form>
    </Modal>
  );
};
export default OrderPrestataireModal;
