import { Checkbox, FormInstance, Table, TableProps } from 'antd';
import {
  EditableOrderPrestataireCell,
  EditableOrderPrestataireRow,
  OrderPrestataireLine,
} from './OrderPrestataireCellTable';
import { useState } from 'react';
import { PriceType } from 'models';

interface PrestataireTableProps {
  form: FormInstance;
  dataSource: OrderPrestataireLine[];
  setDataSource: React.Dispatch<React.SetStateAction<OrderPrestataireLine[]>>;
  isLoading: boolean;
  priceTypes: PriceType[];
  onSelectRow?: (row: OrderPrestataireLine | null) => void;
}

const OrderPrestataireTable = (props: PrestataireTableProps) => {
  const { form, dataSource, setDataSource, isLoading, priceTypes, onSelectRow } = props;
  const [selectedRowKey, setSelectedRowKey] = useState<number | null>(null);

  const handleSelectRow = async (id: number, value: boolean) => {
    try {
      setSelectedRowKey(value ? id : null);

      if (value) {
        const selectedRow = dataSource.find((row) => row.serviceProviderId === id) || null;
        onSelectRow?.(selectedRow);
      } else {
        onSelectRow?.(null);
      }
    } catch (error) {
      console.log(error);
    }
  };

  const updateDataSource = (data: OrderPrestataireLine, serviceProviderId?: number) => {
    setDataSource((prev) =>
      prev.map((item) => (item.serviceProviderId === serviceProviderId ? { ...item, ...data } : item)),
    );
  };

  const defaultColumns = [
    {
      dataIndex: 'rowSelect',
      width: 10,
      onCell: (record: OrderPrestataireLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: OrderPrestataireLine) => (
        <Checkbox
          checked={selectedRowKey === record.serviceProviderId}
          onChange={(e) => handleSelectRow(record.serviceProviderId, e.target.checked)}
        />
      ),
    },
    {
      title: 'Prestataires',
      dataIndex: 'CatalogPriceLineServiceProvider',
      editable: true,
      width: 140,
      onCell: (record: OrderPrestataireLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: OrderPrestataireLine) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
    {
      title: 'Produit',
      dataIndex: 'CatalogPriceLineProducts',
      editable: true,
      width: 380,
      onCell: (record: OrderPrestataireLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: OrderPrestataireLine) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
    {
      title: 'Zones',
      dataIndex: 'CatalogPriceLineZones',
      editable: true,
      width: 100,
      onCell: (record: OrderPrestataireLine) => ({
        rowSpan: record.rowSpan,
      }),
      render: (_: string, record: OrderPrestataireLine) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
    {
      title: 'Prix Presta',
      dataIndex: 'priceValue',
      editable: true,
      width: 120,
      render: (_: string, record: OrderPrestataireLine) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
    {
      title: 'Quantité',
      dataIndex: 'quantity',
      editable: true,
      width: 50,
      render: (_: string, record: OrderPrestataireLine) => {
        if (record.creationType === 'header') {
          return {
            props: {
              colSpan: 6,
            },
          };
        }
      },
    },
  ];

  const columns = defaultColumns.map((col) => {
    if (!col.editable) {
      return col;
    }
    return {
      ...col,
      onCell: (record: OrderPrestataireLine) => ({
        form,
        record,
        editable: col.editable,
        dataIndex: col.dataIndex,
        priceTypes,
        updateDataSource,
      }),
    };
  }) as TableProps<OrderPrestataireLine>['columns'];

  return (
    <section className='section quotation' style={{ width: '100%' }}>
      <Table
        bordered
        className='catalog-price-table'
        loading={isLoading}
        components={{
          body: {
            row: EditableOrderPrestataireRow,
            cell: EditableOrderPrestataireCell,
          },
        }}
        columns={columns}
        dataSource={dataSource}
        pagination={false}
      />
    </section>
  );
};

export default OrderPrestataireTable;
