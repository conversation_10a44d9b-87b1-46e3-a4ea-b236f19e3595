import { MenuOutlined } from '@ant-design/icons';
import { useSortable } from '@dnd-kit/sortable';
import { CSS } from '@dnd-kit/utilities';
import { PriceFamily, Option, ProductTypeRegul, Price } from 'models';
import React, { useState } from 'react';
import { usePriceFamilyRequest, useOptionsRequest, usePricesRequest } from 'hooks';
import { CatalogPriceZone, Loading, TarifType } from 'types';
import { useProductTypeRegulRequest } from 'hooks/productTypeRegulHook';
import useStateCallback from 'hooks/useStateCallback';

interface RowProps extends React.HTMLAttributes<HTMLTableRowElement> {
  'data-row-key': string;
}

export type CatalogPriceZonesType = {
  tarifType: TarifType | null;
  countServiceProvider?: number;
  countServiceProviderForfait?: number;
  forfait?: CatalogPriceZone[];
  semiforfait?: CatalogPriceZone[];
} | null;
export const DocumentTableRowContext = React.createContext<{
  options: Option[];
  priceFamilies: PriceFamily[];
  catalogPriceZones: CatalogPriceZonesType;
  catalogPriceZonesLoading: boolean;
  priceFamiliesLoading: Loading;
  optionsLoading: Loading;
  prices: Price[];
  pricesLoading: Loading;
  productTypeReguls: ProductTypeRegul[];
  productTypeRegulsLoading: Loading;
  fetchPriceFamilies: (params: { [key: string]: string }) => void;
  fetchOptions: (params: { [key: string]: string }) => void;
  setCatalogPriceZones: (state: CatalogPriceZonesType, cb?: (state: CatalogPriceZonesType) => void) => void;
  setCatalogPriceZonesLoading: (catalogPriceZonesLoading: boolean) => void;
  fetchProductTypeReguls: (params: { [key: string]: string }) => void;
  fetchPrices: (params: { [key: string]: string }) => void;
}>({
  options: [],
  priceFamilies: [],
  catalogPriceZones: null,
  catalogPriceZonesLoading: false,
  priceFamiliesLoading: 'idle',
  optionsLoading: 'idle',
  productTypeReguls: [],
  productTypeRegulsLoading: 'idle',
  prices: [],
  pricesLoading: 'idle',
  fetchPriceFamilies: () => {},
  fetchOptions: () => {},
  fetchProductTypeReguls: () => {},
  fetchPrices: () => {},
  setCatalogPriceZones: () => {},
  setCatalogPriceZonesLoading: () => {},
});

const LogistiqueOrderProductRow = ({ children, ...props }: RowProps) => {
  const { attributes, listeners, setNodeRef, setActivatorNodeRef, transform, transition, isDragging } = useSortable({
    id: props['data-row-key'],
  });
  const [priceFamilies, priceFamiliesLoading, fetchPriceFamilies] = usePriceFamilyRequest();
  const [options, optionsLoading, fetchOptions] = useOptionsRequest();
  const [prices, pricesLoading, fetchPrices] = usePricesRequest();
  const [productTypeReguls, productTypeRegulsLoading, fetchProductTypeReguls] = useProductTypeRegulRequest();
  const [catalogPriceZones, setCatalogPriceZones] = useStateCallback<CatalogPriceZonesType>(null);
  const [catalogPriceZonesLoading, setCatalogPriceZonesLoading] = useState<boolean>(false);

  const style: React.CSSProperties = {
    ...props.style,
    transform: CSS.Transform.toString(transform && { ...transform, scaleY: 1 }),
    transition,
    ...(isDragging ? { position: 'relative', zIndex: 1000 } : {}),
  };

  return (
    <DocumentTableRowContext.Provider
      value={{
        options,
        priceFamilies,
        prices,
        pricesLoading,
        catalogPriceZones,
        catalogPriceZonesLoading,
        priceFamiliesLoading,
        optionsLoading,
        productTypeReguls,
        productTypeRegulsLoading,
        fetchPriceFamilies,
        fetchOptions,
        fetchProductTypeReguls,
        fetchPrices,
        setCatalogPriceZones,
        setCatalogPriceZonesLoading,
      }}
    >
      <tr {...props} ref={setNodeRef} style={style} {...attributes}>
        {React.Children.map(children, (child) => {
          if ((child as React.ReactElement).key === 'sort') {
            return React.cloneElement(child as React.ReactElement, {
              children: (
                <div className='text-center'>
                  <MenuOutlined
                    ref={setActivatorNodeRef}
                    style={{ touchAction: 'none', cursor: 'move', color: 'rgb(149, 197, 21)' }}
                    {...listeners}
                  />
                </div>
              ),
            });
          }
          return child;
        })}
      </tr>
    </DocumentTableRowContext.Provider>
  );
};

export default LogistiqueOrderProductRow;
