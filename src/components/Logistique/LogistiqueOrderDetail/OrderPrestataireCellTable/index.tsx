import { Form, FormInstance, message } from 'antd';
import React, { FormEvent, useEffect, useMemo, useState } from 'react';
import CatalogPriceServiceProviderCell from './CatalogPriceServiceProviderCell';
import CatalogPriceLineProductsCell from './CatalogPriceLineProductsCell';
import CatalogPriceLineZonesCell from './CatalogPriceLineZonesCell';
import PriceValueCell from './PriceValueCell';
import ProductQuantityCell from './ProductQuantityCell';
import { CombineCatalogPriceLineZoneServiceProvider } from 'hooks/catalog-price-zone-service-provider';
import { PriceType } from 'models';

export type OrderPrestataireLine = CombineCatalogPriceLineZoneServiceProvider & {
  rowSpan?: number;
  isLast?: boolean;
  isFirst?: boolean;
  isDisplay?: boolean;
  creationType?: string;
  uuid?: string;
};

const EditableContext = React.createContext<FormInstance | null>(null);

export const EditableOrderPrestataireRow = (props: React.HTMLAttributes<HTMLTableRowElement>) => {
  const [form] = Form.useForm();
  return (
    <Form form={form} component={false}>
      <EditableContext.Provider value={form}>
        <tr {...props} />
      </EditableContext.Provider>
    </Form>
  );
};

interface EditablePrestataireCellProps {
  form: FormInstance;
  title: string;
  editable: boolean;
  children: React.ReactNode;
  inputType: string;
  required: boolean;
  dataIndex: keyof object;
  record: OrderPrestataireLine;
  priceTypes: PriceType[];
  updateDataSource: (data: OrderPrestataireLine, serviceProviderId?: number) => void;
  className?: string;
}

export const EditableOrderPrestataireCell: React.FC<EditablePrestataireCellProps> = ({
  form,
  editable,
  children,
  dataIndex,
  record,
  priceTypes,
  updateDataSource,
  ...restProps
}) => {
  const fieldName = dataIndex;
  const cellDataSource = useMemo(() => {
    return record;
  }, [record]);
  const [isEditing, setIsEditing] = useState<boolean>(false);

  useEffect(() => {
    if (record) {
      form.setFieldsValue({
        valueRates: {
          [`${record?.serviceProviderId}`]: {
            [`${fieldName}`]: record[fieldName],
          },
        },
      });
    }
  }, [record]);

  const toggleEdit = () => {
    setIsEditing(!isEditing);
  };

  const save = async (e: FormEvent<HTMLInputElement>) => {
    try {
      const inputElement = e.target as HTMLInputElement;
      const { value } = inputElement;
      try {
        toggleEdit();
        form.setFieldsValue({
          valueRates: {
            [`${record?.serviceProviderId}`]: {
              [`${fieldName}`]: value,
            },
          },
        });
      } catch (error) {
        console.error(error);
        message.error('Error updating field value');
      }
    } catch (errInfo) {
      console.log('Save failed:', errInfo);
    }
  };

  const onChangeQuantity = (qty: number) => {
    console.log(qty);
  };

  const onSelectPriceFamily = (args: { productId: number; priceFamilyId: number }) => {
    const { priceFamilyId, productId } = args;
    const updatedPriceFamilyId = priceFamilyId;
    const catalogPriceLineProducts = cellDataSource?.products || [];
    const updatedProducts = catalogPriceLineProducts.map((product) => {
      if (product.productId === productId) {
        return {
          ...product,
          selectedPriceFamilyId: updatedPriceFamilyId,
        };
      }
      return product;
    });
    updateDataSource(
      {
        ...cellDataSource,
        products: updatedProducts,
      },
      cellDataSource?.serviceProviderId,
    );
  };

  const onRemoveProduct = (productId: number) => {
    const catalogPriceLineProducts = cellDataSource?.products || [];
    const updatedProducts = catalogPriceLineProducts.filter((product) => product.productId !== productId);
    updateDataSource(
      {
        ...cellDataSource,
        products: updatedProducts,
      },
      cellDataSource?.serviceProviderId,
    );
  };

  let childrenNode = <></>;
  switch (dataIndex) {
    case 'CatalogPriceLineServiceProvider':
      {
        const catalogPriceSp = {
          serviceProviderId: cellDataSource?.serviceProviderId,
          serviceProviderName: cellDataSource?.serviceProviderName,
          formattedAddress: cellDataSource?.formattedAddress,
          postalcode: cellDataSource?.postalcode,
        };
        childrenNode = <CatalogPriceServiceProviderCell value={catalogPriceSp} />;
      }
      break;
    case 'CatalogPriceLineProducts':
      {
        const catalogPriceLineProducts = cellDataSource?.products || [];
        childrenNode = (
          <CatalogPriceLineProductsCell
            value={catalogPriceLineProducts}
            record={cellDataSource}
            onSelectPriceFamily={onSelectPriceFamily}
            onRemoveProduct={onRemoveProduct}
          />
        );
      }
      break;
    case 'CatalogPriceLineZones':
      {
        const catalogPriceLineZones = cellDataSource?.zones || [];
        childrenNode = (
          <CatalogPriceLineZonesCell value={catalogPriceLineZones ? catalogPriceLineZones[0] : undefined} />
        );
      }
      break;
    case 'priceValue':
      {
        const catalogPriceLineProducts = cellDataSource?.products || [];
        childrenNode = (
          <PriceValueCell
            value={catalogPriceLineProducts}
            record={cellDataSource}
            onSave={save}
            isEditing={isEditing}
            onTouchEdit={toggleEdit}
            priceTypes={priceTypes}
          />
        );
      }
      break;
    case 'quantity':
      {
        const productQuantities = cellDataSource?.products || [];
        childrenNode = (
          <ProductQuantityCell value={productQuantities} record={cellDataSource} onChangeQuantity={onChangeQuantity} />
        );
      }
      break;
    default:
      break;
  }
  let childNode = children;
  if (editable) {
    childNode = childrenNode;
  }
  return <td {...restProps}>{childNode}</td>;
};
