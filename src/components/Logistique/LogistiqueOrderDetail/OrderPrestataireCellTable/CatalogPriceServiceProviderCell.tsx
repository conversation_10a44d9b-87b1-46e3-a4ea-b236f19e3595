interface CatalogPriceUrbanCenterCellProps {
  value?: {
    serviceProviderId: number;
    serviceProviderName: string;
    formattedAddress: string;
    postalcode: string;
  };
}

const CatalogPriceServiceProviderCell = (props: CatalogPriceUrbanCenterCellProps) => {
  const { value } = props;
  return (
    <>
      <div style={{ fontWeight: 'bold' }} className='mt-1'>
        {value?.serviceProviderName ?? ''}
      </div>
      <div>
        {value?.formattedAddress ?? ''} {value?.postalcode ? '- ' + value.postalcode : ''}
      </div>
    </>
  );
};

export default CatalogPriceServiceProviderCell;
