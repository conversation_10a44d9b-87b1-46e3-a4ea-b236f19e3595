import { DeleteOutlined, PlusOutlined } from '@ant-design/icons';
import { Button, Col, Form, Row, Select, Typography } from 'antd';
import { CatalogPriceZoneSPPriceFamily } from 'hooks/catalog-price-zone-service-provider';
import { Fragment } from 'react';
import { PRICE_TYPE_LOGICS } from 'utils/constant';
import { OrderPrestataireLine } from '.';
import { PriceFamily } from 'models';
const { Text } = Typography;

interface CatalogPriceLineProductsCellProps {
  value?: {
    productId: number;
    productName: string;
    priceFamilies?: CatalogPriceZoneSPPriceFamily[];
    selectedPriceFamilyId?: number | null;
  }[];
  record: OrderPrestataireLine;
  onSelectPriceFamily: (args: { productId: number; priceFamilyId: number }) => void;
  onRemoveProduct: (productId: number) => void;
}

const CatalogPriceLineProductsCell = (props: CatalogPriceLineProductsCellProps) => {
  const { value, record, onSelectPriceFamily, onRemoveProduct } = props;
  return (
    <div>
      {value?.map((item, index) => (
        <>
          <div key={index}>
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }} className='text-align-center pa-1 pl-3 pr-3'>
                <div className='text-tarif'>
                  <Text strong italic className='mr-2'>
                    {item?.productName}
                  </Text>
                  <Button
                    type='link'
                    icon={<DeleteOutlined />}
                    className='datatable__action-destroy-button'
                    onClick={() => onRemoveProduct(item.productId)}
                  />
                </div>
              </Col>
            </Row>
          </div>
          {generateTarifSection(
            record,
            item.productId,
            item?.priceFamilies || [],
            onSelectPriceFamily,
            item?.selectedPriceFamilyId,
          )}
        </>
      ))}
      <Button type='text' size='small' className='center-urban__add-parameter-btn' onClick={() => {}}>
        <PlusOutlined />
        Ajouter un produit
      </Button>
    </div>
  );
};

const generateTarifSection = (
  record: OrderPrestataireLine,
  productId: number,
  priceFamilies: CatalogPriceZoneSPPriceFamily[],
  onSelectPriceFamily: (args: { productId: number; priceFamilyId: number }) => void,
  priceFamilyId?: number | null,
) => {
  let content: JSX.Element = <></>;
  const selectedPriceFamily = priceFamilies.find((priceFamily) => priceFamily.id === priceFamilyId);
  if (priceFamilies.length > 0) {
    content = (
      <>
        <Row gutter={[16, 0]} className='type-du-tarif'>
          <Col xs={{ span: 18 }} style={{ textAlign: 'right', paddingRight: 0 }}>
            <Form.Item
              label='Type du tarif'
              name={['lineItems', `${record?.serviceProviderId}`, 'prestation', 'tarifItems', 'priceFamilyId']}
              rules={[{ required: true }]}
              initialValue={priceFamilyId}
            >
              <Select
                style={{ width: '200px' }}
                placeholder='Sélectionner'
                showSearch={false}
                options={priceFamilies.map((priceFamily) => ({
                  value: priceFamily.id,
                  label: priceFamily.name,
                }))}
                onSelect={(value) =>
                  onSelectPriceFamily({
                    productId: productId,
                    priceFamilyId: value,
                  })
                }
              />
            </Form.Item>
          </Col>
        </Row>
        {priceFamilyId && (
          <Row gutter={[16, 0]} className='mt-2'>
            <Col xs={{ span: 24 }}>
              <Form.Item
                label={selectedPriceFamily?.labelName ?? ''}
                labelCol={{ span: 24 }}
                wrapperCol={{ span: 8 }}
                className='long-label-item'
              ></Form.Item>
              <PriceFamilyHiddenFields
                record={record}
                priceFamilyId={priceFamilyId}
                selectedPriceFamily={selectedPriceFamily}
              />
            </Col>
          </Row>
        )}
        {selectedPriceFamily?.prices?.map((tarif, indexTarif) => (
          <Fragment key={`${tarif?.id}-${indexTarif}`}>
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }}>
                <Form.Item
                  colon={false}
                  labelCol={{ style: { fontWeight: 'bold', height: '30px' } }}
                  label={
                    tarif.name +
                    `${tarif?.PriceTypeLogic?.key === PRICE_TYPE_LOGICS.indicative_purpose ? ' (Informatif)' : ''}`
                  }
                ></Form.Item>
              </Col>
            </Row>
            {tarif?.priceOptions?.map((priceOption, indexPriceOption) => (
              <Fragment key={`${priceOption?.id}-${indexPriceOption}`}>
                <Row gutter={[16, 0]}>
                  <Col xs={{ span: 24 }}>
                    <Form.Item label={priceOption.name} labelCol={{ span: 24 }} wrapperCol={{ span: 8 }}></Form.Item>
                  </Col>
                </Row>
              </Fragment>
            ))}
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }}>
                <Form.Item label={tarif.labelName} labelCol={{ span: 24 }} wrapperCol={{ span: 8 }}></Form.Item>
              </Col>
            </Row>
          </Fragment>
        ))}
      </>
    );
  } else {
    content = (
      <>
        <Row gutter={[16, 0]}>
          <Col xs={{ span: 24 }}>
            <Form.Item
              label='Prix'
              name={['lineItems', `${record?.serviceProviderId}`, 'prestation', 'tarifItems', 'priceFamilyValue']}
              labelCol={{ span: 24 }}
              wrapperCol={{ span: 8 }}
            ></Form.Item>
          </Col>
        </Row>
      </>
    );
  }

  return (
    <section className='section mt-2 mb-4 generate-tarif-section'>
      {content}
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 24 }}>
          <Form.Item label='TOTAL' className='total-price' labelCol={{ span: 24 }} wrapperCol={{ span: 8 }}></Form.Item>
        </Col>
      </Row>
    </section>
  );
};

const PriceFamilyHiddenFields = ({
  record,
  priceFamilyId,
  selectedPriceFamily,
}: {
  record: OrderPrestataireLine;
  priceFamilyId: number;
  selectedPriceFamily?: PriceFamily;
}) => {
  return (
    <>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.serviceProviderId}`,
          'prestation',
          'tarifItems',
          `${priceFamilyId}`,
          'linePriceFamilyId',
        ]}
      ></Form.Item>
      <Form.Item
        className='hidden'
        name={[
          'lineItems',
          `${record?.serviceProviderId}`,
          'prestation',
          'tarifItems',
          `${priceFamilyId}`,
          'priceFamilyLabel',
        ]}
        initialValue={selectedPriceFamily?.labelName}
      ></Form.Item>
    </>
  );
};

export default CatalogPriceLineProductsCell;
