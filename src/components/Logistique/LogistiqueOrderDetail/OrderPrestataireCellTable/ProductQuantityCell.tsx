import { Col, Form, Input, Row } from 'antd';
import { RuleObject } from 'antd/es/form';
import { OrderPrestataireLine } from '.';
import { CatalogPriceZoneSPPriceFamily } from 'hooks/catalog-price-zone-service-provider';
import { Fragment } from 'react';

interface ProductQuantityCellProps {
  value?: {
    productId: number;
    productName: string;
    priceFamilies?: CatalogPriceZoneSPPriceFamily[];
    selectedPriceFamilyId?: number | null;
    quantity?: number;
  }[];
  record: OrderPrestataireLine;
  onChangeQuantity: (productId: number, quantity: number) => void;
}

const ProductQuantityCell = (props: ProductQuantityCellProps) => {
  const { value, record, onChangeQuantity } = props;

  return (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '12px' }}>
      {value?.map((item) => (
        <>
          <Form.Item
            key={item.productId}
            name={['lineItems', `${record.serviceProviderId}`, 'quantity', item.productId]}
            rules={[
              { required: true, message: 'Quantity is required' },
              () => ({
                validator(_: RuleObject, value) {
                  if (isNaN(value) || parseFloat(value) < 0) {
                    return Promise.reject('Invalid quantity');
                  }
                  return Promise.resolve();
                },
              }),
            ]}
            initialValue={item.quantity}
            style={{ marginBottom: 0 }}
          >
            <Input
              style={{ width: '70px', textAlign: 'right' }}
              onChange={(e) => onChangeQuantity(item.productId, parseFloat(e.target.value))}
            />
          </Form.Item>
          {generateTarifSectionSpace(item?.priceFamilies || [], item?.selectedPriceFamilyId)}
        </>
      ))}
    </div>
  );
};

const generateTarifSectionSpace = (priceFamilies: CatalogPriceZoneSPPriceFamily[], priceFamilyId?: number | null) => {
  let content: JSX.Element = <></>;
  const selectedPriceFamily = priceFamilies.find((priceFamily) => priceFamily.id === priceFamilyId);
  if (priceFamilies.length > 0) {
    content = (
      <>
        <Row gutter={[16, 0]} className='type-du-tarif'>
          <Col xs={{ span: 18 }} style={{ textAlign: 'right', paddingRight: 0 }}>
            <Form.Item initialValue={priceFamilyId}></Form.Item>
          </Col>
        </Row>
        {priceFamilyId && (
          <Row gutter={[16, 0]}>
            <Col xs={{ span: 24 }}>
              <Form.Item labelCol={{ span: 24 }} wrapperCol={{ span: 8 }} className='long-label-item'></Form.Item>
            </Col>
          </Row>
        )}
        {selectedPriceFamily?.prices?.map((tarif, indexTarif) => (
          <Fragment key={`${tarif?.id}-${indexTarif}`}>
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }}></Col>
            </Row>
            {tarif?.priceOptions?.map((priceOption, indexPriceOption) => (
              <Fragment key={`${priceOption?.id}-${indexPriceOption}`}>
                <Row gutter={[16, 0]}>
                  <Col xs={{ span: 24 }}>
                    <Form.Item labelCol={{ span: 24 }} wrapperCol={{ span: 8 }}></Form.Item>
                  </Col>
                </Row>
              </Fragment>
            ))}
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }}>
                <Form.Item labelCol={{ span: 24 }} wrapperCol={{ span: 8 }}></Form.Item>
              </Col>
            </Row>
          </Fragment>
        ))}
      </>
    );
  } else {
    content = (
      <>
        <Row gutter={[16, 0]}>
          <Col xs={{ span: 24 }} className='text-align-right'>
            <Form.Item></Form.Item>
          </Col>
        </Row>
      </>
    );
  }
  return (
    <section className='section generate-tarif-section-empty-space'>
      {content}
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 24 }}>
          <Form.Item className='total-price'></Form.Item>
        </Col>
      </Row>
    </section>
  );
};

export default ProductQuantityCell;
