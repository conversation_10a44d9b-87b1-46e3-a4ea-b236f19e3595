import { CatalogPriceZoneSPPriceFamily } from 'hooks/catalog-price-zone-service-provider';
import { FormEvent, Fragment } from 'react';
import { OrderPrestataireLine } from '.';
import { Col, Form, Input, InputNumber, Row, Select } from 'antd';
import { debounce } from 'utils';
import { PRICE_TYPE_LOGICS, PRICE_TYPES } from 'utils/constant';
import { PriceType } from 'models';

interface PriceValueCellProps {
  value?: {
    productId: number;
    productName: string;
    priceFamilies?: CatalogPriceZoneSPPriceFamily[];
    priceValue: string;
    selectedPriceFamilyId?: number | null;
  }[];
  record: OrderPrestataireLine;
  priceTypes: PriceType[];
  onSave: (e: FormEvent<HTMLInputElement>) => void;
  isEditing: boolean;
  onTouchEdit: () => void;
}

const PriceValueCell = (props: PriceValueCellProps) => {
  const { value, record, priceTypes } = props;
  return (
    <div>
      {value?.map((item, index) => (
        <>
          <div key={index}>
            <Row gutter={[16, 0]}>
              <Col xs={{ span: 24 }} className='text-align-center pa-1 pl-3 pr-3'>
                <div className='text-tarif'></div>
              </Col>
            </Row>
          </div>
          {generateCatalogPriceSection(
            record,
            item?.priceFamilies || [],
            priceTypes,
            item?.selectedPriceFamilyId,
            item.priceValue || '',
          )}
        </>
      ))}
    </div>
  );
};

const generateCatalogPriceSection = (
  record: OrderPrestataireLine,
  priceFamilies: CatalogPriceZoneSPPriceFamily[],
  priceTypes: PriceType[],
  priceFamilyId?: number | null,
  productPriceValue?: string,
) => {
  let content: JSX.Element = <></>;
  const selectedPriceFamily = priceFamilies.find((priceFamily) => priceFamily.id === priceFamilyId);
  const selectionPriceType = priceTypes?.find((priceType) => priceType.key === PRICE_TYPES.selection);
  const numberPriceType = priceTypes?.find((priceType) => priceType.key === PRICE_TYPES.numberInput);

  const handlePriceChange = debounce((value: string | null) => {
    console.log('value: ', value);
  }, 500);

  if (priceFamilies.length > 0) {
    content = (
      <>
        <Row gutter={[16, 0]} className='mt-16'>
          <Col xs={{ span: 18 }}></Col>
        </Row>
        {priceFamilyId && (
          <Row gutter={[16, 0]} className='mt-3 mb-4'>
            <Col xs={{ span: 18 }}>
              <Form.Item labelCol={{ span: 24 }} wrapperCol={{ span: 8 }} className='long-label-item'>
                <Form.Item
                  name={[
                    'lineItems',
                    `${record?.serviceProviderId}`,
                    'prestation',
                    'tarifItems',
                    `${priceFamilyId}`,
                    'priceFamilyValue',
                  ]}
                  rules={[{ required: true }]}
                  noStyle
                >
                  <InputNumber
                    className='input-prix-unitaire'
                    style={{ width: '140px' }}
                    suffix='€'
                    formatter={(value) =>
                      value!
                        .toString()
                        .replace(/\s/g, '')
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                        .replace('.', ',')
                    }
                    parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                    onChange={handlePriceChange}
                  />
                </Form.Item>
              </Form.Item>
            </Col>
          </Row>
        )}
        {selectedPriceFamily?.prices?.map((tarif, indexTarif) => (
          <Fragment key={`${tarif?.id}-${indexTarif}`}>
            <Row gutter={[16, 0]} className='mt-4 mb-4'>
              <Col xs={{ span: 18 }}></Col>
            </Row>
            {tarif?.priceOptions?.map((priceOption, indexPriceOption) => (
              <Fragment key={`${priceOption?.id}-${indexPriceOption}`}>
                <Row gutter={[16, 0]} className='mt-3 mb-3'>
                  <Col xs={{ span: 18 }}>
                    {priceOption.priceTypeId === selectionPriceType?.id ? (
                      <>
                        <Form.Item
                          labelCol={{ span: 16 }}
                          wrapperCol={{ span: 8 }}
                          name={[
                            'lineItems',
                            `${record?.serviceProviderId}`,
                            'prestation',
                            'tarifItems',
                            'productLinePrices',
                            'priceOption',
                            `${priceOption?.id}`,
                            'priceSubOptionId',
                          ]}
                          rules={[{ required: true }]}
                        >
                          <Select
                            placeholder='Sélectionner'
                            className='select-price-sub-options long-label-item'
                            showSearch={false}
                            style={{ width: '140px' }}
                            options={priceOption?.PriceSubOptions?.map((priceSubOption) => ({
                              value: priceSubOption.id,
                              label: priceSubOption.name,
                            }))}
                          />
                        </Form.Item>
                        <Form.Item
                          className='hidden'
                          name={[
                            'lineItems',
                            `${record?.serviceProviderId}`,
                            'prestation',
                            'tarifItems',
                            'productLinePrices',
                            'priceOption',
                            `${priceOption?.id}`,
                            'priceSubOptionLabel',
                          ]}
                        ></Form.Item>
                      </>
                    ) : (
                      <Form.Item
                        labelCol={{ span: 16 }}
                        wrapperCol={{ span: 8 }}
                        name={[
                          'lineItems',
                          `${record?.serviceProviderId}`,
                          'prestation',
                          'tarifItems',
                          'productLinePrices',
                          'priceOption',
                          `${priceOption?.id}`,
                          'priceOptionValue',
                        ]}
                        rules={[{ required: true }]}
                        initialValue={priceOption.defaultValue}
                      >
                        {priceOption?.priceTypeId === numberPriceType?.id ? (
                          <InputNumber
                            className='input-prix-unitaire long-label-item'
                            style={{ width: '140px' }}
                            suffix={priceOption.unit}
                            formatter={(value) =>
                              value!
                                .toString()
                                .replace(/\s/g, '')
                                .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                                .replace('.', ',')
                            }
                            parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                          />
                        ) : (
                          <Input
                            className='input-text-prix-unitaire long-label-item'
                            style={{ width: '140px' }}
                            suffix={priceOption.unit}
                          />
                        )}
                      </Form.Item>
                    )}
                  </Col>
                </Row>
              </Fragment>
            ))}
            <Row gutter={[16, 0]} className='mt-2 mb-2'>
              <Col xs={{ span: 18 }}>
                <Form.Item
                  name={[
                    'lineItems',
                    `${record?.serviceProviderId}`,
                    'prestation',
                    'tarifItems',
                    'productLinePrices',
                    `${tarif?.id}`,
                    'priceValue',
                  ]}
                  rules={[{ required: true }]}
                  initialValue={
                    tarif?.PriceTypeLogic?.key === PRICE_TYPE_LOGICS.indicative_purpose ? tarif.priceValue : undefined
                  }
                  noStyle
                >
                  <InputNumber
                    className='input-prix-unitaire long-label-item'
                    style={{ width: '140px' }}
                    suffix='€'
                    formatter={(value) =>
                      value!
                        .toString()
                        .replace(/\s/g, '')
                        .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                        .replace('.', ',')
                    }
                    parser={(value: string | undefined) => value!.replace(/\s/g, '').replace(/,/g, '.')}
                    onChange={(value) => handlePriceChange(value)}
                  />
                </Form.Item>
              </Col>
            </Row>
          </Fragment>
        ))}
      </>
    );
  } else {
    content = (
      <>
        <Row gutter={[16, 0]} className='mt-10 mb-2'>
          <Col xs={{ span: 18 }}>
            <Form.Item
              name={['lineItems', `${record?.serviceProviderId}`, 'prestation', 'tarifItems', 'priceFamilyValue']}
              rules={[{ required: true }]}
              initialValue={productPriceValue}
            >
              <InputNumber
                className='input-prix-unitaire'
                style={{ width: '140px' }}
                suffix='€'
                onChange={handlePriceChange}
                formatter={(value) =>
                  value!
                    .toString()
                    .replace(/\s/g, '')
                    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                    .replace('.', ',')
                }
                parser={(value) => value!.replace(/\s/g, '').replace(/,/g, '.')}
              />
            </Form.Item>
          </Col>
        </Row>
      </>
    );
  }
  return (
    <section className='section mt-2 mb-4'>
      {content}
      <Row gutter={[16, 0]}>
        <Col xs={{ span: 18 }}>
          <Form.Item
            name={['lineItems', `${record?.serviceProviderId}`, 'prestation', 'tarifItems', 'prixUnitaire']}
            className='total-price'
            rules={[{ required: true }]}
          >
            <InputNumber
              className='input-prix-unitaire'
              style={{ width: '140px' }}
              suffix='€'
              onChange={handlePriceChange}
              formatter={(value) =>
                value!
                  .toString()
                  .replace(/\s/g, '')
                  .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
                  .replace('.', ',')
              }
              parser={(value) => value!.replace(/\s/g, '').replace(/,/g, '.')}
            />
          </Form.Item>
        </Col>
      </Row>
    </section>
  );
};

export default PriceValueCell;
