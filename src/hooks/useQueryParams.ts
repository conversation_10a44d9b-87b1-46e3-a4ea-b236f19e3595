import { useMemo, useRef } from 'react';
import { useSearchParams } from 'react-router-dom';
import { getExistProp } from '../utils';

type PaginationType = {
  page?: number;
  limit?: number;
  keyword?: string;
  urbanCenter?: number | null;
  zone?: number | null;
  productType?: number | null;
  tableType?: number | null;
  /* eslint-disable-next-line */
} & { [key: string]: any };
/* eslint-disable-next-line */
export default function useQueryParams<T>(): [query: T & PaginationType, onParamChange: Function] {
  const prevKeyword = useRef('');
  const [searchParams, setSearchParams] = useSearchParams();
  /* eslint-disable-next-line */
  const onParamChange = (param: any) => {
    if (prevKeyword.current !== param.keyword) {
      prevKeyword.current = param.keyword;
    }
    // reset to first page if the users input new keyword
    if (param.keyword && param.keyword !== prevKeyword) {
      param.page = 1;
    }
    setSearchParams(
      getExistProp({
        ...query,
        ...param,
      }),
    );
  };
  const query = useMemo(() => {
    /* eslint-disable-next-line */
    const result: { [key: string]: any } = Object.fromEntries([...searchParams]);
    if (result.page) {
      result.page = parseInt(result.page);
    } else {
      result.page = 1;
    }
    if (result.limit) {
      result.limit = parseInt(result.limit);
    } else {
      result.limit = 100;
    }
    if (result.productType) {
      result.productType = Number(result.productType);
    } else {
      result.productType = null;
    }
    if (result.urbanCenter) {
      result.urbanCenter = Number(result.urbanCenter);
    } else {
      result.urbanCenter = null;
    }
    if (result.zone) {
      result.zone = Number(result.zone);
    } else {
      result.zone = null;
    }
    if (result.tableType) {
      result.tableType = Number(result.tableType);
    } else {
      result.tableType = null;
    }
    return result;
  }, [searchParams]) as T & PaginationType;

  return [query, onParamChange];
}
