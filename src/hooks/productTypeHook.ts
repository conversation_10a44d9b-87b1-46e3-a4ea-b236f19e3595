import { useMemo } from 'react';
import { productService } from 'services';
import { QueryParams } from 'types';
import useRequest from './useRequest';
import { ProductType } from 'models';
import useQueryParams from './useQueryParams';
import useFetchByParams from './useFetchByParams';
import { fetchProductTypes, selectProductTypes, selectProductTypesLoading } from 'store/slices/product.slices';
import { RootState } from 'store';

export const useProductTypesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useProductTypes = (query: QueryParams) => {
  return useFetchByParams<ProductType[]>({
    action: fetchProductTypes,
    dataSelector: (state: RootState) => selectProductTypes(state),
    loadingSelector: (state: RootState) => selectProductTypesLoading(state),
    params: query,
  });
};

export const useCatalogPriceProductTypesQuery = (
  query?: QueryParams & {
    isCatalog: boolean;
  },
) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useCatalogPriceProductTypes = (query: QueryParams) => {
  const action = async () => {
    // const { isCatalog } = query;
    // let productTypeIdsInPrices: number[] = [];
    // if (isCatalog) {
    //   const catalogPricesResponse = await catalogPriceService.getCatalogPrices(
    //     {
    //       isCatalog
    //     }
    //   );
    //   // Extract productTypeIds from catalogPricesResponse
    //   productTypeIdsInPrices = catalogPricesResponse.map(
    //     (item) => item.productTypeId
    //   );
    // } else {
    //   const serviceProviderPricesResponse =
    //     await serviceProviderPriceService.getServiceProviderPrices({
    //       limit: "unlimited",
    //     });
    //   // Extract productTypeIds from serviceProviderPricesResponse
    //   productTypeIdsInPrices = serviceProviderPricesResponse.rows.map(
    //     (item) => item.productTypeId
    //   );
    // }
    delete query.isCatalog;
    const productTypesResponse = await productService.getProductTypes(query);
    // Filter productTypesResponse based on whether their id is included in productTypeIdsInPrices
    // const filteredProductTypes = productTypesResponse.rows.filter(
    //   (productType) => productTypeIdsInPrices.includes(productType.id)
    // );
    // return filteredProductTypes;
    // Feedback on ticket QBO-763
    return productTypesResponse.rows;
  };
  return useRequest<ProductType[]>({
    action,
    params: query,
    default: [],
  });
};

export const useProductTypeByIdQuery = (
  options: QueryParams & {
    productTypeId?: number | null;
  },
) => {
  const { productTypeId } = options;
  const [query] = useQueryParams<QueryParams>();
  return useMemo(() => {
    const queryParams = { ...query, ...options };

    return [queryParams];
  }, [productTypeId]);
};

export const useProductTypeById = (query: QueryParams) => {
  const action = async () => {
    const { productTypeId } = query;
    if (!productTypeId) {
      return null;
    }
    const response = await productService.findProductType(productTypeId, query);
    return response;
  };
  return useRequest<ProductType | null>({
    action,
    params: query,
    default: [],
  });
};
