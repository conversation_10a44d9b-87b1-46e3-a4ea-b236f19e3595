import useFetchByParams from 'hooks/useFetchByParams';
import { ServiceProvider } from 'models';
import { useMemo } from 'react';
import {
  fetchServiceProvidersByIds,
  selectServiceProvidersByIds,
  selectServiceProvidersByIdsLoading,
} from 'store/slices/service_provider.slices';
import { QueryParams } from 'types';

export const useServiceProvidersByIdsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useServiceProvidersByIds = (query: QueryParams) => {
  return useFetchByParams<ServiceProvider[]>({
    action: fetchServiceProvidersByIds,
    dataSelector: selectServiceProvidersByIds,
    loadingSelector: selectServiceProvidersByIdsLoading,
    params: query,
  });
};
