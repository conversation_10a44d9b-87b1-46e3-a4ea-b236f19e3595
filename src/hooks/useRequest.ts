import { useEffect, useState } from 'react';
import { Loading, QueryParams } from 'types';

type RequestByParamsProps = {
  action: Function;
  params?: any;
  default?: any;
  onError?: Function;
};

export default function useRequest<T>(
  props: RequestByParamsProps,
): [T, (options?: QueryParams) => Promise<any>, Loading, any] {
  const { action, params, onError } = props;
  const defaultValue = props.default as T;
  const [isLoading, setIsLoading] = useState<Loading>('idle');
  const [data, setData] = useState<T>(defaultValue);
  const [error, setError] = useState<any>(null);

  const fetch = async (options?: QueryParams) => {
    try {
      setIsLoading('pending');
      const result = await action(options);
      setData(result);
      setIsLoading('idle');
    } catch (error) {
      setIsLoading('idle');
      setError(error);
      onError && onError(error);
    }
  };

  useEffect(() => {
    if (params) fetch();
  }, [params]);

  return [data, fetch, isLoading, error];
}

// Type-safe config
type RequestConfig<TParams, TResponse> = {
  action: (params?: TParams) => Promise<TResponse>;
  params?: TParams;
  defaultValue: TResponse;
  onSuccess?: (data: TResponse) => void;
  onError?: (error: Error) => void;
  autoFetch?: boolean;
};

export function useRequestQuery<TParams extends QueryParams, TResponse>(
  config: RequestConfig<TParams, TResponse>,
): [TResponse, Loading, (params?: TParams) => Promise<TResponse>, Error | null] {
  const { action, params, defaultValue, onSuccess, onError, autoFetch = true } = config;
  const [isLoading, setIsLoading] = useState<Loading>('idle');
  const [data, setData] = useState<TResponse>(defaultValue);
  const [error, setError] = useState<Error | null>(null);

  const fetch = async (options?: TParams): Promise<TResponse> => {
    try {
      setIsLoading('pending');
      setError(null);
      const result = await action(options);
      setData(result);
      onSuccess?.(result);
      return result;
    } catch (err) {
      const error = err instanceof Error ? err : new Error('An error occurred');
      setError(error);
      onError?.(error);
      throw error;
    } finally {
      setIsLoading('succeeded');
    }
  };

  useEffect(() => {
    if (autoFetch && params) {
      fetch(params).catch(() => {
        // Error is already handled in fetch function
      });
    }
  }, [params]);

  return [data, isLoading, fetch, error];
}
