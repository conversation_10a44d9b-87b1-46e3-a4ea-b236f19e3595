import { useMemo } from 'react';
import {
  catalogPriceService,
  priceFamilyService,
  productService,
  regionService,
  serviceProviderService,
} from 'services';
import { QueryParams } from 'types';
import useRequest from '../useRequest';
import { Price, PriceFamily, PriceOption } from 'models';

type CatalogPriceZoneServiceProviderQueryParamsType = QueryParams & {
  serviceProviderZoneIds?: number[];
  products?: {
    productId?: number;
    priceFamilyId?: number;
    quantity?: number;
  }[];
};

export const useCatalogPriceZoneServiceProviderQuery = (query?: CatalogPriceZoneServiceProviderQueryParamsType) => {
  return useMemo(() => {
    return [{ ...query }];
  }, []);
};

export type CatalogPriceZoneSPPriceOption = PriceOption & {
  priceValue: string;
};

export type CatalogPriceZoneSPPrice = Price & {
  priceValue: string;
  priceOptions?: CatalogPriceZoneSPPriceOption[];
};

export type CatalogPriceZoneSPPriceFamily = PriceFamily & {
  priceValue: string;
  prices?: CatalogPriceZoneSPPrice[];
};

export type CombineCatalogPriceLineZoneServiceProvider = {
  serviceProviderId: number;
  serviceProviderName: string;
  formattedAddress: string;
  postalcode: string;
  products: {
    productId: number;
    productName: string;
    priceFamilies?: CatalogPriceZoneSPPriceFamily[];
    priceValue: string;
    selectedPriceFamilyId?: number | null;
    quantity?: number;
  }[];
  zones: {
    zoneId: number;
    zoneName: string;
  }[];
};

/**
 * Hook to fetch catalog price zone service providers based on query parameters.
 *
 * @param query - The query parameters for fetching catalog price zone service providers.
 * @returns A request object with the fetched data.
 */
export const useCatalogPriceZoneServiceProvider = (query: CatalogPriceZoneServiceProviderQueryParamsType) => {
  /**
   * Fetches catalog price zone service providers and associated data.
   *
   * @param options - Optional query parameters to merge with the original query.
   * @returns A promise resolving to an array of combined catalog price line zone service provider data.
   */
  const action = async (options?: CatalogPriceZoneServiceProviderQueryParamsType) => {
    try {
      const mergedQuery = { ...query, ...options };
      const { serviceProviderZoneIds = [], products = [] } = mergedQuery;

      // Return early if no zone IDs or products are provided
      if (!serviceProviderZoneIds.length && !products.length) return [];

      // Fetch catalog entries from the service
      const catalogEntries = await catalogPriceService.getCatalogPriceZoneServiceProviders({
        serviceProviderZoneIds,
        products: products.map((p) => ({ productId: p.productId, priceFamilyId: p.priceFamilyId })),
      });

      // Unique ID collections
      const productIds = new Set<number>();
      const zoneIds = new Set<number>();
      const serviceProviderIds = new Set<number>();

      // Add productIds from products array
      if (products && products.length > 0) {
        products.forEach((p) => {
          if (p.productId != null) productIds.add(p.productId);
        });
      }

      // Add zoneIds and serviceProviderIds from catalog entries
      catalogEntries.forEach(({ zoneId, serviceProviderId }) => {
        if (zoneId != null) zoneIds.add(zoneId);
        if (serviceProviderId != null) serviceProviderIds.add(serviceProviderId);
      });

      // Fetch data for products, zones, and service providers
      const [productsData, zonesData, providersData] = await Promise.all([
        productService.getProductsByIds({ 'id[]': JSON.stringify([...productIds]) }),
        regionService.getZonesByIds({ 'id[]': JSON.stringify([...zoneIds]) }),
        serviceProviderService.getServiceProvidersByIds({ 'id[]': JSON.stringify([...serviceProviderIds]) }),
      ]);

      const productTypeIds = productsData?.rows.map((p) => p.productTypeId);
      const priceFamiliesData = await priceFamilyService.getPriceFamilies({
        'productTypeId[]': JSON.stringify(productTypeIds),
        include: 'Prices.PriceOptions|Prices.PriceTypeLogic',
      });

      // Create maps for zones and providers for easy access
      const zoneMap = new Map(zonesData?.rows.map((z) => [z.id, z.name]));
      const providerMap = new Map(providersData?.rows.map((sp) => [sp.id, sp]));

      // Group entries by serviceProviderId
      const grouped: Record<number, { productIds: Set<number>; zoneIds: Set<number> }> = {};

      for (const entry of catalogEntries) {
        const { serviceProviderId, productId, zoneId } = entry;
        if (serviceProviderId == null) continue;

        if (!grouped[serviceProviderId]) {
          grouped[serviceProviderId] = { productIds: new Set(), zoneIds: new Set() };
        }
        if (productId != null) grouped[serviceProviderId].productIds.add(productId);
        if (zoneId != null) grouped[serviceProviderId].zoneIds.add(zoneId);
      }

      // Construct the final result array
      const result: CombineCatalogPriceLineZoneServiceProvider[] = Object.entries(grouped).map(
        ([spIdStr, { zoneIds }]) => {
          const spId = parseInt(spIdStr, 10);
          const provider = providerMap.get(spId);

          const serviceProviderName = provider?.name || '';
          const formattedAddress = provider?.formattedAddress || '';
          const postalcode = provider?.postalcode || '';

          const productsList =
            productsData?.rows.map((product) => {
              const priceFamilies: CatalogPriceZoneSPPriceFamily[] = priceFamiliesData
                .filter((pf) => pf.productTypeId === product.productTypeId)
                .map((pf) => {
                  const prices: CatalogPriceZoneSPPrice[] = (pf.Prices || []).map((price) => {
                    const catalogEntry = catalogEntries.find(
                      (e) =>
                        e.productId === product.id &&
                        e.priceFamilyId === pf.id &&
                        e.priceId === price.id &&
                        e.serviceProviderId === spId,
                    );

                    // Map price options with their values
                    const priceOptions: CatalogPriceZoneSPPriceOption[] = (price.PriceOptions || []).map((po) => {
                      const entryWithOption = catalogEntries.find(
                        (e) =>
                          e.productId === product.id &&
                          e.priceOptionId === po.id &&
                          e.priceFamilyId === pf.id &&
                          e.priceId === price.id &&
                          e.serviceProviderId === spId,
                      );

                      return {
                        ...po,
                        priceValue: entryWithOption?.priceValue ?? '',
                      };
                    });

                    return {
                      ...price,
                      priceValue: catalogEntry?.priceValue ?? '',
                      priceOptions,
                    };
                  });

                  return {
                    ...pf,
                    priceValue:
                      catalogEntries.find(
                        (e) => e.productId === product.id && e.priceFamilyId === pf.id && e.serviceProviderId === spId,
                      )?.priceValue ?? '',
                    prices,
                  };
                });

              const basePriceValue =
                catalogEntries.find((e) => e.productId === product.id && e.serviceProviderId === spId)?.priceValue ??
                '';

              return {
                productId: product.id,
                productName: product.name,
                priceFamilies,
                priceValue: basePriceValue,
                selectedPriceFamilyId: products.find((p) => p.productId === product.id)?.priceFamilyId,
                quantity: products.find((p) => p.productId === product.id)?.quantity,
              };
            }) ?? [];

          // Map zones with their names
          const zones = [...zoneIds]
            .map((id) => ({
              zoneId: id,
              zoneName: zoneMap.get(id) || '',
            }))
            .sort((a, b) => a.zoneName.localeCompare(b.zoneName));

          return {
            serviceProviderId: spId,
            serviceProviderName,
            formattedAddress,
            postalcode,
            products: productsList,
            zones,
          };
        },
      );

      return result;
    } catch (error) {
      console.error('useCatalogPriceZoneServiceProvider error:', error);
      return [];
    }
  };

  return useRequest<CombineCatalogPriceLineZoneServiceProvider[]>({
    action,
    params: query,
    default: [],
  });
};
