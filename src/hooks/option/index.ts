import useFetchByParams from 'hooks/useFetchByParams';
import { useRequestQuery } from 'hooks/useRequest';
import { Option } from 'models';
import { useMemo } from 'react';
import { optionService } from 'services';
import { fetchAllOptions, selectAllOptions, selectOptionLoading } from 'store/slices/option.slices';
import { QueryParams } from 'types';

export const useOptionsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, isActive: 1 };

    return [queryParams];
  }, []);
};

export const useOptions = (query: QueryParams) => {
  return useFetchByParams<Option[]>({
    action: fetchAllOptions,
    dataSelector: selectAllOptions,
    loadingSelector: selectOptionLoading,
    params: query,
  });
};

export const useOptionsRequest = (initialParams?: QueryParams) => {
  const action = async (params?: QueryParams): Promise<Option[]> => {
    if (params && 'productTypeId[]' in params) {
      const response = await optionService.getOptions({ ...params, isActive: 1 });
      return response;
    }
    return [];
  };

  return useRequestQuery<QueryParams, Option[]>({
    action: action,
    defaultValue: [],
    params: initialParams,
    autoFetch: true,
  });
};
