import { PaginationData, QueryParams } from "types";
import useQueryParams from "./useQueryParams";
import { useMemo } from "react";
import { serviceProviderPriceLineProductService } from "services";
import useRequest from "./useRequest";
import { ServiceProviderPriceLineProduct } from "models";

export const useServiceProviderPriceLineProductsQuery = (
  options: QueryParams & {
    serviceProviderId: number;
    serviceProviderPriceLineId?: number;
  }
) => {
  const { serviceProviderId, serviceProviderPriceLineId } = options;
  return useMemo(() => {
    const queryParams = { ...options };

    return [queryParams];
  }, [serviceProviderId, serviceProviderPriceLineId]);
};

export const useServiceProviderPriceLineProducts = (query: any) => {
  const action = async () => {
    const { serviceProviderId } = query;
    delete query.serviceProviderId;
    const response = serviceProviderId
      ? await serviceProviderPriceLineProductService.getServiceProviderPriceLineProducts(
          serviceProviderId,
          query
        )
      : { count: 0, rows: [] };
    return response;
  };
  return useRequest<PaginationData<ServiceProviderPriceLineProduct>>({
    action,
    params: query,
    default: [],
  });
};
