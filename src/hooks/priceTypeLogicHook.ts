import { useMemo } from 'react';
import { QueryParams } from 'types';
import { PriceTypeLogic } from 'models';
import useFetchByParams from './useFetchByParams';
import { fetchPriceTypeLogics, selectPriceTypeLogics } from 'store/slices/price.slices';

export const usePriceTypeLogicQuery = (
  query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const usePriceTypeLogics = (query: any) => {
   return useFetchByParams<PriceTypeLogic[]>({
    action: fetchPriceTypeLogics,
    dataSelector: selectPriceTypeLogics,
    params: query,
  });
};
