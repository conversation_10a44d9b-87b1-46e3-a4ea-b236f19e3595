import useFetchByParams from 'hooks/useFetchByParams';
import { PriceType, ProductTypeRegul } from 'models';
import { useMemo } from 'react';
import { productTypeRegulService } from 'services';
import { fetchProductTypeReguls, selectProductTypeReguls } from 'store/slices/product_type_regul.slices';
import { QueryParams } from 'types';
import { useRequestQuery } from './useRequest';

export const useProductTypeRegulsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useProductTypeReguls = (query: QueryParams) => {
  return useFetchByParams<PriceType[]>({
    action: fetchProductTypeReguls,
    dataSelector: selectProductTypeReguls,
    params: query,
  });
};

export const useProductTypeRegulRequest = (initialParams?: QueryParams) => {
  const action = async (params?: QueryParams): Promise<ProductTypeRegul[]> => {
    if (params && ('productId[]' in params || params.productId)) {
      const response = await productTypeRegulService.getProductTypeRegul({ ...params });
      return response.rows;
    }
    return [];
  };

  return useRequestQuery<QueryParams, ProductTypeRegul[]>({
    action: action,
    defaultValue: [],
    params: initialParams,
    autoFetch: true,
  });
};
