import { useMemo } from 'react';
import { QueryParams } from 'types';
import useRequest from './useRequest';
import { catalogPriceUrbanCenterService, urbanCenterService } from 'services';
import { UrbanCenter } from 'models';
import useQueryParams from './useQueryParams';

export type UrbanCenterQueryParams = QueryParams & {
  regionId?: number;
  serviceProviderId?: number;
};
export const useUrbanCentersQuery = (options: UrbanCenterQueryParams) => {
  const { regionId } = options;
  const [query] = useQueryParams<QueryParams>();
  return useMemo(() => {
    const queryParams = { ...query, ...options };

    return [queryParams];
  }, [regionId]);
};

export const useUrbanCenters = (query: UrbanCenterQueryParams) => {
  const action = async () => {
    const { regionId, serviceProviderId, isActive } = query;
    const response = await urbanCenterService.getUrbanCenters(regionId, { serviceProviderId, isActive });
    return response;
  };
  return useRequest<UrbanCenter[]>({
    action,
    params: query,
    default: [],
  });
};

export const useUrbanCentersByCatalogPriceQuery = (
  options: QueryParams & {
    regionId: number;
    yearId?: number;
  },
) => {
  const { regionId, yearId } = options;
  const [query] = useQueryParams<QueryParams>();
  return useMemo(() => {
    const queryParams = { ...query, ...options };

    return [queryParams];
  }, [regionId, yearId]);
};

export const useUrbanCentersByCatalogPrice = (query: QueryParams) => {
  const action = async () => {
    const { regionId, yearId } = query;
    const urbanCenterResponse = await urbanCenterService.getUrbanCenters(regionId);
    const catalogPriceUrbanCenterResponse = yearId
      ? await catalogPriceUrbanCenterService.getCatalogPriceUrbanCenters({
          yearId,
          limit: 'unlimited',
        })
      : { count: 0, rows: [] };
    const urbanCenterIdsInCatalogPrices = catalogPriceUrbanCenterResponse.rows.map((item) => item.urbanCenterId);
    const filteredUrbanCenters = urbanCenterResponse.filter(
      (urbanCenter) => urbanCenter?.id && urbanCenterIdsInCatalogPrices.includes(urbanCenter.id),
    );
    return filteredUrbanCenters;
  };
  return useRequest<UrbanCenter[]>({
    action,
    params: query,
    default: [],
  });
};
