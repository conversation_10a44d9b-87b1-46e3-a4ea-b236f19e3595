import { useState, useCallback } from 'react';
import { providerInvoiceService } from 'services';
import { Loading, QueryParams } from 'types';
import { Invoice } from 'types';

/**
 * Hook for fetching invoice details by provider
 * Follows the same pattern as other service hooks in the project
 */
export const useProviderInvoiceDetails = () => {
  const [data, setData] = useState<any[]>([]);
  const [loading, setLoading] = useState<Loading>('idle');
  const [error, setError] = useState<string | null>(null);

  const fetchInvoiceDetailsEdit = useCallback(async (params: { prestataire?: string; [key: string]: any }) => {
    try {
      setLoading('pending');
      setError(null);
      
      const response = await providerInvoiceService.getInvoiceDetailsByProviderIdEdit(params);
      setData(response.rows);
      setLoading('succeeded');
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch invoice details';
      setError(errorMessage);
      setLoading('failed');
      throw err;
    }
  }, []);

  const fetchAvailableDetailsCreate = useCallback(async (params?: QueryParams) => {
    try {
      setLoading('pending');
      setError(null);

      const response = await providerInvoiceService.getInvoiceLinesByProviderIdCreate(params || {});
      setData(response.rows);
      setLoading('succeeded');
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch available invoice details';
      setError(errorMessage);
      setLoading('failed');
      throw err;
    }
  }, []);

  return {
    data,
    loading,
    error,
    fetchInvoiceDetailsEdit,
    fetchAvailableDetailsCreate
  };
};

/**
 * Hook for saving provider invoices
 */
export const useProviderInvoiceSave = () => {
  const [loading, setLoading] = useState<Loading>('idle');
  const [error, setError] = useState<string | null>(null);

  // const saveInvoice = useCallback(async (data: {
  //   currentInvoice: Invoice;
  //   selectedLines: any[];
  //   comments: any[];
  //   avoirs: any[];
  //   status: string;
  //   totalSelectedAmount: number;
  //   [key: string]: any;
  // }) => {
  //   try {
  //     setLoading('pending');
  //     setError(null);
      
  //     const response = await providerInvoiceService.saveProviderInvoice(data);
  //     setLoading('succeeded');
      
  //     return response;
  //   } catch (err) {
  //     const errorMessage = err instanceof Error ? err.message : 'Failed to save invoice';
  //     setError(errorMessage);
  //     setLoading('failed');
  //     throw err;
  //   }
  // }, []);

  // const updateStatus = useCallback(async (invoiceId: number | string, status: string) => {
  //   try {
  //     setLoading('pending');
  //     setError(null);
      
  //     const response = await providerInvoiceService.updateInvoiceStatus(invoiceId, status);
  //     setLoading('succeeded');
      
  //     return response;
  //   } catch (err) {
  //     const errorMessage = err instanceof Error ? err.message : 'Failed to update invoice status';
  //     setError(errorMessage);
  //     setLoading('failed');
  //     throw err;
  //   }
  // }, []);

  // const addComment = useCallback(async (invoiceId: number | string, comment: string) => {
  //   try {
  //     setLoading('pending');
  //     setError(null);
      
  //     const response = await providerInvoiceService.addInvoiceComment(invoiceId, comment);
  //     setLoading('succeeded');
      
  //     return response;
  //   } catch (err) {
  //     const errorMessage = err instanceof Error ? err.message : 'Failed to add comment';
  //     setError(errorMessage);
  //     setLoading('failed');
  //     throw err;
  //   }
  // }, []);

  // const addAvoir = useCallback(async (invoiceId: number | string, avoir: { amount: number; avoirNumber: string }) => {
  //   try {
  //     setLoading('pending');
  //     setError(null);
      
  //     const response = await providerInvoiceService.addInvoiceAvoir(invoiceId, avoir);
  //     setLoading('succeeded');
      
  //     return response;
  //   } catch (err) {
  //     const errorMessage = err instanceof Error ? err.message : 'Failed to add avoir';
  //     setError(errorMessage);
  //     setLoading('failed');
  //     throw err;
  //   }
  // }, []);

  return {
    loading,
    error,
    // saveInvoice,
    // updateStatus,
    // addComment,
    // addAvoir
  };
};

/**
 * Combined hook for all provider invoice operations
 */
export const useProviderInvoice = () => {
  const detailsHook = useProviderInvoiceDetails();
  const saveHook = useProviderInvoiceSave();

  return {
    ...detailsHook,
    ...saveHook
  };
};

// Export the table management hook
export { useProviderInvoiceTable } from './useProviderInvoiceTable';
