import { useCallback, useMemo, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
  setInvoice,
  setFormFilters,
  toggleDisplayFilter,
  toggleLineSelection,
  updateInvoiceTotals,
  addCommentToLine,
  removeCommentFromLine,
  clearInvoiceState,
  selectCurrentInvoice,
  selectOriginalInvoice,
  selectFormFilters,
  selectDisplayFilters,
  selectAllLines,
  selectSelectedLines,
  selectIsFiltering,
  selectIsInitialLoad,
} from 'store/slices/provider_invoice.slices';
import { extractServiceLines } from 'utils/invoiceTransformer';
import { Invoice } from 'types';

/**
 * Custom hook for managing ProviderInvoiceCreate table state and logic
 * 
 * This hook centralizes all table-related operations:
 * - Filtering (form filters + display filters)
 * - Row span calculations
 * - Line selection
 * - Data updates
 * 
 * Benefits:
 * - Keeps component clean and focused on UI
 * - Centralizes complex table logic
 * - Makes state management predictable via Redux
 * - Easier to test and maintain
 * - Reusable across components if needed
 */
export const useProviderInvoiceTable = () => {
  const dispatch = useAppDispatch();

  // Redux selectors
  const currentInvoice = useAppSelector(selectCurrentInvoice);
  const originalInvoice = useAppSelector(selectOriginalInvoice);
  const formFilters = useAppSelector(selectFormFilters);
  const displayFilters = useAppSelector(selectDisplayFilters);
  const allLines = useAppSelector(selectAllLines);
  const selectedLines = useAppSelector(selectSelectedLines);
  const isFiltering = useAppSelector(selectIsFiltering);
  const isInitialLoad = useAppSelector(selectIsInitialLoad);

  // Extract service lines from Invoice for backward compatibility
  const serviceLines = useMemo(() => {
    return currentInvoice ? extractServiceLines(currentInvoice) : [];
  }, [currentInvoice]);

  const originalServiceLines = useMemo(() => {
    return originalInvoice ? extractServiceLines(originalInvoice) : [];
  }, [originalInvoice]);



  // Debug: Hook state (only when data changes)
  if (serviceLines.length > 0 && currentInvoice && !isInitialLoad) {
    console.log(`🎣 Hook: ${serviceLines.length} lines | ${selectedLines.length} selected | ${currentInvoice.prestataire}`);
  }

  // ============================================================================
  // ROW SPAN CALCULATION LOGIC
  // ============================================================================
  
  /**
   * Recalculates grouping flags for filtered data to ensure proper row spans
   */
  const recalculateGroupingFlags = useCallback((lines: any[]) => {
    console.log('🔄 Recalculating grouping flags for', lines.length, 'lines');
    
    if (!lines || lines.length === 0) {
      console.log('📭 No lines to recalculate, returning empty array');
      return [];
    }

    // Group lines by detailId
    const groupedByDetail: { [key: string]: any[] } = {};
    lines.forEach((line, index) => {
      const detailId = line.detailId;
      if (!detailId) {
        console.warn(`⚠️ Line at index ${index} missing detailId:`, line.key);
        return;
      }
      
      if (!groupedByDetail[detailId]) {
        groupedByDetail[detailId] = [];
      }
      groupedByDetail[detailId].push(line);
    });

    // Recalculate flags for each group
    const recalculatedLines: any[] = [];
    Object.entries(groupedByDetail).forEach(([detailId, detailLines]) => {
      detailLines.forEach((line, index) => {
        const updatedLine = {
          ...line,
          isFirstLineOfDetail: index === 0,
          totalLinesInDetail: detailLines.length
        };
        recalculatedLines.push(updatedLine);
      });
    });

    console.log('✅ Recalculation complete:', {
      originalLines: lines.length,
      recalculatedLines: recalculatedLines.length,
      detailGroups: Object.keys(groupedByDetail).length
    });

    return recalculatedLines;
  }, []);

  // ============================================================================
  // FILTERING LOGIC
  // ============================================================================
  
  /**
   * Apply form filters to original data
   */
  const applyFilters = useCallback((filters: any) => {
    console.log('🔍 Applying filters:', filters);
    
    // If no filters, return original data
    if (!filters.date && !filters.commandeNumber && !filters.siteAddress && !filters.product) {
      console.log('📭 No filters applied, using original data');
      // In Invoice-based approach, filtering is handled in the finalTableData calculation
      return;
    }

    // Apply filters
    const filtered = originalServiceLines.filter((line, index) => {
      try {
        // Date filter
        let dateMatch = true;
        if (filters.date) {
          dateMatch = false;
          if (line.prestationDetails && Array.isArray(line.prestationDetails)) {
            const filterDateStr = new Date(filters.date).toISOString().split('T')[0];
            dateMatch = line.prestationDetails.some((detail: any) => {
              if (!detail?.date) return false;
              const detailDateStr = new Date(detail.date).toISOString().split('T')[0];
              return detailDateStr === filterDateStr;
            });
          }
        }

        // Command number filter
        let commandeMatch = true;
        if (filters.commandeNumber && filters.commandeNumber.trim()) {
          const searchTerm = filters.commandeNumber.toLowerCase().trim();
          const fieldsToCheck = [
            line.commandNumber,
            line.invoiceNumber,
            line.orderId,
            line.zohoId,
            line.commandId
          ];
          commandeMatch = fieldsToCheck.some(field => {
            if (!field) return false;
            return String(field).toLowerCase().includes(searchTerm);
          });
        }

        // Site address filter
        let addressMatch = true;
        if (filters.siteAddress && filters.siteAddress.trim()) {
          const searchTerm = filters.siteAddress.toLowerCase().trim();
          const addressFields = [line.siteAddress, line.sitePostalCode, line.siteCity];
          addressMatch = addressFields.some(field => {
            if (!field) return false;
            return String(field).toLowerCase().includes(searchTerm);
          });
        }

        // Product filter
        let productMatch = true;
        if (filters.product) {
          if (typeof filters.product === 'string' && filters.product.trim()) {
            const searchTerm = filters.product.toLowerCase().trim();
            const productFields = [line.detailProductName, line.productName];
            productMatch = productFields.some(field => {
              if (!field) return false;
              return String(field).toLowerCase().includes(searchTerm);
            });
          } else {
            productMatch = line.productId === filters.product || 
                          line.detailProductId === filters.product ||
                          String(line.productId) === String(filters.product);
          }
        }

        return dateMatch && commandeMatch && addressMatch && productMatch;
      } catch (error) {
        console.error('❌ Error filtering line:', error, line);
        return false;
      }
    });

    console.log(`🎯 Filtering results: ${filtered.length} / ${originalServiceLines.length} lines match`);

    // Filtering is now handled in finalTableData calculation - no need to store filtered results
  }, [originalServiceLines]);

  // ============================================================================
  // FINAL TABLE DATA WITH DISPLAY FILTERS
  // ============================================================================
  
  /**
   * Calculate final table data with display filters applied
   *
   * NEW LOGIC:
   * - showSelectedRows ON: Show ALL lines INCLUDING selected
   * - showSelectedRows OFF: Show ALL lines EXCEPT selected (hide selected)
   * - showInvoicedRows ON: Show ALL lines INCLUDING invoiced
   * - showInvoicedRows OFF: Show ALL lines EXCEPT invoiced (hide invoiced)
   */
  const finalTableData = useMemo(() => {
    console.log('🎯 Calculating final table data with filters...');
    console.log('📊 Input data:', {
      serviceLinesCount: serviceLines.length,
      selectedLinesCount: selectedLines.length,
      displayFilters,
      formFilters
    });

    // Step 1: Apply form filters first
    let finalLines = serviceLines;

    if (formFilters && Object.keys(formFilters).length > 0) {
      console.log('🔍 Applying form filters:', formFilters);

      finalLines = serviceLines.filter((line) => {
        try {
          // Date filter
          let dateMatch = true;
          if (formFilters.date) {
            dateMatch = false;
            if (line.prestationDetails && Array.isArray(line.prestationDetails)) {
              const filterDateStr = new Date(formFilters.date).toISOString().split('T')[0];
              dateMatch = line.prestationDetails.some((detail: any) => {
                if (!detail?.date) return false;
                const detailDateStr = new Date(detail.date).toISOString().split('T')[0];
                return detailDateStr === filterDateStr;
              });
            }
          }

          // Command number filter
          let commandeMatch = true;
          if (formFilters.commandeNumber && formFilters.commandeNumber.trim()) {
            const searchTerm = formFilters.commandeNumber.toLowerCase().trim();
            const fieldsToCheck = [
              line.commandNumber,
              line.orderId,
              line.commandId
            ];
            commandeMatch = fieldsToCheck.some(field => {
              if (!field) return false;
              return String(field).toLowerCase().includes(searchTerm);
            });
          }

          // Site address filter
          let addressMatch = true;
          if (formFilters.siteAddress && formFilters.siteAddress.trim()) {
            const searchTerm = formFilters.siteAddress.toLowerCase().trim();
            const addressFields = [
              line.siteAddress,
              line.siteCity,
              line.sitePostalCode
            ];
            addressMatch = addressFields.some(field => {
              if (!field) return false;
              return String(field).toLowerCase().includes(searchTerm);
            });
          }

          // Product filter
          let productMatch = true;
          if (formFilters.product) {
            if (typeof formFilters.product === 'string' && formFilters.product.trim()) {
              const searchTerm = formFilters.product.toLowerCase().trim();
              const productFields = [line.detailProductName, line.productName];
              productMatch = productFields.some(field => {
                if (!field) return false;
                return String(field).toLowerCase().includes(searchTerm);
              });
            } else {
              productMatch = line.productId === formFilters.product ||
                            line.detailProductId === formFilters.product ||
                            String(line.productId) === String(formFilters.product);
            }
          }

          return dateMatch && commandeMatch && addressMatch && productMatch;
        } catch (error) {
          console.error('❌ Error filtering line:', error, line);
          return false;
        }
      });

      console.log(`🔍 Form filters applied: ${finalLines.length}/${serviceLines.length} lines match`);
    }

    // Step 2: Apply display filters

    // Apply "show selected" filter
    if (displayFilters.showSelectedRows === false) {
      // OFF: Hide selected lines (show all EXCEPT selected)
      console.log('🚫 Hiding selected lines...');
      const selectedKeys = selectedLines.map(line => line.key);
      const beforeCount = finalLines.length;
      finalLines = finalLines.filter(line => !selectedKeys.includes(line.key));
      console.log('   - Filtered out', beforeCount - finalLines.length, 'selected lines');

      // Recalculate row spans after filtering
      finalLines = recalculateGroupingFlags(finalLines);
    }
    // If ON: Show all lines including selected (no filtering needed)

    // Apply "show invoiced" filter
    if (displayFilters.showInvoicedRows === false) {
      // OFF: Hide invoiced lines (show all EXCEPT invoiced)
      console.log('🚫 Hiding invoiced lines...');
      const beforeCount = finalLines.length;
      finalLines = finalLines.filter(line => !line.isInvoiced);
      console.log('   - Filtered out', beforeCount - finalLines.length, 'invoiced lines');

      // Recalculate row spans after filtering
      finalLines = recalculateGroupingFlags(finalLines);
    }
    // If ON: Show all lines including invoiced (no filtering needed)

    console.log('✅ Final table data ready with NEW logic:', {
      originalCount: serviceLines.length,
      finalCount: finalLines.length,
      selectedFilterState: displayFilters.showSelectedRows ? 'SHOW_ALL_INCLUDING_SELECTED' : 'HIDE_SELECTED',
      invoicedFilterState: displayFilters.showInvoicedRows ? 'SHOW_ALL_INCLUDING_INVOICED' : 'HIDE_INVOICED',
      filteredOut: serviceLines.length - finalLines.length
    });

    return finalLines;
  }, [serviceLines, displayFilters, selectedLines, formFilters, recalculateGroupingFlags]);

  // Force re-render when serviceLines change (for comments, price updates, etc.)
  useEffect(() => {
    console.log('🔄 Hook: serviceLines updated, finalTableData should recalculate');
  }, [serviceLines]);

  // ============================================================================
  // PUBLIC API
  // ============================================================================
  
  return {
    // State
    currentInvoice,
    originalInvoice,
    originalServiceLines,
    serviceLines,
    formFilters,
    displayFilters,
    selectedLines,
    isFiltering,
    isInitialLoad,
    finalTableData,
    
    // Actions
    setInvoice: useCallback((invoice: Invoice) => {
      console.log('🎣 Hook: setInvoice called');
      dispatch(setInvoice(invoice));
    }, [dispatch]),

    applyFormFilters: useCallback((filters: any) => {
      console.log('🎣 Hook: applyFormFilters called with:', filters);
      dispatch(setFormFilters(filters));
      applyFilters(filters);
    }, [dispatch, applyFilters]),

    toggleDisplayFilter: useCallback((filterType: 'showSelectedRows' | 'showInvoicedRows', value: boolean) => {
      console.log('🎣 Hook: toggleDisplayFilter called:', { filterType, value });
      dispatch(toggleDisplayFilter({ filterType, value }));
    }, [dispatch]),

    toggleLineSelection: useCallback((lineKey: string) => {
      console.log('🎣 Hook: toggleLineSelection called for line:', lineKey);
      console.log('🎣 Hook: Current selected lines before toggle:', [...selectedLines]);
      dispatch(toggleLineSelection(lineKey));
    }, [dispatch, selectedLines]),

    addCommentToLine: useCallback((lineKey: string, commentText: string, author?: string) => {
      console.log('🎣 Hook: addCommentToLine called:', { lineKey, commentText, author });
      const comment = {
        text: commentText,
        author: author || 'Current User'
      };
      dispatch(addCommentToLine({ lineKey, comment }));
    }, [dispatch]),

    removeCommentFromLine: useCallback((lineKey: string, commentId: number) => {
      console.log('🎣 Hook: removeCommentFromLine called:', { lineKey, commentId });
      dispatch(removeCommentFromLine({ lineKey, commentId }));
    }, [dispatch]),

    updateInvoiceTotals: useCallback((totalSelected: number, totalAvoirs: number) => {
      console.log('🎣 Hook: updateInvoiceTotals called:', { totalSelected, totalAvoirs });
      dispatch(updateInvoiceTotals({ totalSelected, totalAvoirs }));
    }, [dispatch]),

    clearInvoiceState: useCallback(() => {
      console.log('🎣 Hook: clearInvoiceState called');
      dispatch(clearInvoiceState());
    }, [dispatch]),
  };
};
