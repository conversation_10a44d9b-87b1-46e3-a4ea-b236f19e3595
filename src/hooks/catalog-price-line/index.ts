import { useMemo } from 'react';
import { catalogPriceLineService, productService } from 'services';
import { PaginationData, QueryParams } from 'types';
import useRequest from '../useRequest';
import { CatalogPrice, CatalogPriceLine } from 'models';
import useQueryParams from '../useQueryParams';

type CatalogPriceLineQueryParamsType = QueryParams & {
  urbanCenterId?: number | null;
  year?: number | null;
};
export const useCatalogPriceLinesQuery = (options?: CatalogPriceLineQueryParamsType) => {
  const [params, onParamChange] = useQueryParams<CatalogPriceLineQueryParamsType>();

  // console.log(params);
  const filteredParams = useMemo(() => {
    return {
      ...params,
      ...options,
      isActive: 1,
      include: [
        'CatalogPrices.PriceFamily',
        'CatalogPrices.PriceOption',
        'CatalogPrices.PriceOption.PriceSubOptions',
        'CatalogPrices.Price',
        'CatalogPriceLineZones',
        'CatalogPriceLineSubOptionZones',
        'CatalogPriceLineProducts',
        'CatalogPriceLinesZoneCheckeds',
      ].join('|'),
    };
  }, [params]);

  return [filteredParams, onParamChange];
};

export type CombineCatalogPriceLine = CatalogPriceLine & {
  rowSpan?: number;
  isLast?: boolean;
  isFirst?: boolean;
  isDisplay?: boolean;
};
export const useCatalogPriceLines = (query: CatalogPriceLineQueryParamsType) => {
  const action = async (options?: CatalogPriceLineQueryParamsType) => {
    try {
      if (!query.urbanCenterId || !query.yearId) {
        return [];
      }
      const queryParams = { ...query, ...options };
      const catalogPriceLines = await catalogPriceLineService.getCatalogPriceLines(queryParams);
      const productIds = [
        ...new Set(catalogPriceLines?.rows?.flatMap((i) => i.CatalogPriceLineProducts?.map((p) => p.productId))),
      ];
      const products = productIds?.length
        ? await productService.getProducts({
            'id[]': JSON.stringify(productIds),
            limit: 'unlimited',
            isActive: 1,
            exclude: 0,
          })
        : { rows: [] };

      // mapping data
      catalogPriceLines.rows = (catalogPriceLines?.rows || []).flatMap((item) => {
        const result = {
          ...item,
          CatalogPrices: item.CatalogPrices?.sort(sortCatalogPrices),
          CatalogPriceLineProducts: item.CatalogPriceLineProducts?.map((lineProduct) => ({
            ...lineProduct,
            Product: products.rows.find((p) => p.id === lineProduct.productId),
          })),
        } as CatalogPriceLine;
        return result;
      });
      // extract data to multiple lines
      catalogPriceLines.rows = catalogPriceLines.rows.flatMap((item) => {
        const catalogPrices = item.CatalogPrices;
        let extractedCatalogPriceLines: CombineCatalogPriceLine[] = [];
        if (!catalogPrices?.length) return [];
        catalogPrices?.forEach((catalogPrice) => {
          if (catalogPrice.PriceOption?.PriceSubOptions?.length || catalogPrice.priceId || catalogPrice.priceOptionId) {
            const result = catalogPrice.PriceOption?.PriceSubOptions?.length
              ? catalogPrice.PriceOption.PriceSubOptions.map((subOption) => ({
                  ...item,
                  rowSpan: 0,
                  isFirst: false,
                  isLast: false,
                  isDisplay: true,
                  CatalogPrices: [{ ...catalogPrice, PriceSubOption: subOption }],
                }))
              : ([
                  {
                    ...item,
                    CatalogPrices: [catalogPrice],
                    rowSpan: 0,
                    isFirst: false,
                    isLast: false,
                    isDisplay: true,
                  },
                ] as CombineCatalogPriceLine[]);

            // add CatalogPriceLine for Price and PriceFamily if not exist and set isDisplay to true
            if (catalogPrice.PriceOption?.PriceSubOptions?.length || catalogPrice.priceOptionId) {
              const isExistPrice = extractedCatalogPriceLines.find(
                (i) =>
                  i.CatalogPrices?.[0]?.priceFamilyId === catalogPrice.priceFamilyId &&
                  i.CatalogPrices?.[0]?.priceId === catalogPrice.priceId &&
                  !i.CatalogPrices?.[0]?.priceOptionId,
              );
              if (!isExistPrice) {
                result.unshift(cloneCatalogPriceLine(item, catalogPrice, 'Price'));
              }
            }
            const isExistPriceFamily = extractedCatalogPriceLines.find(
              (i) =>
                i.CatalogPrices?.[0]?.priceFamilyId === catalogPrice.priceFamilyId &&
                !i.CatalogPrices?.[0]?.priceId &&
                !i.CatalogPrices?.[0]?.priceOptionId,
            );
            if (!isExistPriceFamily) {
              result.unshift(cloneCatalogPriceLine(item, catalogPrice, 'PriceFamily'));
            }
            extractedCatalogPriceLines = extractedCatalogPriceLines.concat(result);
          } else {
            const result = [
              { ...item, CatalogPrices: [catalogPrice], rowSpan: 0, isFirst: false, isLast: false, isDisplay: true },
            ];
            extractedCatalogPriceLines = extractedCatalogPriceLines.concat(result);
          }
        });
        extractedCatalogPriceLines[0].rowSpan = extractedCatalogPriceLines.length;
        extractedCatalogPriceLines[0].isFirst = true;
        extractedCatalogPriceLines[extractedCatalogPriceLines.length - 1].isLast = true;
        return extractedCatalogPriceLines;
      });
      return catalogPriceLines;
    } catch (error) {
      console.log('useCatalogPriceLines', error);
    }
  };
  return useRequest<PaginationData<CatalogPriceLine>>({
    action,
    params: query,
    default: [],
  });
};

const sortCatalogPrices = (a: CatalogPrice, b: CatalogPrice) => {
  const nameA = `${a.priceFamilyId}-${a.priceId ?? ' '}-${a.priceOptionId ?? ' '}`;
  const nameB = `${b.priceFamilyId}-${b.priceId ?? ' '}-${b.priceOptionId ?? ' '}`;
  if (nameA < nameB) {
    return -1;
  }
  if (nameA > nameB) {
    return 1;
  }
  return 0;
};

const cloneCatalogPriceLine = (
  item: CatalogPriceLine,
  catalogPrice: CatalogPrice,
  type: 'Price' | 'PriceFamily',
): CombineCatalogPriceLine => {
  if (type === 'Price') {
    return {
      ...item,
      CatalogPrices: [
        {
          ...catalogPrice,
          PriceSubOption: null,
          priceOptionId: null,
        },
      ],
      rowSpan: 0,
      isDisplay: false,
      isFirst: false,
      isLast: false,
    };
  }
  return {
    ...item,
    CatalogPrices: [
      {
        ...catalogPrice,
        PriceSubOption: null,
        priceOptionId: null,
        PriceOption: null,
        priceId: null,
        Price: null,
      },
    ],
    rowSpan: 0,
    isDisplay: false,
    isFirst: false,
    isLast: false,
  } as CombineCatalogPriceLine;
};
