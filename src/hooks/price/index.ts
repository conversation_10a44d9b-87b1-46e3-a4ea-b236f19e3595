import useFetchByParams from 'hooks/useFetchByParams';
import { useRequestQuery } from 'hooks/useRequest';
import { Price } from 'models';
import { useMemo } from 'react';
import { priceService } from 'services';
import { fetchPrices, selectPrices, selectPricesLoading } from 'store/slices/price.slices';
import { QueryParams } from 'types';

export const usePricesQuery = (options: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...options };

    return [queryParams];
  }, []);
};

export const usePrices = (query: QueryParams) => {
  return useFetchByParams<Price[]>({
    action: fetchPrices,
    dataSelector: selectPrices,
    loadingSelector: selectPricesLoading,
    params: query,
  });
};

export const usePricesRequest = (initialParams?: QueryParams) => {
  const action = async (params?: QueryParams): Promise<Price[]> => {
    if (params && 'priceFamilyId[]' in params) {
      const response = await priceService.getPrices(params);
      return response.rows;
    }
    return [];
  };

  return useRequestQuery<QueryParams, Price[]>({
    action: action,
    defaultValue: [],
    params: initialParams,
    autoFetch: true,
  });
};
