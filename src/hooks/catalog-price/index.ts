import useQueryParams from 'hooks/useQueryParams';
import useRequest from 'hooks/useRequest';
import { CatalogPrice } from 'models';
import { useMemo } from 'react';
import { catalogPriceService } from 'services';
import { CatalogPriceZone, QueryParams } from 'types';

export type CatalogPriceParams = QueryParams & {
  productTypeId?: number | null;
  isCatalog: boolean;
};
export const useCatalogPricesQuery = (options: CatalogPriceParams) => {
  const { productTypeId } = options;
  const [query] = useQueryParams<QueryParams>();
  return useMemo(() => {
    const queryParams = { ...query, ...options };

    return [queryParams];
  }, [productTypeId]);
};

export const useCatalogPrices = (query: CatalogPriceParams) => {
  const action = async () => {
    const { productTypeId, isCatalog } = query;
    const response = query.productTypeId
      ? await catalogPriceService.getCatalogPrices({
          productTypeId: productTypeId,
          isCatalog: isCatalog ? 1 : 0,
        })
      : [];
    return response;
  };
  return useRequest<CatalogPrice[]>({
    action,
    params: query,
    default: [],
  });
};

export type CatalogPriceZoneParams = QueryParams & {
  productTypeIds?: string;
  zoneIds?: string;
};
export const useCatalogPriceZonesQuery = (options: CatalogPriceZoneParams) => {
  const { productTypeIds, zoneIds } = options;
  return useMemo(() => {
    const queryParams = options;

    return [queryParams];
  }, [productTypeIds, zoneIds]);
};

export const useCatalogPriceZones = (query: CatalogPriceZoneParams) => {
  const action = async () => {
    const response =
      query.productTypeIds?.length && query.zoneIds?.length
        ? await catalogPriceService.getCatalogPriceZones(query)
        : [];
    return response;
  };
  return useRequest<CatalogPriceZone[]>({
    action,
    params: query,
    default: [],
  });
};
