import { useMemo } from 'react';
import {
  priceFamilyService,
  productService,
  serviceProviderPriceLineService,
} from 'services';
import { PaginationData, QueryParams } from 'types';
import useRequest from './useRequest';
import { ServiceProviderPrice, ServiceProviderPriceLine } from 'models';

type CombineServiceProviderPriceType = ServiceProviderPrice & {
  items: ServiceProviderPrice;
};
const combineServiceProviderPrices = (
  array: ServiceProviderPrice[]
): CombineServiceProviderPriceType[] => {
  const groupedById: { [key: number]: CombineServiceProviderPriceType } =
    array.reduce((acc: any, obj: any) => {
      const priceId = obj.priceId;
      if (!acc[priceId]) {
        acc[priceId] = { ...obj, items: [obj] };
      } else {
        acc[priceId].items.push(obj);
      }
      return acc;
    }, {});

  return Object.values(groupedById);
};

export type ServiceProviderPriceLineQueryType = QueryParams & {
  serviceProviderId: number | string;
  urbanCenterId?: number | string;
  priceFamilyId?: number | string;
  isActive?: 0 | 1;
};
export const useServiceProviderPriceLineQuery = (
  query?: ServiceProviderPriceLineQueryType
) => {
  return useMemo(() => {
    const queryParams = query;

    return [queryParams as ServiceProviderPriceLineQueryType];
  }, []);
};

export const useServiceProviderPriceLines = (
  query: ServiceProviderPriceLineQueryType
) => {
  const action = async (options?: any) => {
    const { serviceProviderId, ...others } = query;
    const response =
      await serviceProviderPriceLineService.getServiceProviderPriceLines(
        serviceProviderId,
        {
          ...others,
          ...options,
        }
      );
    return response;
  };
  return useRequest<PaginationData<ServiceProviderPriceLine>>({
    action,
    params: query,
    default: [],
  });
};

export const useServiceProviderPriceLineWithRelations = (query: any) => {
  const action = async (options?: ServiceProviderPriceLineQueryType & any) => {
    const { serviceProviderId, ...others } = query;
    const serviceProviderPriceLines =
      await serviceProviderPriceLineService.getServiceProviderPriceLines(
        serviceProviderId,
        {
          ...others,
          ...options,
          include:
            'ServiceProviderPriceLineProducts|ServiceProviderPriceLinePriceOptions|ServiceProviderPrices|ServiceProviderPriceLineZones',
        }
      );
    const productIds = [
      ...new Set(
        serviceProviderPriceLines?.rows?.flatMap((i) =>
          i.ServiceProviderPriceLineProducts?.map((p) => p.productId)
        )
      ),
    ];
    const priceFamilyIds = [
      ...new Set(serviceProviderPriceLines?.rows?.map((i) => i.priceFamilyId)),
    ];
    const [products, priceFamilies] = await Promise.all([
      productService.getProducts({
        'id[]': JSON.stringify(productIds),
        limit: 'unlimited',
        isActive: 1,
        exclude: 0,
      }),
      priceFamilyService.getAllPriceFamilies({
        'id[]': JSON.stringify(priceFamilyIds),
        include: 'Prices.PriceOptions.PriceSubOptions',
        isActive: 1,
      }),
    ]);
    serviceProviderPriceLines.rows = serviceProviderPriceLines.rows.flatMap(
      (item) => {
        const priceFamily = priceFamilies.find(
          (p) => p.id === item.priceFamilyId
        );
        const activePriceOptionIds = item.ServiceProviderPrices?.filter(
          (i) => i.priceOptionId
        ).map((i) => i.priceOptionId);
        const result = {
          ...item,
          PriceOptionIds: activePriceOptionIds,
          PriceFamily: priceFamily,
          ServiceProviderPriceLineProducts:
            item.ServiceProviderPriceLineProducts?.map((i) => ({
              ...i,
              Product: products.rows.find((p) => p.id === i.productId),
            })),
          ServiceProviderPrices: combineServiceProviderPrices(
            item.ServiceProviderPrices?.map((providerPrice) => ({
              ...providerPrice,
              PriceFamily: priceFamily,
              Price: priceFamily?.Prices?.find(
                (i) => i.id === providerPrice.priceId
              ),
            })).filter((i) => i.Price) as ServiceProviderPrice[]
          ).map((p: any) => ({
            ...p,
            ServiceProviderPriceLineZones:
              item?.ServiceProviderPriceLineZones?.filter(
                (i) => i.priceId === p.priceId
              ),
          })) as ServiceProviderPrice[],
        } as ServiceProviderPriceLine;

        const serviceProviderPrices = result.ServiceProviderPrices ?? [];
        if (serviceProviderPrices.length === 0) {
          return [{ ...result, rowSpan: 1 }];
        }
        return serviceProviderPrices.map(
          (i, index) =>
            ({
              ...result,
              rowSpan: index === 0 ? serviceProviderPrices.length : 0,
              nonBorder: index < (serviceProviderPrices.length || 0) - 1,
              ServiceProviderPrices: [i],
            } as ServiceProviderPriceLine)
        ) as ServiceProviderPriceLine[];
      }
    );
    console.log(serviceProviderPriceLines);
    return serviceProviderPriceLines;
  };
  return useRequest<PaginationData<ServiceProviderPriceLine>>({
    action,
    params: query,
    default: [],
  });
};
