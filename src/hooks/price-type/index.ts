import useFetchByParams from 'hooks/useFetchByParams';
import { PriceType } from 'models';
import { useMemo } from 'react';
import { fetchPriceTypes, selectPriceTypes, selectPriceTypesLoading } from 'store/slices/price.slices';
import { QueryParams } from 'types';

export const usePriceTypesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const usePriceTypes = (query: QueryParams) => {
  return useFetchByParams<PriceType[]>({
    action: fetchPriceTypes,
    dataSelector: selectPriceTypes,
    loadingSelector: selectPriceTypesLoading,
    params: query,
  });
};
