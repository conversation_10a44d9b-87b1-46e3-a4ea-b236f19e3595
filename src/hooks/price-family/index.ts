import { useMemo } from 'react';
import { QueryParams } from 'types';
import { PriceFamily } from 'models';
import useFetchByParams from 'hooks/useFetchByParams';
import { fetchPriceFamilies, selectPriceFamilies, selectPriceFamiliesLoading } from 'store/slices/price.slices';
import { priceFamilyService } from 'services';
import { useRequestQuery } from 'hooks/useRequest';

export const usePriceFamiliesQuery = (options: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...options, isActive: 1 };

    return [queryParams];
  }, []);
};

export const usePriceFamilies = (query: QueryParams) => {
  return useFetchByParams<PriceFamily[]>({
    action: fetchPriceFamilies,
    dataSelector: selectPriceFamilies,
    loadingSelector: selectPriceFamiliesLoading,
    params: query,
  });
};

export const usePriceFamilyRequest = (initialParams?: QueryParams, cb?: (data: PriceFamily[]) => void) => {
  const action = async (params?: QueryParams): Promise<PriceFamily[]> => {
    if (params && ('productTypeId[]' in params || params.productTypeId)) {
      const response = await priceFamilyService.getPriceFamilies({ ...params, isActive: 1 });
      return response;
    }
    return [];
  };

  return useRequestQuery<QueryParams, PriceFamily[]>({
    action: action,
    defaultValue: [],
    params: initialParams,
    autoFetch: true,
    onSuccess: (data) => {
      cb?.(data);
    },
  });
};
