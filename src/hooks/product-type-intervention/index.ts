import useFetchByParams from 'hooks/useFetchByParams';
import { DocumentStatus } from 'models';
import { useMemo } from 'react';
import {
  fetchProductTypeInterventions,
  selectProductTypeInterventions,
  selectProductTypeInterventionsLoading,
} from 'store/slices/product.slices';
import { QueryParams } from 'types';

export const useProductTypeInterventionsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useProductTypeInterventions = (query: QueryParams) => {
  return useFetchByParams<DocumentStatus[]>({
    action: fetchProductTypeInterventions,
    dataSelector: selectProductTypeInterventions,
    loadingSelector: selectProductTypeInterventionsLoading,
    params: query,
  });
};
