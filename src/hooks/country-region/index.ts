import useFetchByParams from 'hooks/useFetchByParams';
import { CountryRegion } from 'models';
import { useMemo } from 'react';
import { fetchCountryRegions, selectCountryRegions, selectCountryRegionsLoading } from 'store/slices/region.slices';
import { QueryParams } from 'types';

export const useCountryRegionsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useCountryRegions = (query: QueryParams) => {
  return useFetchByParams<CountryRegion[]>({
    action: fetchCountryRegions,
    dataSelector: selectCountryRegions,
    loadingSelector: selectCountryRegionsLoading,
    params: query,
  });
};
