import useAuthContext from 'store/auth-context';
import { permissionService, Permissions } from 'services/permission.service';

export const usePermission = () => {
  const { profile } = useAuthContext();

  return {
    hasPermission: (permission: Permissions) => permissionService.hasPermission(profile?.Role || null, permission),

    hasAnyPermission: (permissions: Permissions[]) =>
      permissionService.hasAnyPermission(profile?.Role || null, permissions),

    hasAllPermissions: (permissions: Permissions[]) =>
      permissionService.hasAllPermissions(profile?.Role || null, permissions),
  };
};
