import { useMemo } from 'react';
import { boService } from 'services';
import useRequest from '../useRequest';
import { BenneWasteManager } from 'models/bo';

export const useBenneWasteManagerQuery = (query: { supplier_id: number }) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useBenneWasteManagers = (query: { supplier_id: number }) => {
  const action = async () => {
    const response = await boService.getBenneWasteManagers(query);
    return response?.listBenneWasteManager;
  };
  return useRequest<BenneWasteManager[]>({
    action,
    params: query,
    default: [],
  });
};
