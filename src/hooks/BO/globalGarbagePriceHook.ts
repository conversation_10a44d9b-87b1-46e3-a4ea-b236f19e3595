import { useMemo } from 'react';
import { boService } from 'services';
import useRequest from '../useRequest';
import { GlobalGarbagePricing } from 'models/bo';
import { QueryParams } from 'types';
export const useGlobalGarbagePriceQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...(query ?? {}) };

    return [queryParams];
  }, []);
};

export const useGlobalGarbagePrices = (query?: QueryParams) => {
  const action = async () => {
    const response = await boService.getGlobalGarbagePricings();
    return response;
  };
  return useRequest<GlobalGarbagePricing>({
    action,
    params: query,
    default: {
      globalGarbagePricing: [],
      specialGarbagePricing: [],
    },
  });
};
