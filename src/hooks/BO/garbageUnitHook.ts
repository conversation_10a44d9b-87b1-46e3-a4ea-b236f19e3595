import { useMemo } from 'react';
import { boService } from 'services';
import useRequest from '../useRequest';
import { GarbageUnit } from 'models/bo';
import { QueryParams } from 'types';

export const useGarbageUnitsPriceQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...(query ?? {}) };

    return [queryParams];
  }, []);
};

export const useGarbageUnitsPrices = (query?: QueryParams) => {
  const action = async () => {
    const response = await boService.getGarbageUnits();
    return response.unit;
  };
  return useRequest<GarbageUnit[]>({
    action,
    params: query,
    default: [],
  });
};
