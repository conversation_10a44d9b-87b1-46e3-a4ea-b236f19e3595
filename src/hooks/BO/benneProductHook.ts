import { useMemo } from 'react';
import { boService } from 'services';
import useRequest from '../useRequest';
import { BenneProduct } from 'models/bo';

export const useBennePointPProductQuery = (query: { type: 'BENNE' | 'BIG_BAG'; service: string }) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useBennePointPProducts = (query: { type: 'BENNE' | 'BIG_BAG'; service: string }) => {
  const action = async () => {
    const response = await boService.getBennePointPProducts({
      platform: query.service,
      type: query.type,
    });
    return response?.listProduct;
  };
  return useRequest<BenneProduct[]>({
    action,
    params: query,
    default: [],
  });
};
