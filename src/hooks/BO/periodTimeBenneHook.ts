import { useMemo } from 'react';
import { boService } from 'services';
import useRequest from '../useRequest';
import { TimePeriod } from 'models/bo';

export const usePeriodTimeBenneQuery = (query: { service: string }) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const usePeriodTimeBenne = (query: { service: string }) => {
  const action = async () => {
    const response = await boService.getPeriodTimeBennes(query);
    return {
      periodsDepositTimeBenne: response?.periodsDepositTimeBenne?.filter((item) => !item.disable),
      periodsPickupTimeBenne: response?.periodsPickupTimeBenne?.filter((item) => !item.disable),
    };
  };
  return useRequest<{ periodsDepositTimeBenne: TimePeriod[]; periodsPickupTimeBenne: TimePeriod[] }>({
    action,
    params: query,
    default: {
      periodsDepositTimeBenne: [],
      periodsPickupTimeBenne: [],
    },
  });
};
