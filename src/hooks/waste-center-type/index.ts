import useFetchByParams from 'hooks/useFetchByParams';
import { WasteCenterType } from 'models';
import { useMemo } from 'react';
import { RootState } from 'store';
import {
  fetchWasteCenterTypes,
  selectWasteCenterTypes,
  selectWasteCenterTypesLoading,
} from 'store/slices/waste_center_type.slices';
import { QueryParams } from 'types';

export const useWasteCenterTypesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useWasteCenterTypes = (query: QueryParams) => {
  return useFetchByParams<WasteCenterType[]>({
    action: fetchWasteCenterTypes,
    dataSelector: selectWasteCenterTypes,
    loadingSelector: (state: RootState) => selectWasteCenterTypesLoading(state),
    params: query,
  });
};
