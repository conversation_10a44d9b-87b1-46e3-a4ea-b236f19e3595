import { PaginationData, QueryParams } from 'types';
import { useMemo } from 'react';
import { productService } from 'services';
import useRequest from './useRequest';
import { Product } from 'models';
import useFetchByParams from './useFetchByParams';
import { fetchProductsByIds, selectProductsByIds, selectProductsByIdsLoading } from 'store/slices/product.slices';

export const useProductsQuery = (
  options: QueryParams & {
    productTypeId?: number | null;
  },
) => {
  const { productTypeId } = options;
  return useMemo(() => {
    const queryParams = {
      ...options,
      limit: 'unlimited',
      exclude: 0,
      isActive: 1,
    };

    return [queryParams];
  }, [productTypeId]);
};

export const useProducts = (
  query: QueryParams & {
    productTypeId?: number | null;
  },
) => {
  const action = async () => {
    const response = await productService.getProducts(query);
    return response;
  };
  return useRequest<PaginationData<Product>>({
    action,
    params: query,
    default: [],
  });
};

export const useProductsByIdsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useProductsByIds = (query: QueryParams) => {
  return useFetchByParams<Product[]>({
    action: fetchProductsByIds,
    dataSelector: selectProductsByIds,
    loadingSelector: selectProductsByIdsLoading,
    params: query,
  });
};
