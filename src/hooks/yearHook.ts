import { useMemo } from 'react';
import { QueryParams } from 'types';
import { Year } from 'models';
import useFetchByParams from './useFetchByParams';
import { fetchYears, selectYears } from 'store/slices/year.slices';

export const useYearQuery = (
  query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useYears = (query: any) => {
   return useFetchByParams<Year[]>({
    action: fetchYears,
    dataSelector: selectYears,
    params: query,
  });
};
