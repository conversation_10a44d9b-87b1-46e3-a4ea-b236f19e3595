import { useState, useCallback, useMemo, useEffect } from 'react';
import { useAppDispatch, useAppSelector } from 'store';
import {
  setInvoice,
  setFormFilters,
  toggleDisplayFilter,
  toggleLineSelection,
  updateInvoiceTotals,
  addCommentToLine,
  removeCommentFromLine,
  clearInvoiceState,
  selectCurrentInvoice,
  selectOriginalInvoice,
  selectFormFilters,
  selectDisplayFilters,
  selectSelectedLines,
  selectIsFiltering,
  selectIsInitialLoad,
  // Import new memoized selectors
  selectInvoiceList,
  selectInvoiceLines,
  selectInvoiceDetails,
  selectLoadingStates,
  selectFilters,
  selectTotalSelected,
  selectTotalAvoirs,
  selectSelectedLinesCount,
  selectInvoiceSummary,
  selectIsValidForSave,
  // Import thunks
  fetchInvoiceDetailsByProviderIdEdit,
  fetchInvoiceLinesByProviderIdCreate,
  fetchProvidersInvoicesList,
} from 'store/slices/provider_invoice.slices';
import { extractServiceLines, ExtractedServiceLine } from 'utils/invoiceTransformer';
import { providerInvoiceService } from 'services';
import { Loading, QueryParams, Invoice } from 'types';

export const useProviderInvoiceTable = () => {
  const dispatch = useAppDispatch();

  const currentInvoice = useAppSelector(selectCurrentInvoice);
  const originalInvoice = useAppSelector(selectOriginalInvoice);
  const formFilters = useAppSelector(selectFormFilters);
  const displayFilters = useAppSelector(selectDisplayFilters);

  const selectedLines = useAppSelector(selectSelectedLines);
  const isFiltering = useAppSelector(selectIsFiltering);
  const isInitialLoad = useAppSelector(selectIsInitialLoad);

  const serviceLines: ExtractedServiceLine[] = useMemo(() => {
    return currentInvoice ? extractServiceLines(currentInvoice) : [];
  }, [currentInvoice]);

  const originalServiceLines = useMemo(() => {
    return originalInvoice ? extractServiceLines(originalInvoice) : [];
  }, [originalInvoice]);

  const applyFilters = useCallback((filters: Record<string, unknown>) => {
    if (!filters || Object.keys(filters).length === 0) {
      return;
    }
  }, []);

  const recalculateGroupingFlags = useCallback((lines: ExtractedServiceLine[]) => {
    const groupedByDetail: { [key: string]: ExtractedServiceLine[] } = {};
    
    lines.forEach(line => {
      const detailId = line.detailId || 'unknown';
      if (!groupedByDetail[detailId]) {
        groupedByDetail[detailId] = [];
      }
      groupedByDetail[detailId].push(line);
    });

    const result: ExtractedServiceLine[] = [];
    Object.values(groupedByDetail).forEach(detailLines => {
      detailLines.forEach((line, index) => {
        result.push({
          ...line,
          rowSpan: index === 0 ? detailLines.length : 0,
          isFirstLineOfDetail: index === 0,
          totalLinesInDetail: detailLines.length
        });
      });
    });

    return result;
  }, []);

  const finalTableData = useMemo(() => {
    let finalLines = serviceLines;

    if (formFilters.date) {
      finalLines = finalLines.filter(line => {
        const lineDate = line.prestationDetails?.[0]?.date;
        return lineDate === formFilters.date;
      });
    }

    if (formFilters.commandeNumber) {
      finalLines = finalLines.filter(line =>
        line.commandNumber?.toLowerCase().includes(formFilters.commandeNumber!.toLowerCase())
      );
    }

    if (formFilters.product) {
      finalLines = finalLines.filter(line => line.productId === formFilters.product);
    }



    if (formFilters.siteAddress) {
      finalLines = finalLines.filter(line =>
        line.siteAddress?.toLowerCase().includes(formFilters.siteAddress!.toLowerCase())
      );
    }

    if (displayFilters.showSelectedRows === false) {
      finalLines = finalLines.filter(line => !line.isSelected);
      finalLines = recalculateGroupingFlags(finalLines);
    }

    if (displayFilters.showInvoicedRows === false) {
      finalLines = finalLines.filter(line => !line.isInvoiced);
      finalLines = recalculateGroupingFlags(finalLines);
    }

    return finalLines;
  }, [serviceLines, displayFilters, formFilters, recalculateGroupingFlags]);

  useEffect(() => {
    // Force re-render when serviceLines change
  }, [serviceLines]);

  return {
    currentInvoice,
    originalInvoice,
    originalServiceLines,
    serviceLines,
    formFilters,
    displayFilters,
    selectedLines,
    isFiltering,
    isInitialLoad,
    finalTableData,
    
    setInvoice: useCallback((invoice: Invoice) => {
      dispatch(setInvoice(invoice));
    }, [dispatch]),

    applyFormFilters: useCallback((filters: Record<string, unknown>) => {
      dispatch(setFormFilters(filters));
      applyFilters(filters);
    }, [dispatch, applyFilters]),

    toggleDisplayFilter: useCallback((filterType: 'showSelectedRows' | 'showInvoicedRows', value: boolean) => {
      dispatch(toggleDisplayFilter({ filterType, value }));
    }, [dispatch]),

    toggleLineSelection: useCallback((lineKey: string) => {
      dispatch(toggleLineSelection(lineKey));
    }, [dispatch]),

    addCommentToLine: useCallback((lineKey: string, commentText: string, author?: string) => {
      const comment = {
        text: commentText,
        author: author || 'Current User'
      };
      dispatch(addCommentToLine({ lineKey, comment }));
    }, [dispatch]),

    removeCommentFromLine: useCallback((lineKey: string, commentId: number) => {
      dispatch(removeCommentFromLine({ lineKey, commentId }));
    }, [dispatch]),

    updateInvoiceTotals: useCallback((totalSelected: number, totalAvoirs: number) => {
      dispatch(updateInvoiceTotals({ totalSelected, totalAvoirs }));
    }, [dispatch]),

    clearInvoiceState: useCallback(() => {
      dispatch(clearInvoiceState());
    }, [dispatch]),
  };
};

export const useProviderInvoiceDetails = () => {
  const [data, setData] = useState<unknown[]>([]);
  const [loading, setLoading] = useState<Loading>('idle');
  const [error, setError] = useState<string | null>(null);

  const fetchInvoiceDetailsEdit = useCallback(async (params: { prestataire?: string; [key: string]: unknown }) => {
    try {
      setLoading('pending');
      setError(null);
      
      const response = await providerInvoiceService.getInvoiceDetailsByProviderIdEdit(params);
      setData(response.rows);
      setLoading('succeeded');
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch invoice details';
      setError(errorMessage);
      setLoading('failed');
      throw err;
    }
  }, []);

  const fetchAvailableDetailsCreate = useCallback(async (params?: QueryParams) => {
    try {
      setLoading('pending');
      setError(null);

      const response = await providerInvoiceService.getInvoiceLinesByProviderIdCreate(params || {});
      setData(response.rows);
      setLoading('succeeded');
      
      return response;
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch available invoice details';
      setError(errorMessage);
      setLoading('failed');
      throw err;
    }
  }, []);

  return {
    data,
    loading,
    error,
    fetchInvoiceDetailsEdit,
    fetchAvailableDetailsCreate
  };
};

export const useProviderInvoiceSave = () => {
  const [loading] = useState<Loading>('idle');
  const [error] = useState<string | null>(null);

  return {
    loading,
    error,
  };
};

// ===== NEW OPTIMIZED HOOKS =====

/**
 * Hook for data fetching operations with memoized callbacks
 */
export const useProviderInvoiceData = () => {
  const dispatch = useAppDispatch();

  const fetchInvoicesList = useCallback((params?: QueryParams) => {
    return dispatch(fetchInvoicesList(params));
  }, [dispatch]);

  const fetchCreateData = useCallback((params: { prestataire?: string; [key: string]: unknown }) => {
    return dispatch(fetchInvoiceLinesByProviderIdCreate(params));
  }, [dispatch]);

  const fetchEditData = useCallback((params: { prestataire?: string; invoiceId?: string | number; [key: string]: unknown }) => {
    return dispatch(fetchInvoiceDetailsByProviderIdEdit(params));
  }, [dispatch]);

  return {
    fetchInvoicesList,
    fetchCreateData,
    fetchEditData,
  };
};

/**
 * Hook for accessing state with memoized selectors
 */
export const useProviderInvoiceState = () => {
  const invoiceList = useAppSelector(selectInvoiceList);
  const invoiceLines = useAppSelector(selectInvoiceLines);
  const invoiceDetails = useAppSelector(selectInvoiceDetails);
  const currentInvoice = useAppSelector(selectCurrentInvoice);
  const filters = useAppSelector(selectFilters);
  const loadingStates = useAppSelector(selectLoadingStates);

  return {
    invoiceList,
    invoiceLines,
    invoiceDetails,
    currentInvoice,
    filters,
    loadingStates,
  };
};

/**
 * Hook for computed values with memoization
 */
export const useProviderInvoiceCalculations = () => {
  const totalSelected = useAppSelector(selectTotalSelected);
  const totalAvoirs = useAppSelector(selectTotalAvoirs);
  const selectedLinesCount = useAppSelector(selectSelectedLinesCount);
  const invoiceSummary = useAppSelector(selectInvoiceSummary);
  const isValidForSave = useAppSelector(selectIsValidForSave);

  return {
    totalSelected,
    totalAvoirs,
    selectedLinesCount,
    invoiceSummary,
    isValidForSave,
  };
};

export const useProviderInvoice = () => {
  const detailsHook = useProviderInvoiceDetails();
  const saveHook = useProviderInvoiceSave();

  return {
    ...detailsHook,
    ...saveHook
  };
};
