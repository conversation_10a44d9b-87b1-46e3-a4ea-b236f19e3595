import { useMemo } from 'react';
import { regionService } from 'services';
import { PaginationData, QueryParams } from 'types';
import useRequest from './useRequest';
import { Region } from 'models';

export const useRegionQuery = (
  query?: QueryParams & {
    serviceProviderId?: number | string;
    platformId?: number | string;
  }
) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useRegions = (query: any) => {
  const action = async () => {
    const response = await regionService.getRegions(query);
    return response;
  };
  return useRequest<PaginationData<Region>>({
    action,
    params: query,
    default: [],
  });
};

export const useRegion = (query: any) => {
  const action = async () => {
    const response = await regionService.findOneRegion(query);
    return response;
  };
  return useRequest<Region>({
    action,
    params: query,
    default: null,
  });
};
