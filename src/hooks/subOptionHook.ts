import { SubOption } from "models";
import { useMemo } from "react";
import { optionService } from "services";
import { QueryParams } from "types";
import useRequest from "./useRequest";

export const useSubOptionsQuery = (
  query?: QueryParams & {
    productTypeId?: number;
    optionId?: number;
  }
) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, [query?.productTypeId, query?.optionId]);
};

export const useSubOptions = (query: any) => {
  const action = async () => {
    const { productTypeId, optionId, ...others } = query;
    const response =
      productTypeId && optionId
        ? await optionService.getSubOptions(productTypeId, optionId, others)
        : [];
    return response;
  };
  return useRequest<SubOption[]>({
    action,
    params: query,
    default: [],
  });
};
