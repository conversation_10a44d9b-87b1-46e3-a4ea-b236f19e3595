import { useMemo } from 'react';
import { serviceProviderPriceService } from 'services';
import { PaginationData, QueryParams } from 'types';
import useRequest from './useRequest';
import { ServiceProviderPrice } from 'models';

export const useServiceProviderPriceQuery = (
  query?: QueryParams & {
    productTypeId?: number | string | null;
    priceFamilyId?: number | string;
    priceId?: number | string;
    priceOptionId?: number | string;
  }
) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, [query?.productTypeId, query?.priceFamilyId, query?.priceId, query?.priceOptionId]);
};

export const useServiceProviderPrices = (query: any) => {
  const action = async (options?: any) => {
    const response = await serviceProviderPriceService.getServiceProviderPrices(
      { ...query, ...options }
    );
    return response;
  };
  return useRequest<PaginationData<ServiceProviderPrice>>({
    action,
    params: query,
    default: [],
  });
};
