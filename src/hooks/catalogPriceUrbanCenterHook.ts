import { useMemo } from "react";
import { catalogPriceUrbanCenterService } from "services";
import { PaginationData, QueryParams } from "types";
import useRequest from "./useRequest";
import { CatalogPriceUrbanCenter } from "models";

export const useCatalogPriceUrbanCentersQuery = (
  options: QueryParams & {
    yearId?: number;
  }
) => {
  const { yearId } = options;
  return useMemo(() => {
    const queryParams = { ...options, limit: 'unlimited' };

    return [queryParams];
  }, [yearId]);
};

export const useCatalogPriceUrbanCenters = (query: any) => {
  const action = async () => {
    const { yearId } = query;
    const response = yearId
      ? await catalogPriceUrbanCenterService.getCatalogPriceUrbanCenters(query)
      : { count: 0, rows: [] };
    return response;
  };
  return useRequest<PaginationData<CatalogPriceUrbanCenter>>({
    action,
    params: query,
    default: [],
  });
};
