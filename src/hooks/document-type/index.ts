import useFetchByParams from 'hooks/useFetchByParams';
import { DocumentType } from 'models';
import { useMemo } from 'react';
import { fetchDocumentTypes, selectDocumentTypes, selectDocumentTypesLoading } from 'store/slices/document.slice';
import { QueryParams } from 'types';

export const useDocumentTypesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useDocumentTypes = (query: QueryParams) => {
  return useFetchByParams<DocumentType[]>({
    action: fetchDocumentTypes,
    dataSelector: selectDocumentTypes,
    loadingSelector: selectDocumentTypesLoading,
    params: query,
  });
};
