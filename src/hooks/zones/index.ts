import useFetchByParams from 'hooks/useFetchByParams';
import { Zone } from 'models';
import { useMemo } from 'react';
import { fetchZonesByIds, selectZonesByIds, selectZonesByIdsLoading } from 'store/slices/region.slices';
import { QueryParams } from 'types';

export const useZonesByIdsQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query, limit: 'unlimited' };

    return [queryParams];
  }, []);
};

export const useZonesByIds = (query: QueryParams) => {
  return useFetchByParams<Zone[]>({
    action: fetchZonesByIds,
    dataSelector: selectZonesByIds,
    loadingSelector: selectZonesByIdsLoading,
    params: query,
  });
};
