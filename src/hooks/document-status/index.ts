import useFetchByParams from 'hooks/useFetchByParams';
import { DocumentStatus } from 'models';
import { useMemo } from 'react';
import {
  fetchDocumentStatuses,
  selectDocumentStatuses,
  selectDocumentStatusesLoading,
} from 'store/slices/document.slice';
import { QueryParams } from 'types';

export const useDocumentStatusesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useDocumentStatuses = (query: QueryParams) => {
  return useFetchByParams<DocumentStatus[]>({
    action: fetchDocumentStatuses,
    dataSelector: selectDocumentStatuses,
    loadingSelector: selectDocumentStatusesLoading,
    params: query,
  });
};
