import useFetchByParams from 'hooks/useFetchByParams';
import { OptionType } from 'models';
import { useMemo } from 'react';
import { fetchOptionTypes, selectOptionTypes } from 'store/slices/option.slices';
import { QueryParams } from 'types';

export const useOptionTypesQuery = (query?: QueryParams) => {
  return useMemo(() => {
    const queryParams = { ...query };

    return [queryParams];
  }, []);
};

export const useOptionTypes = (query: QueryParams) => {
  return useFetchByParams<OptionType[]>({
    action: fetchOptionTypes,
    dataSelector: selectOptionTypes,
    params: query,
  });
};
