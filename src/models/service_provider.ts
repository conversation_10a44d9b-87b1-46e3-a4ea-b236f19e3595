import Contact from './contact';
import ServiceType from './service_type';
import WasteCenter from './waste_center';

export default interface ServiceProvider {
  id: number;
  name: string;
  formattedAddress: string;
  address: string;
  city: string;
  postalcode: string;
  country: string;
  longitude: number;
  latitude: number;
  email: string;
  password: string;
  fichiers: string;
  siren: string;
  siret: string;
  supplierCode: string;
  colorCode: string;
  colorFormat: string;
  phone: string;
  legalForm: string;
  receiptNumber: string;
  isActive: boolean;
  isSynced: boolean;
  enableSync: boolean;
  ServiceTypes?: ServiceType[];
  WasteCenters?: WasteCenter[];
  Contacts?: Contact[];
  boId?: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
