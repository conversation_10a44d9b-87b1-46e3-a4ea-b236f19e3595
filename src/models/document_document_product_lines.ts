import DocumentProductLinePrestation from './document_document_product_line_prestations';
import DocumentProductLinePrice from './document_document_product_line_prices';
import DocumentProductLineSubOption from './document_document_product_line_sub_options';

export default interface DocumentProductLine {
  id?: number;
  documentId?: number;
  productId?: number;
  productNameForClient?: string;
  description?: string;
  descriptionWithParameter?: string;
  prestationVueClient?: string;
  priceFamilyId?: number | null;
  quantity?: number;
  totalBeforeDiscount?: number;
  discount?: number;
  total?: number;
  isSetTotalZero?: boolean;
  booksProductLineId?: number;
  headerName?: string;
  booksProductLineHeaderId?: number | string;
  unitPrice?: number;
  discountUnit?: string;
  lineOrderNumber?: number | string;
  creationType?: string;
  uuid?: string;
  logComment?: string;
  productTypeUnitId?: number;
  productTypeUnitLabel?: string;
  DocumentProductLinePrestations?: DocumentProductLinePrestation[];
  DocumentProductLinePrices?: DocumentProductLinePrice[];
  DocumentProductLineSubOptions?: DocumentProductLineSubOption[];
  interventionId?: number;
  interventionKey?: string;
  unitPriceMargin?: number | null;
  mainProductId?: string;
  parentProductLineId?: string;
  clientOrderProductStatus?: string;
  serviceProviderOrderQuantity?: number;
}
