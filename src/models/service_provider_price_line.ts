import PriceFamily from './price_family';
import ServiceProvider from './service_provider';
import ServiceProviderPrice from './service_provider_price';
import ServiceProviderPriceLinePriceOption from './service_provider_price_line_price_option';
import ServiceProviderPriceLineProduct from './service_provider_price_line_product';
import ServiceProviderPriceLineZone from './service_provider_price_line_zone';
import UrbanCenter from './urban_center';
import ProductType from './product_type';

export default interface ServiceProviderPriceLine {
  id: number;
  serviceProviderId: number;
  ServiceProvider?: ServiceProvider;
  urbanCenterId: number;
  UrbanCenter?: UrbanCenter;
  priceFamilyId: number;
  PriceFamily?: PriceFamily;
  productTypeId?: number;
  ProductType?: ProductType;
  isChecked: boolean;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  ServiceProviderPriceLineProducts?: ServiceProviderPriceLineProduct[];
  ServiceProviderPrices?: ServiceProviderPrice[];
  ServiceProviderPriceLineZones?: ServiceProviderPriceLineZone[];
  ServiceProviderPriceLinePriceOptions?: ServiceProviderPriceLinePriceOption[];
}
