interface Siege {
  siret: string;
  siret_formate: string;
  nic: string;
  numero_voie: number;
  indice_repetition: string | null;
  type_voie: string;
  libelle_voie: string;
  complement_adresse: string | null;
  adresse_ligne_1: string;
  adresse_ligne_2: string | null;
  code_postal: string;
  ville: string;
  pays: string;
}

interface Etablissement {
  siret: string;
  siret_formate: string;
  nic: string;
  numero_voie: number;
  indice_repetition: string | null;
  type_voie: string;
  libelle_voie: string;
  complement_adresse: string | null;
  adresse_ligne_1: string;
  adresse_ligne_2: string | null;
  code_postal: string;
  ville: string;
  pays: string;
  latitude?: string;
  longitude?: string;
}

export default interface Company {
  siren: string;
  siren_formate: string;
  diffusable: boolean;
  nom_entreprise: string;
  personne_morale: boolean;
  denomination: string;
  nom: string | null;
  prenom: string | null;
  sexe: string | null;
  entreprise_cessee: number;
  statut_rcs: string;
  categorie_juridique: string;
  forme_juridique: string;
  date_creation: string;
  date_creation_formate: string;
  code_naf: string;
  libelle_code_naf: string;
  domaine_activite: string;
  siege: Siege;
  villes: string[];
  conventions_collectives: string[];
  date_cessation: string | null;
  entreprise_employeuse: number;
  tranche_effectif: string | null;
  effectif: string;
  effectif_min: number;
  effectif_max: number;
  economie_sociale_et_solidaire: boolean;
  annee_effectif: number;
  capital: number;
  chiffre_affaires: number | null;
  resultat: number | null;
  effectifs_finances: number | null;
  annee_finances: number | null;
  etablissements: Etablissement[];
  dirigeants: string[];
  beneficiaires: string[];
  documents: string[];
  publications: string[];
  nb_dirigeants_total: number;
  nb_beneficiaires_total: number;
  nb_documents_avec_mentions: number;
  nb_documents_total: number;
  nb_publications_avec_mentions: number;
  nb_publications_total: number;
  objet_social?: string;
}
