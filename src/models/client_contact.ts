import Address from './client_address';
import ContactAddress from './client_contact_addresses';
import ClientContactPerson from './client_contact_person';
import Company from './company';

export default interface ClientContact {
  id?: number;
  clientType?: string;
  name?: string;
  siren?: string;
  service?: string;
  enCompte?: string;
  typeDeCompte?: string;
  crmOwnerId?: string;
  crmOwnerName?: string;
  crmAccountId?: string;
  crmContactId?: string;
  booksContactId?: string;
  encoursAut?: string;
  encoursDispo?: string;
  estimatesM?: string;
  sales?: string;
  estimates?: string;
  orders?: string;
  invoices?: string;
  booksTaxId?: string;
  taxPercentage?: number;
  typeBdc?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  ContactAddresses?: ContactAddress[];
  ContactPersons?: ClientContactPerson[];
  billingAddress?: Address;
  companyDetail?: Company | null;
  modeDeRGlement1?: string;
}
