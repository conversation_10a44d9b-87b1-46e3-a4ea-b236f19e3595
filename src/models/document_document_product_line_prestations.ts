import DocumentProductLinePrestationSubOption from './document_document_product_line_prestation_sub_options';
import DocumentProductLinePrestationStatus from './document_document_product_line_prestations_statuses';

export default interface DocumentProductLinePrestation {
  id?: number;
  documentProductLineId?: string | number;
  prestationDate?: Date | string | null;
  booksPackageId?: string;
  booksShipmentId?: string;
  boOrderId?: string;
  isActive?: boolean;
  documentProductLinePrestationStatusId?: string | number;
  DocumentProductLinePrestationStatus?: DocumentProductLinePrestationStatus;
  DocumentProductLinePrestationSubOptions?: DocumentProductLinePrestationSubOption[];
  uuid?: string;
  optionId?: string;
  optionLabel?: string;
  timeSlotId?: string | null;
  subOptionId?: string | null;
  subOptionLabel?: string | null;
  isNewLine?: boolean;
}
