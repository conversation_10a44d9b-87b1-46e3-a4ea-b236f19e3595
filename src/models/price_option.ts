import CatalogPrice from './catalog_price';
import Price from './price';
import PriceSubOption from './price_sub_option';
import PriceType from './price_type';

export default interface PriceOption {
  id?: number;
  priceId?: number | null;
  priceTypeId?: number | null;
  name?: string | null;
  unit?: string | null;
  Price?: Price;
  PriceType?: PriceType;
  PriceSubOptions?: PriceSubOption[];
  CatalogPrices?: CatalogPrice[];
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  defaultValue?: string | null;
}
