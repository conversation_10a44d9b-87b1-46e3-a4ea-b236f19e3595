import { UploadFileCustom } from 'components/Estimate/FileUpload';
import Address from './client_address';
import ClientContact from './client_contact';
import DocumentCCContact from './document_document_cc_contact';
import DocumentProductLinePrestationStatus from './document_document_product_line_prestations_statuses';
import DocumentStatus from './document_document_status';
import DocumentType from './document_document_type';
import Document from './document_documents';
import ProductLineDevis from './product_lines_devis';

export default interface Quotation {
  status?: string;
  billingAddress?: Address;
  chantierAddress?: Address;
  isGpsAddress?: boolean;
  gpsAddress?: string;
  latLongGpsAddress?: string;
  document?: Document;
  addressBillingArray?: Address[];
  addressChantierArray?: Address[];
  documentId?: number;
  DocumentCCContacts?: DocumentCCContact[];
  addressArray?: Address[];
  clientContact?: ClientContact;
  isAddressChantierTemporary?: boolean;
  createdFrom?: 'Zoho' | 'QBO';
  documentTypes?: DocumentType[];
  documentStatus?: DocumentStatus[];
  lineProducts?: ProductLineDevis[];
  fileUpload?: UploadFileCustom[];
  documentProductLinePrestationStatus?: DocumentProductLinePrestationStatus[];
}
