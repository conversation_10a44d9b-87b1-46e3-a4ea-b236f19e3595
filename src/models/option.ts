import OptionType from './option_type';
import ProductType from './product_type';
import SubOption from './suboption';
export default interface Option {
  id: number;
  productTypeId?: number;
  productType?: ProductType;
  optionTypeId?: number;
  OptionType?: OptionType;
  name?: string;
  order?: number;
  isActive?: boolean;
  isPrestationOption?: boolean;
  SubOptions?: SubOption[];
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
