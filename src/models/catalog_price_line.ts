import CatalogPrice from './catalog_price';
import CatalogPriceLineProduct from './catalog_price_line_product';
import CatalogPriceLineSubOptionZone from './catalog_price_line_sub_option_zone';
import CatalogPriceLineZone from './catalog_price_line_zone';
import CatalogPriceLinesZoneChecked from './catalog_price_lines_zone_checked';
import CatalogPriceUrbanCenter from './catalog_price_urban_center';

export default interface CatalogPriceLine {
  id: number;
  catalogPriceUrbanCenterId: number;
  isActive: boolean;
  isChecked: boolean;
  isVisible: boolean;
  comment: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  CatalogPrices?: CatalogPrice[];
  CatalogPriceLineZones?: CatalogPriceLineZone[];
  CatalogPriceLineSubOptionZones?: CatalogPriceLineSubOptionZone[];
  CatalogPriceLineProducts?: CatalogPriceLineProduct[];
  CatalogPriceUrbanCenter?: CatalogPriceUrbanCenter;
  CatalogPriceLinesZoneCheckeds?: CatalogPriceLinesZoneChecked[];
}
