import DocumentProductLine from './document_document_product_lines';
import ProductType from './product_type';
import DocumentProductLinePrestation from './document_document_product_line_prestations';
import DocumentProductLineSubOption from './document_document_product_line_sub_options';
import DocumentProductLinePrice from './document_document_product_line_prices';
import { MultiProduct } from './product';
export type ProductLineDevisType = 'header' | 'product' | 'custom' | 'header-multi-product' | 'multi-product';
export default interface ProductLineDevis {
  id?: number;
  documentId?: number;
  productId?: number;
  productNameForClient?: string;
  description?: string;
  descriptionWithParameter?: string;
  priceFamilyId?: number | null;
  productTypeId?: number;
  interventionId?: number;
  interventionKey?: string;
  productTypeRegulId?: number[];
  quantity?: number;
  totalBeforeDiscount?: number;
  discount?: number;
  total?: number;
  isSetTotalZero?: boolean;
  booksProductLineId?: number;
  uuid?: string;
  creationType?: string;
  isCatalog?: boolean | null;
  headerName?: string;
  lineOrderNumber?: number;
  booksProductLineHeaderId?: string;
  product?: MultiProduct & {
    id?: number;
    productTypeId?: number;
    name?: string;
    isActive?: boolean;
    isVisible?: boolean;
    exclude?: boolean;
    description?: string;
    ProductType?: ProductType;
    mainProductId?: string;
  };
  discountUnit?: string;
  product0?: boolean;
  prestationDateList?: DocumentProductLinePrestation[];
  lineItems?: DocumentProductLine[];
  DocumentProductLineSubOptions?: DocumentProductLineSubOption[];
  DocumentProductLinePrices?: DocumentProductLinePrice[];
  priceFamilyValue?: string | number;
  priceFamilyBuyingPrice?: string | number | null;
  priceFamilyPriceMargin?: string | number | null;
  priceFamilyValidSP?: string | number | null;
  priceFamilyLabel?: string;
  priceFamilyIdOriginal?: string | number | null;
  listPriceOptions?: DocumentProductLinePrice[];
  linePriceFamilyId?: string | number;
  productTypeUnitId?: number;
  productTypeUnitLabel?: string;
  buyingPrice?: number | null;
  parentKey?: string;
  mainProductId?: string;
  parentProductLineId?: string;
  serviceProviderOrderQuantity?: number;
}
