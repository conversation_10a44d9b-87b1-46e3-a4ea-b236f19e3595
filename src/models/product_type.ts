import RegionProductType from './region_product_type';
import Product from './product';
import PriceFamily from './price_family';
import Option from './option';
import ProductTypeUnit from './product_type_unit';
import ProductTypeInterventions from './product-type-interventions';
import FacturationType from './facturation_type';

export default interface ProductType {
  id: number;
  name?: string;
  accountingCode?: string;
  isActive?: boolean;
  isVisible?: boolean;
  isCatalog?: boolean;
  isFournisseur?: boolean;
  noFollowUp?: boolean;
  description?: string;
  purchaseAccountingCode?: string;
  salesAccountingCode?: string;
  Products?: Product[];
  PriceFamilies?: PriceFamily[];
  RegionProductTypes?: RegionProductType[];
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  Options?: Option[];
  productTypeUnitId?: number;
  ProductTypeUnit?: ProductTypeUnit;
  interventionId?: number;
  ProductTypeIntervention?: ProductTypeInterventions;
  facturationTypeId?: number;
  FacturationType?: FacturationType;
  isMultiProduct?: boolean;
}
