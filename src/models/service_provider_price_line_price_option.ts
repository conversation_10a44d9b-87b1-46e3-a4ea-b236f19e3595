import ServiceProviderPriceLine from './service_provider_price_line';

export default interface ServiceProviderPriceLinePriceOption {
  id: number;
  serviceProviderPriceLineId: number;
  ServiceProviderPriceLine?: ServiceProviderPriceLine;
  priceId?: number;
  priceOptionId?: number;
  priceSubOptionId?: number;
  priceOptionValue?: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
