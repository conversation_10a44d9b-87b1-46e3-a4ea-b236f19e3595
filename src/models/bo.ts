export default interface TruckDriver {
  id: number;
  name: string;
}

export interface TimePeriod {
  id: number;
  key_value: string;
  name: string;
  sort: number;
  time_end_hour: number;
  time_end_minute: number;
  time_start_hour: number;
  time_start_minute: number;
  type: string;
  disable: boolean;
}

export interface TimeHour {
  key: string;
  name: string;
  hour: number;
  minute: number;
  disable: boolean;
}

export interface PUP {
  id: number;
  company_name: string;
  address1: string;
  city: string;
  zip_code: string;
  ref_client: string;
}

export interface BenneWasteManager {
  id: number;
  name?: string;
  address?: string;
  phone?: string;
  benne_supplier_id?: number;
  zip_code?: string;
  city?: string;
  is_important?: boolean;
  benne_waste_type?: string;
  garbage_type?: string;
}

export interface Craftmen {
  user_id: number;
  craftmen_id: number;
  craftmen_call_center_id: number;
  craftmen_name: string;
  craftmen_ref_client: string;
  email: string;
  user_phone: string;
}

export interface GarbageType {
  id: number;
  is_dangerous: boolean;
  name: string;
}

export interface PriceType {
  id: number;
  name: string;
}

export interface GarbagePricing {
  id: number;
  buying_price: string;
  commission: string;
  commission_collect: string;
  garbage_type: GarbageType;
  garbage_unit: string;
  is_important: boolean;
  price_type: PriceType;
  selling_price_m3: string;
  selling_price_ton: string;
  sort_index: string;
}

export interface GlobalGarbagePricing {
  globalGarbagePricing: GarbagePricing[];
  specialGarbagePricing: GarbagePricing;
}

export interface BenneProduct {
  id: number;
  benne_pointp_type_id: number;
  name: string;
  price_ecodrop_HT: string;
  price_pointp_HT: string;
  code_article: string;
  benne_region_id: number;
  created_at: string; // ISO datetime string
  updated_at: string; // ISO datetime string
  deleted_at: string | null;
  benne_product_id: number;
  sort_index: number;
  platform: string;
  price_ecodrop_HA_HT: string;
  benne_zone_id: number;
  quantity_reduction: number;
  price_ecodrop_quantity_reduction_HT: string;
  price_pointp_quantity_reduction_HT: string;
  zoho_product_id: string | null;
}

export interface BennePointp {
  id: number;
  benne_pointp_type_id: number;
  name: string;
  price_ecodrop_HT: string;
  price_pointp_HT: string | null;
  code_article: string | null;
  benne_region_id: number | null;
  created_at: string;
  updated_at: string;
  deleted_at: string | null;
  benne_product_id: number;
  sort_index: number | null;
  platform: string;
  price_ecodrop_HA_HT: string;
  benne_zone_id: number | null;
  quantity_reduction: number | null;
  price_ecodrop_quantity_reduction_HT: string;
  price_pointp_quantity_reduction_HT: string;
  zoho_product_id: string;
  benne_pointp_type: {
    id: number;
    key_value: string;
    name: string;
    created_at: string;
    updated_at: string;
    deleted_at: string | null;
  };
}

export interface GarbageUnit {
  id: number;
  name: string;
}

export interface LogisticAction {
  key: string;
  name: string;
  disable: boolean;
}
export interface PriseEnCharge {
  name: string;
  value: string;
}

export interface BenneSupplier {
  id: number;
  supplier_name: string;
  address_full: string;
  address: string | null;
  zip_code: string;
  city: string;
  country: string | null;
  contact_name: string;
  contact_lastname: string | null;
  contact_function: string | null;
  phone: string;
  email: string;
  lat: string;
  long: string;
  color: string;
  color_text: string;
  supplier_code: string;
  number_recepisse: string;
  password_show: string | null;
  is_active: number;
  taux_de_valorisations: number;
  benne_waste_managers: BenneWasteManager[];
}
