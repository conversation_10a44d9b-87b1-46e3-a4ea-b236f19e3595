import Price from './price';
import PriceFamily from './price_family';
import PriceOption from './price_option';
import ServiceProviderPriceLineZone from './service_provider_price_line_zone';

export default interface ServiceProviderPrice {
  id: number;
  productTypeId: number;
  priceFamilyId?: number;
  PriceFamily?: PriceFamily;
  priceId?: number;
  Price?: Price;
  priceOptionId?: number;
  PriceOption?: PriceOption;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  ServiceProviderPriceLineZones?: ServiceProviderPriceLineZone[];
}
