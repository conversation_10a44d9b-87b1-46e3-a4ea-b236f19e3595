export default interface Address {
  id?: number;
  formattedAddress?: string;
  address: string;
  postalCode: string;
  postalcode?: string;
  city: string;
  country: string;
  latitude: string;
  longitude: string;
  countryRegion?: string;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  address_id?: string;
  isCreate?: boolean;
  contactAddressId?: number;
  countryRegionId?: number;
}
