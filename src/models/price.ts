import CatalogPrice from './catalog_price';
import PriceFamily from './price_family';
import PriceOption from './price_option';
import PriceTypeLogic from './price_type_logic';

export default interface Price {
  id: number;
  priceFamilyId?: number;
  name: string;
  labelName: string;
  priceFamily?: PriceFamily;
  CatalogPrices?: CatalogPrice[];
  PriceOptions?: PriceOption[];
  description: string;
  priceTypeLogicId: number;
  PriceTypeLogic?: PriceTypeLogic;
  defaultPrice: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
