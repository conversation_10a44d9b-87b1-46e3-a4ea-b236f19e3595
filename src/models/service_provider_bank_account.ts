export default interface ServiceProviderBankAccount {
  id?: number;
  serviceProviderId: number;
  bankName?: string;
  ownerName?: string;
  IBAN?: string;
  formattedAddress?: string;
  address?: string;
  city?: string;
  postalcode?: string;
  country?: string;
  BIC?: string;
  region?: string;
  isConnectedMP?: boolean;
  countryMP?: string;
  isPayoutAuto?: boolean;
  isPaymentActive?: boolean;
  MPUserId?: boolean;
  MPBankAccountId?: boolean;
  MPWalletId?: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
