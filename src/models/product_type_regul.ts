import Product from "./product";
import ProductType from "./product_type";

export default interface ProductTypeRegul {
    id?: number;
    productTypeId?: number;
    regulProductTypeId?: number;
    productId?: number;
    description?: string;
    defaultPrice?: any;
    isActive?: boolean;
    Product?: Product;
    ProductType?: ProductType;
    RegulProductType?: ProductType;
    createdAt?: Date;
    updatedAt?: Date;
    deletedAt?: Date;
  }
  