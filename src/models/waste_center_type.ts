import WasteCenter from './waste_center';
import WasteType from './waste_type';

export default interface WasteCenterType {
  id: number;
  wasteCenterId: number;
  wasteCenter?: WasteCenter;
  wasteTypeId: number;
  wasteType?: WasteType;
  tauxDeReutilisation: number;
  tauxDeRecyclage: number;
  tauxDeValoEnerge: number;
  tauxElimination: number;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
