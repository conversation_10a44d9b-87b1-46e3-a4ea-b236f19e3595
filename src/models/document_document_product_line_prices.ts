export default interface DocumentProductLinePrice {
  id?: number;
  documentProductLineId?: string | number;
  priceFamilyId?: string | number;
  priceId?: string | number;
  priceOptionId?: string | number;
  value?: string | number;
  isOptionPriceValue?: boolean;
  priceFamilyLabel?: string;
  priceLabel?: string;
  priceOptionLabel?: string;
  priceValue?: string | number;
  priceSubOptionId?: string | number;
  priceSubOptionLabel?: string;
  linePriceId?: string | number;
  validSP?: number | null;
  priceMargin?: number | null;
  buyingPrice?: number | null;
}
