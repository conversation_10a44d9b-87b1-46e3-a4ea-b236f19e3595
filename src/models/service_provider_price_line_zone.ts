import Price from './price';
import Zone from './zone';
import ServiceProviderPriceLine from './service_provider_price_line';

export default interface ServiceProviderPriceLineZone {
  id: number;
  serviceProviderPriceLineId: number;
  ServiceProviderPriceLine?: ServiceProviderPriceLine;
  zoneId: number;
  Zone?: Zone;
  priceId: number;
  Price?: Price;
  price: string;
  isActive: boolean;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
