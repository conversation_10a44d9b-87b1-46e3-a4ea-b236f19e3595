import { TarifType } from "types";
import CatalogPrice from "./catalog_price";
import Price from "./price";
import ProductType from "./product_type";

export default interface PriceFamily {
  id: number;
  productTypeId?: number;
  name: string;
  key: TarifType;
  labelName: string;
  isActive: boolean;
  productType?: ProductType;
  CatalogPrice?: CatalogPrice;
  CatalogPrices?: CatalogPrice[];
  Prices?: Price[];
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
