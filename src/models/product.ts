import ProductCatalogOptionPrice from './product_catalog_option_price';
import ProductCatalogPrice from './product_catalog_price';
import ProductType from './product_type';

export default interface Product {
  id: number;
  productTypeId: number;
  name: string;
  isActive?: boolean;
  isVisible?: boolean;
  exclude?: boolean;
  description: string;
  ProductCatalogPrices?: ProductCatalogPrice[];
  ProductCatalogOptionPrices?: ProductCatalogOptionPrice[];
  groupId?: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  booksProductId?: string;
  ProductType?: ProductType;
  parentKey?: string;
  mainProductId?: string;
  lastSyncZohoDate?: Date;
  syncStatus?: 'in_progress' | 'success' | 'failed';
  frequencyCount?: number;
}
export interface MultiProduct extends Product {
  MainProduct?: Product;
  SubProduct?: Product[];
}
