import DocumentCCContact from './document_document_cc_contact';
import DocumentCCLibresContact from './document_document_cc_libres_contact';
import DocumentProductLine from './document_document_product_lines';
import DocumentType from './document_document_type';
import DocumentStatus from './document_document_status';
import { UploadFileCustom } from 'components/Estimate/FileUpload';
import Contact from './contact';
import Sale from './sale';

export default interface Document {
  id: number;
  referentDocumentID: number;
  documentTypeId: number;
  cdeZoho: string;
  booksDocumentId: string;
  vendeur: number;
  referent: number;
  contactId: number;
  contactPersonId: number;
  bdcClient: string;
  contactSurPlace: string;
  billingAddressId: number;
  billingAddressFull: string;
  siteAddressId: number;
  siteAddressFull: string;
  siteCountryRegionId: number;
  siteAddressIsGps: boolean;
  siteAddressLatitude: string;
  siteAddressLongitude: string;
  siteAddressPostalCode?: string;
  siteAddressCity?: string;
  objectDuDocument: string;
  demandeCommerciale: string;
  responseLogistique: string;
  prestationVueClient: string;
  priceDescription: string;
  logComment: string;
  comment: string;
  isActive: boolean;
  documentStatusId: number;
  isAddressChantierTemporary: boolean;
  paymentStatus: string;
  paymentType: string;
  paiement: string;
  montantPaiement: string;
  conditionsDePaiement: string;
  retainerInvoiceSubTotal: string;
  createdFrom: 'Zoho' | 'QBO';
  latestPrestationDate?: Date;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  DocumentCCLibresContacts?: DocumentCCLibresContact[];
  DocumentCCContacts?: DocumentCCContact[];
  DocumentType?: DocumentType;
  DocumentStatus?: DocumentStatus;
  genererLienDePaiement?: string;
  booksCurrentSubStatusId?: string;
  booksCurrentSubStatus?: string;
  rappelDevis?: string;
  DocumentProductLines?: DocumentProductLine[];
  booksTaxId?: string;
  DocumentFileUploads?: UploadFileCustom[];
  EstimateDocument?: Document;
  Contact?: Contact;
  Vendeur?: Sale;
  Referent?: Sale;
}
