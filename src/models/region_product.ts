import ProductCatalogOptionPrice from './product_catalog_option_price';
import ProductCatalogPrice from './product_catalog_price';

export default interface RegionProduct {
  id: number;
  name: string;
  unit?: any;
  ProductCatalogPrices?: ProductCatalogPrice[];
  ProductCatalogOptionPrices?: ProductCatalogOptionPrice[];
  groupId?: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
