import ClientContact from './client_contact';
import ContactAddress from './client_contact_addresses';

export default interface ClientContactPerson {
  id?: number;
  contactId?: number;
  firstName?: string;
  lastName?: string;
  phone?: string;
  email?: string;
  crmContactId?: number | string;
  booksContactPersonsId?: number;
  referent?: number;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
  Contact?: ClientContact;
  ContactAddresses?: ContactAddress[];
}
