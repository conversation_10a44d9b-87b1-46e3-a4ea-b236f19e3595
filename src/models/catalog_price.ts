import CatalogPriceLineCatalogPrice from './catalog_price_line_catalog_price';
import Price from './price';
import PriceFamily from './price_family';
import PriceOption from './price_option';
import PriceSubOption from './price_sub_option';
import ProductType from './product_type';
export default interface CatalogPrice {
  id?: number;
  productTypeId: number;
  priceFamilyId?: number | null;
  priceId?: number | null;
  priceOptionId?: number | null;
  isActive?: boolean;
  isCatalog?: boolean;
  PriceFamily?: PriceFamily | null;
  ProductType?: ProductType | null;
  Price?: Price | null;
  PriceOption?: PriceOption | null;
  PriceSubOption?: PriceSubOption | null;
  CatalogPriceLineCatalogPrice?: CatalogPriceLineCatalogPrice;
  createdAt?: Date;
  updatedAt?: Date;
  deletedAt?: Date;
}
