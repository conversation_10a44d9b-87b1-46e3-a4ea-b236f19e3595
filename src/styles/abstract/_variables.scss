// COLOR VARIABLES
$color-primary: #95C515;
$color-secondary: #E75012;
$color-white: #fff;
$color-dark: #1D2123;
$color-green: #BAE34D;
$color-grey: #DEDEDE;
$color-grey-weight: #A1A4AC;
$color-blue: #3481ff;
$color-danger: #cf1421;
$color-warning: #FFA940;
$color-yellow: #D4B106;
$color-pink: #f759ab;
$color-turmeric: #d4b108;
$color-crimson: #820014;
$color-marquer: #722ED1;

// TEXT COLOR VARIABLES
$text-white: #fff;
$text-grey: #A9A9A9;
$text-dark: #000000;
$text-green: #A6C84D;
$text-danger: #cf1421;

// BACKGROUND COLOR VARIABLES
$bg-white: #fff;
$bg-primary: #D9D9D9;
$bg-danger: #cf1421;
$bg-grey: #EFEFEF;
$bg-green: #A6C84D;
$bg-dark: #1D2123;
$bg-bold-blue: #224957;

// HOVER AND PRESS COLOR
$hover-item-color: rgba(0, 0, 0, 0.25);
$press-item-color: #E0EDC2 ;

$border-dark: rgba(0, 0, 0, 0.15);

// COLOR BUTTON
$color-bnt-primary: #1677ff;
$color-bnt-red: #820014;

// Tag
$color-status-new: #531dab;
$color-status-new-border: #d3adf7;
$color-status-new-background: #f9f0ff;

$color-status-draft: #d46b08;
$color-status-draft-border: #ffd591;
$color-status-draft-background: #fff7e6;

$color-status-sent: #0958d9;
$color-status-sent-border: #91caff;
$color-status-sent-background: #e6f4ff;

$color-status-accepted: #52C41A;
$color-status-accepted-border: #B7EB8F;
$color-status-accepted-background: #F6FFED;

$color-status-facture: #13C2C2;
$color-status-facture-border: #87E8DE;
$color-status-facture-background: #E6FFFB;

$color-status-modify: #722ED1;
$color-status-modify-border: #D3ADF7;
$color-status-modify-background: #F9F0FF;

$color-status-refuse: #F5222D;
$color-status-refuse-border: #FFA39E;
$color-status-refuse-background: #FFF1F0;

$color-status-expire: #000000E0;
$color-status-expire-border: #afafaf;
$color-status-expire-background: #E3E3E3;

$color-status-todo: #FADB14;
$color-status-todo-border: #FFFB8F;
$color-status-todo-background: #FEFFE6;

$color-status-prise: #1677FF;
$color-status-prise-border: #91CAFF;
$color-status-prise-background: #E6F4FF;