.product-page {
    &__creation-and-searching {
        display: flex;
        flex-direction: row;
        margin-top: 23px;
        margin-bottom: 42px;
    }

    &__searching {
        margin-right: 40px !important;
        margin-left: auto !important;
    }

    &__search-bar {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        width: 423px !important;
        line-height: 40px !important;
        background: $color-white !important;
        border-radius: 8px;

        &::placeholder {
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.25) !important;
        }

        &:hover {
            border-color: $color-green !important;
        }

        &:focus-within {
            border-color: $color-green !important;
        }
    }

    &__datatable {
        width: 100% !important;
    }
}

.datatable {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 8px;
    width: 32px;
    height: 32px;

    &__item {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__action-edit-button {
        margin-right: 16px;
        color: #389E0D !important;
        gap: 0 !important;
    }

    &__action-destroy-button {
        color: #FF4D4F !important;
        gap: 0 !important;
    }
}

.anticon-double-left,
.anticon-double-right {
    color: $color-green !important;
}

.ant-pagination-item-active {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px 6px;
    gap: 10px;
    border: 1px solid $color-green !important;
    border-radius: 6px;
    background: none !important;
}

.ant-pagination-item-active a {
    font-family: 'Roboto';
    font-style: italic;
    font-weight: 100;
    color: $color-green !important;
}

// .ant-input-wrapper {
//     display: inherit;
//     height: 40px !important;
// }

.ant-input-affix-wrapper {
    &:hover {
        border-color: $color-green !important;
    }

    &:focus {
        border-color: $color-green !important;
    }
}

// .ant-input-search-button {
//     display: flex;
//     flex-direction: column;
//     justify-content: center;
//     align-items: center;
//     padding: 0px;
//     gap: 8px;
//     top: 0;
//     position: absolute;
//     width: 42px !important;
//     height: 100% !important;
//     background: $color-white !important;
//     border: 1px solid rgba(0, 0, 0, 0.15);
//     border-radius: 0px 8px 8px 0px;

//     &:hover {
//         border-color: $color-green !important;
//     }

//     &:focus {
//         border-color: $color-green !important;
//     }
// }