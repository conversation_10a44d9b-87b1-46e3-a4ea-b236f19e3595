.provider-invoice-page {
  &__creation-and-searching {
    display: flex;
    flex-direction: row;
    margin-top: 23px;
    margin-bottom: 42px;
  }

  &__searching {
    margin-right: 40px !important;
    margin-left: auto !important;
  }

  &__search-bar {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 0px;
    width: 423px !important;
    line-height: 40px !important;
    background: $color-white !important;
    border-radius: 8px;

    &::placeholder {
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.25) !important;
    }

    &:hover {
      border-color: $color-green !important;
    }

    &:focus-within {
      border-color: $color-green !important;
    }
  }

  &__datatable {
    width: 100% !important;
  }

  &__btn {
    padding: 0px 16px;
    gap: 8px;
    min-width: 77px;
    height: 32px;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    color: $color-white;
    cursor: pointer;
    &__send {
      border: 1px solid $text-green;
      background: $text-green;
      color: $color-white;
      &:hover {
        border: 1px solid $text-green !important;
        color: $color-white !important;
      }
      &:disabled {
        border: 1px solid $text-green !important;
        background: $text-green !important;
        color: $color-white !important;
        cursor: not-allowed !important;
      }
    }
    &__quit {
      border: 1px solid $color-danger;
      background: $color-danger;
      color: $color-white !important;
      &:hover {
        border: 1px solid $color-danger !important;
        color: $color-white !important;
      }
    }
    &__draft {
      border: 1px solid $color-blue;
      background: $color-blue;
      color: $color-white !important;
      &:hover {
        border: 1px solid $color-blue !important;
        color: $color-white !important;
      }
    }
    &__info {
      border: 1px solid $color-turmeric;
      background: $color-turmeric;
      color: $color-white !important;
      &:hover {
        border: 1px solid $color-turmeric !important;
        color: $color-white !important;
      }
    }
    &__validate {
      border: 1px solid $color-crimson;
      background: $color-crimson;
      color: $color-white !important;
      &:hover {
        border: 1px solid $color-crimson !important;
        color: $color-white !important;
      }
    }
    &__marquer {
      border: 1px solid $color-marquer;
      background: $color-marquer;
      color: $color-white !important;
      &:hover {
        border: 1px solid $color-marquer !important;
        color: $color-white !important;
      }
      &:disabled {
        border: 1px solid $color-marquer !important;
        background: $color-marquer !important;
        color: $color-white !important;
        cursor: not-allowed !important;
      }
    }
  }
}
.datatable {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  padding: 0px;
  gap: 8px;
  width: 32px;
  height: 32px;

  &__item {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      display: flex;
      align-items: center;
      color: rgba(0, 0, 0, 0.88);
  }

  &__action-edit-button {
      margin-right: 16px;
      color: #389E0D !important;
      gap: 0 !important;
  }

  &__action-destroy-button {
      color: #FF4D4F !important;
      gap: 0 !important;
  }
}

.anticon-double-left,
.anticon-double-right {
    color: $color-green !important;
}

.order-payment-info-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  height: 50px;
  margin-bottom: 20px;
}

.line {
  width: 100%;
  height: 1px;
  background-color: rgb(204, 204, 204);
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.centered-div {
  z-index: 1;
  display: inline-block;
  padding: 9px 12px;
  background: #F5F5F5;
  border-radius: 8px;
  box-shadow: 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 9px 28px 8px rgba(0, 0, 0, 0.05);
  pointer-events: all;
}

.centered-div-popup {
  z-index: 1;
  display: inline-block;
  padding: 9px 0px;
  background: #F5F5F5;
  pointer-events: all;
}

.invoice-blocks-container {
  margin-top: 20px;
}

.invoice-block {
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  margin-bottom: 24px;
  overflow: hidden;
}

.invoice-header {
  background-color: #1890ff;
  color: white;
  padding: 12px 16px;
  font-weight: bold;
  font-size: 16px;
}

.service-line {
  display: flex;
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
  background-color: white;
}

.service-line.selected {
  background-color: #f6ffed;
}

.line-section {
  padding: 0 8px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

/* Product info */
.product-info {
  min-width: 200px;
}

.product-name {
  font-weight: bold;
  margin-bottom: 4px;
}

.order-ref {
  font-size: 0.85em;
  text-decoration: underline;
}

/* Date info */
.date-info {
  min-width: 200px;
}

.description {
  color: #666;
  font-size: 0.85em;
  margin-top: 4px;
}

/* Price section */
.price-section {
  min-width: 180px;
}

.price-item {
  margin-bottom: 8px;
}

.price-item.highlighted {
  border: 1px solid #52c41a;
  border-radius: 4px;
  padding: 4px;
}

/* Quantity section */
.quantity-section {
  display: flex;
  gap: 8px;
}

/* Total section */
.total-section {
  min-width: 150px;
}

.client-price {
  color: #666;
  font-size: 0.9em;
}

.margin {
  color: #52c41a;
  font-weight: bold;
}

/* Actions section */
.actions-section {
  min-width: 200px;
  display: flex;
  gap: 8px;
}

.status-pending {
  background-color: #fa8c16;
  color: white;
  border-color: #fa8c16;
}

/* Order info panel */
.order-info-panel {
  background-color: #f9f9f9;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  height: 100%;
}

.order-info-header {
  font-weight: bold;
  font-size: 16px;
  border-bottom: 1px solid #e8e8e8;
  padding-bottom: 8px;
  margin-bottom: 16px;
}

.order-details {
  margin-bottom: 20px;
}

.detail-item, .detail-section {
  margin-bottom: 12px;
}

.detail-section {
  border-top: 1px solid #eee;
  padding-top: 12px;
}

.order-summary {
  background-color: white;
  border: 1px solid #e8e8e8;
  border-radius: 4px;
  padding: 16px;
  margin-top: 20px;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-weight: 500;
}