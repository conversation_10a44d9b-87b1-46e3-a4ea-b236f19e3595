.service-provider {
  &__info {
    .ant-form {
      .ant-form-item-explain-error {
        display: none;
      }

      .ant-color-picker-trigger {
        border: none;

        &.ant-color-picker-trigger-disabled {
          background: none;
          color: rgba(0, 0, 0, 0.88);

          & .ant-color-picker-trigger-text {
            color: rgba(0, 0, 0, 0.88);
          }
        }

        &.ant-color-picker-trigger-active {
          box-shadow: none;
        }
      }

      .ant-form-item-has-error {
        .google-auto-complete-input {
          border-color: $color-danger !important;
        }
      }
    }

    @include media-screen(lg) {
      .email-group {
        padding-right: 1.5% !important;
      }

      .color-group {
        padding-left: 0 !important;
      }
    }
    .fichier-field{
      display: flex;
      justify-content: center;
    }
  }

  &__creation-and-searching {
    display: flex;
    flex-direction: row;
    margin-top: 23px;
    margin-bottom: 42px;
  }

  &__searching {
    margin-right: 40px !important;
    margin-left: auto !important;
  }

  &__search-bar {
    display: flex;
    flex-direction: row;
    align-items: flex-start;
    padding: 0px;
    width: 423px !important;
    line-height: 40px !important;
    background: $color-white !important;
    border-radius: 8px;

    &::placeholder {
      font-size: 16px;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.25) !important;
    }

    &:hover {
      border-color: $color-green !important;
    }

    &:focus-within {
      border-color: $color-green !important;
    }
  }

  &__bank-account {
    .mangopay-img-container {
      display: flex;
      justify-content: flex-end;

      .mangopay-img {
        height: 60px;
        margin-bottom: 24px;
      }
    }

    .mangopay-block {
      display: flex;
      align-items: center;
    }

    .ant-form {
      .ant-form-item-explain-error {
        display: none;
      }

      .ant-form-item-has-error {
        .google-auto-complete-input {
          border-color: $color-danger !important;
        }
      }

      .ant-select-status-error {

        .ant-select-selector:hover,
        .ant-select-selector:focus,
        .ant-select-selector:focus-within {
          border-color: $color-danger !important;
        }
      }
    }
  }
  &__remote-select {
    .fade-in-option {
      animation: fadeInUp 0.3s ease forwards;
    }

    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(6px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .select-spinner {
      transition: opacity 0.3s ease;
      opacity: 0.8;
    }
  }

}