.login-page {
  background-color: $bg-dark;
  background-size: cover;
  min-height: 100vh;
  position: relative;
  overflow-x: hidden;

  &__bg {
    background: $bg-dark;
    width: 200%;
    height: calc(100% - 330px);
    display: block;
    border-radius: 50% 50% 0 0;
    position: absolute;
    top: 330px;
    left: -50%;
    right: -50%;
  }

  &__container {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    position: relative;
    z-index: 99;
    height: 100vh;
  }

  &__logo-ecodrop-box {
    width: auto;
    height: 87.23px;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
    padding: 5.12px 82px;
    gap: 10px;
    flex: none;
    order: 0;
    flex-grow: 0;
    margin-bottom: 8px;
  }

  &__logo-ecodrop {
    width: 141px;
    height: 77px;
    flex: none;
    order: 0;
    flex-grow: 0;
    object-fit: contain;
    object-position: center;
  }

  .login-form {
    background: $bg-dark 0% 0% no-repeat padding-box;
    max-width: auto;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 33px 22.5px;
    gap: 8px;
    width: auto;
    height: auto;
    left: 545px;
    top: 240px;
    border: 1px solid $bg-bold-blue;
    border-radius: 6px;
    .ant-form {
      width: 100%;
      label {
        color: $color-dark;
      }
    }
    @include media-screen(xs) {
      width: 308px;
    }
  }
}

.login-form {
  &__title {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    font-size: 14px;
    color: $color-white;
    line-height: 22px;
    display: flex;
    align-items: center;
    text-align: center;
    margin-bottom: 8px;
  }

  .ant-form label {
    color: $color-dark;
  }

  .ant-input {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px 0px;
    padding-left: 12px;
    gap: 10px;
    width: 90%;
    height: 22px;
    border: 1px dashed $bg-bold-blue;
    border-radius: 6px;
    background: $bg-bold-blue;
    color: $text-white;
    font-size: 14px;
    font-weight: 400;
    flex: none;
    order: 1;
    align-self: stretch;
    flex-grow: 0;

    &:hover {
      border-color: transparent;
      border-right-width: 0;
    }
    &:focus-within {
      border: none;
    }

    &::placeholder {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      text-align: left;
      color: $color-white;
    }
    .ant-input-affix-wrapper {
      line-height: 2.5 !important;
    }
  }

  .ant-input:-webkit-autofill,
  .ant-input:-webkit-autofill:hover,
  .ant-input:-webkit-autofill:focus,
  .ant-input:-webkit-autofill:active{
      -webkit-box-shadow: 0 0 0 30px $bg-bold-blue inset !important;
      -webkit-text-fill-color: $color-white;
  }

  #ant-input-error-placeholder {
    &::placeholder {
      color: $text-danger !important;
    }
  }

  .ant-input-prefix {
    height: 22px;
  }

  &__input-email {
    height: 32px;
    margin-bottom: 24px;
  }

  &__input-password {
    height: 32px;
    margin-bottom: 29px;
  }

  .ant-input-affix-wrapper {
    height: 32px;
    border: none;
    border-color: transparent;
    border-radius: 6px;
    padding: 5px 12px;
    background: $bg-bold-blue;
    outline: none !important;
    box-shadow: none;
    color: $text-grey;
  }

  &__login {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px 16px;
    gap: 8px;
    border: 1px solid $bg-green;
    border-radius: 6px;
    width: 305px;
    height: 32px;
    background: $bg-green;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
    color: $text-white;
    span {
      font-family: 'Roboto';
      font-style: normal;
      font-weight: 400;
      font-size: 14px;
      line-height: 22px;
      color: $text-white;
    }
  }

  .ant-input-suffix {
    border: none;
  }

  .ant-checkbox-wrapper span {
    font-style: normal;
    font-weight: 500;
    font-size: 14px;
    line-height: 17px;
    text-align: center;
    color: $text-white;
  }

  .ant-checkbox-input {
    background: $bg-bold-blue !important;
  }

  &__forgot {
    font-family: 'Roboto';
    font-style: normal;
    font-weight: 400;
    line-height: 22px;
    margin-bottom: 29px;
    color: $text-green;
    font-weight: 500;
    text-align: right;
    padding-left: 2px;
    letter-spacing: 0.2px;
  }

  .login-form__login {
    &:hover {
        color: $text-white;
        border: none;
    }
  }

    .login-form__forgot {
    &:hover {
      color: $text-green;
    }
  }
}

// .ant-checkbox-inner {
//   border: none !important;
//   background-color: $bg-bold-blue !important;
// }