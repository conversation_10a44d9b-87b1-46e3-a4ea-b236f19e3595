.back-to-product-list {
    margin-bottom: 30px;
}

.back-to-product-list-btn {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 0px 16px;
    padding-left: 0;
    gap: 8px;
    position: absolute;
    height: 32px;
    border-radius: 6px;
    color: $text-green;

    &:hover {
        color: $text-green !important;
    }

    span {
        margin-inline-start: 0 !important;
    }

    .left-arrow-icon {
        width: 16px !important;
        height: 16px !important;
        margin-right: 4px !important;
        padding-top: 2px;
    }
}
.service-provider{
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.service-provider-detail__content{
    padding: 0 20px 0 0;
}
.product-form {
    &__create-product-space {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 0px;
        gap: 12px;
    }

    &__button-block {
        display: flex;
        justify-content: center;
        align-items: center;
    }

    &__create-product-button {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0px 46px !important;
        gap: 8px;
        width: 180px;
        height: 40px;
        background: $text-green;
        box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
        border-radius: 8px;
        margin-right: 12px;

        span {
            font-size: 14px !important;
        }

        &:hover {
            background: $text-green !important;
        }

        &:disabled {
            background: rgba(0, 0, 0, 0.04) !important;
        }
    }

    &__export-product-button {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0px 46px !important;
        gap: 8px;
        width: 180px;
        height: 40px;
        background: $text-green;
        box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
        border-radius: 8px;

        span {
            font-size: 14px !important;
        }

        &:hover {
            background: $text-green !important;
        }

        &:disabled {
            background: rgba(0, 0, 0, 0.04) !important;
        }
    }
}
.datatable-loader {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 8px;
    width: 100%;
    height: 80px;
}