.catalog-price-table {
    .editable-cell-value-wrap {
        padding: 2px 7px;
        cursor: pointer;
        &.right-icon {
            padding-right: 0px;
            padding-left: 4px;
        }
        text-align: right;
    }
    .editable-row {
        .editable-cell-value-wrap:hover {
            padding: 4px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            &.right-icon {
                padding-right: 0px;
                padding-left: 4px;
            }
        }
    }
    &__bullet-point-icon {
        width: 6px !important;
        height: 6px !important;
        color: rgba(0, 0, 0, 0.45);
        margin-right: 6px !important;
        cursor: pointer;

        svg {
            width: 6px !important;
            height: 6px !important;
        }
    }

    &__price-option-tag {
        margin: 2px 2px;
    }

    &__price-line-name {
        margin-right: 2px;
        text-decoration: underline;
    }
    &__zone {
        vertical-align: top;
    }

    // &__price-line {
    //     // display: flex !important;
    //     // align-items: center  !important;
    //     // margin-bottom: 10px;
    //     // padding-bottom: 6px;
    // }

    &__zone-price {
        display: flex !important;
        justify-content: center !important;
        align-items: center  !important;
        margin-bottom: 10px;
        padding-bottom: 6px;
    }

    .ant-table-row-selected td {
        background-color: rgba(166, 200, 77, 0.5) !important;
    }
    .ant-table-tbody >tr >td {
        // vertical-align: middle;
        background: #FFFFFF !important;
        padding: 16px 12px;
        .text-underline {
            text-decoration: underline;
        }
        .sub-option-container {
            display: flex;
            padding-left: 8px;
            .sub-option-name {
                font-style: italic;
                min-width: 65px;
            }
        }
        .catalog-price-comment {
            padding-top: 10px;
        }
        .btn-add__ajouter-btn > span {
            text-decoration: underline;
        }
        &.sub-option-line {
            padding-top: 6px;
        }
        .ant-form-item {
            margin-bottom: 0;
        }
        .ant-form-item-label {
            font-style: italic;
        }
        &.non-bottom-border {
            border-bottom: 0px !important;
            padding-bottom: 0;
        }
        .datatable__action-edit-button, 
        .datatable__action-destroy-button {
            width: 25px;
        }
        &.table-action {
            padding: 16px 6px;
            text-align: center;
        }
        .ant-form-item-control-input {
            min-height: 24px;
            .ant-input-affix-wrapper {
                padding: 0px 7px;
            }
        }
    }
    .ant-table-tbody > tr.ant-table-row:hover > td {
        background: #FFFFFF !important;
    }
}