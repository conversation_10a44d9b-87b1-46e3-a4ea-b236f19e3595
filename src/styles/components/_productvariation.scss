.product-variation {
    &__title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-top: 45px;
        width: 100%;
    }

    &__title-label {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 22px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
    }

    &__title-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 182px !important;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 4px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__option-list {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 16px;
    }

    &__option-item {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
        margin-bottom: 24px;
    }

    &__option-item-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-bottom: 8px;
    }

    &__option-item-title-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__option-item-cancel-icon {
        margin-left: 25px;
    }

    &__sub-option-description-cancel-icon {
        margin-left: 12px !important;
    }

    &__option-item-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 182px !important;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 4px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__option-item-ajouter-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__option-item-trash-icon {
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__option-item-add-icon {
        color: $text-green;
        border: none;
    }

    &__sub-option-tag {
        padding: 1px 8px;
        gap: 4px;
        width: auto;
        height: auto;
        border: 1px solid #A6C84D;
        border-radius: 8px;
        cursor: pointer;

        &.active {
            background-color: rgba(166, 200, 77, 0.5);
        }
    }

    &__sub-option-trash-icon {
        margin-left: 5.5px;
    }

    &__sub-option-description-container {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        isolation: isolate;

        textarea {
            padding: 5px 12px;
            width: 694px;
            margin-right: 15px;
            font-size: 14px !important;

            &:read-only:focus-within {
                border: 1px solid rgba(0, 0, 0, 0.15);
            }
        }
    }
}