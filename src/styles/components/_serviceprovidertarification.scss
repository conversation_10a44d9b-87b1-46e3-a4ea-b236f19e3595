.urban-center-modal {
    &__body {
        .ant-form {
            .ant-form-item-has-error {
                .google-auto-complete-input {
                    border-color: $color-danger !important;
                }
            }
        }
    }

    &__form-input {
        .ant-row {
            display: block !important;
        }

        margin-bottom: 14px !important;
    }

    &__service-provider-urban-center {
        padding-top: 6px !important;
    }

    &__input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 326px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }
}

.adding-produits-modal {
    &__body {
        .ant-form {
            .ant-form-item-has-error {
                .google-auto-complete-input {
                    border-color: $color-danger !important;
                }
            }
        }
    }

    &__form-input {
        .ant-row {
            display: block !important;
        }
        margin-bottom: 14px !important;
    }

    &__selection {
        gap: 8px;
        width: 350px !important;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;
    }

    &__selection.ant-select-status-error {
        :hover {
            border-color: $color-danger !important;
        }

        &:focus-within {
            border-color: $color-danger !important;
        }
    }
}

.duplicate-produits-modal {
    &__body {
        .ant-form {
            .ant-form-item-has-error {
                .google-auto-complete-input {
                    border-color: $color-danger !important;
                }
            }
        }
    }

    &__form-input {
        .ant-row {
            display: block !important;
        }
        margin-bottom: 14px !important;
    }

    &__selection {
        gap: 8px;
        width: 100% !important;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;
    }

    &__selection.ant-select-status-error {
        :hover {
            border-color: $color-danger !important;
        }

        &:focus-within {
            border-color: $color-danger !important;
        }
    }
}

.modify-produits-modal {
    &__body {
        width: 100% !important;
        padding-top: 6px;

        .ant-form {
            .ant-form-item-has-error {
                .google-auto-complete-input {
                    border-color: $color-danger !important;
                }
            }
        }
    }

    &__form-input {
        .ant-row {
            display: block !important;
        }
        margin-bottom: 14px !important;
    }

    &__selection {
        gap: 8px;
        width: 350px !important;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;
    }

    &__selection.ant-select-status-error {
        :hover {
            border-color: $color-danger !important;
        }

        &:focus-within {
            border-color: $color-danger !important;
        }
    }

    &__transfer-selection {
        .ant-transfer-list {
            height: 352px !important;
        }
    }
}

.service-provider-tarification-tabs {
    .ant-spin .ant-spin-dot-item {
        background-color: $text-green !important;
    }
    background: $bg-white;
    box-shadow: 0px 5px 60px 4px rgba(0, 0, 0, 0.09);

    .ant-tabs-nav {
        background: rgba(243, 243, 243, 0.720) !important;
    }

    .ant-tabs-content-holder {
        padding: 24px;
        padding-top: 4px;
    }

    .ant-tabs-nav-list {
        .ant-tabs-tab-active {
            .ant-tabs-tab-btn {
                color: $text-green;
                font-weight: 700;
            }
        }
    }

    .ant-tabs-nav-list {
        .ant-tabs-tab {
            &:hover {
                color: $text-green;
            }
        }
    }

    .urban-center-edit-icon,
    .urban-center-add-icon {
        color: $text-green;
    }
}

.ant-table-filter-dropdown {
    .ant-space-item {
        .ant-btn {
            background: $bg-green !important;
            color: $bg-white !important;
            border: none !important;
        }
    }
}

.service-provider-price-line-products-btn {
    .btn-add {
        &__creation-text {
            background-color: $bg-green !important;
            color: $text-white !important;
        }

        &__creation-icon {
            color: $text-white !important;
        }
    }

    &:hover {
        background-color: $bg-green !important;
        color: $text-white !important;
    }

    &:focus {
        background-color: $bg-green !important;
        color: $text-white !important;
    }
}

.service-provider-price-table {
    .editable-cell-value-wrap {
        padding: 5px 12px;
        cursor: pointer;
        &.right-icon {
            padding-right: 0px;
            padding-left: 4px;
        }
    }
    .editable-row {
        .editable-cell-value-wrap:hover {
            padding: 4px 11px;
            border: 1px solid #d9d9d9;
            border-radius: 6px;
            &.right-icon {
                padding-right: 0px;
                padding-left: 4px;
            }
        }
    }
    &__bullet-point-icon {
        width: 6px !important;
        height: 6px !important;
        color: rgba(0, 0, 0, 0.45);
        margin-right: 6px !important;
        cursor: pointer;

        svg {
            width: 6px !important;
            height: 6px !important;
        }
    }

    &__price-option-tag {
        margin: 2px 2px;
    }

    &__price-line-name {
        margin-right: 2px;
        text-decoration: underline;
    }
    &__zone {
        vertical-align: top;
    }
    &__price-line-options {
        // text-align: right;
    }

    // &__price-line {
    //     // display: flex !important;
    //     // align-items: center  !important;
    //     // margin-bottom: 10px;
    //     // padding-bottom: 6px;
    // }

    &__zone-price {
        display: flex !important;
        justify-content: center !important;
        align-items: center  !important;
        margin-bottom: 10px;
        padding-bottom: 6px;
    }

    .ant-table-row-selected td {
        background-color: rgba(166, 200, 77, 0.5) !important;
    }
    .ant-table-tbody >tr >td {
        background: #FFFFFF !important;
        padding: 16px 12px;
        .ant-form-item {
            margin-bottom: 0;
        }
        .ant-form-item-label {
            font-style: italic;
        }
        &.non-bottom-border {
            border-bottom: 0px !important;
            padding-bottom: 0;
        }
        .datatable__action-edit-button, 
        .datatable__action-destroy-button {
            width: 25px;
        }
        &.service-provider-price-table__zone {
            padding: 16px 6px;
        }
        &.table-action {
            padding: 16px 6px;
            text-align: center;
        }
    }
    .ant-table-tbody > tr.ant-table-row:hover > td {
        background: #FFFFFF !important;
    }
}


