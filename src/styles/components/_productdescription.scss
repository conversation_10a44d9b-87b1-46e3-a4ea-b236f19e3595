.product-description {
    &__selection-dropdown {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        padding: 0px 16px;
        gap: 8px;
        width: 134px;
        height: 32px;
        background: $bg-white;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        color: $text-green;
        margin-bottom: 8px;

        &:hover {
            color: $text-green !important;
            border-color: $text-green !important;
        }
    }

    &__description-textarea-item {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        padding: 0px;
        gap: 8px;
    }

    &__description-textarea {
        padding: 5px 12px;
        isolation: isolate;
        width: 930px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        margin-right: 8px;

        &::placeholder {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.25);
        }
    }

    &__description-apply-edit-icon {
        margin-right: 8px;
    }

    &__description-loading-icon {
        svg {
            width: 20px !important;
            height: 20px !important;
            border-radius: 6px;
            color: $color-green;
        }
    }

    &__description-cancel-icon {
        margin-left: 14px;
    }

    &__sub-tag {
        padding: 1px 8px;
        gap: 4px;
        background: rgba(0, 0, 0, 0.15);
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 12px;
        margin-bottom: 8px !important;
        margin-inline-end: 0px;
    }

    &__trash-icon {
        padding-left: 5px;
        margin-inline-start: 0px !important;
        cursor: pointer;

        &.loading {
            padding-left: 0px !important;
            margin-left: 5px !important;
        }
    }
}