.product-tarifs {
    &__title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-top: 28px;
        width: 100%;
    }

    &__title-label {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 32px;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__title-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 182px !important;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 4px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__title-of-product-cancel-icon {
        margin-left: 14px;
    }

    &__tarif-list {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;

        .ant-list {
            width: 100% !important;
        }
    }

    &__tarif-item {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-bottom: 16px;

        &-switch {
            margin-left: 10px;
        }
    }

    &__title-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 16px;
    }

    &__edit-icon {
        margin-right: 10px;
    }

    &__trash-icon {
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__add-parameter-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        width: 172px;
        height: 24px;
        border-radius: 4px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__end-line {
        margin-top: 24px;
        width: 100% !important;
        border: 1px solid rgba(0, 0, 0, 0.15);
        margin-bottom: 24px;
    }

    &__option-item-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-bottom: 8px;
    }

    &__item-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 132px !important;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 16px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__item-cancel-icon {
        margin-left: 25px;
    }

    &__parameter-space {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        margin-bottom: 16px;
    }

    &__parameter-block {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: flex-start;
        isolation: isolate;
        width: fit-content;
        width: 1000px !important;
        min-width: 850px !important;
        height: 235px;
        padding: 20px 28px;
        border: 1px solid $text-green;
        margin-right: 8px;
    }

    &__parameter-title {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 16px;
        width: 100% !important;
    }

    &__bullet-point-icon {
        width: 10px !important;
        height: 10px !important;
        color: rgba(0, 0, 0, 0.45);
        cursor: pointer;

        svg {
            width: 10px !important;
            height: 10px !important;
        }
    }

    &__parameter-title-label {
        font-size: 13px;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__parameter-title-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        // width: 244px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }

    &__add-typeform-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        width: 196px;
        height: 24px;
        border-radius: 4px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__edit-cancel-btn {
        display: flex;
        flex-direction: row;
        align-items: flex-end !important;
    }

    &__parameter-cancel-icon {
        margin-left: 14px;
    }

    &__price-option-list {
        display: flex;
        flex-direction: row;
        justify-content: center;
        align-items: center;
    }

    &__price-option-item {
        display: flex;
        flex-direction: row;
        align-items: baseline;
        padding: 0px;
        padding-left: 26px;
        gap: 16px;
        margin-bottom: 8px;
        
        .ant-switch {
            margin-top: 8px;
        }
    }

    &__price-option-name {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__bullet-point-empty-icon {
        margin-right: 8px;
    }

    &__price-option-name-label {
        width: 34px;
        height: 22px;
        font-size: 13px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__price-option-name-input {
        padding: 0px 12px;
        gap: 10px;
        width: 244px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }

    &__price-option-input-type {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__price-option-input-type-label {
        width: 70px;
        height: 22px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__price-option-input-type-select {
        width: auto !important;
        min-width: 130px;
        height: 32px;
        background: #FFFFFF;
        border-radius: 6px;

        &:hover,
        &:focus-within,
        &:active {
            .ant-select-selector {
                border-color: $text-green !important;
            }
        }
    }

    &__price-option-unit {
        display: flex;
        align-items: center;
        justify-content: center;
    }

    &__price-option-unit-label {
        width: 38px;
        height: 22px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
    }

    &__price-option-unit-input {
        padding: 0px 12px;
        gap: 10px;
        width: 76px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }

    &__price-option-selection {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
    }

    &__price-option-value-selection-label {
        width: 50px;
        height: 22px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__price-option-value-selection-input {
        padding: 0px 12px;
        gap: 10px;
        width: 150px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
    }

    &__sub-option-tag {
        padding: 1px 8px;
        gap: 4px;
        width: auto;
        height: auto;
        border: 1px solid #A6C84D;
        border-radius: 4px;
        cursor: pointer;

        &.active {
            background-color: rgba(166, 200, 77, 0.5);
        }
    }

    &__sub-option-trash-icon {
        margin-left: 5.5px;
    }

    &__price-sub-option {
        padding: 0px;
        gap: 4px;
        width: 165px !important;
        max-width: 230px !important;
    }

    &__price-sub-option-ajouter-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        height: 32px !important;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__price-option-items {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-bottom: 16px;
    }

    &__price-option-bullet-point-icon {
        width: 10px !important;
        height: 10px !important;
        color: rgba(0, 0, 0, 0.45);
        margin-right: 16px !important;
        cursor: pointer;

        svg {
            width: 10px !important;
            height: 10px !important;
        }
    }

    &__price-option-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 13px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 6px;
    }

    &__price-option-input {
        display: flex;
        margin-bottom: 10px !important;
    }
    &__price-option-input-left {
        display: flex;
        margin-bottom: 10px !important;
        justify-content: flex-end;
    }
    &__price-option-item-left {
        display: flex;
        margin-bottom: 10px !important;
        justify-content: flex-end;
        margin-right: 5px !important;
    }
}

.product-tarifs__parameter-block .ant-space-item {
    width: 100% !important;
}