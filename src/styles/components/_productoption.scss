.option {
    display: flex;
    flex-direction: row;
    align-items: center;

    &__option_item {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 24px;
    }

    &__option-title {
        display: flex;
        flex-direction: row;
        align-items: center;
    }

    &__option-title-label {
        padding: 0;
        font-size: 14px;
        display: flex;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 8px;
    }

    &__option-title-content-input {
        padding: 0px 12px;
        gap: 10px;
        width: 260px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 29px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__option-attributes {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        padding: 0px;
        margin-bottom: 24px;
    }

    &__option-attributes-items {
        display: flex;
        align-items: center;
        margin-bottom: 16px;
    }

    &__option-attributes-bullet-point-icon {
        margin-right: 12px;
    }

    &__option-attributes-title {
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 5.75px;
    }

    &__option-attributes-edit-icon {
        margin-right: auto !important;
    }

    &__option-attributes-reference-type-select {
        font-size: 14px;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-left: 20px;

        &:hover,
        &:focus-within,
        &:active {
            .ant-select-selector {
                border-color: $color-green !important;
            }
        }
    }

    &__reference-types-trash-icon {
        margin-left: 2px;
        cursor: pointer;

        svg {
            width: 12px !important;
            height: 12px !important;
        }
    }

    &__option-attributes-default-block {
        position: relative;
        display: block !important;
        margin-top: 12px !important;
        margin-left: 30px !important;
        margin-bottom: 15px !important;
    }

    &__option-attributes-tip-default {
        position: absolute;
        left: 11%;
        top: -40px;
        z-index: 9999 !important;
        padding: 6px 8px;
        width: 130px;
        height: 34px;
        background: $bg-green !important;
        border-radius: 6px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: none;
    }

    &__option-attributes-arrow-icon {
        position: absolute;
        width: 16px !important;
        height: 8px !important;
        left: 14%;
        top: -6px;
        z-index: 9999 !important;
        color: $bg-green !important;
        fill-opacity: 1 !important;
        display: none;

        path {
            fill: $bg-green !important;
            fill-opacity: 1 !important;
        }
    }

    &__option-attributes-switch-default {
        border-radius: 16px;
    }

    &__option-attributes-trash-icon {
        margin-left: 36px;
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__sub-option-tag {
        padding: 1px 8px;
        gap: 4px;
        border: 1px solid #A6C84D;
        border-radius: 8px;
        cursor: pointer;

        &.active {
            background-color: rgba(166, 200, 77, 0.5);
        }
    }

    &__sub-option-trash-icon {
        margin-left: 5.5px;
    }

    &__sub-option {
        padding: 0px;
        margin-left: 30px !important;
    }
}

.ant-list-empty-text {
    display: none !important;
}

.option__option-attributes-switch-default:hover ~ .option__option-attributes-tip-default,
.option__option-attributes-switch-default:hover ~ .option__option-attributes-arrow-icon {
    display: block;
}

.ant-select-item-option-state {
    color: $text-green !important;
}