.service-provider-contact {
    &__form {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 12px;
        width: 100%;
    }

    &__label {
        // font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        height: 32px !important;
        line-height: 32px !important;
        align-items: center;
        white-space: nowrap;
        /* make sure the text does not wrap */
        // overflow: hidden;
        /* hide overflowed text */
        // text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
        color: rgba(0, 0, 0, 0.88);
    }

    &__name-text {
        padding: 0px 12px;
        width: auto;
        min-width: 80px;
        height: 32px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__mobile-text {
        padding: 0px 12px;
        width: auto;
        min-width: 110px;
        height: 32px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__email-text {
        padding: 0px 12px;
        width: auto;
        min-width: 180px;
        height: 32px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__function-selection {
        gap: 8px;
        width: 180px;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;

        &:hover {
            border: 0px solid $color-green !important;
        }

        &:focus-within {
            border: 0px solid $color-green !important;
        }
    }

    &__end-line {
        margin-top: 24px;
        width: 100% !important;
        border: 1px solid rgba(0, 0, 0, 0.15);
        margin-bottom: 24px;
    }

    &__update-cancel-icon {
        margin-left: 14px;
    }

    &__edit-icon {
        margin-right: 8px;
    }

    &__trash-icon {
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__ajouter-btn {
        justify-content: center;
        align-items: flex-start;
        padding: 0px 8px;
        gap: 8px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__add-icon {
        color: $text-green;
        border: none;
    }

    &__cancel-icon {
        vertical-align: -0.3em !important;
        cursor: pointer;
        margin-left: 14px;
    }

    &__input {
        padding: 0px 12px;
        gap: 10px;
        height: 32px !important;
        line-height: 32px !important;
        background: $bg-white;
        border: 1px solid $text-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
    }
}

.ant-select-selection-item {
    padding-inline-end: 0 !important;
}