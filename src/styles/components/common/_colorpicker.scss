.color-picker-readonly {
  display: flex;
  align-items: center;
  min-width: 32px;
  height: 32px;
  border-radius: 6px;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: #ffffff;
  padding: 3px;
  cursor: not-allowed;
  span {
    padding-left: 8px;
    cursor: not-allowed;
  }
  .color-box {
    width: 24px;
    height: 24px;
    cursor: not-allowed;
    border-radius: 6px;
    border: 1px solid #d9d9d9;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s;
    background: #ffffff;
    padding: 3px;
  }
}
.ant-form-item-has-error {
  .ant-color-picker-color-block {
    border: 1px solid $color-danger;
  }
}