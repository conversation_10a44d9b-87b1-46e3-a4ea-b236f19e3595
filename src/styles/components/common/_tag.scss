.ant-tag {
    text-transform: unset;

    &.color_status_new {
        color: $color-status-new;
        border-color: $color-status-new-border;
        background: $color-status-new-background;
    }

    &.color_status_draft {
        color: $color-status-draft;
        border-color: $color-status-draft-border;
        background: $color-status-draft-background;
    }

    &.color_status_sent {
        color: $color-status-sent;
        border-color: $color-status-sent-border;
        background: $color-status-sent-background;
    }

    &.color_status_accepted {
        color: $color-status-accepted;
        border-color: $color-status-accepted-border;
        background: $color-status-accepted-background;
    }

    &.color_status_facture {
        color: $color-status-facture;
        border-color: $color-status-facture-border;
        background: $color-status-facture-background;
    }

    &.color_status_modify {
        color: $color-status-modify;
        border-color: $color-status-modify-border;
        background: $color-status-modify-background;
    }

    &.color_status_refuse {
        color: $color-status-refuse;
        border-color: $color-status-refuse-border;
        background: $color-status-refuse-background;
    }

    &.color_status_expire {
        color: $color-status-expire;
        border-color: $color-status-expire-border;
        background: $color-status-expire-background;
    }

    &.color_status_todo {
        color: $color-status-todo;
        border-color: $color-status-todo-border;
        background: $color-status-todo-background;
    }

    &.color_status_prise {
        color: $color-status-prise;
        border-color: $color-status-prise-border;
        background: $color-status-prise-background;
    }

} 