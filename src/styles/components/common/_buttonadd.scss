.btn-add {
    &__creation {
        margin-left: 0 !important;
        margin-right: auto !important;
    }

    &__filled-creation-button {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0px 16px !important;
        gap: 8px;
        position: inherit;
        width: auto;
        height: auto;
        border: 1px solid $text-green !important;
        background: none;
        box-shadow: none !important;

        .btn-add {
            &__creation-text {
                background-color: $bg-green !important;
                color: $text-white !important;
            }

            &__creation-icon {
                color: $text-white !important;
            }

            &:hover {
                background-color: $bg-green !important;
                color: $text-white !important;
            }

            &:focus {
                background-color: $bg-green !important;
                color: $text-white !important;
            }
        }
    }

    &__creation-button {
        display: flex;
        justify-content: center;
        align-items: center;
        padding: 0px 16px !important;
        gap: 8px;
        position: inherit;
        width: auto;
        height: auto;
        border: 1px solid $text-green !important;
        background: none;
        box-shadow: none !important;

        &:hover {
            background: none !important;
        }

        &:focus {
            background: none !important;
        }
    }

    &__creation-icon {
        color: $text-green;
        border: none;
    }

    &__creation-text {
        font-size: 16px;
        line-height: 24px;
        margin-left: 0 !important;
        color: $text-green;
    }

    &__ajouter-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }
    
    &__add_icon {
        color: $text-green;
        border: none;
    }
}