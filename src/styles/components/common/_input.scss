.ant-input, .google-auto-complete-input {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.15);
  border-radius: 6px;
  margin-right: 4px;

  &:hover, &:focus, &:focus-within, &:focus-visible {
    border: 1px solid $color-green;
    outline: 0;
  }

  &:focus {
    box-shadow: none !important;
  }
}
.ant-input-number  {
  background: #FFFFFF;
  border: 1px solid rgba(0, 0, 0, 0.15);
  box-shadow: none !important;
  &:hover {
    border: 1px solid $color-green;
  }

  &:focus-within {
    border: 1px solid $color-green;
  }

  &:focus {
    box-shadow: none !important;
  }
  &.input-number-non-spinner {
    padding: 0 1px;
    .ant-input-number-handler-wrap {
      display: none;
    }
  }
}
.ant-input-affix-wrapper {
  &:hover {
    border: 1px solid $color-green;
  }

  &:focus-within {
    border: 1px solid $color-green;
  }

  &:focus {
    box-shadow: none !important;
  }
  &.ant-input-affix-wrapper-status-error {
    border: 1px solid $color-danger;
    &:hover {
      border: 1px solid $color-danger !important;
    }
  
    &:focus-within {
      border: 1px solid $color-danger;
    }
  
    &:focus {
      box-shadow: none !important;
    }
  }
}
.ant-input-affix-wrapper.ant-input-affix-wrapper-focused {
  box-shadow: none !important;
  &.ant-input-affix-wrapper-status-error {
    border: 1px solid $color-danger;
    &:hover {
      border: 1px solid $color-danger;
    }
  
    &:focus-within {
      border: 1px solid $color-danger;
    }
  
    &:focus {
      box-shadow: none !important;
    }
  }
}
.google-auto-complete-input {
  padding: 4px 11px;
  color: rgba(0, 0, 0, 0.88);
  font-size: 14px;
  line-height: 1.5714285714285714;
  list-style: none;
  position: relative;
  display: inline-block;
  width: 100%;
  min-width: 0;
}
.urban-center-modal::placeholder{
  color: rgba(0, 0, 0, 0.25)
}
.space-remise{
    gap: 0 !important;

    .unit-remise {
      .ant-select-selector {
        border-radius: 0px 6px 6px 0px;
      }
    }
}
.remise-hover-input-and-select{
  display: inline-flex;
}
.remise-hover-input-and-select .ant-input-number {
  width: auto !important;
  border-radius: 6px 0px 0px 6px;
  border-right: none;
  margin: 0;
}
.remise-hover-input-and-select .text-right {
  width: 50px;
}
.remise-hover-input-and-select:hover input,
.remise-hover-input-and-select:hover .ant-select-selector,
.remise-hover-input-and-select:focus-within input,
.remise-hover-input-and-select:focus-within .ant-select-selector{
  border-color: $color-green !important;
}
.input-prix-unitaire input{
  text-align: right !important;
  padding-right: 24px !important;
}
.input-prix-unitaire .ant-input-number-handler-wrap,
.input-prix-tonne .ant-input-number-handler-wrap,
.input-forfait-max .ant-input-number-handler-wrap
{
  display: none;
}
.input-text-prix-unitaire input{
  text-align: right !important;
}
.input-line-under{
  margin-bottom: 0;
  border-bottom: 2px solid rgba(194, 194, 194, 0.45);
}
.ant-form-item-label > label {
  white-space: normal;
  word-break: break-all;
  width: max-content;
}