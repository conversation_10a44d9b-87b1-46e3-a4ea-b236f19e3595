.ant-table-content > table {
  border: 0.5px solid rgba(187, 187, 187, 0.5);
}

.ant-table-tbody {
  vertical-align: top;
}

.ant-table-wrapper .ant-table-thead > tr > th,
.ant-table-wrapper .ant-table-thead > tr > td {
  background: $press-item-color;
  border-color: red;
  border: none;
}

.ant-table-wrapper .ant-table-thead .ant-table-cell-fix-right {
  background: $press-item-color !important;
  border-color: red;
  border: none;
}

.ant-table-wrapper table,
.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :first-child,
.ant-table-wrapper .ant-table-container table > thead > tr:first-child > :last-child {
  border-radius: 0 !important;
}

.ant-table-thead .ant-table-cell {
  font-family: 'Roboto-Bold';
  font-weight: 700 !important;
  font-size: 14px;
  line-height: 22px;
}

.search-table .ant-table-thead > tr > th {
  background: #fafafa;
  color: black;
  font-style: italic;
  font-weight: normal !important;
}

.ant-table-wrapper .ant-table.ant-table-bordered > .ant-table-container {
  .ant-table-content > table > thead > tr > th,
  .ant-table-header > table > thead > tr > th,
  .ant-table-body > table > thead > tr > th,
  .ant-table-summary > table > thead > tr > th,
  .ant-table-content > table > tbody > tr > th,
  .ant-table-header > table > tbody > tr > th,
  .ant-table-body > table > tbody > tr > th,
  .ant-table-summary > table > tbody > tr > th,
  .ant-table-content > table > tfoot > tr > th,
  .ant-table-header > table > tfoot > tr > th,
  .ant-table-body > table > tfoot > tr > th,
  .ant-table-summary > table > tfoot > tr > th {
    border-inline-end: none;

    &:before {
      border-inline-end: 1px solid rgba(0, 0, 0, 0.15);
    }
  }

  .quotation-title-line {
    background: #fafafa;

    .ant-table-cell {
      border-inline-end: none;
      position: sticky;
      width: 100%;
      left: 0 !important;

      input {
        position: sticky;
        width: 95% !important;
        z-index: 99;
      }
    }

    .ant-table-cell {
      background: #fafafa;
      border-left: 0.5px solid rgba(0, 0, 0, 0.06);
    }
  }
}

table .ant-table-cell:nth-child(2) .ant-space {
  width: 100% !important;
}

table .ant-table-cell:nth-child(2) .ant-space .ant-picker {
  width: 100% !important;
}
table .hover-cursor-pointer{
  cursor: pointer;
}
