.product-information {
    display: flex;
    flex-direction: row;
    padding: 0px;
    gap: 24px;

    &__left-block {
        margin-right: 60px;
    }

    &__title-of-product {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        font-family: '<PERSON><PERSON>';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
        margin-bottom: 16px;
    }

    &__title-of-product-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        height: 22px !important;
        margin-right: 24px;
    }

    &__title-of-product-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 32px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 5px;
    }

    &__title-of-product-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 151px;
        height: 32px;
        background: $color-white;
        border: 1px solid $color-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 27.25px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__loading-icon svg {
        width: 20px !important;
        height: 20px !important;
        border-radius: 6px;
        color: $color-green;
    }

    &__cancel-icon {
        margin-left: 25px;
    }

    &__purchase-accounting-code-block {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 16px;
    }

    &__purchase-accounting-code-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        height: 22px !important;
        margin-right: 24px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
    }

    &__purchase-accounting-code-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 32px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 5px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
    }

    &__purchase-accounting-code-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 180px;
        height: 32px;
        background: $color-white;
        border: 1px solid $color-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 27.25px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__sales-accounting-code-block {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 16px;
    }

    &__sales-accounting-code-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        height: 22px !important;
        margin-right: 24px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
    }

    &__sales-accounting-code-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 32px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 5px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
    }

    &__sales-accounting-code-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 180px;
        height: 32px;
        background: $color-white;
        border: 1px solid $color-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 27.25px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__timeslot {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 8px;
    }

    &__timeslot-label {
        width: 119px !important;
        height: 22px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 10px;
    }

    &__timeslot-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 169px;
        height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 6px;
        margin-right: 4px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__timeslot-add-icon {
        color: $text-green;
        border: none;
    }

    &__timeslot-tag {
        padding: 0px;
        gap: 4px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 12px;
        line-height: 20px;
        color: rgba(0, 0, 0, 0.88);
        border-color: $bg-green !important;
        margin-bottom: 6px;
        cursor: pointer;

        &.active {
            background-color: rgba(166, 200, 77, 0.5);
        }

        .span-text {
            padding: 5px 0px 5px 8px;
        }
    }

    &__timeslot-trash-icon {
        padding-left: 5.5px;
        padding: 5px 8px 5px;
        margin-inline-start: 0px !important;
    }
}