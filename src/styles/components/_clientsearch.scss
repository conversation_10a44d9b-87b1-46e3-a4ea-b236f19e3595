.client-search {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: center;

  &__add-btn {
    padding: 0px 16px;
    gap: 8px;
    min-width: 77px;
    height: 32px;
    background: $text-green;
    border: 1px solid $text-green;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    color: $color-white;
    cursor: pointer;

    &:hover {
      background: $text-green !important;
      color: $color-white !important;
    }
  }
  &__search-btn {
    padding: 0px 16px;
    gap: 8px;
    min-width: 77px;
    height: 32px;
    background: $color-blue;
    border: 1px solid $color-blue;
    box-shadow: 0px 2px 0px rgba(0, 0, 0, 0.02);
    border-radius: 8px;
    color: $color-white;
    cursor: pointer;

    &:hover {
      background: $color-blue !important;
      color: $color-white !important;
    }
  }


  &__datatable {
    width: 100% !important;
  }
}

.client-search-modal {
  z-index: 9999 !important;
  .ant-input {
    margin-right: 0;
  }
  .ant-input-search-button {
    background: $text-green !important;
    border: 1px solid $text-green;
    .ant-btn-icon {
      color: $text-white;
    }
    &:hover {
      background: $text-green !important;
    }

    &:focus {
      background: $text-green !important;
    }
  }

  .ant-input-wrapper {
      display: table;
      height: auto !important;
  }
  
}
