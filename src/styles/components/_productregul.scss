.product-regul {
    &__form {
        max-width: 1080px;
        padding-top: 20px;
        .ant-form-item-control {
            .ant-select-disabled {
                .ant-select-selector {
                    background-color: $bg-white !important;
                    color: $text-dark;
                }
            }
        }
    }

    &__title-prix-block {
        max-width: 600px;
        margin-bottom: 8px;
    }

    &__description-block {
        display: flex;
        width: 100%;
        max-width: 100%;
    }

    &__description {
        display: inline-flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
        .custom-textarea {
            width: 694px;
            min-height: 120px;
        }
    }

    &__description-label {
        display: inline-flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 4px;
        width: auto;
        vertical-align: top;
        line-height: 22px;
        margin-right: 8px;
        color: rgba(0, 0, 0, 0.88);
    }

    &__description-textarea {
        gap: 8px;
        width: 694px;
        margin-right: 18px;
        &::placeholder {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.25);
        }
    }

    &__edit-delete-block {
        display: flex;
        flex-direction: row;
        align-items: flex-end;
        height: 91px !important;
    }

    &__edit-delete {
        display: inline-flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 8px;
    }

    &__title-of-product-loading-icon svg {
        width: 20px !important;
        height: 20px !important;
        border-radius: 6px;
        color: $color-green;
    }

    &__close-icon {
        vertical-align: -0.3em !important;
        margin-left: 14px;
    }

    &__title-of-product-cancel-icon {
        margin-left: 14px;
    }

    &__description-edit-icon {
        margin-right: 8px;
    }

    &__description-trash-icon {
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__end-line {
        margin-top: 24px;
        width: 100% !important;
        border: 1px solid rgba(0, 0, 0, 0.15);
    }

    &__add-prices-block {
        width: 100%;
        gap: 8px;
        margin-left: 75px;
        padding: 0px;
        margin-bottom: 10px;
    }
}