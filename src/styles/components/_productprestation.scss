.product-prestation-group-product {
  .ant-col {
    max-width: 100%;
  }
  .ant-form-item {
    display: flex;
    align-content: end;
    margin-bottom: 0;
    margin-top: 5px;
  }
  .ant-form-item-label {
    width: 130px !important;
  }
}
.product-prestation-group-price {
  .ant-col {
    max-width: 100%;
  }
  .ant-form-item {
    display: flex;
    align-content: end;
    margin-bottom: 0;
    margin-top: 5px;
  }
  .ant-form-item-label {
    min-width: 160px !important;
    height: 48px;
  }
}
.text-tarif {
  background-color: $press-item-color;
  font-weight: 700;
  width: 100%;
}
.total-price label {
  color: $text-green !important;
  font-weight: bold;
}
.ant-input-number-affix-wrapper {
  .ant-input-number-suffix {
    position: unset !important;
    height: auto !important;
  }
  .ant-input-number-input {
    padding: 4px !important;
  }
}
.prestataire-comparison-modal {
  width: 1000px !important;
  border-radius: none !important;

  &__body {
    .ant-form {
      .ant-form-item-has-error {
        .google-auto-complete-input {
          border-color: $color-danger !important;
        }
      }
    }
  }

  &__form-input {
    .ant-row {
      display: block !important;
    }
    margin-bottom: 14px !important;
  }

  &__selection {
    gap: 8px;
    width: 100% !important;
    height: 32px;
    border-radius: 6px;
    border: 0px solid $color-green;
  }

  &__selection.ant-select-status-error {
    :hover {
      border-color: $color-danger !important;
    }

    &:focus-within {
      border-color: $color-danger !important;
    }
  }

  &__header {
    display: flex;
    justify-content: flex-end !important;
    align-items: center;
    width: 100%;
    position: relative;

    &.collapsed {
      justify-content: flex-start;
      padding: 12px 24px;
    }
  }

  .ant-modal-body {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    padding: 0px 24px 12px;
    width: 100%;
    height: 100%;
    overflow: auto !important;
    box-sizing: border-box;

    .ant-form-item-label {
      text-align: right !important;
      padding-right: 0 !important;
    }
  }

  .generate-tarif-section {
    .ant-form-item {
      .ant-col {
        width: 100% !important;
      }
    }
  }

  .generate-tarif-section-empty-space {
    .ant-row {
      height: 38px !important;
    }
  }

  .type-du-tarif {
    .ant-form-item {
      .ant-col {
        width: 55% !important;
      }
    }
  }

  .ant-modal-content {
    .ant-modal-footer {
      display: flex;
      flex-direction: row;
      justify-content: center;
    }

    .ant-modal-header {
      .ant-modal-title {
        width: 100%;
        display: flex;
        justify-content: center;
        align-items: flex-end;
      }
    }
  }

  &.collapsed {
    width: 600px !important;

    .ant-modal-body {
      display: none;
      height: 0;
      padding: 0;
      overflow: hidden;
    }

    .ant-modal-content {
      .ant-modal-header {
        text-align: left;
      }

      .ant-modal-title {
        justify-content: flex-start !important;
        align-items: center;
      }
    }
  }
}
.collapsed-wrap {
  pointer-events: none;
  overflow: hidden !important;
  border-radius: none !important;
  margin-left: 20px !important;
}

.bottom-modal-wrap {
  display: flex !important;
  align-items: flex-end !important;
  justify-content: center !important;
  margin: 0 !important;
}

