@import 'sidebar';

.layout {
  display: flex;
  flex-direction: row;
  min-height: 100vh;
  overflow: hidden;
  @include media-screen(xs) {
    background: #EFEFEF;
  }
}

.main {
  background: $color-white;

  @include media-screen(xs) {
    background: #EFEFEF;
  }
}

.container {
  background: #EFEFEF;
}

.main-content {
  flex: 1 !important;
  padding-top: 24px;
  padding-left: 24px;
  padding-right: 24px;
  padding-bottom: 54px;
  color: $text-dark;
  margin-left: 264px;
  &.main-content-devis {
    margin-left: 100px !important;
  }
}
.Toastify__toast-theme--colored.Toastify__toast--success {
  background: $bg-green !important;
}
