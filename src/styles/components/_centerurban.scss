.center-urban {
    &__form {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 12px;
        padding-top: 20px;
    }

    &__title {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 8px;
        margin-right: 90px;
    }

    &__title-label {
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 22px;
        white-space: nowrap;
        /* make sure the text does not wrap */
        overflow: hidden;
        /* hide overflowed text */
        text-overflow: ellipsis;
        /* add ellipsis to indicate truncated text */
        color: rgba(0, 0, 0, 0.88);
    }

    &__title-text {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        height: 32px !important;
        line-height: 32px !important;
        width: 182px !important;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__title-text {
        padding: 0px 12px;
        width: auto;
        min-width: 180px;
        height: 32px !important;
        font-family: '<PERSON>o';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__title-input {
        padding: 0px 12px;
        gap: 10px;
        width: 111px;
        height: 32px !important;
        line-height: 32px !important;
        background: $bg-white;
        border: 1px solid $text-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 72px !important;
    }

    &__address-text {
        padding: 0px 12px;
        width: auto;
        min-width: 373px;
        height: 32px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__address-input {
        padding: 0px 12px;
        gap: 10px;
        width: 293px;
        height: 32px !important;
        line-height: 32px !important;
        background: $bg-white;
        border: 1px solid $text-green !important;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 80px !important;
        outline: none !important;
        &:focus-within {
            border: 1px solid $text-green !important;
        }

        &::placeholder {
            color: rgba(0, 0, 0, 0.25);
        }
    }

    &__address-input-error {
        border-color: $text-danger !important;
    }

    &__ajouter-btn {
        justify-content: center;
        align-items: flex-start;
        padding: 0px 8px;
        gap: 8px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__add-icon {
        color: $text-green;
        border: none;
    }

    &__cancel-icon {
        vertical-align: -0.3em !important;
        cursor: pointer;
        margin-left: 14px;
    }

    &__update-cancel-icon {
        margin-left: 14px;
    }

    &__edit-icon {
        margin-right: 8px;
    }

    &__trash-icon {
        cursor: pointer;

        svg {
            width: 20px !important;
            height: 20px !important;
        }
    }

    &__add-parameter-btn {
        justify-content: center;
        align-items: center;
        padding: 0px 8px;
        gap: 8px;
        width: 172px;
        height: 24px;
        border-radius: 4px;
        border: none;
        color: $text-green;
        box-shadow: unset !important;

        &:hover {
            color: $text-green !important;
            background: none !important;
        }

        &:focus {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }

        &:active {
            box-shadow: unset !important;
            border: none !important;
            background: none !important;
        }
    }

    &__zones {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
    }

    &__zone-list {
        display: flex;
        flex-direction: column;
        justify-content: flex-start;
        align-items: flex-start;
        padding-top: 18px;
        padding-bottom: 16px;
    }

    &__zone-item {
        padding-top: 4px;
        padding-bottom: 4px;
    }

    &__zone-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
    }

    &__zone-text {
        width: auto;
        min-width: 25px;
        height: 32px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__zone-input {
        padding: 0px 12px;
        width: 76px;
        height: 32px;
        line-height: 32px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 4px;
    }

    &__zone-unit {
        font-size: 12px;
        line-height: 22px;
        color: #000000;
    }

    &__bullet-point-icon {
        margin-right: 4px;
    }
}