.region-information {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 0px;
    gap: 24px;

    &__title-of-region {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        font-family: '<PERSON>o';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-bottom: 16px;
    }

    &__title-of-region-label {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        height: 22px !important;
        margin-right: 24px;
    }

    &__title-of-region-content {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px;
        gap: 4px;
        width: auto;
        height: 32px;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 5px;
    }

    &__title-of-region-content-input {
        display: flex;
        flex-direction: row;
        align-items: center;
        padding: 0px 12px;
        gap: 10px;
        width: 151px;
        height: 32px;
        background: $color-white;
        border: 1px solid $color-green;
        box-shadow: 0px 0px 0px 2px #E6F4FF;
        border-radius: 6px;
        margin-right: 27.25px;

        &:hover {
            border: 1px solid $color-green !important;
        }

        &:focus-within {
            border: 1px solid $color-green !important;
        }
    }

    &__title-of-region-loading-icon svg {
        width: 20px !important;
        height: 20px !important;
        border-radius: 6px;
        color: $color-green;
    }

    &__title-of-region-cancel-icon {
        margin-left: 25px;
    }

    &__platform {
        display: flex;
        flex-direction: row;
        align-items: center;
        margin-bottom: 8px;
    }

    &__platform-label {
        width: 119px !important;
        height: 22px !important;
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        margin-right: 10px;
    }

    &__platform-selection {
        gap: 8px;
        width: 150px;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;

        &:hover {
            border: 0px solid $color-green !important;
        }

        &:focus-within {
            border: 0px solid $color-green !important;
        }
    }
    &__description-textarea {
        padding: 5px 12px;
        isolation: isolate;
        width: 930px;
        background: #FFFFFF;
        border: 1px solid rgba(0, 0, 0, 0.15);
        border-radius: 8px;
        margin-right: 8px;
        font-size: 14px;
        &::placeholder {
            font-family: 'Roboto';
            font-style: normal;
            font-weight: 400;
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.25);
        }
    }
}