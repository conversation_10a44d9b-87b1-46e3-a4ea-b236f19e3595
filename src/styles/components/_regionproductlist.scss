.region-product-list {
    &__creation-and-searching {
        display: flex;
        flex-direction: row;
        margin-top: 23px;
        margin-bottom: 42px;
    }

    &__searching {
        margin-right: 40px !important;
        margin-left: auto !important;
    }

    &__search-bar {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        width: 423px !important;
        line-height: 40px !important;
        background: $color-white !important;
        border-radius: 8px;

        &::placeholder {
            font-size: 16px;
            line-height: 24px;
            color: rgba(0, 0, 0, 0.25) !important;
        }

        &:hover {
            border-color: $color-green !important;
        }

        &:focus-within {
            border-color: $color-green !important;
        }
    }

    &__selections {
        display: flex;
        flex-direction: row;
        align-items: flex-start;
        padding: 0px;
        gap: 24px;
    }

    &__urban-center-selection {
        width: auto !important;
        min-width: 157px !important;
        height: 32px;
        background: $bg-white;
        border-radius: 6px;
    }

    &__zone-selection {
        width: auto !important;
        min-width: 107px;
        height: 32px;
        background: $bg-white;
        border-radius: 6px;
    }

    &__product-selection {
        width: auto !important;
        min-width: 157px;
        height: 32px;
        background: $bg-white;
        border-radius: 6px;
    }

    &__datatable {
        width: 100% !important;

        tr.editable-row {
            td {

                .ant-input-number,
                .ant-input,
                .ant-select {
                    width: 100%;
                }

                .ant-input-number {
                    border-right: 0;
                }

                .ant-input-number-wrapper {
                    .ant-input-number-group-addon {
                        background-color: #fff;
                    }
                }

                .ant-form-item-explain-error,
                .ant-form-item-explain {
                    display: none;
                }
            }
        }
    }

    &__platform-selection {
        gap: 8px;
        width: auto !important;
        height: 32px;
        border-radius: 6px;
        border: 0px solid $color-green;

        &:hover {
            border: 0px solid $color-green !important;
        }

        &:focus-within {
            border: 0px solid $color-green !important;
        }
    }

    &__prix-forfait {
        display: flex;
        align-items: center;
    }

    &__prix-forfait-tonne {
        display: flex;
        align-items: center;
    }

    &__traitement {
        display: flex;
        align-items: center;
    }

    &__price-text {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
        cursor: pointer;
    }

    &__prix-ht-unit {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 700;
        font-size: 14px;
        line-height: 22px;
        color: rgba(0, 0, 0, 0.88);
        margin-left: 8px;
    }
}

.datatable {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    padding: 0px;
    gap: 8px;
    width: 32px;
    height: 32px;

    &__item {
        font-family: 'Roboto';
        font-style: normal;
        font-weight: 400;
        font-size: 14px;
        line-height: 22px;
        display: flex;
        align-items: center;
        color: rgba(0, 0, 0, 0.88);
    }

    &__action-copy-button {
        margin-right: 16px;
        color: rgba(0, 0, 0, 0.45) !important;
        gap: 0 !important;
    }

    &__action-destroy-button {
        color: #FF4D4F !important;
        gap: 0 !important;
    }
}