import { configureStore } from '@reduxjs/toolkit';
import { TypedUseSelectorHook, useDispatch, useSelector } from 'react-redux';
import accountSlice from './slices/account.slices';
import productSlice from './slices/product.slices';
import optionSlice from './slices/option.slices';
import priceSlices from './slices/price.slices';
import regionSlices from './slices/region.slices';
import platformSlice from './slices/platform.slices';
import urbanCenterSlice from './slices/urban_center.slices';
import regionProductTypeSlice from './slices/region_product_type.slices';
import catalogPriceSlice from './slices/catalog_price.slices';
import serviceProviderSlice from './slices/service_provider.slices';
import contactSlice from './slices/contact.slices';
import productTypeRegulSlice from './slices/product_type_regul.slices';
import yearSlice from './slices/year.slices';
import saleSlice from './slices/sale.slices';
import clientContactSlice from './slices/client_contact.slice';
import clientContactPersonSlice from './slices/client_contact_person.slice';
import documentSlice from './slices/document.slice';
import addressSlice from './slices/address.slices';
import contactAddressSlice from './slices/contact_addresses';
import searchPricesSlice from './slices/search_price.slices';
import wasteCenterTypeSlice from './slices/waste_center_type.slices';
import providerInvoiceSlice from './slices/provider_invoice.slices';

const store = configureStore({
  reducer: {
    account: accountSlice,
    product: productSlice,
    productTypeRegul: productTypeRegulSlice,
    option: optionSlice,
    price: priceSlices,
    region: regionSlices,
    platform: platformSlice,
    urbanCenter: urbanCenterSlice,
    regionProductType: regionProductTypeSlice,
    catalogPrice: catalogPriceSlice,
    serviceProvider: serviceProviderSlice,
    contact: contactSlice,
    year: yearSlice,
    sale: saleSlice,
    clientContact: clientContactSlice,
    clientContactPerson: clientContactPersonSlice,
    document: documentSlice,
    address: addressSlice,
    contactAddress: contactAddressSlice,
    searchPrice: searchPricesSlice,
    wasteCenterType: wasteCenterTypeSlice,
    providerInvoice: providerInvoiceSlice,
  },
});

export type RootState = ReturnType<typeof store.getState>;

export type AppDispatch = typeof store.dispatch;
export const useAppDispatch = () => useDispatch<AppDispatch>();
export const useAppSelector: TypedUseSelectorHook<RootState> = useSelector;

export default store;
