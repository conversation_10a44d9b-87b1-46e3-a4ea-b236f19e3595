import React, { createContext, useState, useEffect, ReactNode, useContext } from 'react';
import { useLocation } from 'react-router-dom';

// Define the shape of the global state
interface GlobalState {
  isDevisPath: boolean;
}

// Define the shape of the context
interface GlobalContextType {
  globalState: GlobalState;
  setGlobalState: React.Dispatch<React.SetStateAction<GlobalState>>;
}
// Default value for the context
const defaultContextValue: GlobalContextType = {
  globalState: {
    isDevisPath: false,
  },
  setGlobalState: () => {},
};
// Create a context with a default value
const GlobalContext = createContext<GlobalContextType>(defaultContextValue);

// Create a provider component
interface GlobalProviderProps {
  children: ReactNode;
}

export const GlobalProvider: React.FC<GlobalProviderProps> = ({ children }) => {
  // Create a state to hold your global data
  const [globalState, setGlobalState] = useState<GlobalState>({
    isDevisPath: false,
  });

  // Get the current location from React Router
  const location = useLocation();

  // useEffect to listen for route changes
  useEffect(() => {
    const isDevisPath = location.pathname.includes('quotation') || location.pathname.includes('order');
    setGlobalState((prevState) => ({
      ...prevState,
      isDevisPath,
    }));
  }, [location]);

  return <GlobalContext.Provider value={{ globalState, setGlobalState }}>{children}</GlobalContext.Provider>;
};
const useGlobalContext = () => useContext(GlobalContext);
export default useGlobalContext;
