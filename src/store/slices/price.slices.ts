import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import {
  priceFamilyService,
  priceOptionService,
  priceService,
  priceSubOptionService,
  priceTypeService,
} from 'services';
import { Price, PriceFamily, PriceOption, PriceSubOption, PriceType, PriceTypeLogic } from 'models';
import { Loading, QueryParams } from 'types';

interface ProductState {
  priceFamilies: PriceFamily[];
  priceFamiliesLoading: Loading;
  prices: Price[];
  pricesLoading: Loading;
  priceTypes: PriceType[];
  priceTypesLoading: Loading;
  priceOptions: PriceOption[];
  priceOptionsLoading: Loading;
  priceSubOptions: PriceSubOption[];
  priceSubOptionsLoading: Loading;
  priceTypeLogics: PriceTypeLogic[];
  priceTypeLogicsLoading: Loading;
}

const name = 'price';
const initialState: ProductState = {
  priceFamilies: [],
  priceFamiliesLoading: 'idle',
  prices: [],
  pricesLoading: 'idle',
  priceTypes: [],
  priceTypesLoading: 'idle',
  priceOptions: [],
  priceOptionsLoading: 'idle',
  priceSubOptions: [],
  priceSubOptionsLoading: 'idle',
  priceTypeLogics: [],
  priceTypeLogicsLoading: 'idle',
};

export const fetchPriceFamilies = createAsyncThunk(
  `${name}/list-management-price-families`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response =
        'productTypeId[]' in payload || payload.productTypeId ? await priceFamilyService.getPriceFamilies(payload) : [];
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchPrices = createAsyncThunk(
  `${name}/list-management-prices`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = 'priceFamilyId[]' in payload ? await priceService.getPrices(payload) : { count: 0, rows: [] };
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchPriceTypes = createAsyncThunk(
  `${name}/list-management-price-types`,
  async (payload: QueryParams, { rejectWithValue, getState }) => {
    try {
      const state = getState() as unknown as RootState;
      if (state.price.priceTypes.length) {
        return state.price.priceTypes;
      }
      const response = await priceTypeService.getPriceTypes();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchPriceOptions = createAsyncThunk(
  `${name}/list-management-price-options`,
  async (
    payload: {
      priceId: number;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await priceOptionService.getPriceOptions(payload.priceId, {
        include: 'PriceType|Price|PriceSubOptions|CatalogPrice',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchPriceSubOptions = createAsyncThunk(
  `${name}/list-management-price-sub-options`,
  async (
    payload: {
      priceOptionId: number;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await priceSubOptionService.getPriceSubOptions(payload.priceOptionId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchPriceTypeLogics = createAsyncThunk(
  `${name}/list-management-price-type-logics`,
  async (payload: QueryParams, { rejectWithValue, getState }) => {
    try {
      const state = getState() as unknown as RootState;
      if (state.price.priceTypeLogics.length) {
        return state.price.priceTypeLogics;
      }
      const response = await priceService.getPriceTypeLogics(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const priceSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPriceFamilies.pending, (state) => {
        state.priceFamiliesLoading = 'pending';
      })
      .addCase(fetchPriceFamilies.fulfilled, (state, action) => {
        state.priceFamiliesLoading = 'idle';
        state.priceFamilies = action.payload;
      })
      .addCase(fetchPriceFamilies.rejected, (state) => {
        state.priceFamiliesLoading = 'idle';
      });
    builder
      .addCase(fetchPrices.pending, (state) => {
        state.pricesLoading = 'pending';
      })
      .addCase(fetchPrices.fulfilled, (state, action) => {
        state.pricesLoading = 'idle';
        state.prices = action.payload.rows;
      })
      .addCase(fetchPrices.rejected, (state) => {
        state.pricesLoading = 'idle';
      });
    builder
      .addCase(fetchPriceTypes.pending, (state) => {
        state.priceTypesLoading = 'pending';
      })
      .addCase(fetchPriceTypes.fulfilled, (state, action) => {
        state.priceTypesLoading = 'idle';
        state.priceTypes = action.payload;
      })
      .addCase(fetchPriceTypes.rejected, (state) => {
        state.priceTypesLoading = 'idle';
      });
    builder
      .addCase(fetchPriceOptions.pending, (state) => {
        state.priceOptionsLoading = 'pending';
      })
      .addCase(fetchPriceOptions.fulfilled, (state, action) => {
        state.priceOptionsLoading = 'idle';
        state.priceOptions = action.payload;
      })
      .addCase(fetchPriceOptions.rejected, (state) => {
        state.priceOptionsLoading = 'idle';
      });
    builder
      .addCase(fetchPriceSubOptions.pending, (state) => {
        state.priceSubOptionsLoading = 'pending';
      })
      .addCase(fetchPriceSubOptions.fulfilled, (state, action) => {
        state.priceSubOptionsLoading = 'idle';
        state.priceSubOptions = action.payload;
      })
      .addCase(fetchPriceSubOptions.rejected, (state) => {
        state.priceSubOptionsLoading = 'idle';
      });
    builder
      .addCase(fetchPriceTypeLogics.pending, (state) => {
        state.priceTypeLogicsLoading = 'pending';
      })
      .addCase(fetchPriceTypeLogics.fulfilled, (state, action) => {
        state.priceTypeLogicsLoading = 'idle';
        state.priceTypeLogics = action.payload;
      })
      .addCase(fetchPriceTypeLogics.rejected, (state) => {
        state.priceTypeLogicsLoading = 'idle';
      });
  },
});

export const selectPriceFamilies = (state: RootState) => state.price.priceFamilies;
export const selectPriceFamiliesLoading = (state: RootState) => state.price.priceFamiliesLoading;
export const selectPrices = (state: RootState) => state.price.prices;
export const selectPricesLoading = (state: RootState) => state.price.pricesLoading;
export const selectPriceTypes = (state: RootState) => state.price.priceTypes;
export const selectPriceTypesLoading = (state: RootState) => state.price.priceTypesLoading;
export const selectPriceOptions = (state: RootState) => state.price.priceOptions;
export const selectPriceOptionsLoading = (state: RootState) => state.price.priceOptionsLoading;
export const selectPriceSubOptions = (state: RootState) => state.price.priceSubOptions;
export const selectPriceSubOptionsLoading = (state: RootState) => state.price.priceSubOptionsLoading;
export const selectPriceTypeLogics = (state: RootState) => state.price.priceTypeLogics;
export const selectPriceTypeLogicsLoading = (state: RootState) => state.price.priceTypeLogicsLoading;
export default priceSlice.reducer;
