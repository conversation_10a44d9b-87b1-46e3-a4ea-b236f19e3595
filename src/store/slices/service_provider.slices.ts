import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { ServiceProvider, ServiceType, WasteType } from 'models';
import { serviceProviderService, serviceTypeService } from 'services';
import { Loading, QueryParams } from 'types';

interface ServiceProviderState {
  serviceTypes: ServiceType[];
  serviceProvider: ServiceProvider | null;
  serviceProviderLoading: Loading;
  serviceProviders: ServiceProvider[];
  serviceProvidersLoading: Loading;
  serviceProvidersTotal: number;
  wasteTypes: WasteType[];
  serviceProvidersByIds: ServiceProvider[];
  serviceProvidersByIdsLoading: Loading;
}

const name = 'serviceProvider';
const initialState: ServiceProviderState = {
  serviceTypes: [],
  serviceProvider: null,
  serviceProviderLoading: 'idle',
  serviceProviders: [],
  serviceProvidersLoading: 'idle',
  serviceProvidersTotal: 0,
  wasteTypes: [],
  serviceProvidersByIds: [],
  serviceProvidersByIdsLoading: 'idle',
};

export const fetchServiceTypes = createAsyncThunk(
  `${name}/list-management-service-types`,
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as unknown as RootState;
      if (state.serviceProvider.serviceTypes.length) {
        return state.serviceProvider.serviceTypes;
      }
      const response = await serviceTypeService.getServiceTypes();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchServiceProviders = createAsyncThunk(
  `${name}/list-management-service-providers`,
  async (
    payload: {
      serviceType: string;
      name?: string;
      page?: number | string;
      limit?: number | string;
      include?: string;
      orderBy: string;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await serviceProviderService.getServiceProviders(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchServiceProviderById = createAsyncThunk(
  `${name}/get-one-service-provider`,
  async (params: { id: number; serviceType: string; include?: string }, { rejectWithValue }) => {
    try {
      const { id, ...others } = params;
      const response = await serviceProviderService.getServiceProvider(id, others);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchWasteTypes = createAsyncThunk(
  `${name}/list-management-waste-types`,
  async (_, { rejectWithValue, getState }) => {
    try {
      const state = getState() as unknown as RootState;
      if (state.serviceProvider.wasteTypes.length) {
        return state.serviceProvider.wasteTypes;
      }
      const response = await serviceTypeService.getWasteTypes();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchServiceProvidersByIds = createAsyncThunk(
  `${name}/list-management-service-providers-by-ids`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await serviceProviderService.getServiceProvidersByIds(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const serviceProviderSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder.addCase(fetchServiceTypes.fulfilled, (state, action) => {
      state.serviceTypes = action.payload;
    });
    builder
      .addCase(fetchServiceProviderById.pending, (state) => {
        state.serviceProviderLoading = 'pending';
      })
      .addCase(fetchServiceProviderById.fulfilled, (state, action) => {
        state.serviceProviderLoading = 'idle';
        state.serviceProvider = action.payload;
      })
      .addCase(fetchServiceProviderById.rejected, (state) => {
        state.serviceProviderLoading = 'idle';
      });
    builder
      .addCase(fetchServiceProviders.pending, (state) => {
        state.serviceProvidersLoading = 'pending';
      })
      .addCase(fetchServiceProviders.fulfilled, (state, action) => {
        state.serviceProvidersLoading = 'idle';
        state.serviceProviders = action.payload.rows;
        state.serviceProvidersTotal = action.payload.count;
      })
      .addCase(fetchServiceProviders.rejected, (state) => {
        state.serviceProvidersLoading = 'idle';
      });
    builder.addCase(fetchWasteTypes.fulfilled, (state, action) => {
      state.wasteTypes = action.payload;
    });
    builder
      .addCase(fetchServiceProvidersByIds.pending, (state) => {
        state.serviceProvidersByIdsLoading = 'pending';
      })
      .addCase(fetchServiceProvidersByIds.fulfilled, (state, action) => {
        state.serviceProvidersByIdsLoading = 'idle';
        state.serviceProvidersByIds = action.payload.rows;
      })
      .addCase(fetchServiceProvidersByIds.rejected, (state) => {
        state.serviceProvidersByIdsLoading = 'idle';
      });
  },
});

export const fetchAllServiceProviders = createAsyncThunk(
  'serviceProviders/fetchAll',
  async (_, { rejectWithValue }) => {
    try {
      const response = await serviceProviderService.getServiceProviders({
        orderBy: 'createdAt,desc|name',
        limit: 'unlimited',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const selectServiceTypes = (state: RootState) => state.serviceProvider.serviceTypes;
export const selectServiceProvider = (state: RootState) => state.serviceProvider.serviceProvider;
export const selectServiceProviderLoading = (state: RootState) => state.serviceProvider.serviceProviderLoading;
export const selectServiceProviders = (state: RootState) => state.serviceProvider.serviceProviders;
export const selectServiceProvidersLoading = (state: RootState) => state.serviceProvider.serviceProvidersLoading;
export const selectServiceProvidersTotal = (state: RootState) => state.serviceProvider.serviceProvidersTotal;
export const selectWasteTypes = (state: RootState) => state.serviceProvider.wasteTypes;
export const selectServiceProvidersByIds = (state: RootState) => state.serviceProvider.serviceProvidersByIds;
export const selectServiceProvidersByIdsLoading = (state: RootState) =>
  state.serviceProvider.serviceProvidersByIdsLoading;

export default serviceProviderSlice.reducer;
