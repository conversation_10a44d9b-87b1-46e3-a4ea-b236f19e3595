import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import Year from 'models/year';
import yearService from 'services/year.service';
import { Loading, SearchData } from 'types';

interface YearState {
  years: Year[];
  yearsLoading: Loading;
}

const name = 'year';
const initialState: YearState = {
  years: [],
  yearsLoading: 'idle',
};

export const fetchYears = createAsyncThunk(
  `${name}/list-management-years`,
  async (query: SearchData, { rejectWithValue, getState }) => {
    const state = getState() as unknown as RootState;
    if (state.year.years.length) {
      return state.year.years;
    }
    try {
      const response = await yearService.getYears(query);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const yearSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchYears.pending, (state) => {
        state.yearsLoading = 'pending';
      })
      .addCase(fetchYears.fulfilled, (state, action) => {
        state.yearsLoading = 'idle';
        state.years = action.payload;
      })
      .addCase(fetchYears.rejected, (state) => {
        state.yearsLoading = 'idle';
      });
  },
});

export const selectYears = (state: RootState) => state.year.years;
export const selectYearsLoading = (state: RootState) => state.year.yearsLoading;

export default yearSlice.reducer;
