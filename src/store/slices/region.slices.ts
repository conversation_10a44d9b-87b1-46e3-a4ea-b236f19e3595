import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { CountryRegion, Region, Zone } from 'models';
import { Loading, QueryParams, SearchData } from 'types';
import { regionService } from 'services';
import countryRegionService from 'services/country_region.service';
interface RegionState {
  regions: Region[];
  regionsTotal: number;
  regionsLoading: Loading;
  region: Region | null;
  regionLoading: Loading;
  countryRegions: CountryRegion[];
  countryRegionsTotal: number;
  countryRegionsLoading: Loading;
  zonesByIds: Zone[];
  zonesByIdsLoading: Loading;
}

const name = 'region';
const initialState: RegionState = {
  regions: [],
  regionsTotal: 0,
  regionsLoading: 'idle',
  region: null,
  regionLoading: 'idle',
  countryRegions: [],
  countryRegionsTotal: 0,
  countryRegionsLoading: 'idle',
  zonesByIds: [],
  zonesByIdsLoading: 'idle',
};

export const fetchRegions = createAsyncThunk(
  `${name}/list-management-regions`,
  async (query: SearchData, { rejectWithValue }) => {
    try {
      const response = await regionService.getRegions({
        ...query,
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const findRegionById = createAsyncThunk(
  `${name}/get-region-by-id`,
  async (regionId: number, { rejectWithValue }) => {
    try {
      const response = await regionService.findRegion(regionId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchCountryRegions = createAsyncThunk(
  `${name}/list-management-country-regions`,
  async (query: SearchData, { rejectWithValue }) => {
    try {
      const response = await countryRegionService.getRegions({
        ...query,
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchZonesByIds = createAsyncThunk(
  `${name}/list-management-zones-by-ids`,
  async (query: QueryParams, { rejectWithValue }) => {
    try {
      const response = await regionService.getZonesByIds({
        ...query,
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const regionSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRegions.pending, (state) => {
        state.regionsLoading = 'pending';
      })
      .addCase(fetchRegions.fulfilled, (state, action) => {
        state.regionsLoading = 'idle';
        state.regions = action.payload.rows;
        state.regionsTotal = action.payload.count;
      })
      .addCase(fetchRegions.rejected, (state) => {
        state.regionsLoading = 'idle';
      });
    builder
      .addCase(findRegionById.pending, (state) => {
        state.regionLoading = 'pending';
      })
      .addCase(findRegionById.fulfilled, (state, action) => {
        state.regionLoading = 'idle';
        state.region = action.payload;
      })
      .addCase(findRegionById.rejected, (state) => {
        state.regionLoading = 'idle';
      });
    builder
      .addCase(fetchCountryRegions.pending, (state) => {
        state.countryRegionsLoading = 'pending';
      })
      .addCase(fetchCountryRegions.fulfilled, (state, action) => {
        state.countryRegionsLoading = 'idle';
        state.countryRegions = action.payload.rows;
        state.countryRegionsTotal = action.payload.count;
      })
      .addCase(fetchCountryRegions.rejected, (state) => {
        state.countryRegionsLoading = 'idle';
      });
    builder
      .addCase(fetchZonesByIds.pending, (state) => {
        state.zonesByIdsLoading = 'pending';
      })
      .addCase(fetchZonesByIds.fulfilled, (state, action) => {
        state.zonesByIdsLoading = 'idle';
        state.zonesByIds = action.payload.rows;
      })
      .addCase(fetchZonesByIds.rejected, (state) => {
        state.zonesByIdsLoading = 'idle';
      });
  },
});

export const selectRegionsTotal = (state: RootState) => state.region.regionsTotal;
export const selectRegions = (state: RootState) => state.region.regions;
export const selectRegionsLoading = (state: RootState) => state.region.regionsLoading;
export const selectRegion = (state: RootState) => state.region.region;
export const selectRegionLoading = (state: RootState) => state.region.regionLoading;
export const selectCountryRegionsTotal = (state: RootState) => state.region.countryRegionsTotal;
export const selectCountryRegions = (state: RootState) => state.region.countryRegions;
export const selectCountryRegionsLoading = (state: RootState) => state.region.countryRegionsLoading;
export const selectZonesByIds = (state: RootState) => state.region.zonesByIds;
export const selectZonesByIdsLoading = (state: RootState) => state.region.zonesByIdsLoading;
export default regionSlice.reducer;
