import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { WasteCenterType } from 'models';
import { wasteCenterTypeService } from 'services';
import { Loading, QueryParams } from 'types';

interface WasteCenterTypeState {
  wasteCenterTypes: WasteCenterType[];
  wasteCenterTypesLoading: Loading;
  wasteCenterTypesTotal: number;
}

const name = 'wasteCenterType';
const initialState: WasteCenterTypeState = {
  wasteCenterTypes: [],
  wasteCenterTypesLoading: 'idle',
  wasteCenterTypesTotal: 0,
};

export const fetchWasteCenterTypes = createAsyncThunk(
  `${name}/list-management-waste-center-types`,
  async (query: QueryParams, { rejectWithValue }) => {
    try {
      const response = await wasteCenterTypeService.getWasteCenterTypes(query);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const wasteCenterTypeSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchWasteCenterTypes.pending, (state) => {
        state.wasteCenterTypesLoading = 'pending';
      })
      .addCase(fetchWasteCenterTypes.fulfilled, (state, action) => {
        state.wasteCenterTypesLoading = 'idle';
        state.wasteCenterTypes = action.payload.rows;
        state.wasteCenterTypesTotal = action.payload.count;
      })
      .addCase(fetchWasteCenterTypes.rejected, (state) => {
        state.wasteCenterTypesLoading = 'idle';
      });
  },
});

export const selectWasteCenterTypes = (state: RootState) => state.wasteCenterType.wasteCenterTypes;
export const selectWasteCenterTypesLoading = (state: RootState) => state.wasteCenterType.wasteCenterTypesLoading;
export const selectWasteCenterTypesTotal = (state: RootState) => state.wasteCenterType.wasteCenterTypesTotal;

export default wasteCenterTypeSlice.reducer;
