import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import providerInvoiceService from 'services/provider_invoice.service';
import { Loading, Invoice, InvoiceDetail, InvoiceLine, Comment, Avoir } from 'types';

interface ProviderInvoiceFilters {
  prestataire?: string;
  invoiceNumber?: string;
  amount?: number | null;
  status?: string;
}

interface RawInvoiceData {
  id: number;
  createdAt: string;
  prestataire: string;
  prestataireInvoiceNumber: string;
  status: string;
  totalSelected: number;
  totalAvoirs: number;
  totalAmount: number;
  invoicedAmount: number;
  avoirDetails: Avoir[];
  commentDetails: Comment[];
  invoiceDetails: RawInvoiceDetail[];
}

interface RawInvoiceDetail {
  invoiceLines: RawInvoiceLine[];
  [key: string]: unknown;
}

interface RawInvoiceLine {
  key?: string;
  isSelected?: boolean;
  isInvoiced?: boolean;
  comment?: Comment[];
  comments?: Comment[];
  [key: string]: unknown;
}

interface ProviderInvoiceState {
  invoices: Invoice[];
  invoicesLoading: Loading;
  invoicesTotal: number;
  invoiceLines: InvoiceLine[];
  invoiceLinesLoading: Loading;
  invoiceDetails: InvoiceDetail[];
  invoiceDetailsLoading: Loading;
  filters: ProviderInvoiceFilters;
  expandedComments: string[];

  currentInvoice: {
    invoice: Invoice | null;
    originalInvoice: Invoice | null;
    formFilters: {
      date?: string;
      commandeNumber?: string;
      siteAddress?: string;
      product?: string | number;
    };
    displayFilters: {
      showSelectedRows: boolean;
      showInvoicedRows: boolean;
    };
    isFiltering: boolean;
    isInitialLoad: boolean;
  };
}

const processEditModeData = (rawInvoice: RawInvoiceData): Invoice => {
  const invoice: Invoice = {
    id: rawInvoice.id,
    createdAt: rawInvoice.createdAt,
    prestataire: rawInvoice.prestataire,
    prestataireInvoiceNumber: rawInvoice.prestataireInvoiceNumber,
    status: rawInvoice.status,
    totalSelected: rawInvoice.totalSelected,
    totalAvoirs: rawInvoice.totalAvoirs,
    totalAmount: rawInvoice.totalAmount,
    invoicedAmount: rawInvoice.invoicedAmount,
    avoirDetails: rawInvoice.avoirDetails || [],
    commentDetails: rawInvoice.commentDetails || [],
    invoiceDetails: processInvoiceDetails(rawInvoice.invoiceDetails, rawInvoice.id)
  };
  return invoice;
};

interface CreateModeMetadata {
  prestataire?: string;
  prestataireInvoiceNumber?: string;
  invoicedAmount?: number;
}

const processCreateModeData = (rawLines: { invoiceDetails: RawInvoiceDetail[] }, metadata?: CreateModeMetadata): Invoice => {
  const invoice: Invoice = {
    id: Date.now(),
    createdAt: new Date().toISOString(),
    prestataire: metadata?.prestataire || 'Unknown Prestataire',
    prestataireInvoiceNumber: metadata?.prestataireInvoiceNumber || 'New Invoice',
    status: 'brouillon',
    totalSelected: 0,
    totalAvoirs: 0,
    totalAmount: 0,
    invoicedAmount: metadata?.invoicedAmount || 0,
    avoirDetails: [],
    commentDetails: [],
    invoiceDetails: processInvoiceDetails(rawLines.invoiceDetails, Date.now())
  };
  return invoice;
};

const processInvoiceDetails = (rawDetails: RawInvoiceDetail[], invoiceId: number): InvoiceDetail[] => {
  if (!rawDetails || !Array.isArray(rawDetails)) {
    return [];
  }
  return rawDetails.map((detail: RawInvoiceDetail, detailIndex: number) => {
    const processedDetail: InvoiceDetail = {
      ...(detail as unknown as InvoiceDetail),
      invoiceLines: processInvoiceLines(detail.invoiceLines, invoiceId, detailIndex)
    };
    return processedDetail;
  });
};

const processInvoiceLines = (rawLines: RawInvoiceLine[], invoiceId: number, detailIndex: number): InvoiceLine[] => {
  if (!rawLines || !Array.isArray(rawLines)) {
    return [];
  }
  return rawLines.map((line: RawInvoiceLine, lineIndex: number) => {
    const uniqueKey = `${invoiceId}-detail${detailIndex}-line${lineIndex}`;
    const processedLine: InvoiceLine = {
      ...(line as unknown as InvoiceLine),
      key: uniqueKey,
      isSelected: line.isSelected || false,
      isInvoiced: line.isInvoiced || false,
      comment: line.comment || line.comments || [],
      rowSpan: lineIndex === 0 ? rawLines.length : 0,
      isFirstLineOfDetail: lineIndex === 0,
      totalLinesInDetail: rawLines.length,
      detailId: `${invoiceId}-${detailIndex}`
    };
    return processedLine;
  });
};

const name = 'providerInvoice';
const initialState: ProviderInvoiceState = {
  invoices: [],
  invoicesLoading: 'idle',
  invoicesTotal: 0,
  invoiceLines: [],
  invoiceLinesLoading: 'idle',
  invoiceDetails: [],
  invoiceDetailsLoading: 'idle',
  filters: {},
  expandedComments: [],
  currentInvoice: {
    invoice: null,
    originalInvoice: null,
    formFilters: {},
    displayFilters: {
      showSelectedRows: true,
      showInvoicedRows: true,
    },
    isFiltering: false,
    isInitialLoad: true,
  },
};



export const fetchInvoiceDetailsByProviderIdEdit = createAsyncThunk(
  `${name}/fetch-invoices-by-provider`,
  async (params: { prestataire?: string; [id: string]: any }, { rejectWithValue }) => {
    try {
      const response = await providerInvoiceService.getInvoiceDetailsByProviderIdEdit(params);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchInvoiceLinesByProviderIdCreate = createAsyncThunk(
  `${name}/fetch-invoice-lines-by-provider`,
  async (params: { prestataire?: string; metadata?: any; [id: string]: any }, { rejectWithValue }) => {
    try {
      const response = await providerInvoiceService.getInvoiceLinesByProviderIdCreate(params);
      return { ...response, metadata: params.metadata }; // Pass metadata through
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProvidersInvoicesList = createAsyncThunk(
  `${name}/fetch-invoices-list`,
  async (params: {} ,{ rejectWithValue }) => {
    try {
      const response = await providerInvoiceService.getInvoicesList(params);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const providerInvoiceSlice = createSlice({
  name,
  initialState,
  reducers: {
    setFilters: (state, action) => {
      state.filters = { ...state.filters, ...action.payload };
    },
    clearFilters: (state) => {
      state.filters = {};
    },
    toggleExpandedComment: (state, action) => {
      const commentKey = action.payload;
      const index = state.expandedComments.indexOf(commentKey);
      if (index >= 0) {
        state.expandedComments.splice(index, 1);
      } else {
        state.expandedComments.push(commentKey);
      }
    },

    setInvoice: (state, action) => {
      const invoice: Invoice = action.payload;
      state.currentInvoice.invoice = invoice;
      state.currentInvoice.originalInvoice = JSON.parse(JSON.stringify(invoice));
      state.currentInvoice.isInitialLoad = false;
    },

    setFormFilters: (state, action) => {
      const filters = action.payload;
      state.currentInvoice.formFilters = filters;
      state.currentInvoice.isFiltering = false;
    },

    toggleDisplayFilter: (state, action) => {
      const { filterType, value } = action.payload;
      if (filterType === 'showSelectedRows') {
        state.currentInvoice.displayFilters.showSelectedRows = value;
      } else if (filterType === 'showInvoicedRows') {
        state.currentInvoice.displayFilters.showInvoicedRows = value;
      }
    },

    toggleLineSelection: (state, action) => {
      const lineKey: string = action.payload;
      if (!state.currentInvoice.invoice) {
        return;
      }
      state.currentInvoice.invoice.invoiceDetails.forEach(detail => {
        detail.invoiceLines.forEach(line => {
          if (line.key === lineKey) {
            line.isSelected = !line.isSelected;
          }
        });
      });
    },

    clearInvoiceState: (state) => {
      state.currentInvoice = {
        invoice: null,
        originalInvoice: null,
        formFilters: {},
        displayFilters: {
          showSelectedRows: true,
          showInvoicedRows: true,
        },
        isFiltering: false,
        isInitialLoad: true,
      };
      state.invoiceDetails = [];
      state.invoiceLines = [];
      state.invoiceDetailsLoading = 'idle';
      state.invoiceLinesLoading = 'idle';
    },

    updateInvoiceTotals: (state, action) => {
      const { totalSelected, totalAvoirs } = action.payload;
      if (!state.currentInvoice.invoice) {
        return;
      }
      state.currentInvoice.invoice.totalSelected = totalSelected;
      state.currentInvoice.invoice.totalAvoirs = totalAvoirs;
    },

    addCommentToLine: (state, action) => {
      const { lineKey, comment } = action.payload;
      if (!state.currentInvoice.invoice) {
        return;
      }
      const newComment: Comment = {
        id: Date.now(),
        createdBy: comment.author || 'Unknown User',
        createdAt: new Date().toISOString(),
        comment: comment.text || comment
      };
      state.currentInvoice.invoice.invoiceDetails.forEach(detail => {
        detail.invoiceLines.forEach(line => {
          if (line.key === lineKey) {
            if (!line.comment) {
              line.comment = [];
            }
            line.comment.push(newComment);
          }
        });
      });
    },

    removeCommentFromLine: (state, action) => {
      const { lineKey, commentId } = action.payload;
      if (!state.currentInvoice.invoice) {
        return;
      }
      state.currentInvoice.invoice.invoiceDetails.forEach(detail => {
        detail.invoiceLines.forEach(line => {
          if (line.key === lineKey && line.comment) {
            line.comment = line.comment.filter(comment => comment.id !== commentId);
          }
        });
      });
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProvidersInvoicesList.pending, (state) => {
        state.invoicesLoading = 'pending';
      })
      .addCase(fetchProvidersInvoicesList.fulfilled, (state, action) => {
        state.invoicesLoading = 'succeeded';
        state.invoices = action.payload.rows || [];
        state.invoicesTotal = action.payload.count || 0;
      })
      .addCase(fetchProvidersInvoicesList.rejected, (state) => {
        state.invoicesLoading = 'failed';
        state.invoices = [];
        state.invoicesTotal = 0;
      })
      .addCase(fetchInvoiceLinesByProviderIdCreate.pending, (state) => {
        state.invoiceLinesLoading = 'pending';
      })
      .addCase(fetchInvoiceLinesByProviderIdCreate.fulfilled, (state, action) => {
        state.invoiceLinesLoading = 'succeeded';
        state.invoiceLines = (action.payload.rows as InvoiceLine[]) || [];
        if (state.invoiceLines.length > 0) {
          const metadata = action.payload.metadata;
          const processedInvoice = processCreateModeData(state.invoiceLines[0] as unknown as { invoiceDetails: RawInvoiceDetail[] }, metadata);
          state.currentInvoice.invoice = processedInvoice;
          state.currentInvoice.originalInvoice = JSON.parse(JSON.stringify(processedInvoice));
          state.currentInvoice.isInitialLoad = false;
        }
      })
      .addCase(fetchInvoiceLinesByProviderIdCreate.rejected, (state) => {
        state.invoiceLinesLoading = 'failed';
        state.invoiceLines = [];
      })
      .addCase(fetchInvoiceDetailsByProviderIdEdit.pending, (state) => {
        state.invoiceDetailsLoading = 'pending';
      })
      .addCase(fetchInvoiceDetailsByProviderIdEdit.fulfilled, (state, action) => {
        state.invoiceDetailsLoading = 'succeeded';
        state.invoiceDetails = (action.payload.rows as InvoiceDetail[]) || [];
        if (state.invoiceDetails.length > 0) {
          const processedInvoice = processEditModeData(state.invoiceDetails[0] as unknown as RawInvoiceData);
          state.currentInvoice.invoice = processedInvoice;
          state.currentInvoice.originalInvoice = JSON.parse(JSON.stringify(processedInvoice));
          state.currentInvoice.isInitialLoad = false;
        }
      })
      .addCase(fetchInvoiceDetailsByProviderIdEdit.rejected, (state) => {
        state.invoiceDetailsLoading = 'failed';
        state.invoiceDetails = [];
      });
  },
});

export const {
  setFilters,
  clearFilters,
  toggleExpandedComment,
  setInvoice,
  setFormFilters,
  toggleDisplayFilter,
  toggleLineSelection,
  updateInvoiceTotals,
  addCommentToLine,
  removeCommentFromLine,
  clearInvoiceState,
} = providerInvoiceSlice.actions;

export const selectInvoices = (state: RootState) => state.providerInvoice.invoices;
export const selectInvoicesLoading = (state: RootState) => state.providerInvoice.invoicesLoading;
export const selectFilters = (state: RootState) => state.providerInvoice.filters;
export const selectExpandedComments = (state: RootState) => state.providerInvoice.expandedComments;

export const selectCurrentInvoice = (state: RootState) => state.providerInvoice.currentInvoice.invoice;
export const selectOriginalInvoice = (state: RootState) => state.providerInvoice.currentInvoice.originalInvoice;
export const selectFormFilters = (state: RootState) => state.providerInvoice.currentInvoice.formFilters;
export const selectDisplayFilters = (state: RootState) => state.providerInvoice.currentInvoice.displayFilters;
export const selectIsFiltering = (state: RootState) => state.providerInvoice.currentInvoice.isFiltering;
export const selectIsInitialLoad = (state: RootState) => state.providerInvoice.currentInvoice.isInitialLoad;

export const selectSelectedLines = (state: RootState) => {
  const invoice = state.providerInvoice.currentInvoice.invoice;
  if (!invoice) return [];
  const allLines: InvoiceLine[] = [];
  invoice.invoiceDetails.forEach(detail => {
    detail.invoiceLines.forEach(line => {
      if (line.isSelected) {
        allLines.push(line);
      }
    });
  });
  return allLines;
};

// Export the reducer as default
export default providerInvoiceSlice.reducer;
