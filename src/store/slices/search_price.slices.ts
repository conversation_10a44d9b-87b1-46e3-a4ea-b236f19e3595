import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

import { priceService } from 'services';

import { SearchData, RawDataLog, RawDataSales, PaginationData } from 'types';
import { RootState } from '..';

const name = 'search';

type ProviderInfo = {
  distanceByCar: string;
  zoneId: number;
  zoneName: string;
};

const initialState: {
  products: RawDataLog[];
  productsTotal: number;
  productsLoading: string;
  salesProducts: RawDataSales[];
  salesProductsTotal: number;
  salesProductsLoading: string;
} = {
  products: [],
  productsTotal: 0,
  productsLoading: 'idle',
  salesProducts: [],
  salesProductsTotal: 0,
  salesProductsLoading: 'idle',
};

export const fetchProductWithZonesAndPrices = createAsyncThunk<PaginationData<RawDataLog>, SearchData>(
  `${name}/logistique`,
  async (query: SearchData, { rejectWithValue }) => {
    try {
      const response = await priceService.getPriceProductWithZonesPagination({
        ...query,
      });
      return response as unknown as PaginationData<RawDataLog>;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProductWithZonesAndPricesSales = createAsyncThunk(
  `${name}/commercial`,
  async (
    {
      query,
      providerData,
      productInactive,
    }: {
      query: { page: number; limit: number; orderBy: string };
      providerData: ProviderInfo[];
      productInactive: string[];
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await priceService.getPriceProductWithZonesSales({
        ...query,
        dataList: providerData,
        productInactive,
      });
      return response?.result;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const searchPricesSlice = createSlice({
  name,
  initialState,
  reducers: {
    resetState: () => initialState,
    triggerLoading: (state) => {
      state.productsLoading = 'pending'; // set the loading state to pending
    },
    triggerSalesLoading: (state) => {
      state.salesProductsLoading = 'pending'; // set the loading state to pending
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchProductWithZonesAndPrices.pending, (state) => {
        state.productsLoading = 'pending';
      })
      .addCase(fetchProductWithZonesAndPrices.fulfilled, (state, action) => {
        state.productsLoading = 'idle';
        state.products = action.payload?.rows;
        state.productsTotal = action.payload.count;
      })
      .addCase(fetchProductWithZonesAndPrices.rejected, (state) => {
        state.productsLoading = 'idle';
      });
    builder
      .addCase(fetchProductWithZonesAndPricesSales.pending, (state) => {
        state.salesProductsLoading = 'pending';
      })
      .addCase(fetchProductWithZonesAndPricesSales.fulfilled, (state, action) => {
        state.salesProductsLoading = 'idle';
        state.salesProducts = action.payload;
        state.salesProductsTotal = action.payload?.length;
      })
      .addCase(fetchProductWithZonesAndPricesSales.rejected, (state) => {
        state.salesProductsLoading = 'idle';
      });
  },
});

export const selectSearchProducts = (state: RootState): RawDataLog[] => state.searchPrice.products;
export const selectSearchProductsTotal = (state: RootState) => state.searchPrice.productsTotal;
export const selectSearchProductsLoading = (state: RootState) => state.searchPrice.productsLoading;
export const selectSearchProductsSales = (state: RootState): RawDataSales[] => state.searchPrice.salesProducts;
export const selectSearchProductsTotalSales = (state: RootState) => state.searchPrice.salesProductsTotal;
export const selectSearchProductsLoadingSales = (state: RootState) => state.searchPrice.salesProductsLoading;
export const { triggerLoading, triggerSalesLoading, resetState } = searchPricesSlice.actions;

export default searchPricesSlice.reducer;
