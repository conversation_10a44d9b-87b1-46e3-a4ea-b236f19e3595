import { createSlice, createAsyncThunk } from "@reduxjs/toolkit";
import { RootState } from "..";
import { UrbanCenter } from "models";
import { urbanCenterService } from "services";
import { Loading } from "types";

interface UrbanCenterState {
  urbanCenters: UrbanCenter[];
  urbanCentersLoading: Loading;
}

const name = "urbanCenter";
const initialState: UrbanCenterState = {
  urbanCenters: [],
  urbanCentersLoading: "idle",
};

export const fetchUrbanCenters = createAsyncThunk(
  `${name}/list-management-urban-centers`,
  async (regionId: number, { rejectWithValue }) => {
    try {
      const response = await urbanCenterService.getUrbanCenters(regionId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const urbanCenterSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchUrbanCenters.pending, (state) => {
        state.urbanCentersLoading = "pending";
      })
      .addCase(fetchUrbanCenters.fulfilled, (state, action) => {
        state.urbanCentersLoading = "idle";
        state.urbanCenters = action.payload;
      })
      .addCase(fetchUrbanCenters.rejected, (state) => {
        state.urbanCentersLoading = "idle";
      });
  },
});

export const selectUrbanCenters = (state: RootState) =>
  state.urbanCenter.urbanCenters;
export const selectUrbanCentersLoading = (state: RootState) =>
  state.urbanCenter.urbanCentersLoading;

export default urbanCenterSlice.reducer;
