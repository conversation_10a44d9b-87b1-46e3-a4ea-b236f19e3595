import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { RegionProductType } from 'models';
import { regionProductTypeService } from 'services';
import { Loading } from 'types';

interface RegionProductTypeState {
  regionProductTypes: RegionProductType[];
  regionProductTypesLoading: Loading;
}

const name = 'regionProductType';
const initialState: RegionProductTypeState = {
  regionProductTypes: [],
  regionProductTypesLoading: 'idle'
};

export const fetchRegionProductTypes = createAsyncThunk(
  `${name}/list-management-region-product-types`,
  async (
    query: {
      regionId: number;
      optionTypeId?: string | number | null;
      include?: boolean;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await regionProductTypeService.getRegionProductTypes(
        query
      );
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const regionProductTypeSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchRegionProductTypes.pending, (state) => {
        state.regionProductTypesLoading = 'pending';
      })
      .addCase(fetchRegionProductTypes.fulfilled, (state, action) => {
        state.regionProductTypesLoading = 'idle';
        state.regionProductTypes = action.payload;
      })
      .addCase(fetchRegionProductTypes.rejected, (state) => {
        state.regionProductTypesLoading = 'idle';
      });
  }
});

export const selectRegionProductTypes = (state: RootState) =>
  state.regionProductType.regionProductTypes;
export const selectRegionProductTypesLoading = (state: RootState) =>
  state.regionProductType.regionProductTypesLoading;

export default regionProductTypeSlice.reducer;
