import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { Sale } from 'models';
import { saleService } from 'services';
import { Loading } from 'types';

interface SaleState {
  sales: Sale[];
  salesLoading: Loading;
  loggedSale: Sale | null;
  saleLoading: Loading;
}

const name = 'sale';
const initialState: SaleState = {
  sales: [],
  salesLoading: 'idle',
  loggedSale: null,
  saleLoading: 'idle',
};

export const fetchSales = createAsyncThunk(
  `${name}/list-management-sales`,
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await saleService.getSales(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchSaleById = createAsyncThunk(
  `${name}/get-sale-by-id`,
  async (payload: any, { rejectWithValue }) => {
    console.log('payload', payload);
    try {
      const response = await saleService.getSaleById(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const saleSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchSales.pending, (state) => {
        state.salesLoading = 'pending';
      })
      .addCase(fetchSales.fulfilled, (state, action) => {
        state.salesLoading = 'idle';
        state.sales = action.payload ?? [];
      })
      .addCase(fetchSales.rejected, (state) => {
        state.salesLoading = 'idle';
        state.sales = [];
      });
    builder
      .addCase(fetchSaleById.pending, (state) => {
        state.saleLoading = 'pending';
      })
      .addCase(fetchSaleById.fulfilled, (state, action) => {
        state.saleLoading = 'idle';
        state.loggedSale = action.payload ?? null;
      })
      .addCase(fetchSaleById.rejected, (state) => {
        state.saleLoading = 'idle';
        state.loggedSale = null;
      });
  }
});

export const selectSales = (state: RootState) => state.sale.sales;
export const selectSalesLoading = (state: RootState) => state.sale.salesLoading;
export const selectSale = (state: RootState) => state.sale.loggedSale;
export const selectSaleLoading = (state: RootState) => state.sale.saleLoading;

export default saleSlice.reducer;
