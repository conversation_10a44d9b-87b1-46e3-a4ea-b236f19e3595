import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { Contact, ContactFunction } from 'models';
import { contactFunctionService, contactService } from 'services';
import { Loading } from 'types';

interface ContactState {
  contacts: Contact[];
  contactTotal: number;
  contactsLoading: Loading;
  contactFunctions: ContactFunction[];
  contactFunctionsLoading: Loading;
}

const name = 'contact';
const initialState: ContactState = {
  contacts: [],
  contactTotal: 0,
  contactsLoading: 'idle',
  contactFunctions: [],
  contactFunctionsLoading: 'idle',
};

export const fetchContacts = createAsyncThunk(
  `${name}/list-management-contacts`,
  async (
    payload: {
      isActive: number;
      serviceProviderId: number;
    },
    { rejectWithValue }
  ) => {
    try {
      const response = await contactService.getContacts(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const fetchContactFunctions = createAsyncThunk(
  `${name}/list-management-contact-functions`,
  async (_, { rejectWithValue }) => {
    try {
      const response = await contactFunctionService.getContactFunctions();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const contactSlices = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchContacts.pending, (state) => {
        state.contactsLoading = 'pending';
      })
      .addCase(fetchContacts.fulfilled, (state, action) => {
        state.contactsLoading = 'idle';
        state.contacts = action.payload.rows;
        state.contactTotal = action.payload.count;
      })
      .addCase(fetchContacts.rejected, (state) => {
        state.contactsLoading = 'idle';
      });
    builder
      .addCase(fetchContactFunctions.pending, (state) => {
        state.contactFunctionsLoading = 'pending';
      })
      .addCase(fetchContactFunctions.fulfilled, (state, action) => {
        state.contactFunctionsLoading = 'idle';
        state.contactFunctions = action.payload;
      })
      .addCase(fetchContactFunctions.rejected, (state) => {
        state.contactFunctionsLoading = 'idle';
      });
  },
});

export const selectContacts = (state: RootState) => state.contact.contacts;
export const selectContactTotal = (state: RootState) =>
  state.contact.contactTotal;
export const selectContactsLoading = (state: RootState) =>
  state.contact.contactsLoading;
export const selectContactFunctions = (state: RootState) =>
  state.contact.contactFunctions;
export const selectContactFunctionsLoading = (state: RootState) =>
  state.contact.contactFunctionsLoading;

export default contactSlices.reducer;
