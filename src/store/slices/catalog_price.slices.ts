import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { CatalogPrice } from 'models';
import { catalogPriceService } from 'services';
import { Loading, QueryParams } from 'types';

interface CatalogPriceState {
  catalogPrices: CatalogPrice[];
  catalogPricesLoading: Loading;
}

const name = 'catalogPrice';
const initialState: CatalogPriceState = {
  catalogPrices: [],
  catalogPricesLoading: 'idle',
};

export const fetchCatalogPrices = createAsyncThunk(
  `${name}/list-management-catalog-prices`,
  async (
    query: {
      productTypeId: number | null;
      include?: boolean;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await catalogPriceService.getCatalogPrices(query as QueryParams);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const catalogPriceSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchCatalogPrices.pending, (state) => {
        state.catalogPricesLoading = 'pending';
      })
      .addCase(fetchCatalogPrices.fulfilled, (state, action) => {
        state.catalogPricesLoading = 'idle';
        state.catalogPrices = action.payload;
      })
      .addCase(fetchCatalogPrices.rejected, (state) => {
        state.catalogPricesLoading = 'idle';
      });
  },
});

export const selectCatalogPrices = (state: RootState) => state.catalogPrice.catalogPrices;
export const selectCatalogPricesLoading = (state: RootState) => state.catalogPrice.catalogPricesLoading;

export default catalogPriceSlice.reducer;
