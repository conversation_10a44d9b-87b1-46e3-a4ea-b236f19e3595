import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { Address } from 'models';
import addressService from 'services/client_address.service';
import { RootState } from 'store';
import { Loading } from 'types';

interface AddressState {
    address: Address[];
    addresses: Address | null;
    addressTotal: number;
    addressLoading: Loading;
}

const name = 'address';
const initialState: AddressState = {
    address: [],
    addresses: null,
    addressTotal: 0,
    addressLoading: 'idle',
};

export const fetchAddress = createAsyncThunk(
    `${name}/list-management-addresses`,
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await addressService.getAddresses(payload);
            return response;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);

export const findAddressByParams = createAsyncThunk(
    `${name}/find-address-by-params`,
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await addressService.findAddressesByParams(payload);
            if (response?.data?.error) {
                return rejectWithValue(response.data);
            }
            return response;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);

export const findAddressById = createAsyncThunk(
    `${name}/find-address-by-id`,
    async (payload: {
        addressId: number;
        params?: any;
    }, { rejectWithValue }) => {
        try {
            const response = await addressService.findAddress(payload?.addressId, payload?.params);
            if (response?.data?.error) {
                return rejectWithValue(response.data);
            }
            return response;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);

export const addressSlice = createSlice({
    name,
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchAddress.pending, (state) => {
                state.addressLoading = 'pending';
            })
            .addCase(fetchAddress.fulfilled, (state, action) => {
                state.address = action.payload.rows;
                state.addressTotal = action.payload.count;
                state.addressLoading = 'idle';
            })
            .addCase(fetchAddress.rejected, (state) => {
                state.addressLoading = 'idle';
            });
        builder.addCase(findAddressByParams.fulfilled, (state, action) => {
            state.addresses = action.payload;
        });
        builder
            .addCase(findAddressById.pending, (state) => {
            state.addressLoading = 'pending';
            })
            .addCase(findAddressById.fulfilled, (state, action) => {
            state.addresses = action.payload;
            })
            .addCase(findAddressById.rejected, (state) => {
            state.addressLoading = 'idle';
            });
    },
});
export const selectAddress = (state: RootState) => state.address.address;
export const selectAddresses = (state: RootState) => state.address.addresses;
export const selectAddressTotal = (state: RootState) => state.address.addressTotal;
export const selectAddressLoading = (state: RootState) => state.address.addressLoading;
export default addressSlice.reducer;
