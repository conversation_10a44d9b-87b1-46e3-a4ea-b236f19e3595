import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { ClientContact } from 'models';
import { clientContactService } from 'services';
import { Loading } from 'types';

interface ClientContactState {
  clientContacts: ClientContact[];
  clientContact: ClientContact | null;
  clientContactTotal: number;
  clientContactLoading: Loading;
}

const name = 'client_contact';
const initialState: ClientContactState = {
  clientContacts: [],
  clientContact: null,
  clientContactTotal: 0,
  clientContactLoading: 'idle',
};

export const fetchClientContactById = createAsyncThunk(
  `${name}/fetch-client-contact`,
  async (contactId: string, { rejectWithValue }) => {
    try {
      const response = await clientContactService.findContact(contactId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchClientContacts = createAsyncThunk(
  `${name}/list-management-client-contacts`,
  /* eslint-disable-next-line */
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await clientContactService.getContacts(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
export const findContactByParams = createAsyncThunk(
  `${name}/find-client-contact-by-crm-contact-id`,
  /* eslint-disable-next-line */
  async (payload: {}, { rejectWithValue }) => {
    try {
      const response = await clientContactService.findContactByParams(payload);
      if (response?.data?.error) {
        return rejectWithValue(response.data);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const clientContactSlice = createSlice({
  name,
  initialState,
  reducers: {
    setClientContact: (state, action) => {
      state.clientContact = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchClientContacts.pending, (state) => {
        state.clientContactLoading = 'pending';
      })
      .addCase(fetchClientContacts.fulfilled, (state, action) => {
        state.clientContactLoading = 'idle';
        state.clientContacts = action.payload.rows;
        state.clientContactTotal = action.payload.count;
      })
      .addCase(fetchClientContacts.rejected, (state) => {
        state.clientContactLoading = 'idle';
      });
    builder
      .addCase(fetchClientContactById.pending, (state) => {
        state.clientContactLoading = 'pending';
      })
      .addCase(fetchClientContactById.fulfilled, (state, action) => {
        state.clientContactLoading = 'idle';
        state.clientContact = action.payload;
      })
      .addCase(fetchClientContactById.rejected, (state) => {
        state.clientContactLoading = 'idle';
      });
    builder.addCase(findContactByParams.fulfilled, (state, action) => {
      state.clientContact = action.payload.rows.length > 0 ? action.payload.rows[0] : null;
    });
  },
});

export const selectClientContacts = (state: RootState) => state.clientContact.clientContacts;
export const selectClientContactTotal = (state: RootState) => state.clientContact.clientContactTotal;
export const selectClientContactsLoading = (state: RootState) => state.clientContact.clientContactLoading;
export const selectClientContact = (state: RootState) => state.clientContact.clientContact;

export const { setClientContact } = clientContactSlice.actions;

export default clientContactSlice.reducer;
