import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { optionService, productService, referenceTypeService } from 'services';
import { FacturationType, Option, Product, ProductType, ProductTypeUnit, ReferenceType } from 'models';
import { Loading, QueryParams, SearchData } from 'types';
import ProductTypeInterventions from 'models/product-type-interventions';
interface ProductState {
  products: Product[];
  productsTotal: number;
  productsLoading: Loading;
  productsByIds: Product[];
  productsByIdsLoading: Loading;
  productsByZoneIdForProductCatalogPrices: Product[];
  productsByZoneIdForProductCatalogPricesLoading: Loading;
  productsByZoneIdForProductCatalogPricesTotal: number;
  productsByZoneIdForProductCatalogOptionPrices: Product[];
  productsByZoneIdForProductCatalogOptionPricesLoading: Loading;
  productsByZoneIdForProductCatalogOptionPricesTotal: number;
  productTypes: ProductType[];
  productTypesTotal: number;
  productTypesLoading: Loading;
  productType: ProductType | null;
  productTypeLoading: Loading;
  options: Option[];
  optionsLoading: Loading;
  referenceTypes: ReferenceType[];
  referenceTypesLoading: Loading;
  productTypesByRegion: ProductType[];
  productTypesByRegionLoading: Loading;
  productTypeUnit: ProductTypeUnit[];
  productTypeUnitTotal: number;
  productTypeUnitLoading: Loading;
  productTypeInterventions: ProductTypeInterventions[];
  productTypeInterventionsTotal: number;
  productTypeInterventionsLoading: Loading;
  facturationType: FacturationType[];
  facturationTypeTotal: number;
  facturationTypeLoading: Loading;
}

const name = 'product';
const initialState: ProductState = {
  products: [],
  productsLoading: 'idle',
  productsTotal: 0,
  productsByIds: [],
  productsByIdsLoading: 'idle',
  productsByZoneIdForProductCatalogPrices: [],
  productsByZoneIdForProductCatalogPricesLoading: 'idle',
  productsByZoneIdForProductCatalogPricesTotal: 0,
  productsByZoneIdForProductCatalogOptionPrices: [],
  productsByZoneIdForProductCatalogOptionPricesLoading: 'idle',
  productsByZoneIdForProductCatalogOptionPricesTotal: 0,
  productTypes: [],
  productTypesTotal: 0,
  productTypesLoading: 'idle',
  productType: null,
  productTypeLoading: 'idle',
  options: [],
  optionsLoading: 'idle',
  referenceTypes: [],
  referenceTypesLoading: 'idle',
  productTypesByRegion: [],
  productTypesByRegionLoading: 'idle',
  productTypeUnit: [],
  productTypeUnitLoading: 'idle',
  productTypeUnitTotal: 0,
  productTypeInterventions: [],
  productTypeInterventionsLoading: 'idle',
  productTypeInterventionsTotal: 0,
  facturationType: [],
  facturationTypeLoading: 'idle',
  facturationTypeTotal: 0,
};

export const fetchProductTypes = createAsyncThunk(
  `${name}/list-management-product-types`,
  async (query: SearchData, { rejectWithValue }) => {
    try {
      query.isVisible = 1;
      const response = await productService.getProductTypes(query);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const findProductTypeById = createAsyncThunk(
  `${name}/get-product-type-by-id`,
  async (productTypeId: number, { rejectWithValue }) => {
    try {
      const response = await productService.findProductType(productTypeId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const getProducTypesByRegionId = createAsyncThunk(
  `${name}/get-product-types-by-region-id`,
  async (regionId: number, { rejectWithValue }) => {
    try {
      const response = await productService.getProductTypesByRegionId(regionId);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchOptions = createAsyncThunk(
  `${name}/list-management-options`,
  async (
    payload: QueryParams & {
      productTypeId?: number;
    },
    { rejectWithValue },
  ) => {
    const { productTypeId, ...restQueries } = payload;
    try {
      if (!productTypeId) return [];
      const response = await optionService.getAllOptions(productTypeId, {
        ...restQueries,
        include: 'OptionType|SubOptions',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchReferenceTypes = createAsyncThunk(
  `${name}/list-management-reference-types`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await referenceTypeService.getAllReferenceTypes();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
export const fetchProductTypeUnit = createAsyncThunk(
  `${name}/list-product-type-units`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProductTypeUnit(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProductTypeInterventions = createAsyncThunk(
  `${name}/list-productTypeInterventions`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProductTypeInterventions(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
export const fetchFacturationType = createAsyncThunk(
  `${name}/list-facturation-type`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getFacturationType(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProducts = createAsyncThunk(
  `${name}/list-management-products`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProducts(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProductsByIds = createAsyncThunk(
  `${name}/list-management-products-by-ids`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProductsByIds(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProductsByZoneIdForProductCatalogPrices = createAsyncThunk(
  `${name}/list-management-products-by-zone-id-for-product-catalog-prices`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProductsByZoneIdForProductCatalogPrices(payload.zoneId, payload.query);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchProductsByZoneIdForProductCatalogOptionPrices = createAsyncThunk(
  `${name}/list-management-products-by-zone-id-for-product-catalog-option-prices`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await productService.getProductsByZoneIdForProductCatalogOptionPrices(
        payload.zoneId,
        payload.query,
      );
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const productSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchProductTypes.pending, (state) => {
        state.productTypesLoading = 'pending';
      })
      .addCase(fetchProductTypes.fulfilled, (state, action) => {
        state.productTypesLoading = 'idle';
        state.productTypes = action.payload.rows;
        state.productTypesTotal = action.payload.count;
      })
      .addCase(fetchProductTypes.rejected, (state) => {
        state.productTypesLoading = 'idle';
      });
    builder
      .addCase(findProductTypeById.pending, (state) => {
        state.productTypeLoading = 'pending';
      })
      .addCase(findProductTypeById.fulfilled, (state, action) => {
        state.productTypeLoading = 'idle';
        state.productType = action.payload;
      })
      .addCase(findProductTypeById.rejected, (state) => {
        state.productTypeLoading = 'idle';
      });
    builder
      .addCase(fetchOptions.pending, (state) => {
        state.optionsLoading = 'pending';
      })
      .addCase(fetchOptions.fulfilled, (state, action) => {
        state.optionsLoading = 'idle';
        state.options = action.payload;
      })
      .addCase(fetchOptions.rejected, (state) => {
        state.optionsLoading = 'idle';
      });
    builder
      .addCase(fetchReferenceTypes.pending, (state) => {
        state.referenceTypesLoading = 'pending';
      })
      .addCase(fetchReferenceTypes.fulfilled, (state, action) => {
        state.referenceTypesLoading = 'idle';
        state.referenceTypes = action.payload;
      })
      .addCase(fetchReferenceTypes.rejected, (state) => {
        state.referenceTypesLoading = 'idle';
      });
    builder
      .addCase(fetchProducts.pending, (state) => {
        state.productsLoading = 'pending';
      })
      .addCase(fetchProducts.fulfilled, (state, action) => {
        state.productsLoading = 'idle';
        state.products = action.payload.rows;
        state.productsTotal = action.payload.count;
      })
      .addCase(fetchProducts.rejected, (state) => {
        state.productsLoading = 'idle';
      });
    builder
      .addCase(fetchProductsByIds.pending, (state) => {
        state.productsByIdsLoading = 'pending';
      })
      .addCase(fetchProductsByIds.fulfilled, (state, action) => {
        state.productsByIdsLoading = 'idle';
        state.productsByIds = action.payload.rows;
      })
      .addCase(fetchProductsByIds.rejected, (state) => {
        state.productsByIdsLoading = 'idle';
      });
    builder
      .addCase(getProducTypesByRegionId.pending, (state) => {
        state.productTypesByRegionLoading = 'pending';
      })
      .addCase(getProducTypesByRegionId.fulfilled, (state, action) => {
        state.productTypesByRegionLoading = 'idle';
        state.productTypesByRegion = action.payload;
      })
      .addCase(getProducTypesByRegionId.rejected, (state) => {
        state.productTypesByRegionLoading = 'idle';
      });
    builder
      .addCase(fetchProductsByZoneIdForProductCatalogPrices.pending, (state) => {
        state.productsByZoneIdForProductCatalogPricesLoading = 'pending';
      })
      .addCase(fetchProductsByZoneIdForProductCatalogPrices.fulfilled, (state, action) => {
        state.productsByZoneIdForProductCatalogPricesLoading = 'idle';
        state.productsByZoneIdForProductCatalogPrices = action.payload.rows;
        state.productsByZoneIdForProductCatalogPricesTotal = action.payload.count;
      })
      .addCase(fetchProductsByZoneIdForProductCatalogPrices.rejected, (state) => {
        state.productsByZoneIdForProductCatalogPricesLoading = 'idle';
      });
    builder
      .addCase(fetchProductsByZoneIdForProductCatalogOptionPrices.pending, (state) => {
        state.productsByZoneIdForProductCatalogOptionPricesLoading = 'pending';
      })
      .addCase(fetchProductsByZoneIdForProductCatalogOptionPrices.fulfilled, (state, action) => {
        state.productsByZoneIdForProductCatalogOptionPricesLoading = 'idle';
        state.productsByZoneIdForProductCatalogOptionPrices = action.payload.rows;
        state.productsByZoneIdForProductCatalogOptionPricesTotal = action.payload.count;
      })
      .addCase(fetchProductsByZoneIdForProductCatalogOptionPrices.rejected, (state) => {
        state.productsByZoneIdForProductCatalogOptionPricesLoading = 'idle';
      });
    builder
      .addCase(fetchProductTypeUnit.pending, (state) => {
        state.productTypeUnitLoading = 'pending';
      })
      .addCase(fetchProductTypeUnit.fulfilled, (state, action) => {
        state.productTypeUnitLoading = 'idle';
        state.productTypeUnit = action.payload.rows;
        state.productTypeUnitTotal = action.payload.count;
      })
      .addCase(fetchProductTypeUnit.rejected, (state) => {
        state.productTypeUnitLoading = 'idle';
      });
    builder
      .addCase(fetchProductTypeInterventions.pending, (state) => {
        state.productTypeInterventionsLoading = 'pending';
      })
      .addCase(fetchProductTypeInterventions.fulfilled, (state, action) => {
        state.productTypeInterventionsLoading = 'idle';
        state.productTypeInterventions = action.payload.rows;
        state.productTypeInterventionsTotal = action.payload.count;
      })
      .addCase(fetchProductTypeInterventions.rejected, (state) => {
        state.productTypeInterventionsLoading = 'idle';
      });
    builder
      .addCase(fetchFacturationType.pending, (state) => {
        state.facturationTypeLoading = 'pending';
      })
      .addCase(fetchFacturationType.fulfilled, (state, action) => {
        state.facturationTypeLoading = 'idle';
        state.facturationType = action.payload.rows;
        state.facturationTypeTotal = action.payload.count;
      })
      .addCase(fetchFacturationType.rejected, (state) => {
        state.facturationTypeLoading = 'idle';
      });
  },
});

export const selectProductTypesTotal = (state: RootState) => state.product.productTypesTotal;
export const selectProductTypes = (state: RootState) => state.product.productTypes;
export const selectProductTypesLoading = (state: RootState) => state.product.productTypesLoading;
export const selectProductType = (state: RootState) => state.product.productType;
export const selectProductTypeLoading = (state: RootState) => state.product.productTypeLoading;
export const selectOptions = (state: RootState) => state.product.options;
export const selectOptionsLoading = (state: RootState) => state.product.optionsLoading;
export const selectReferenceTypes = (state: RootState) => state.product.referenceTypes;
export const selectReferenceTypesLoading = (state: RootState) => state.product.referenceTypesLoading;
export const selectProducts = (state: RootState) => state.product.products;
export const selectProductsLoading = (state: RootState) => state.product.productsLoading;
export const selectProductsTotal = (state: RootState) => state.product.productsTotal;
export const selectProductsByIds = (state: RootState) => state.product.productsByIds;
export const selectProductsByIdsLoading = (state: RootState) => state.product.productsByIdsLoading;
export const selectProductTypesByRegion = (state: RootState) => state.product.productTypesByRegion;
export const selectProductTypesByRegionLoading = (state: RootState) => state.product.productTypesByRegionLoading;
export const selectProductsByZoneIdForProductCatalogPrices = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogPrices;
export const selectProductsByZoneIdForProductCatalogPricesLoading = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogPricesLoading;
export const selectProductsByZoneIdForProductCatalogPricesTotal = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogPricesTotal;
export const selectProductsByZoneIdForProductCatalogOptionPrices = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogOptionPrices;
export const selectProductsByZoneIdForProductCatalogOptionPricesLoading = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogOptionPricesLoading;
export const selectProductsByZoneIdForProductCatalogOptionPricesTotal = (state: RootState) =>
  state.product.productsByZoneIdForProductCatalogOptionPricesTotal;
export const selectProductTypeUnit = (state: RootState) => state.product.productTypeUnit;
export const selectProductTypeUnitLoading = (state: RootState) => state.product.productTypeUnitLoading;
export const selectProductTypeInterventions = (state: RootState) => state.product.productTypeInterventions;
export const selectProductTypeInterventionsLoading = (state: RootState) =>
  state.product.productTypeInterventionsLoading;
export const selectFacturationType = (state: RootState) => state.product.facturationType;
export const selectFacturationTypeTotal = (state: RootState) => state.product.facturationTypeTotal;
export const selectFacturationTypeLoading = (state: RootState) => state.product.facturationTypeLoading;
export default productSlice.reducer;
