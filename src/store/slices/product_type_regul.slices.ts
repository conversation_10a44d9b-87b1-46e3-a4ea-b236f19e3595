import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { ProductTypeRegul } from 'models';
import { Loading, QueryParams, SearchData } from 'types';
import productTypeRegulService from 'services/product_type_regul.service';

interface ProductTypeRegulState {
    productTypeReguls: ProductTypeRegul[];
    productTypeRegul: ProductTypeRegul | null;
    loading: Loading;
    productTypeRegulTotal: number;
}

const name = 'productTypeRegul';

const initialState: ProductTypeRegulState = {
    productTypeReguls: [],
    productTypeRegul: null,
    loading: 'idle',
    productTypeRegulTotal: 0,
};

export const fetchProductTypeReguls = createAsyncThunk(
    `${name}/list-management-product-type-reguls`,
    async (
        query: {
            productTypeId: number;
        } & QueryParams,
        { rejectWithValue },
    ) => {
        try {
            const response = await productTypeRegulService.getProductTypeRegul(query);
            return response;
        } catch (error: any) {
            return rejectWithValue(error.response.data);
        }
    },
);

export const findProductTypeRegulById = createAsyncThunk(
    `${name}/get-product-type-regul-by-id`,
    async (productTypeRegulId: number, { rejectWithValue }) => {
        try {
            const response = await productTypeRegulService.getOneProductTypeRegul(productTypeRegulId);
            return response;
        } catch (error: any) {
            return rejectWithValue(error.response.data);
        }
    },
);

export const productTypeRegulSlice = createSlice({
    name,
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchProductTypeReguls.pending, (state) => {
                state.loading = 'pending';
            })
            .addCase(fetchProductTypeReguls.fulfilled, (state, action) => {
                state.loading = 'idle';
                state.productTypeReguls = action.payload.rows;
                state.productTypeRegulTotal = action.payload.count;
            })
            .addCase(fetchProductTypeReguls.rejected, (state) => {
                state.loading = 'idle';
            });
        builder
            .addCase(findProductTypeRegulById.pending, (state) => {
                state.loading = 'pending';
            })
            .addCase(findProductTypeRegulById.fulfilled, (state, action) => {
                state.loading = 'idle';
                state.productTypeRegul = action.payload;
            })
            .addCase(findProductTypeRegulById.rejected, (state) => {
                state.loading = 'idle';
            });
        },
});

export const selectProductTypeRegul = (state: RootState) => state.productTypeRegul.productTypeRegul;
export const selectProductTypeRegulTotal = (state: RootState) => state.productTypeRegul.productTypeRegulTotal;
export const selectProductTypeReguls = (state: RootState) => state.productTypeRegul.productTypeReguls;
export const selectProductTypeRegulLoading = (state: RootState) => state.productTypeRegul.loading;

export default productTypeRegulSlice.reducer;