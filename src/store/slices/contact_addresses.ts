import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { ContactAddresses } from 'models';
import { contactAddressesService } from 'services';
import { RootState } from 'store';
import { Loading } from 'types';

interface ContactAddressesState {
    contactAddress: ContactAddresses[];
    contactAddresses: ContactAddresses | null;
    contactAddressTotal: number;
    contactAddressLoading: Loading;
}

const name = 'contactAddress';
const initialState: ContactAddressesState = {
    contactAddress: [],
    contactAddresses: null,
    contactAddressTotal: 0,
    contactAddressLoading: 'idle',
};

export const fetchContactAddress = createAsyncThunk(
    `${name}/list-management-contactAddress`,
    async (payload: any, { rejectWithValue }) => {
        try {
            const response = await contactAddressesService.getContactAddresses(payload);
            return response;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);

export const findContactAddressById = createAsyncThunk(
    `${name}/find-contactAddress-by-id`,
    async (payload: {
        contactAddressId: number;
        params?: any;
    }, { rejectWithValue }) => {
        try {
            const response = await contactAddressesService.findContactAddress(payload?.contactAddressId, payload?.params);
            if (response?.data?.error) {
                return rejectWithValue(response.data);
            }
            return response;
        } catch (error) {
            return rejectWithValue(error);
        }
    }
);

export const contactAddressSlice = createSlice({
    name,
    initialState,
    reducers: {},
    extraReducers: (builder) => {
        builder
            .addCase(fetchContactAddress.pending, (state) => {
                state.contactAddressLoading = 'pending';
            })
            .addCase(fetchContactAddress.fulfilled, (state, action) => {
                state.contactAddress = action.payload.rows;
                state.contactAddressTotal = action.payload.count;
                state.contactAddressLoading = 'idle';
            })
            .addCase(fetchContactAddress.rejected, (state) => {
                state.contactAddressLoading = 'idle';
            });
        builder
            .addCase(findContactAddressById.pending, (state) => {
            state.contactAddressLoading = 'pending';
            })
            .addCase(findContactAddressById.fulfilled, (state, action) => {
            state.contactAddresses = action.payload;
            })
            .addCase(findContactAddressById.rejected, (state) => {
            state.contactAddressLoading = 'idle';
            });
    },
});
export const selectContactAddress = (state: RootState) => state.contactAddress.contactAddress;
export const selectContactAddresses = (state: RootState) => state.contactAddress.contactAddresses;
export const selectContactAddressTotal = (state: RootState) => state.contactAddress.contactAddressTotal;
export const selectContactAddressLoading = (state: RootState) => state.contactAddress.contactAddressLoading;
export default contactAddressSlice.reducer;
