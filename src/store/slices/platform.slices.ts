import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import Platform from 'models/platform';
import platformService from 'services/platform.service';
import { Loading } from 'types';

interface PlatformState {
  platforms: Platform[];
  platformsLoading: Loading;
}

const name = 'platform';
const initialState: PlatformState = {
  platforms: [],
  platformsLoading: 'idle',
};

export const fetchPlatforms = createAsyncThunk(
  `${name}/list-management-platforms`,
  async (_, { rejectWithValue, getState }) => {
    const state = getState() as unknown as RootState;
    if (state.platform.platforms.length) {
      return state.platform.platforms;
    }
    try {
      const response = await platformService.getPlatforms();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  }
);

export const platformSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPlatforms.pending, (state) => {
        state.platformsLoading = 'pending';
      })
      .addCase(fetchPlatforms.fulfilled, (state, action) => {
        state.platformsLoading = 'idle';
        state.platforms = action.payload;
      })
      .addCase(fetchPlatforms.rejected, (state) => {
        state.platformsLoading = 'idle';
      });
  },
});

export const selectPlatforms = (state: RootState) => state.platform.platforms;
export const selectPlatformsLoading = (state: RootState) =>
  state.platform.platformsLoading;

export default platformSlice.reducer;
