/* eslint-disable @typescript-eslint/no-explicit-any */
import { createAsyncThunk, createSlice } from '@reduxjs/toolkit';
import { Documents, DocumentStatus, DocumentType } from 'models';
import { documentService } from 'services';
import { RootState } from 'store';
import { Loading, QueryParams } from 'types';

interface DocumentState {
  documents: Documents[];
  document: Documents | null;
  documentTotal: number;
  documentLoading: Loading;
  documentTypes: DocumentType[];
  documentTypesLoading: Loading;
  documentStatuses: DocumentStatus[];
  documentStatusesLoading: Loading;
}

const name = 'document';
const initialState: DocumentState = {
  documents: [],
  document: null,
  documentTotal: 0,
  documentLoading: 'idle',
  documentTypes: [],
  documentTypesLoading: 'idle',
  documentStatuses: [],
  documentStatusesLoading: 'idle',
};

export const fetchDocuments = createAsyncThunk(
  `${name}/list-management-documents`,
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocuments(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const findDocumentsByParams = createAsyncThunk(
  `${name}/find-document-by-params`,
  async (payload: any, { rejectWithValue }) => {
    try {
      const response = await documentService.findDocumentsByParams(payload);
      if (response?.data?.error) {
        return rejectWithValue(response.data);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const findDocumentById = createAsyncThunk(
  `${name}/find-document-by-id`,
  async (
    payload: {
      documentId: number | string;
      params?: any;
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await documentService.findDocument(payload?.documentId, payload?.params);
      if (response?.data?.error) {
        return rejectWithValue(response.data);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchDocumentTypes = createAsyncThunk(
  `${name}/list-management-document-types`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocumentTypes(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchDocumentStatuses = createAsyncThunk(
  `${name}/list-management-document-statuses`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = await documentService.getDocumentStatus(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
export const documentSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchDocuments.pending, (state) => {
        state.documentLoading = 'pending';
      })
      .addCase(fetchDocuments.fulfilled, (state, action) => {
        state.documentLoading = 'idle';
        state.documents = action.payload.rows;
        state.documentTotal = action.payload.count;
      })
      .addCase(fetchDocuments.rejected, (state) => {
        state.documentLoading = 'idle';
      });
    builder.addCase(findDocumentsByParams.fulfilled, (state, action) => {
      state.document = action.payload?.[0];
    });
    builder
      .addCase(findDocumentById.pending, (state) => {
        state.documentLoading = 'pending';
      })
      .addCase(findDocumentById.fulfilled, (state, action) => {
        state.document = action.payload;
      })
      .addCase(findDocumentById.rejected, (state) => {
        state.documentLoading = 'idle';
      });
    builder
      .addCase(fetchDocumentTypes.pending, (state) => {
        state.documentTypesLoading = 'pending';
      })
      .addCase(fetchDocumentTypes.fulfilled, (state, action) => {
        state.documentTypesLoading = 'idle';
        state.documentTypes = action.payload.rows;
      })
      .addCase(fetchDocumentTypes.rejected, (state) => {
        state.documentTypesLoading = 'idle';
      });
    builder
      .addCase(fetchDocumentStatuses.pending, (state) => {
        state.documentStatusesLoading = 'pending';
      })
      .addCase(fetchDocumentStatuses.fulfilled, (state, action) => {
        state.documentStatusesLoading = 'idle';
        state.documentStatuses = action.payload;
      })
      .addCase(fetchDocumentStatuses.rejected, (state) => {
        state.documentStatusesLoading = 'idle';
      });
  },
});

export const selectDocuments = (state: RootState) => state.document.documents;
export const selectDocumentsTotal = (state: RootState) => state.document.documentTotal;
export const selectDocumentsLoading = (state: RootState) => state.document.documentLoading;
export const selectDocument = (state: RootState) => state.document.document;
export const selectDocumentTypes = (state: RootState) => state.document.documentTypes;

export const selectDocumentTypesLoading = (state: RootState) => state.document.documentTypesLoading;
export const selectDocumentStatuses = (state: RootState) => state.document.documentStatuses;

export const selectDocumentStatusesLoading = (state: RootState) => state.document.documentStatusesLoading;
export default documentSlice.reducer;
