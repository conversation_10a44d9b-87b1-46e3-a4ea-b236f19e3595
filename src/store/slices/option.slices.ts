import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { optionService } from 'services';
import { Option, OptionType } from 'models';
import { Loading, QueryParams } from 'types';
interface OptionState {
  optionTypes: OptionType[];
  optionTypesLoading: Loading;
  allOptions: Option[];
  allOptionsLoading: Loading;
  variationOptions: Option[];
  variationOptionsLoading: Loading;
  regulOptions: Option[];
  regulOptionsLoading: Loading;
}

const name = 'option';
const initialState: OptionState = {
  optionTypes: [],
  optionTypesLoading: 'idle',
  allOptions: [],
  allOptionsLoading: 'idle',
  variationOptions: [],
  variationOptionsLoading: 'idle',
  regulOptions: [],
  regulOptionsLoading: 'idle',
};

export const fetchOptionTypes = createAsyncThunk(
  `${name}/fetchOptionTypes`,
  async (_, { rejectWithValue, getState }) => {
    const state = getState() as unknown as RootState;
    if (state.option.optionTypes.length) {
      return state.option.optionTypes;
    }
    try {
      const response = await optionService.getOptionTypes();
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchAllOptions = createAsyncThunk(
  `${name}/fetch-all-options`,
  async (payload: QueryParams, { rejectWithValue }) => {
    try {
      const response = 'productTypeId[]' in payload ? await optionService.getOptions(payload) : [];
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchVariationOptions = createAsyncThunk(
  `${name}/list-management-variation-options`,
  async (
    payload: {
      productTypeId: number;
      query: {
        optionType: string | undefined;
      };
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await optionService.getAllOptions(payload.productTypeId, {
        ...payload.query,
        include: 'OptionType|SubOptions',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const fetchRegulOptions = createAsyncThunk(
  `${name}/list-management-regul-options`,
  async (
    payload: {
      productTypeId: number;
      query: {
        optionType: string | undefined;
      };
    },
    { rejectWithValue },
  ) => {
    try {
      const response = await optionService.getAllOptions(payload.productTypeId, {
        ...payload.query,
        include: 'OptionType|SubOptions',
      });
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const optionSlice = createSlice({
  name,
  initialState,
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchOptionTypes.pending, (state) => {
        state.optionTypesLoading = 'pending';
      })
      .addCase(fetchOptionTypes.fulfilled, (state, action) => {
        state.optionTypesLoading = 'idle';
        state.optionTypes = action.payload;
      })
      .addCase(fetchOptionTypes.rejected, (state) => {
        state.optionTypesLoading = 'idle';
      });
    builder
      .addCase(fetchAllOptions.pending, (state) => {
        state.allOptionsLoading = 'pending';
      })
      .addCase(fetchAllOptions.fulfilled, (state, action) => {
        state.allOptionsLoading = 'idle';
        state.allOptions = action.payload;
      })
      .addCase(fetchAllOptions.rejected, (state) => {
        state.allOptionsLoading = 'idle';
      });
    builder
      .addCase(fetchVariationOptions.pending, (state) => {
        state.variationOptionsLoading = 'pending';
      })
      .addCase(fetchVariationOptions.fulfilled, (state, action) => {
        state.variationOptionsLoading = 'idle';
        state.variationOptions = action.payload;
      })
      .addCase(fetchVariationOptions.rejected, (state) => {
        state.variationOptionsLoading = 'idle';
      });
    builder
      .addCase(fetchRegulOptions.pending, (state) => {
        state.regulOptionsLoading = 'pending';
      })
      .addCase(fetchRegulOptions.fulfilled, (state, action) => {
        state.regulOptionsLoading = 'idle';
        state.regulOptions = action.payload;
      })
      .addCase(fetchRegulOptions.rejected, (state) => {
        state.regulOptionsLoading = 'idle';
      });
  },
});

export const selectOptionTypes = (state: RootState) => state.option.optionTypes;
export const selectOptionTypesLoading = (state: RootState) => state.option.optionTypesLoading;
export const selectAllOptions = (state: RootState) => state.option.allOptions;
export const selectOptionLoading = (state: RootState) => state.option.allOptionsLoading;
export const selectVariationOptions = (state: RootState) => state.option.variationOptions;
export const selectVariationOptionsLoading = (state: RootState) => state.option.variationOptionsLoading;
export const selectRegulOptions = (state: RootState) => state.option.regulOptions;
export const selectRegulOptionsLoading = (state: RootState) => state.option.regulOptionsLoading;

export default optionSlice.reducer;
