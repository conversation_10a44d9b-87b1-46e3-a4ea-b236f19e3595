import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';
import { RootState } from '..';
import { ClientContactPerson } from 'models';
import { clientContactPersonService } from 'services';
import { Loading } from 'types';

interface ClientContactPersonState {
  clientContactPersons: ClientContactPerson[];
  clientContactPerson: ClientContactPerson | null;
  clientContactPersonTotal: number;
  clientContactPersonLoading: Loading;
}

const name = 'client_contact_person';
const initialState: ClientContactPersonState = {
  clientContactPersons: [],
  clientContactPerson: null,
  clientContactPersonTotal: 0,
  clientContactPersonLoading: 'idle',
};

export const fetchClientContactPersons = createAsyncThunk(
  `${name}/list-management-client-contact-persons`,
  async (payload: object, { rejectWithValue }) => {
    try {
      const response = await clientContactPersonService.getContactPersons(payload);
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);
export const findContactPersonByParams = createAsyncThunk(
  `${name}/find-client-contact-person-by-crm-contact-id`,
  async (payload: object, { rejectWithValue }) => {
    try {
      const response = await clientContactPersonService.findContactPersonByParams(payload);
      if (response?.data?.error) {
        return rejectWithValue(response.data);
      }
      return response;
    } catch (error) {
      return rejectWithValue(error);
    }
  },
);

export const clientContactPersonSlice = createSlice({
  name,
  initialState,
  reducers: {
    setClientContactPersons: (state, action) => {
      state.clientContactPersons = action.payload;
    },
    setClientContactPerson: (state, action) => {
      state.clientContactPerson = action.payload;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchClientContactPersons.pending, (state) => {
        state.clientContactPersonLoading = 'pending';
      })
      .addCase(fetchClientContactPersons.fulfilled, (state, action) => {
        state.clientContactPersonLoading = 'idle';
        state.clientContactPersons = action.payload.rows ?? [];
        state.clientContactPersonTotal = action.payload.count;
      })
      .addCase(fetchClientContactPersons.rejected, (state) => {
        state.clientContactPersonLoading = 'idle';
      });
    builder.addCase(findContactPersonByParams.fulfilled, (state, action) => {
      state.clientContactPerson = action.payload.rows.length > 0 ? action.payload.rows[0] : null;
    });
  },
});

export const selectClientContactPersons = (state: RootState) => state.clientContactPerson.clientContactPersons;
export const selectClientContactPersonTotal = (state: RootState) => state.clientContactPerson.clientContactPersonTotal;
export const selectClientContactPersonsLoading = (state: RootState) =>
  state.clientContactPerson.clientContactPersonLoading;
export const selectClientContactPerson = (state: RootState) => state.clientContactPerson.clientContactPerson;

export const { setClientContactPersons, setClientContactPerson } = clientContactPersonSlice.actions;
export default clientContactPersonSlice.reducer;
