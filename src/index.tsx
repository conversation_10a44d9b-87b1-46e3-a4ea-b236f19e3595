import React from 'react';
import { Provider } from 'react-redux';
import ReactDOM from 'react-dom/client';
import { BrowserRouter } from 'react-router-dom';
import { HelmetProvider } from 'react-helmet-async';
import { AuthContextProvider } from './store/auth-context';
import App from './App';
import store from './store';
import './styles/main.scss';
import 'react-toastify/dist/ReactToastify.css';
import { ToastContainer } from 'react-toastify';
import { GlobalProvider } from 'store/global-context';

const root = ReactDOM.createRoot(document.getElementById('root') as HTMLElement);
root.render(
  <React.StrictMode>
    <Provider store={store}>
      <BrowserRouter basename={`${process.env.REACT_APP_BASE_PATH}`}>
        <AuthContextProvider>
          <GlobalProvider>
            <HelmetProvider>
              <App />
              <ToastContainer
                position='top-right'
                autoClose={5000}
                hideProgressBar={false}
                newestOnTop={false}
                closeOnClick
                rtl={false}
                pauseOnFocusLoss
                draggable
                pauseOnHover
                theme='colored'
              />
            </HelmetProvider>
          </GlobalProvider>
        </AuthContextProvider>
      </BrowserRouter>
    </Provider>
    ,
  </React.StrictMode>,
);
