import { PaginationData } from "types";
import request from './requesters/client.request';
import { ContactAddresses } from "models";
const contactAddressesService = {
    getContactAddresses: (params: any) =>
        request.get<PaginationData<ContactAddresses>>('/contact-addresses', { params }),
    findContactAddress: (contactAddressId: number, params?: any) =>
        request.get(`/contact-addresses/${contactAddressId}`, { params }),
    findContactAddressesByParams: (params: any) =>
        request.get(`/contact-addresses`, { params }),
    createContactAddress: (data: any) => request.post<ContactAddresses>('/contact-addresses', data),
    updateContactAddress: (contactAddressId: number, data: any) =>
        request.put<ContactAddresses>(`/contact-addresses/${contactAddressId}`, data),
    deleteContactAddress: (contactAddressId: number) =>
        request.delete(`/contact-addresses/${contactAddressId}`),
}

export default contactAddressesService;