import { CatalogPrice } from 'models';
import request from './requesters/price.request';
import { CatalogPriceZone, CatalogPriceZoneResponse, CatalogPriceZoneServiceProviderRequest, QueryParams } from 'types';
import { CatalogPriceZoneParams } from 'hooks/catalog-price';

const catalogPriceService = {
  getCatalogPrices: (query: QueryParams) =>
    request.get<CatalogPrice[]>(`/catalog-prices`, {
      params: query,
    }),
  createCatalogPrice: (data: CatalogPrice) => request.post<CatalogPrice>(`/catalog-prices`, data),
  getCatalogPriceZoneServiceProviders: (data: CatalogPriceZoneServiceProviderRequest) =>
    request.post<CatalogPriceZone[]>(`/catalog-prices-zone-service-provider`, data),
  getCatalogPriceZones: (data: CatalogPriceZoneParams) =>
    request.post<CatalogPriceZoneResponse>(`/catalog-prices-zone`, data),
};

export default catalogPriceService;
