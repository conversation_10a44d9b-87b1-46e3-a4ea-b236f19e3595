import axios from 'axios';
import { getIdToken, removeToken } from '../token.service';

const boRequester = axios.create({
  baseURL: process.env.REACT_APP_BASE_BO_SERVICE_API,
  headers: {
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
});
boRequester.interceptors.request.use(async (config) => {
  const idToken = getIdToken();
  if (idToken) {
    config.headers.Authorization = `Bearer ${idToken}`;
  } else {
    config.headers.Authorization = undefined;
  }
  return config;
});
// Add a response interceptor
boRequester.interceptors.response.use(
  function (response: {
    data: {
      status: string;
      output: string;
      cause: string;
      error?: {
        Cause: string;
      };
      statusCode?: number;
      message?: string;
      details?: {
        error?: string;
      };
    };
  }) {
    if (response?.data?.status === 'FAILED') {
      const causeForSyncSP =
        response.data?.cause && typeof response.data?.cause === 'string'
          ? JSON.parse(response.data?.cause)
          : response.data?.error?.Cause;
      if (causeForSyncSP?.errorMessage) {
        const messageData =
          typeof causeForSyncSP?.errorMessage === 'string'
            ? JSON.parse(causeForSyncSP?.errorMessage)
            : causeForSyncSP?.errorMessage;
        return Promise.reject({ error: true, message: messageData?.message });
      }
      return Promise.reject({ error: true, message: response.data?.cause });
    }
    if (response?.data?.output) {
      response.data = JSON.parse(response.data.output);
    }
    if (response?.data?.statusCode === 500) {
      // If the server returns a 500 status code, it means there is an internal error.
      // We reject the promise with an error object containing the error message.
      // The message is built by concatenating the message and the error details with a colon separator.
      // If one of the two properties is null or undefined, it is filtered out.
      const message = [response?.data?.message, response?.data?.details?.error ? response?.data?.details?.error : null]
        ?.filter(Boolean)
        ?.join(' : ');
      return Promise.reject({ error: true, message: message || 'Erreur' });
    }
    return Promise.resolve(response.data);
  },
  function (error) {
    if (error.response) {
      error.status = error.response.status;
      error.message = error.response.statusText;
    } else if (error.request) {
      // The request was made but no response was received
      // `error.request` is an instance of XMLHttpRequest in the browser and an instance of
      // http.BoRequest in node.js
      // console.log(error.request);
    } else {
      // Something happened in setting up the request that triggered an Error
      // console.log('Error', error.message);
    }
    console.log(error);
    if (error?.status && error.status === 401) {
      removeToken();
      window.location.href = '/login';
    }
    return Promise.reject(error);
  },
);

export default boRequester;
