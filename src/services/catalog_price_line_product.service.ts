import { PaginationData } from 'types';
import { CatalogPriceLineProduct } from '../models';
import request from './requesters/price.request';

const catalogPriceLineProductService = {
  getCatalogPriceLineProducts: (params?: any) =>
    request.get<PaginationData<CatalogPriceLineProduct>>(`/catalog-price-line-products`, {
      params,
    }),
  getOneCatalogPriceLineProduct: (catalogPriceLineProductId: number, params?: any) =>
    request.get<CatalogPriceLineProduct>(`/catalog-price-line-products/${catalogPriceLineProductId}`, { params }),
  createCatalogPriceLineProduct: (data: any) =>
    request.post<CatalogPriceLineProduct>(`/catalog-price-line-products`, data),
  updateCatalogPriceLineProduct: (
    catalogPriceLineProductId: number,
    data: any
  ) =>
    request.put<CatalogPriceLineProduct>(
      `/catalog-price-line-products/${catalogPriceLineProductId}`,
      data
    ),
    multipleCreateOrUpdateCatalogPriceLineProducts: (data: any) =>
    request.post<CatalogPriceLineProduct[]>(
      `/catalog-price-line-products/multiple-create-or-update`,
      data
    ),
    deactiveCatalogPriceLineProduct: (
    catalogPriceLineProductId: number | string
  ) =>
    request.delete<CatalogPriceLineProduct>(
      `/catalog-price-line-products/${catalogPriceLineProductId}`
    ),
};

export default catalogPriceLineProductService;
