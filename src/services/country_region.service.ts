import { CountryRegion } from '../models';
import { PaginationData, QueryParams } from '../types';
import request from './requesters/region.request';

const countryRegionService = {
  getRegions: (params: QueryParams) => request.get<PaginationData<CountryRegion>>('/country-regions', { params }),

  getOneRegion: (params: QueryParams) => request.get<CountryRegion>('/country-regions/find-one', { params }),
};

export default countryRegionService;
