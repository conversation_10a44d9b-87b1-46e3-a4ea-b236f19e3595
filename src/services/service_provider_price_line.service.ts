import { PaginationData } from 'types';
import { ServiceProviderPriceLine } from '../models';
import request from './requesters/service_provider.request';

const serviceProviderPriceLineService = {
  createServiceProviderPriceLine: (serviceProviderId: number, data: any) =>
    request.post<ServiceProviderPriceLine>(
      `/service-providers/${serviceProviderId}/service-provider-price-lines`,
      data
    ),
  duplicateServiceProviderPriceLine: (serviceProviderPriceLineId: number) =>
    request.post<ServiceProviderPriceLine>(
      `/service-provider-price-lines/${serviceProviderPriceLineId}/duplicate`
    ),
  updateServiceProviderPriceLine: (
    serviceProviderId: number,
    priceLineId: number,
    data: any
  ) =>
    request.put<ServiceProviderPriceLine>(
      `/service-providers/${serviceProviderId}/service-provider-price-lines/${priceLineId}`,
      data
    ),
  multipleCheckServiceProviderPriceLines: (data: {
    serviceProviderPriceLineIds: number[];
    isChecked: boolean;
  }) =>
    request.put<ServiceProviderPriceLine>(
      `/service-provider-price-lines/multiple-check`,
      data
    ),
  getServiceProviderPriceLines: (
    serviceProviderId: number | string,
    params?: any
  ) =>
    request.get<PaginationData<ServiceProviderPriceLine>>(
      `service-providers/${serviceProviderId}/service-provider-price-lines`,
      {
        params,
      }
    ),
  deactiveServiceProviderPriceLine: (
    serviceProviderId: number | string,
    serviceProviderPriceLineId: number | string
  ) =>
    request.delete(
      `service-providers/${serviceProviderId}/service-provider-price-lines/${serviceProviderPriceLineId}`
    ),
};

export default serviceProviderPriceLineService;
