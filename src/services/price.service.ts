import { PaginationData, QueryParams } from 'types';
import request from './requesters/price.request';
import productRequest from './requesters/product.request';
import { Price, PriceTypeLogic } from 'models';

const priceService = {
  getPrices: (params?: QueryParams) => request.get<PaginationData<Price>>(`/prices`, { params }),
  findPrice: (productTypeId: number, priceFamilyId: number, priceId: number) =>
    request.get(`product-types/${productTypeId}/price-families/${priceFamilyId}/prices/${priceId}`),
  createPrice: (data: object) => request.post(`/prices`, data),
  updatePrice: (priceId: number, data: object) => request.put(`/prices/${priceId}`, data),
  deletePrice: (productTypeId: number, priceFamilyId: number, priceId: number) =>
    request.delete(`/product-types/${productTypeId}/price-families/${priceFamilyId}/prices/${priceId}`),
  createMultiplePrices: (data: object) => productRequest.post('/prices', data),
  updateMultiplePrices: (priceId: number, data: object) => productRequest.put(`/prices/${priceId}`, data),
  getPriceTypeLogics: (params?: QueryParams) => request.get<PriceTypeLogic[]>('/price-type-logics', { params }),
  getPriceProductWithZonesPagination: (params: object) =>
    request.get<PaginationData<Price>>('/price-products-with-zones', { params }),
  getPriceProductWithZonesSales: (body: {
    dataList: unknown[];
    productInactive: string[];
    page: number;
    limit: number;
    orderBy: string;
  }) => request.post('/price-products-with-zones-sales', body),
};

export default priceService;
