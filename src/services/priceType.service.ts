import request from "./requesters/price.request";

const priceTypeService = {
  getPriceTypes: () => request.get(`/price-types`),
  findPriceType: (priceTypeId: number) => request.get(`/price-types/${priceTypeId}`),
  createPriceType: (data: any) => request.post(`/price-types`, data),
  updatePriceType: (priceTypeId: number, data: any) => request.put(`/price-types/${priceTypeId}`, data),
  deletePriceType: (priceTypeId: number) => request.delete(`/price-types/${priceTypeId}`)
};

export default priceTypeService;
