import { Contact } from '../models';
import { PaginationData } from '../types';
import request from './requesters/service_provider.request';

const contactService = {
  getContacts: (params: any) =>
    request.get<PaginationData<Contact>>('/contacts', { params }),
  findContact: (contactId: number, params: any) =>
    request.get(`/contacts/${contactId}`, { params }),
  createContact: (data: any) => request.post<Contact>('/contacts', data),
  updateContact: (contactId: number, data: any) =>
    request.put<Contact>(`/contacts/${contactId}`, data),
  deleteContact: (contactId: number) =>
    request.delete(`/contacts/${contactId}`),
};

export default contactService;
