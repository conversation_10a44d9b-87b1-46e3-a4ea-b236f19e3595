import { WasteCenterType } from '../models';
import { PaginationData, QueryParams } from '../types';
import request from './requesters/service_provider.request';

const wasteCenterTypeService = {
  getWasteCenterTypes: (params: QueryParams) =>
    request.get<PaginationData<WasteCenterType>>('/waste-center-types', {
      params,
    }),
  multipleWasteCenterTypeUpdates: (data: { wasteCenterTypes: WasteCenterType[] }) =>
    request.put<WasteCenterType[]>('/waste-center-types/multiple-updates', data),
};

export default wasteCenterTypeService;
