import request from './requesters/bo.request';
import TruckDriver, {
  BennePointp,
  BenneProduct,
  BenneSupplier,
  BenneWasteManager,
  Craftmen,
  GarbageUnit,
  GlobalGarbagePricing,
  PUP,
  TimePeriod,
} from '../models/bo';

const boService = {
  getPrestataire: () => request.get<{ listTruckDriver: TruckDriver[] }>('/list-truck-driver'),
  getTimePeriods: () => request.get<{ list_periods_time: TimePeriod[] }>('/logistic-get-list-periods-time-camion'),
  getPeriodTimeBennes: (query: { service: string }) =>
    request.get<{ periodsDepositTimeBenne: TimePeriod[]; periodsPickupTimeBenne: TimePeriod[] }>(
      '/logistic-get-list-periods-time-benne',
      { params: query },
    ),
  getDecheteries: () => request.get<{ pups: PUP[] }>('/list-all-pup-camion'),
  getBenneSupplier: (id: number | undefined) => request.get<{ benneSupplier: BenneSupplier }>(`/benneurs/${id}`),
  getBenneWasteManagers: (params?: { supplier_id: number }) =>
    request.get<{ listBenneWasteManager: BenneWasteManager[] }>('/get-list-waste-managers-follow-supplier', { params }),
  getBennePointPProducts: (data: { platform: string; type: string }) =>
    request.post<{ listProduct: BenneProduct[] }>('/get-product-benne-pointp-logistic', data),
  getClients: () => request.get<{ listUserEcodropCM: Craftmen[] }>('/list-user-ecodrop-cm'),
  getGlobalGarbagePricings: () => request.get<GlobalGarbagePricing>('/global-garbage-pricing'),
  getProductTypes: () => request.get<{ listProduct: BennePointp[] }>('/get-list-product-create-bcc?service=ECODROP'),
  getProductBenneTypes: () => request.get<{ listProductEcodrop: BennePointp[] }>('/get-all-product-ecodrop-benne'),
  getProductCamions: (param: string) => request.get<BenneProduct[]>(`/get-product-camion-with-zone?zoneId=${param}`),
  getGarbageUnits: () => request.get<{ unit: GarbageUnit[] }>('/garbage-units'),

  findCraftmenPhone: (param: object) => request.post('/find-cm-phone', param),
  submitCamionOrder: (data: object) => request.post('/camion', data),

  createOrUpdateSpFromQBO: (data: object) => request.post('/create-or-update-service-provider-from-QBO', data),
  deleteSpFromQBO: (data: object) => request.post('/delete-service-provider', data),
  submitBenneBigbagOrder: (data: object) => request.post('/benne-bigbag', data),
};

export default boService;
