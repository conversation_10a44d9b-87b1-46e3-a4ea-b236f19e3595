import { PaginationData } from 'types';
import request from './requesters/document.request';
import { DocumentProductLine, Documents, DocumentStatus, DocumentType } from 'models';
import { UploadFileCustom } from 'components/Estimate/FileUpload';

const documentService = {
  getDocuments: (params: object) => request.get<PaginationData<Documents>>('/documents', { params }),
  getDocumentTypes: (params?: object) => request.get<PaginationData<DocumentType>>('/document-types', { params }),
  getDocumentStatus: (params?: object) => request.get<DocumentStatus[]>('/document-statuses', { params }),
  getDocumentProductLinePrestationStatus: (params?: object) =>
    request.get<PaginationData<Documents>>('/document-product-line-prestation-statuses', { params }),
  findDocument: (documentId: number | string, params?: object) => request.get(`/document/${documentId}`, { params }),
  findDocumentsByParams: (params: object) => request.get(`/documents`, { params }),
  createDocument: (data: object) => request.post<Documents>('/documents', data),
  updateDocument: (documentId: number, data: object) => request.put<Documents>(`/documents/${documentId}`, data),
  updateDocumentComment: (documentId: number, data: object) =>
    request.put<Documents>(`/document-update-comment/${documentId}`, data),
  deleteDocument: (documentId: number) => request.delete(`/document/${documentId}`),
  uploadFileToSalesorder: (file_upload: UploadFileCustom, salesorder_id?: string | number) =>
    request.post(`/document-file-uploads`, { ...file_upload, documentId: salesorder_id }),
  deleteFileToSalesorder: (id: string | number) => request.delete(`/document-file-uploads/${id}`),
  getDocumentProductLines: (params?: object) =>
    request.get<PaginationData<DocumentProductLine>>('/document-product-line', { params }),
  createEstimateAndOrder: (data: object) => request.post<Documents>('/documents/create-estimate-and-order', data),
  updateStatusDocument: (documentId: number, data: object) => request.put(`/document/${documentId}/status`, data),
};

export default documentService;
