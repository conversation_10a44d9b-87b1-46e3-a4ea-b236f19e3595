import { ProductCatalogOptionPrice } from '../models';
import { PaginationData } from '../types';
import request from './requesters/product.request';

const productCatalogOptionPriceService = {
  getProductCatalogOptionPrices: (query: any) =>
    request.get<PaginationData<ProductCatalogOptionPrice>>(
      '/product-catalog-option-prices',
      {
        params: query
      }
    ),
  createProductCatalogOptionPrice: (data: any) =>
    request.post<ProductCatalogOptionPrice>(
      '/product-catalog-option-prices',
      data
    ),
  updateProductCatalogOptionPrice: (
    productCatalogOptionPriceId: number,
    data: any
  ) =>
    request.put<ProductCatalogOptionPrice>(
      `/product-catalog-option-prices/${productCatalogOptionPriceId}`,
      data
    )
};

export default productCatalogOptionPriceService;
