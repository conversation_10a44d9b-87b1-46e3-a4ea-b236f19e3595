import request from './requesters/document.request';
import axios, { AxiosHeaders } from 'axios';
import { QueryParams } from 'types';

const fileService = {
  startMultipartUpload: (params: { fileName: string; fileType: string }) => {
    return request.get('/start-multipart-upload', { params });
  },
  getSignedMultipartUpload: (params: QueryParams) => {
    return request.get('/pre-signed-url-multipart-upload', { params });
  },
  completeMultipartUpload: (params: QueryParams) => {
    return request.post('/complete-multipart-upload', params);
  },
  getSignedUrlUpload: (params: { fileType: string; fileName: string }) => {
    return request.get<{
      preSignedURL: string;
      fileUrl: string;
      keyFile: string;
    }>('/pre-signed-url', { params });
  },
  getSignedUrlGet: (params: { keyFile: string }): Promise<{ preSignedURL: string }> => {
    return request.get('/pre-signed-url-get', { params });
  },
  uploadFileSignedUrl: (presignedUrl: string, blob: Blob | File, fileType: string) => {
    return axios.put<{
      fileName: string;
      fileUrl: string;
      headers: AxiosHeaders;
    }>(presignedUrl, blob, {
      headers: {
        'Content-Type': fileType,
      },
    });
  },
  uploadFileAdvanced: async (
    file: File,
    fileName: string,
    fileType: string,
    chunkSize = 10,
  ): Promise<{ fileName: string; fileUrl: string; keyFile: string }> => {
    return new Promise((resolve, reject) => {
      const run = async () => {
        try {
          const fileSize = file.size;
          // Start multipart upload if file size > 5MB
          if (fileSize > 5000000) {
            const { uploadId, keyFile } = await fileService.startMultipartUpload({
              fileName,
              fileType,
            });
            const CHUNK_SIZE = chunkSize * 5000000;
            const CHUNKS_COUNT = Math.floor(fileSize / CHUNK_SIZE) + 1;
            const promisesArray = [];
            let start;
            let end;
            let blob;
            for (let index = 1; index < CHUNKS_COUNT + 1; index++) {
              start = (index - 1) * CHUNK_SIZE;
              end = index * CHUNK_SIZE;
              blob = index < CHUNKS_COUNT ? file.slice(start, end) : file.slice(start);

              // Get presigned URL for each part
              const { preSignedURL } = await fileService.getSignedMultipartUpload({
                keyFile,
                partNumber: index,
                uploadId,
              });

              // Send part to aws server

              promisesArray.push(fileService.uploadFileSignedUrl(preSignedURL, blob, fileType));
            }
            const resolvedArray = await Promise.all(promisesArray);
            const uploadPartsArray: {
              ETag: {
                fileName: string;
                fileUrl: string;
                headers: string;
              };
              PartNumber: number;
            }[] = [];
            resolvedArray.forEach((resolvedPromise, index) => {
              uploadPartsArray.push({
                ETag: resolvedPromise.headers.etag,
                PartNumber: index + 1,
              });
            });

            // CompleteMultipartUpload in the backend server
            const response = await fileService.completeMultipartUpload({
              fileType,
              keyFile,
              parts: uploadPartsArray,
              uploadId,
            });
            resolve(response);
          } else {
            // Get presigned URL for each part
            const { preSignedURL, fileUrl, keyFile } = await fileService.getSignedUrlUpload({
              fileName,
              fileType,
            });

            await fileService.uploadFileSignedUrl(preSignedURL, file, fileType);
            resolve({ fileName, fileUrl, keyFile });
          }
        } catch (error) {
          reject(error);
        }
      };

      return run();
    });
  },
};
export default fileService;
