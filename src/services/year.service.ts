import { Year } from "../models";
import request from "./requesters/price.request";

const yearService = {
  getYears: (params: any) =>
    request.get<Year[]>("/years", { params }),
  findYear: (yearId: number) => request.get(`/years/${yearId}`),
  findOneYear: (params: any) => request.get(`/years`, { params }),
  createYear: (data: any) => request.post<Year>("/years", data),
  updateYear: (yearId: number, data: any) =>
    request.put<Year>(`/years/${yearId}`, data),
  deactivateYear: (yearId: number) => request.delete(`/years/${yearId}`),
  activateYearPrice: (yearId: number, data: any) => request.put(`/years/${yearId}/year-price-activate`, data),
  duplicateYearPrice: (yearId: number, data: any) => request.post(`/years/${yearId}/price-duplicate`, data),
};

export default yearService;
