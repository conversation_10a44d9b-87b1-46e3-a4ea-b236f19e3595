import { Client<PERSON><PERSON>actPerson } from '../models';
import { PaginationData, QueryParams } from '../types';
import request from './requesters/client.request';

const clientContactPersonService = {
  getContactPersons: (params: QueryParams) =>
    request.get<PaginationData<ClientContactPerson>>('/contact-persons', { params }),
  findContactPerson: (contactPersonId: string | number | undefined, params: QueryParams) =>
    request.get(`/contact-persons/${contactPersonId}`, { params }),
  findContactPersonByParams: (params: QueryParams) => request.get(`/contact-persons`, { params }),
  createContactPerson: (data: Client<PERSON><PERSON>act<PERSON>erson) =>
    request.post<ClientContactPerson>('/create-contact-persons', data),
  updateContactPerson: (contactPersonId: number, data: ClientContactPerson) =>
    request.put<ClientContactPerson>(`/contact-persons/${contactPersonId}`, data),
  deleteContactPerson: (contactPersonId: number) => request.delete(`/contact-persons/${contactPersonId}`),
};

export default clientContactPersonService;
