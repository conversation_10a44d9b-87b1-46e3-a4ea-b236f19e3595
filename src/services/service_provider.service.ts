import { PaginationData, QueryParams } from 'types';
import { ServiceProvider, ServiceProviderBankAccount } from '../models';
import request from './requesters/service_provider.request';

const serviceProviderService = {
  getServiceProviders: (params?: QueryParams) =>
    request.get<PaginationData<ServiceProvider>>('/service-providers', {
      params,
    }),
  getServiceProvidersByIds: (params?: QueryParams) =>
    request.get<PaginationData<ServiceProvider>>('/service-providers-by-ids', {
      params,
    }),
  getServiceProvider: (id: number, params: QueryParams) =>
    request.get<ServiceProvider>(`/service-providers/${id}`, { params }),
  createServiceProvider: (data: object) => request.post<ServiceProvider>('/service-providers', data),
  reactivateServiceProvider: (serviceProviderId: number) =>
    request.put(`/service-providers/${serviceProviderId}/re-activate`),
  deleteServiceProvider: (serviceProviderId: number) => request.delete(`/service-providers/${serviceProviderId}`),
  updateServiceProvider: (id: number, data: object) => request.put<ServiceProvider>(`/service-providers/${id}`, data),
  getBankAccount: (params: QueryParams) =>
    request.get<PaginationData<ServiceProviderBankAccount>>('/service-provider-bank-accounts', { params }),
  createBankAccount: (data: object) =>
    request.post<ServiceProviderBankAccount>('/service-provider-bank-accounts', data),
  updateBankAccount: (id: number, data: object) =>
    request.put<ServiceProviderBankAccount>(`/service-provider-bank-accounts/${id}`, data),
  setDefaultWasteCenter: (serviceProviderId: number, wasteCenterId: number) =>
    request.put(`/service-providers/${serviceProviderId}/set-default-waste-center/${wasteCenterId}`),
  getInactiveServiceProvider: () => request.get(`/service-providers/inactive`),
};

export default serviceProviderService;
