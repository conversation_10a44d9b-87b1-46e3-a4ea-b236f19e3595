import { ProductCatalogPrice } from '../models';
import { PaginationData } from '../types';
import request from './requesters/product.request';

const productCatalogPriceService = {
  getProductCatalogPrices: (params: any) =>
    request.get<PaginationData<ProductCatalogPrice>>(
      '/product-catalog-prices',
      { params }
    ),
  createProductCatalogPrice: (data: any) =>
    request.post<ProductCatalogPrice>('/product-catalog-prices', data),
  duplicateProductCatalogPrice: (data: {
    productId: number;
    groupId: number;
    zoneId: number;
  }) =>
    request.post<ProductCatalogPrice[]>(
      '/product-catalog-prices/duplicate',
      data
    ),
  updateProductCatalogPrice: (productCatalogPriceId: number, data: any) =>
    request.put<ProductCatalogPrice>(
      `/product-catalog-prices/${productCatalogPriceId}`,
      data
    ),
  deleteProductCatalogPrice: (data: any) =>
    request.delete('/product-catalog-prices', { data })
};

export default productCatalogPriceService;
