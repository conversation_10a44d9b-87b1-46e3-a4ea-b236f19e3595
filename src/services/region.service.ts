import { Region, Zone } from '../models';
import { PaginationData, QueryParams } from '../types';
import request from './requesters/region.request';

const regionService = {
  getRegions: (params: QueryParams) => request.get<PaginationData<Region>>('/regions', { params }),
  findRegion: (regionId: number) => request.get(`/regions/${regionId}`),
  findOneRegion: (params: QueryParams) => request.get(`/regions/find-one`, { params }),
  createRegion: (data: object) => request.post<Region>('/regions', data),
  updateRegion: (regionId: number, data: object) => request.put<Region>(`/regions/${regionId}`, data),
  deactivateRegion: (regionId: number) => request.delete(`/regions/${regionId}`),
  unVisibleRegion: (regionId: number) => request.delete(`/regions/${regionId}/un-visible`),
  getZonesNames: () => request.get(`/zones-names`, {}),
  getZonesByIds: (params: QueryParams) => request.get<PaginationData<Zone>>(`/zones-by-ids`, { params }),
};

export default regionService;
