import { PaginationData } from "types";
import request from './requesters/client.request';
import { Address } from "models";
const addressService = {
    getAddresses: (params: any) =>
        request.get<PaginationData<Address>>('/addresses', { params }),
    findAddress: (addressId: number, params?: any) =>
        request.get(`/addresses/${addressId}`, { params }),
    findAddressesByParams: (params: any) =>
        request.get(`/addresses`, { params }),
    createAddress: (data: any) => request.post<Address>('/addresses', data),
}

export default addressService;