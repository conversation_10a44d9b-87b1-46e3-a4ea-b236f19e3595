import envService from './env.service';
import accountService from './account.service';
import productService from './product.service';
import optionService from './option.service';
import referenceTypeService from './reference_type.service';
import priceFamilyService from './priceFamily.service';
import priceService from './price.service';
import priceTypeService from './priceType.service';
import priceOptionService from './priceOption.service';
import priceSubOptionService from './priceSubOption.service';
import regionService from './region.service';
import urbanCenterService from './urban_center.service';
import regionProductTypeService from './region_product_type.service';
import catalogPriceService from './catalog_price.service';
import serviceProviderService from './service_provider.service';
import productCatalogPriceService from './product_catalog_price.service';
import productCatalogOptionPriceService from './product_catalog_option_price.service';
import serviceTypeService from './service_types.service';
import contactService from './contact.service';
import contactFunctionService from './contact_function.service';
import wasteCenterService from './waste_center.service';
import serviceProviderPriceService from './service_provider_price.service';
import serviceProviderPriceLineService from './service_provider_price_line.service';
import serviceProviderPriceLineZoneService from './service_provider_price_line_zone.service';
import serviceProviderPriceLinePriceOptionService from './service_provider_price_lline_price_option.service';
import serviceProviderPriceLineProductService from './service_provider_price_line_product.service';
import productTypeRegulService from './product_type_regul.service';
import yearService from './year.service';
import catalogPriceUrbanCenterService from './catalog_price_urban_center.service';
import catalogPriceLineZoneService from './catalog_price_line_zone.service';
import catalogPriceLineSubOptionZoneService from './catalog_price_line_sub_option_zone.service';
import catalogPriceLineService from './catalog_price_line.service';
import catalogPriceLineProductService from './catalog_price_line_product.service';
import saleService from './sale.service';
import clientContactService from './client_contact.service';
import clientContactPersonService from './client_contact_person.service';
import documentService from './document_documents.service';
import contactAddressesService from './client_contact_addresses.service';
import addressService from './client_address.service';
import pappersService from './pappers.service';
import { permissionService } from './permission.service';
import catalogPriceLineZoneCheckedService from './catalog_price_line_zone_checked.service';
import wasteCenterTypeService from './waste-center-type.service';
import zohoService from './zoho.service';
import boService from './bo.service';
import providerInvoiceService from './provider_invoice.service';

export {
  envService,
  accountService,
  productService,
  optionService,
  regionService,
  referenceTypeService,
  priceFamilyService,
  priceService,
  priceTypeService,
  serviceTypeService,
  serviceProviderService,
  priceOptionService,
  priceSubOptionService,
  urbanCenterService,
  regionProductTypeService,
  catalogPriceService,
  productCatalogPriceService,
  productCatalogOptionPriceService,
  contactService,
  contactFunctionService,
  wasteCenterService,
  serviceProviderPriceService,
  serviceProviderPriceLineService,
  serviceProviderPriceLineZoneService,
  serviceProviderPriceLinePriceOptionService,
  serviceProviderPriceLineProductService,
  productTypeRegulService,
  yearService,
  catalogPriceLineService,
  catalogPriceLineSubOptionZoneService,
  catalogPriceLineZoneService,
  catalogPriceUrbanCenterService,
  catalogPriceLineProductService,
  saleService,
  clientContactService,
  clientContactPersonService,
  documentService,
  contactAddressesService,
  addressService,
  pappersService,
  permissionService,
  catalogPriceLineZoneCheckedService,
  wasteCenterTypeService,
  zohoService,
  boService,
  providerInvoiceService,
};
