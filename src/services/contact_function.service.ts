import { ContactFunction } from '../models';
import request from './requesters/service_provider.request';

const contactFunctionService = {
  getContactFunctions: () =>
    request.get<ContactFunction[]>('/contact-functions'),
  findContactFunction: (contactFunctionId: number) =>
    request.get(`/contact-functions/${contactFunctionId}`),
  createContactFunction: (data: any) =>
    request.post<ContactFunction>('/contact-functions', data),
  updateContactFunction: (
    contactFunctionId: number,
    data: any
  ) =>
    request.put<ContactFunction>(
      `/contact-functions/${contactFunctionId}`,
      data
    ),
  deleteContactFunction: (contactFunctionId: number) =>
    request.delete(`/contact-functions/${contactFunctionId}`),
};

export default contactFunctionService;
