import { ServiceType, WasteType } from '../models';
import request from './requesters/service_provider.request';

const serviceTypeService = {
  getServiceTypes: () => request.get<ServiceType[]>('/service-types'),
  findServiceType: (serviceTypeId: number) =>
    request.get(`/service-types/${serviceTypeId}`),
  createServiceType: (data: any) =>
    request.post<ServiceType>('/service-types', data),
  updateServiceType: (serviceTypeId: number, data: any) =>
    request.put<ServiceType>(`/service-types/${serviceTypeId}`, data),
  deleteServiceType: (serviceTypeId: number) =>
    request.delete(`/service-types/${serviceTypeId}`),
  getWasteTypes: () => request.get<WasteType[]>('/waste-types'),
};

export default serviceTypeService;
