import request from "./requesters/price.request";

const priceSubOptionService = {
  getPriceSubOptions: (priceOptionId: number) =>
    request.get(`/price-options/${priceOptionId}/price-sub-options`),
  findPriceSubOption: (priceOptionId: number, priceSubOptionId: number) =>
    request.get(`/price-options/${priceOptionId}/price-sub-options/${priceSubOptionId}`),
  createPriceSubOption: (priceOptionId: number, data: any) => request.post(`/price-options/${priceOptionId}/price-sub-options`, data),
  updatePriceSubOption: (priceOptionId: number, priceSubOptionId: number, data: any) =>
        request.put(`/price-options/${priceOptionId}/price-sub-options/${priceSubOptionId}`, data),
  deletePriceSubOption: (priceOptionId: number, priceSubOptionId: number) => request.delete(`/price-options/${priceOptionId}/price-sub-options/${priceSubOptionId}`)
};

export default priceSubOptionService;
