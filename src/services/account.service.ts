import requester from './requesters/user.request';
import { AuthenticationResult, QueryParams } from '../types';
import User from 'models/user';

const accountService = {
  signIn: (query: { email: string; password: string }) =>
    requester.post<{ AuthenticationResult: AuthenticationResult }>('/sign-in', query),
  signOut: (data: { accessToken: string | undefined }) => requester.post('/sign-out', data),
  refreshToken: (refreshToken: string | undefined) =>
    requester.post<{ AuthenticationResult: AuthenticationResult }>('/refresh-token', {
      refreshToken,
    }),
  profile: (data: { accessToken: string | undefined }) => requester.post<User>('/user-profile', data),
  forgetPassword: (query: QueryParams) => requester.post('/forget-password', query),
};

export default accountService;
