import { RegionProductType } from '../models';
import request from './requesters/product.request';

const regionProductTypeService = {
  getRegionProductTypes: (query: any) =>
    request.get<RegionProductType[]>('/region-product-types', {
      params: query
    }),
  createRegionProductType: (data: any) =>
    request.post<RegionProductType[]>('/region-product-types', data)
};

export default regionProductTypeService;
