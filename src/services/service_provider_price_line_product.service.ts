import { PaginationData } from "types";
import { ServiceProviderPriceLineProduct } from "../models";
import request from "./requesters/service_provider.request";

const serviceProviderPriceLineProductService = {
  getServiceProviderPriceLineProducts: (
    serviceProviderId: number | string,
    params?: any
  ) =>
    request.get<PaginationData<ServiceProviderPriceLineProduct>>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products`,
      {
        params,
      }
    ),
  findOneServiceProviderPriceLineProduct: (
    serviceProviderId: number,
    serviceProviderPriceLineProductId: number,
    params?: any
  ) =>
    request.get<ServiceProviderPriceLineProduct>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products/${serviceProviderPriceLineProductId}`,
      { params }
    ),
  createServiceProviderPriceLineProduct: (
    serviceProviderId: number,
    data: any
  ) =>
    request.post<ServiceProviderPriceLineProduct>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products`,
      data
    ),
  updateServiceProviderPriceLineProduct: (
    serviceProviderId: number,
    serviceProviderPriceLineProductId: number,
    data: any
  ) =>
    request.put<ServiceProviderPriceLineProduct>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products/${serviceProviderPriceLineProductId}`,
      data
    ),
  multipleCreateOrUpdate: (serviceProviderId: number, data: any) =>
    request.post<ServiceProviderPriceLineProduct[]>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products/multiple-create-or-update`,
      data
    ),
  deactiveServiceProviderPriceLineProduct: (
    serviceProviderId: number | string,
    serviceProviderPriceLineProductId: number | string
  ) =>
    request.delete<ServiceProviderPriceLineProduct>(
      `/service-providers/${serviceProviderId}/service-provider-price-line-products/${serviceProviderPriceLineProductId}`
    ),
};

export default serviceProviderPriceLineProductService;
