/* eslint-disable @typescript-eslint/no-explicit-any */
import { Client<PERSON>ontact, Contact, ContactAddresses } from '../models';
import { PaginationData } from '../types';
import request from './requesters/client.request';

const clientContactService = {
  /* eslint-disable-next-line */
  getContacts: (params: any) => request.get<PaginationData<ClientContact>>('/contacts', { params }),
  /* eslint-disable-next-line */
  findContact: (contactId: string, params?: any) => request.get(`/contacts/${contactId}`, { params }),
  /* eslint-disable-next-line */
  findContactByParams: (params: any) => request.get(`/contacts`, { params }),
  /* eslint-disable-next-line */
  createContact: (data: any) => request.post<ClientContact>('/contacts', data),
  createAccountContact: (data: object) => request.post<ClientContact>('/create-account-contacts', data),
  /* eslint-disable-next-line */
  updateContact: (contactId: number, data: any) => request.put<ClientContact>(`/contacts/${contactId}`, data),
  deleteContact: (contactId: number) => request.delete(`/contacts/${contactId}`),
  createContactAddress: (contactId: string | number | undefined, data: any) =>
    request.post<Contact | ContactAddresses>(`/contacts/${contactId}/create-contact-address`, data),
  /* eslint-disable-next-line */
  updateContactAddressLastUsed: (contactAddressesId: number, data: any) =>
    request.put<ContactAddresses>(`/update-contact-addresses-last-used/${contactAddressesId}`, data),
};

export default clientContactService;
