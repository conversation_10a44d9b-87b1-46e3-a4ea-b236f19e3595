import { Role } from 'models';

export enum Permissions {
  DEVIS = 'DEVIS',
  PRESTATAIRE_BENNEUR = 'PRESTATAIRE_BENNEUR',
  PRESTATAIRE_CAMION = 'PRESTATAIRE_CAMION',
  PRESTATAIRE_PUP = 'PRESTATAIRE_PUP',
  PRODUITS = 'PRODUITS',
  LOGISTIQUE = 'LOGISTIQUE',
  REGIONS = 'REGIONS',
}

export const RolePermissions: { [key: string]: Permissions[] } = {
  ADMIN: Object.values(Permissions),
  SALES: [Permissions.DEVIS],
  LOGISTIC: [
    Permissions.DEVIS,
    Permissions.PRESTATAIRE_BENNEUR,
    Permissions.PRESTATAIRE_CAMION,
    Permissions.PRESTATAIRE_PUP,
  ],
};

class PermissionService {
  hasPermission(userRole: Role | null, permission: Permissions): boolean {
    if (!userRole?.key) return false;
    const allowedPermissions = RolePermissions[userRole.key.toUpperCase()];
    return allowedPermissions?.includes(permission) || false;
  }

  hasAnyPermission(userRole: Role | null, permissions: Permissions[]): boolean {
    return permissions.some((permission) => this.hasPermission(userRole, permission));
  }

  hasAllPermissions(userRole: Role | null, permissions: Permissions[]): boolean {
    return permissions.every((permission) => this.hasPermission(userRole, permission));
  }
}

export const permissionService = new PermissionService();
