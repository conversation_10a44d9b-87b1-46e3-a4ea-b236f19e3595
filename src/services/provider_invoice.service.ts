import { PaginationData, QueryParams, Invoice } from 'types';
import { invoiceList, commandePrestaCreate, commandePrestaEdit } from '../pages/ProviderInvoiceCreate/mockdata';

// Interface for create mode parameters
interface CreateModeParams {
  prestataire?: string;
  [key: string]: string | number | boolean | undefined;
}

// Interface for edit mode parameters
interface EditModeParams {
  prestataire?: string;
  invoiceId?: string | number;
  [key: string]: unknown;
}



const providerInvoiceService = {
  getInvoicesList: async (params?: QueryParams): Promise<PaginationData<Invoice>> => {
    await new Promise(resolve => setTimeout(resolve, 300));

    // TODO: Use params for filtering/pagination in future implementation
    console.log('Fetching invoices with params:', params);

    return {
      rows: invoiceList,
      count: invoiceList.length
    };
  },

  getInvoiceLinesByProviderIdCreate: async (params: CreateModeParams): Promise<PaginationData<unknown>> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const { prestataire } = params;

    console.log(prestataire);
    return {
      rows: commandePrestaCreate,
      count: commandePrestaCreate.length
    };
  },

  getInvoiceDetailsByProviderIdEdit: async (params: EditModeParams): Promise<PaginationData<unknown>> => {
    await new Promise(resolve => setTimeout(resolve, 500));

    const { prestataire, invoiceId } = params;

    console.log(`🔄 Service: EDIT - Fetching invoice for ${prestataire} (ID: ${invoiceId})`);

    return {
      rows: commandePrestaEdit,
      count: commandePrestaEdit.length
    };
  },

};

export default providerInvoiceService;
