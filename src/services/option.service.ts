import { QueryParams } from 'types';
import { OptionType, SubOption } from '../models';
import request from './requesters/product.request';

const optionService = {
  getOptions: (query: QueryParams) =>
    request.get('/options', {
      params: query,
    }),
  getAllOptions: (productTypeId: number, query: QueryParams) =>
    request.get(`/product-types/${productTypeId}/options`, {
      params: query,
    }),
  createOption: (productTypeId: number, data: object) => request.post(`/product-types/${productTypeId}/options`, data),
  updateOption: (productTypeId: number, optionId: number, data: object) =>
    request.put(`/product-types/${productTypeId}/options/${optionId}`, data),
  deleteOption: (productTypeId: number, optionId: number) =>
    request.delete(`/product-types/${productTypeId}/options/${optionId}`),
  getSubOptions: (productTypeId: number, optionId: number, query: QueryParams) =>
    request.get(`/product-types/${productTypeId}/options/${optionId}/sub-options`, {
      params: query,
    }),
  createSubOption: (productTypeId: number, optionId: number, data: SubOption) =>
    request.post(`/product-types/${productTypeId}/options/${optionId}/sub-options`, data),
  updateSubOption: (productTypeId: number, optionId: number, data: SubOption) =>
    request.put(`/product-types/${productTypeId}/options/${optionId}/sub-options/${data.id}`, data),
  deleteSubOption: (productTypeId: number, optionId: number, id: number) =>
    request.delete(`/product-types/${productTypeId}/options/${optionId}/sub-options/${id}`),
  getOptionTypes: () => request.get<OptionType[]>('/option-types'),
  addSubOptionReferenceType: (productTypeId: number, optionId: number, subOptionId: number, data: object) =>
    request.post(
      `/product-types/${productTypeId}/options/${optionId}/sub-options/${subOptionId}/sub-option-reference-types`,
      data,
    ),
  deleteSubOptionReferenceType: (
    productTypeId: number,
    optionId: number,
    subOptionId: number,
    referenceTypeId: number,
  ) =>
    request.delete(
      `/product-types/${productTypeId}/options/${optionId}/sub-options/${subOptionId}/sub-option-reference-types/${referenceTypeId}`,
    ),
};

export default optionService;
