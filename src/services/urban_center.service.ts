import { UrbanCenterQueryParams } from 'hooks';
import { UrbanCenter } from '../models';
import { PaginationData, QueryParams, UrbanCenterWithZoneResponse } from '../types';
import request from './requesters/region.request';

const urbanCenterService = {
  getUrbanCenters: (regionId?: number, params?: UrbanCenterQueryParams) =>
    request.get<UrbanCenter[]>(regionId ? `/regions/${regionId}/urban-centers` : '/urban-centers', { params }),
  findUrbanCenter: (regionId: number, urbanCenterId: number) =>
    request.get<UrbanCenter>(`/regions/${regionId}/urban-centers/${urbanCenterId}`),
  createUrbanCenter: (regionId: number, data: UrbanCenter) =>
    request.post<UrbanCenter>(`/regions/${regionId}/urban-centers`, data),
  // createUrbanCenterSF: (data: any) =>
  //   sfRegionRequest.post(`/urban-centers`, data),
  createUrbanCenterAttachServiceProvider: (data: UrbanCenter) => request.post(`/urban-centers`, data),
  createUrbanCenterWithoutRegion: (data: object) => request.post(`/urban-centers`, data),
  updateUrbanCenter: (regionId: number, urbanCenterId: number, data: UrbanCenter) =>
    request.put(`/regions/${regionId}/urban-centers/${urbanCenterId}`, data),
  deactivateUrbanCenter: (regionId: number, urbanCenterId: number) =>
    request.delete(`/regions/${regionId}/urban-centers/${urbanCenterId}`),
  getZonesByChantierAddress: (latitude: string, longitude: string) =>
    request.get<UrbanCenterWithZoneResponse>(`/urban-centers-zone`, {
      params: { latitude, longitude },
    }),
  getProvidersZonesByChantierAddress: (
    latitude: string,
    longitude: string,
    isCatalog: boolean = false,
    excludeServiceProviderId?: string,
  ) =>
    request.get<
      {
        id: number;
        zoneId: number;
        latitude: string;
        longitude: string;
        name: string;
        formattedAddress: string;
        zoneName: string;
        maxDistance: number;
        minDistance: number;
        platformName?: string;
        serviceProviderId?: number;
      }[]
    >(`/urban-centers-zone-search-price`, {
      params: { latitude, longitude, isCatalog, excludeServiceProviderId },
    }),
  getAllProviders: (params: QueryParams) => request.get<PaginationData<UrbanCenter>>('/urban-centers-zone', { params }),
};

export default urbanCenterService;
