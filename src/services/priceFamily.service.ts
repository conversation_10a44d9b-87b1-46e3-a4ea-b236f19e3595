import { PriceFamily } from 'models';
import request from './requesters/price.request';

const priceFamilyService = {
  getAllPriceFamilies: (params?: any) =>
    request.get<PriceFamily[]>('/price-families', { params }),
  getPriceFamilies: (
    // productTypeId: number,
    params?: any
  ) =>
    request.get<PriceFamily[]>(
      `/price-families`,
      { params }
    ),
  findPriceFamily: (
    productTypeId: number,
    priceFamilyId: number
  ) =>
    request.get(
      `/product-types/${productTypeId}/price-families/${priceFamilyId}`
    ),
  createPriceFamily: (productTypeId: number, data: any) =>
    request.post(`/product-types/${productTypeId}/price-families`, data),
  updatePriceFamily: (
    productTypeId: number,
    priceFamilyId: number,
    data: any
  ) =>
    request.put(
      `/product-types/${productTypeId}/price-families/${priceFamilyId}`,
      data
    ),
  deactivatePriceFamily: (
    productTypeId: number,
    priceFamilyId: number
  ) =>
    request.delete(
      `/product-types/${productTypeId}/price-families/${priceFamilyId}`
    ),
};

export default priceFamilyService;
