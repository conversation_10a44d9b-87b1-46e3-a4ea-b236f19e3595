import { PaginationData } from 'types';
import { ProductTypeRegul } from '../models';
import request from './requesters/product.request';

const productTypeRegulService = {
    getProductTypeRegul: (params: any) => request.get<PaginationData<ProductTypeRegul>>(`/product-type-reguls`, { params }),
    getOneProductTypeRegul: (id: number) => request.get<ProductTypeRegul>(`/product-type-reguls/${id}`),
    createProductTypeRegul: (input: any) => request.post('/product-type-reguls', input),
    updateProductTypeRegul: (id: number, input: any) => request.put(`/product-type-reguls/${id}`, input),
    deleteProductTypeRegul: (id: number) => request.delete(`/product-type-reguls/${id}`)
};

export default productTypeRegulService;