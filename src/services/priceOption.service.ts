import { QueryParams } from 'types';
import request from './requesters/price.request';
import { PriceOption } from 'models';

const priceOptionService = {
  getPriceOptions: (priceId: number, params?: QueryParams) =>
    request.get<PriceOption[]>(`/prices/${priceId}/price-options`, { params }),
  findPriceOption: (priceId: number, priceOptionId: number) =>
    request.get(`/prices/${priceId}/price-options/${priceOptionId}`),
  createPriceOption: (priceId: number, data: PriceOption) => request.post(`/prices/${priceId}/price-options`, data),
  updatePriceOption: (priceId: number, priceOptionId: number, data: PriceOption) =>
    request.put(`/prices/${priceId}/price-options/${priceOptionId}`, data),
  deletePriceOption: (priceId: number, priceOptionId: number) =>
    request.delete(`/prices/${priceId}/price-options/${priceOptionId}`),
};

export default priceOptionService;
