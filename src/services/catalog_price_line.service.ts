import request from './requesters/price.request';
import { PaginationData } from 'types';
import { CatalogPriceLine } from '../models/';
const catalogPriceLineService = {
  getCatalogPriceLines: (query: object) =>
    request.get<PaginationData<CatalogPriceLine>>('/catalog-price-lines', {
      params: query,
    }),
  countCatalogPriceLines: (query: object) =>
    request.get<number>('/catalog-price-lines/count', {
      params: query,
    }),
  findCatalogPriceLine: (catalogPriceLineId: number) =>
    request.get<CatalogPriceLine>(`/catalog-price-lines/${catalogPriceLineId}`),
  createCatalogPriceLine: (data: object) => request.post(`/catalog-price-lines`, data),
  updateCatalogPriceLine: (catalogPriceLineId: number, data: object) =>
    request.put(`/catalog-price-lines/${catalogPriceLineId}`, data),
  deactivateCatalogPriceLine: (catalogPriceLineId: number) =>
    request.delete(`/catalog-price-lines/${catalogPriceLineId}`),
  duplicateCatalogPriceLine: (catalogPriceLineId: number) =>
    request.post<CatalogPriceLine>(`/catalog-price-lines/${catalogPriceLineId}/duplicate`),
  multipleCheckCatalogPriceLines: (data: { catalogPriceLineIds: number[]; isChecked: boolean }) =>
    request.put<CatalogPriceLine>(`/catalog-price-lines/multiple-check`, data),
};
export default catalogPriceLineService;
