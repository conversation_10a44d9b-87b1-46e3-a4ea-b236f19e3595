import request from './requesters/service_provider.request';

const serviceProviderPriceService = {
  getServiceProviderPrices: (params?: any) =>
    request.get('/service-provider-prices', { params }),
  findOneServiceProviderPrice: (params?: any) =>
    request.get('/service-provider-prices/find-one', { params }),
  createServiceProviderPrice: (data: any) =>
    request.post('/service-provider-prices', data),
  deactivateServiceProviderPrice: (data: { priceFamilyId: number | string }) =>
    request.put('/service-provider-prices/deactivate', data),
};

export default serviceProviderPriceService;
