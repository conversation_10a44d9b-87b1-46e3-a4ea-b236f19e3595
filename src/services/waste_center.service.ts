import { WasteCenter } from '../models';
import { PaginationData } from '../types';
import request from './requesters/service_provider.request';

const wasteCenterService = {
  getWasteCenters: (params: any) =>
    request.get<PaginationData<WasteCenter>>('/waste-centers', { params }),
  findWasteCenter: (wasteCenterId: number) =>
    request.get(`/waste-centers/${wasteCenterId}`),
  createWasteCenter: (data: any) =>
    request.post<WasteCenter>('/waste-centers', data),
  updateWasteCenter: (wasteCenterId: number, data: any) =>
    request.put<WasteCenter>(`/waste-centers/${wasteCenterId}`, data),
  deactivateWasteCenter: (wasteCenterId: number, params: any) =>
    request.delete(`/waste-centers/${wasteCenterId}`, {params}),
};

export default wasteCenterService;
