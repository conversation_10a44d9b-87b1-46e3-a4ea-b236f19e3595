import request from './requesters/price.request';
import { PaginationData } from "types";
import { CatalogPriceUrbanCenter } from "../models/";
const catalogPriceUrbanCenterService = {
    getCatalogPriceUrbanCenters: (query: any) =>
        request.get<PaginationData<CatalogPriceUrbanCenter>>(
          "/catalog-price-urban-centers",
          {
            params: query,
          }
        ),
  // findCatalogPriceUrbanCenter: (catalogPriceUrbanCenterId: number) =>
  //   request.get<CatalogPriceUrbanCenter>(`/catalog-price-urban-centers/${catalogPriceUrbanCenterId}`),
  createCatalogPriceUrbanCenter: (data: any) => request.post<CatalogPriceUrbanCenter>(`/catalog-price-urban-centers`, data),
  // updateCatalogPriceUrbanCenter: (catalogPriceUrbanCenterId: number, data: any) =>
  //   request.put(`/catalog-price-urban-centers/${catalogPriceUrbanCenterId}`, data),
  // deactivateCatalogPriceUrbanCenter: (catalogPriceUrbanCenterId: number) =>
  //   request.delete(`/catalog-price-urban-centers/${catalogPriceUrbanCenterId}`),
};
export default catalogPriceUrbanCenterService;
