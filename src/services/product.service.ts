/* eslint-disable @typescript-eslint/no-explicit-any */
import { FacturationType, Product, ProductType, ProductTypeUnit } from '../models';
import { PaginationData, QueryParams } from 'types';
import request from './requesters/product.request';
import ProductTypeIntervention from 'models/product-type-interventions';

const productService = {
  getProductTypes: (params: QueryParams) => request.get<PaginationData<ProductType>>(`/product-types`, { params }),
  findProductType: (productTypeId: number, params?: QueryParams) =>
    request.get<ProductType>(`/product-types/${productTypeId}`, { params }),
  createProductType: (data: object) => request.post('/product-types', data),
  updateProductType: (productTypeId: number, data: object) => request.put(`/product-types/${productTypeId}`, data),
  getProducts: (params: QueryParams) =>
    request.get<PaginationData<Product>>(`/products`, {
      params,
    }),
  getProductsByIds: (params: QueryParams) =>
    request.get<PaginationData<Product>>(`/products-by-ids`, {
      params,
    }),
  exportProducts: (productTypeId: string | number) => request.get(`/exports/products/${productTypeId}/export-to-excel`),
  excludeProducts: (productTypeId: number, data: { exclusion: boolean; ids: string[] }) =>
    request.put(`/products/exclude-products/${productTypeId}`, data),
  generateProduct: (productTypeId: string | number) => request.post(`/products/generate-products/${productTypeId}`),
  getProductTypesByRegionId: (regionId: number) => request.get<ProductType[]>(`/product-types-by-region/${regionId}`),
  getProductsByZoneIdForProductCatalogPrices: (zoneId: number, query: QueryParams) =>
    request.get<PaginationData<Product>>(`/products/products-by-zone-id/${zoneId}/product-catalog-prices`, {
      params: query,
    }),
  getProductsByZoneIdForProductCatalogOptionPrices: (zoneId: number, query: QueryParams) =>
    request.get<PaginationData<Product>>(`/products/products-by-zone-id/${zoneId}/product-catalog-option-prices`, {
      params: query,
    }),
  unVisibleProductType: (productTypeId: number) =>
    request.put<ProductType>(`/product-types-unvisible/${productTypeId}`),
  getProductTypeUnit: (params: QueryParams) =>
    request.get<PaginationData<ProductTypeUnit>>(`/product-type-units`, { params }),
  getProductTypeInterventions: (params: QueryParams) =>
    request.get<PaginationData<ProductTypeIntervention>>(`/product-type-interventions`, { params }),
  getInactiveProduct: () => request.get(`/products/inactive`),
  getFacturationType: (params: QueryParams) =>
    request.get<PaginationData<FacturationType>>(`/facturation-types`, { params }),
  getOneMultiProduct: (productId: number, params: QueryParams) =>
    request.get(`/products/multi-product/${productId}`, { params }),
  getMultiProduct: (params: QueryParams) => request.get(`/products-and-main-products`, { params }),
};

export default productService;
