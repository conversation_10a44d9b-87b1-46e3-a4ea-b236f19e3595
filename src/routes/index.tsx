import { lazy, Suspense } from 'react';
import { Routes, Route } from 'react-router-dom';

import { AppRouteType } from '../types';
import { Spinner } from '../components/Common';
import BasePage from '../components/Layout/BasePage/BasePage';
import useAuthContext from '../store/auth-context';
import { envService } from 'services';
import { HiddenFeatures } from 'services/env.service';
import { Permissions, permissionService } from 'services/permission.service';
import { usePermission } from 'hooks/usePermission';
import Role from 'models/role';

const Login = lazy(() => import('../pages/Login'));
const Product = lazy(() => import('../pages/Product'));
const Region = lazy(() => import('../pages/Region'));
const DocumentCreate = lazy(() => import('../pages/DocumentCreate'));
const DocumentDetail = lazy(() => import('../pages/DocumentDetail'));
const ProductDetail = lazy(() => import('../pages/ProductDetail'));
const RegionDetail = lazy(() => import('../pages/RegionDetail'));
const RegionProduct = lazy(() => import('../pages/RegionProduct'));
const ServiceProvider = lazy(() => import('../pages/ServiceProvider'));
const Logistique = lazy(() => import('../pages/Logistique'));
const ServiceProviderDetail = lazy(() => import('../pages/ServiceProviderDetail'));
const SearchPriceLogistique = lazy(() => import('../pages/SearchPriceLogistique'));
const SearchPriceCommercial = lazy(() => import('../pages/SearchPriceCommercial'));
const ProviderInvoice = lazy(() => import('../pages/ProviderInvoice'));
const ProviderInvoiceCreateRefactored = lazy(() => import('pages/ProviderInvoiceCreate/ProviderInvoiceCreateRefactored'));

const waitFor = (Tag: React.LazyExoticComponent<() => JSX.Element | null>) => (
  <Suspense fallback={<Spinner />}>
    <Tag />
  </Suspense>
);

export const routes: AppRouteType[] = [
  {
    name: 'Login',
    auth: false,
    path: '/login',
    component: Login,
  },
  {
    name: 'Product',
    auth: true,
    path: '/products',
    component: Product,
    permissions: [Permissions.PRODUITS],
  },
  {
    name: 'ProductDetail',
    auth: true,
    path: '/products/:productTypeId',
    component: ProductDetail,
    permissions: [Permissions.PRODUITS],
  },
  {
    name: 'Region',
    auth: true,
    path: '/regions',
    component: Region,
    permissions: [Permissions.REGIONS],
  },
  {
    name: 'RegionDetail',
    auth: true,
    path: '/regions/:regionId',
    component: RegionDetail,
    permissions: [Permissions.REGIONS],
  },
  {
    name: 'RegionProduct',
    auth: true,
    path: '/regions/:regionId/products',
    component: RegionProduct,
    permissions: [Permissions.REGIONS],
  },
  {
    name: 'Nouveslles Commandes',
    auth: true,
    path: '/logistique/nouvelles-commandes',
    component: Logistique,
    permissions: [Permissions.LOGISTIQUE],
  },
  {
    name: 'Benneur',
    auth: true,
    path: '/prestataires/benneur',
    component: ServiceProvider,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  {
    name: 'Benneur Detail',
    auth: true,
    path: '/prestataires/benneur/:serviceProviderId',
    component: ServiceProviderDetail,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  {
    name: 'Camionneur',
    auth: true,
    path: '/prestataires/camionneur',
    component: ServiceProvider,
    permissions: [Permissions.PRESTATAIRE_CAMION],
  },
  {
    name: 'Camionneur Detail',
    auth: true,
    path: '/prestataires/camionneur/:serviceProviderId',
    component: ServiceProviderDetail,
    permissions: [Permissions.PRESTATAIRE_CAMION],
  },
  {
    name: 'Pup',
    auth: true,
    path: '/prestataires/pup',
    component: ServiceProvider,
    permissions: [Permissions.PRESTATAIRE_PUP],
  },
  {
    name: 'Pup Detail',
    auth: true,
    path: '/prestataires/pup/:serviceProviderId',
    component: ServiceProviderDetail,
    permissions: [Permissions.PRESTATAIRE_PUP],
  },
  {
    name: 'Search',
    auth: true,
    path: '/search/logistique',
    component: SearchPriceLogistique,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  {
    name: 'Search',
    auth: true,
    path: '/search/commercial',
    component: SearchPriceCommercial,
    permissions: [Permissions.DEVIS],
  },
  {
    name: 'SupplierInvoice',
    auth: true,
    path: '/logistique/factures-fournisseurs',
    component: ProviderInvoice,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  {
    name: 'SupplierInvoice',
    auth: true,
    path: '/logistique/factures-fournisseurs/:invoiceNumber',
    component: ProviderInvoiceCreateRefactored,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  {
    name: 'SupplierInvoice',
    auth: true,
    path: '/logistique/factures-fournisseurs/enregistrer-facture',
    component: ProviderInvoiceCreateRefactored,
    permissions: [Permissions.PRESTATAIRE_BENNEUR],
  },
  ...(!envService.isFeatureHidden(HiddenFeatures.DEVIS)
    ? [
        {
          name: 'Quotation',
          auth: true,
          path: '/quotation',
          component: DocumentCreate,
          permissions: [Permissions.DEVIS],
        },
        {
          name: 'Order',
          auth: true,
          path: '/order',
          component: DocumentCreate,
          permissions: [Permissions.DEVIS],
        },
        {
          name: 'QuotationDetail',
          auth: true,
          path: '/quotation/:documentId',
          component: DocumentDetail,
          permissions: [Permissions.DEVIS],
        },
        {
          name: 'OrderDetail',
          auth: true,
          path: '/order/:documentId',
          component: DocumentDetail,
          permissions: [Permissions.DEVIS],
        },
      ]
    : []),
];

// const landingRoute = routes.find((route) => route.auth) as AppRouteType;
const findFirstPermittedRoute = (
  routes: AppRouteType[],
  hasAnyPermission: (permissions: Permissions[]) => boolean,
): AppRouteType | undefined => {
  return routes.find((route) => route.auth && route.permissions && hasAnyPermission(route.permissions));
};

export const getLandingPage = (role?: Role) => {
  console.log('role', role);
  // Memoize the landing page to avoid recalculation
  const landingPage = findFirstPermittedRoute(routes, (permissions) =>
    permissionService.hasAnyPermission(role || null, permissions),
  );

  return landingPage ?? null;
};

export default function RoutesAppRoutes() {
  const { idToken } = useAuthContext();
  const { hasAnyPermission } = usePermission();

  const publicRoutes = routes
    .filter((route) => !route.auth || route.isPublic)
    .map((route) => <Route key={route.path} path={route.path} element={waitFor(route.component)} />);

  // public routes
  if (!idToken) return <Routes>{publicRoutes}</Routes>;

  // authenticated routes
  const authenticatedRoutes = routes
    .filter((route) => (route.auth && (!route.permissions || hasAnyPermission(route.permissions))) || route.isPublic)
    .map((route) => <Route key={route.path} path={route.path} element={waitFor(route.component)} />);

  return (
    <BasePage>
      <Routes>{authenticatedRoutes}</Routes>
    </BasePage>
  );
}
