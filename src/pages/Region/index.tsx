import { PageTitle } from 'components/Common';
import { useEffect, useState } from 'react';
import { MainTitle, Spinner } from 'components/Common';
import ButtonAdd from 'components/Common/ButtonAdd';
import { useQueryParams } from 'hooks';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { Region as RegionModel } from 'models';
import regionService from 'services/region.service';
import { Space, Button, Input, Table, Modal, Typography, Divider, Spin } from 'antd';
import { BlockOutlined, DoubleRightOutlined, EditOutlined, DeleteOutlined, LoadingOutlined } from '@ant-design/icons';
import { ScaleLoader } from 'react-spinners';
import InfiniteScroll from 'react-infinite-scroll-component';
import { Loading, QueryParams } from '../../types';

const { Search } = Input;
const { Text } = Typography;

const Region = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [titleOfRegion, setTitleOfRegion] = useState<string | undefined>('');
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [loading, setLoading] = useState<boolean>(false);
  const [query, onParamChange] = useQueryParams();
  const [deletingIds, setDeletingIds] = useState<number[]>([]);
  const [regions, setRegions] = useState<RegionModel[]>([]);
  const [regionLoading, setRegionLoading] = useState<Loading>('idle');
  const [regionTotal, setRegionTotal] = useState(0);
  const [pageLoad, setPageLoad] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const NUMBER_ITEM_LOAD = 50;

  const loadMoreData = () => {
    if (regions.length < regionTotal) {
      setPageLoad(pageLoad + 1);
    }
  };

  useEffect(() => {
    if (pageLoad > 1) {
      getRegions(true, { ...query });
    }
  }, [pageLoad]);

  useEffect(() => {
    if (regions.length < regionTotal) {
      setHasMore(true);
    } else {
      setHasMore(false);
    }
  }, [regions, regionTotal]);

  const getRegions = async (isLoadMore: boolean, query: QueryParams) => {
    try {
      setRegionLoading('pending');
      const response = await regionService.getRegions({
        name: query.keyword ? `*${query.keyword}*` : undefined,
        page: isLoadMore ? pageLoad : 1,
        limit: NUMBER_ITEM_LOAD,
        orderBy: 'createdAt,desc',
        include: 'Platform',
        isVisible: 1,
      });

      setRegionLoading('succeeded');
      if (response.rows && response.rows.length === 0) {
        setHasMore(false);
      }

      if (isLoadMore) {
        setRegions((prev) => [...prev, ...response.rows]);
      } else {
        setRegions(response.rows);
      }
      setRegionTotal(response.count);
    } catch (error) {
      setRegionLoading('failed');
      console.error(error);
    }
  };

  useEffect(() => {
    setPageLoad(1);
    setRegions([]);
    setRegionTotal(0);
    setHasMore(true);
    getRegions(false, query);

    // Reset scroll position
    window.scrollTo(0, 0);
  }, [query]);

  const handleGotoDetail = (item: RegionModel, isGotoProduct?: boolean) => {
    navigate(isGotoProduct === true ? `/regions/${item.id}/products` : `/regions/${item.id}`, { state: location });
  };

  const handleOk = async () => {
    if (!titleOfRegion) return;
    setLoading(true);
    try {
      const result: RegionModel = await regionService.createRegion({
        name: titleOfRegion,
      });
      handleGotoDetail(result);
      setLoading(false);
      toast.success('Succès');
    } catch (error) {
      console.error(error);
      setLoading(false);
      toast.error('Erreur');
    }
  };

  const handleCancel = () => {
    setTitleOfRegion('');
    setIsModalOpen(false);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  const handleUnVisible = async (regionId: number) => {
    setDeletingIds((prevState) => [...prevState, regionId]);
    try {
      await regionService.unVisibleRegion(regionId).then((response) => {
        if (response.data) {
          toast.success('Succès');
          getRegions(false, query);
        }
      });
      setDeletingIds(deletingIds.filter((id) => id !== regionId));
    } catch (error) {
      console.error(error);
      setDeletingIds(deletingIds.filter((id) => id !== regionId));
      toast.error('Erreur');
    }
  };

  console.log('dataSource.length total', regions, 'total: ', regionTotal);
  const LoaderRow = () => (
    <div className='datatable-loader'>
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  );

  return (
    <>
      <PageTitle>CATALOGUE RÉGIONS</PageTitle>
      <Spinner loading={loading || regionLoading === 'pending'}>
        <>
          <MainTitle parent='CATALOGUE RÉGIONS' child='LISTE' />
          <div className='region-list__creation-and-searching'>
            <ButtonAdd handleClick={handleOpenModal} otherStyles={{ height: '42.1px' }}>
              Ajouter une Région
            </ButtonAdd>
            <div className='region-list__searching'>
              <Space direction='vertical'>
                <Search
                  placeholder='Rechercher'
                  allowClear={true}
                  defaultValue={query.keyword}
                  size='large'
                  onSearch={(value) => onParamChange({ keyword: value })}
                  style={{
                    width: 304,
                  }}
                  className='region-list__search-bar'
                />
              </Space>
            </div>
          </div>
          <div>
            <InfiniteScroll
              dataLength={regions.length}
              next={loadMoreData}
              hasMore={hasMore}
              loader={<LoaderRow />}
              endMessage={<Divider plain>Vous avez atteint la fin du flux</Divider>}
            >
              <Table
                columns={[
                  {
                    title: 'Type de Région',
                    dataIndex: 'Platform',
                    key: 'platform',
                    width: 400,
                    render: (platform) => <p className='datatable__item'>{platform?.name ?? ''}</p>,
                  },
                  {
                    title: 'Titre de la Région',
                    dataIndex: 'name',
                    key: 'name',
                    width: 400,
                    render: (text) => <p className='datatable__item'>{text}</p>,
                  },
                  {
                    title: 'Action',
                    key: 'action',
                    width: 150,
                    render: (_, record) => (
                      <Space size='middle'>
                        <Button
                          type='link'
                          icon={<EditOutlined />}
                          className='datatable__action-edit-button'
                          onClick={() => handleGotoDetail(record)}
                        />
                        <Button type='link' icon={<BlockOutlined />} className='datatable__action-copy-button' />
                        <Button
                          type='link'
                          icon={<DeleteOutlined />}
                          className='datatable__action-destroy-button'
                          loading={deletingIds.includes(record.id)}
                          onClick={() => handleUnVisible(record.id)}
                        />
                        <Button
                          type='link'
                          icon={<DoubleRightOutlined />}
                          className='datatable__action-next-button'
                          onClick={() => handleGotoDetail(record, true)}
                        />
                      </Space>
                    ),
                  },
                ]}
                dataSource={regions}
                pagination={false}
                className='region-list__datatable'
                loading={{
                  indicator: (
                    <ScaleLoader
                      color='#A6C84D'
                      cssOverride={{
                        display: 'inline-block !important',
                        margin: '0 auto',
                        left: 0,
                        height: '100%',
                        width: '100%',
                      }}
                      aria-label='Loading Spinner'
                      data-testid='loader'
                    />
                  ),
                  spinning: regionLoading === 'pending' && !hasMore,
                }}
              />
            </InfiniteScroll>
          </div>
          <Modal
            title='AJOUTER UNE REGION'
            open={isModalOpen}
            onOk={handleOk}
            onCancel={handleCancel}
            centered
            footer={[
              <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
                Annuler
              </Button>,
              <Button
                key='ajouter'
                type='primary'
                onClick={handleOk}
                loading={loading}
                className='ant-modal-content__add-btn'
              >
                Ajouter
              </Button>,
            ]}
          >
            <Space direction='vertical'>
              <Text className='ant-modal-content__title-of-product-label'>Titre de la Region</Text>
              <Input
                placeholder='Input'
                value={titleOfRegion}
                className='ant-modal-content__title-of-product-input'
                onChange={(e) => setTitleOfRegion(e.target.value)}
              />
            </Space>
          </Modal>
        </>
      </Spinner>
    </>
  );
};

export default Region;
