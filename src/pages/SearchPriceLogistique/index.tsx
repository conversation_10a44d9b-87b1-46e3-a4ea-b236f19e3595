import { useEffect, useState } from 'react';
import { Form, Select, Divider, Row, Col, Table, Pagination } from 'antd';
import { PageTitle, MainTitle, Spinner } from 'components/Common';
import SelectWithGoogleSuggestion from 'components/Common/SelectWithGoogleSuggestion';
import {
  RawDataLog,
  TableRow,
  Product,
  ServiceProvider,
  FinalData,
  Address
} from 'types';
import type { ColumnsType } from 'antd/es/table';
import { ScaleLoader } from 'react-spinners';
import { urbanCenterService, regionService, productService, serviceProviderService } from 'services';
import {
  useProductsQuery,
  useProducts,
  useQueryParams,
} from 'hooks';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchAllServiceProviders } from 'store/slices/service_provider.slices';
import { toast } from 'react-toastify';
import { 
  fetchProductWithZonesAndPrices,
  selectSearchProducts,
  selectSearchProductsTotal,
  selectSearchProductsLoading,
  resetState,
  triggerLoading,
} from 'store/slices/search_price.slices';

const defaultCenter  = {
  lat: 48.8566,
  lng: 2.3522,
};

const SearchPriceLogistique = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(true);
  const [tableIsLoading, setTableIsLoading] = useState<boolean>(true);
  const [query, onParamChange] = useQueryParams();
  const totalItems = useAppSelector(selectSearchProductsTotal);
  const [serviceProviders, setServiceProviders] = useState<any>([]);
  const [addressChantier, setAddressChantier] = useState<Address[]>([]);
  const [selectAddressChantier, setSelectAddressChantier] = useState<Address>({});
  const [mapCenter, setMapCenter] = useState(defaultCenter);
  const [selectedPrestataire, setSelectedPrestataire] = useState<string | undefined>(undefined);
  const [selectedProduct, setSelectedProduct] = useState<string | undefined>(undefined);
  const [selectedZoneIds, setSelectedZoneIds] = useState<any>([]);
  const [listPrestataire, setListPrestataire] = useState<any>([]);
  const productsPrice: RawDataLog[]  = useAppSelector(selectSearchProducts);
  const [productsQuery] = useProductsQuery({
    orderBy: 'createdAt,desc|name',
    isVisible: 1,
  });
  const [getAllProducts] = useProducts(productsQuery);
  const porvidersWithProductsLoading = useAppSelector(selectSearchProductsLoading);
  const [zonesNames, setZonesNames] = useState<any>([]);
  const [inactiveProductList, setInactiveProductList] = useState<any>([]);

  const processData = (
    rawData: RawDataLog[],
    products: Product[],
    serviceProviders: ServiceProvider[],
    zonesNames: { zoneId: number; zoneName: string }[]
  ): FinalData[] => {
    // Map for fast lookup
    const productMap = new Map(products.map((p) => [p.id, p.name]));
    const serviceProviderMap = new Map(
      serviceProviders.map((sp) => [
        sp.id,
        {
          name: sp.name,
          address: {
            city: sp.city,
            postalcode: sp.postalcode,
            country: sp.country,
            address: sp.address,
            latitude: sp.latitude,
            longitude: sp.longitude,
            formattedAddress: sp.formattedAddress,
            fullAddress: `${sp.formattedAddress}`,
          },
        }
      ])
    );
    const zoneMap = new Map(zonesNames.map((z) => [z.zoneId, z.zoneName]));
  
    const groupedData: Record<number, FinalData> = {};
  
    rawData.forEach((item) => {
      const serviceProvider = serviceProviderMap.get(item.serviceProviderId);
      const productName = productMap.get(item.productId) || item.Produit || 'Unknown Product';
      const spName = serviceProvider?.name || `Provider (${item.serviceProviderId})`;
      const address = serviceProvider?.address || null;
  
      if (!groupedData[item.serviceProviderId]) {
        groupedData[item.serviceProviderId] = {
          name: spName,
          Address: address ? [address] : [],
          offers: [],
        };
      }
  
      const serviceProviderGroup = groupedData[item.serviceProviderId];
  
      // Add each product as a separate entry in offers
      const zoneName = zoneMap.get(item.zoneId) || `Unknown Zone (${item.zoneId})`;
      const offerType =
        (item.Tarif || '') + (item.Tarif1 ? ', ' + item.Tarif1 : '') || ' ';
  
      serviceProviderGroup.offers.push({
        type: offerType,
        zones: [{ zone: zoneName, price: item.priceValue }],
        products: [productName],
        comments: item.comment || null,
      });
    });
  
    return Object.values(groupedData);
  };
  
  const getAllServiceProviders = async () => {
    try {
      const result = await dispatch(fetchAllServiceProviders()).unwrap();
      setServiceProviders(result.rows);
    } catch (error) {
      toast.error('Erreur');
    }
  };

  const getInactiveProductHandler = async () => {
    try {
      const result = await productService?.getInactiveProduct();
      const idString = result.map((item:any) => item.id).join(',');
      setInactiveProductList(idString);
      return idString;
    } catch (error) {
      toast.error('Erreur');
      setInactiveProductList([]);
    }
  }

  const getInactiveSrerviceProviderHandler = async () => {
    try {
      const result = await serviceProviderService?.getInactiveServiceProvider();
      const idString = result.map((item:any) => item.id).join(',');
      return idString;
    } catch (error) {
      toast.error('Erreur');
    }
  }
  
  const triggerLoadingFunc = () => {
    setTableIsLoading(true);
    setTimeout(() => {
      setTableIsLoading(false);
    }, 500);
  };

  const getAllProductsWithPagination = async (query: any) => {
    await dispatch(
      fetchProductWithZonesAndPrices({
        page: query.page,
        limit: query.limit,
        orderBy: 'createdAt,desc',
        productInactive: inactiveProductList?.length > 0 ? inactiveProductList : "",
      })
    )
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  };

  const getZonesNamesHandler = async () => {
    const getAllZonesNames = await regionService?.getZonesNames()
    .catch(() => {
      toast.error('Erreur');
    });
    setZonesNames(getAllZonesNames)
  }

  // Handle filtering of providers based on the selected prestataire
  const handlePrestataireChange = (value: string | undefined) => {
    setSelectedPrestataire(value);
    triggerLoadingFunc();
  };

  const handleProductChange = (value: string | undefined) => {
    setSelectedProduct(value);
    triggerLoadingFunc();
  };

  const getProviderIds = async (address: Address | null) => {
    const inactiveServiceProviderLists = await getInactiveSrerviceProviderHandler();

    if (address?.latitude && address?.longitude && inactiveServiceProviderLists.length > 0) {
        const serviceProviders = await urbanCenterService?.getProvidersZonesByChantierAddress(
            address.latitude,
            address.longitude,
            false,
            inactiveServiceProviderLists
        );

        // Helper function to determine if the response is "empty"
        const isServiceProvidersEmpty = (providers: any): boolean => {
            if (!providers) return true; // If null or undefined
            return Object.values(providers).every(
                (value) => Array.isArray(value) && value.length === 0
            );
        };

        if (isServiceProvidersEmpty(serviceProviders)) {
            setListPrestataire([]);
            dispatch(resetState());
        } else if (serviceProviders?.length > 0) {
            // Process `rows` if they exist
            const uniqueZoneIds = Array.from(new Set(
                serviceProviders.map((serviceProvider) => serviceProvider.zoneId)
            ));
            setSelectedZoneIds(uniqueZoneIds);
            return uniqueZoneIds;
        }
    }

    // Default return if address is missing or no providers found
    return [];
};

  const handleSelectChantierAddress = async (address: Address) => {
    dispatch(triggerLoading());
    const lat = typeof address.latitude === 'number' ? address.latitude : Number(address.latitude) || defaultCenter.lat;
    const lng = typeof address.longitude === 'number' ? address.longitude : Number(address.longitude) || defaultCenter.lng;
    
    // Update selected address and map center
    setSelectAddressChantier(address);
    setMapCenter({ lat, lng });
  
    // Check for duplicate addresses
    const existingAddress = addressChantier.find((a) => a.address_id === address.address_id);
    if (!existingAddress) {
      setAddressChantier((prevAddress) => [...prevAddress, address]);
    }
    // Fetch zones with service providers
    try {
        const serviceProvideIds = await getProviderIds(address);
        const productsInactive = await getInactiveProductHandler();
        if (serviceProvideIds?.length > 0) {
          await dispatch(
            fetchProductWithZonesAndPrices({
              zoneIds: serviceProvideIds?.length ? serviceProvideIds.join(',') : undefined,
              page: 1,
              limit: 10000,
              orderBy: 'createdAt,desc',
              productInactive: productsInactive,
            })
          )
            .unwrap()
            .catch(() => {
              toast.error('Erreur');
          });
        }
      // disptach the values here to do the search 
    } catch (error) {
      console.error('Error fetching zones with providers:', error);
    }
  };
  
  // table data mock
  // Helper pour calculer les lignes et rowSpans
  const calculateRowSpans = (providers: any) => {
    const tableData: TableRow[] = [];
    
    providers.forEach((provider: any, providerIndex: number) => {
      const providerOfferCount = provider.offers.reduce(
        (acc: number, offer: any) => acc + offer.zones.length,
        0
      );

      (
        <>
          {provider.name}
          <br />
          {provider.Address.city} {provider.Address.postalcode}
        </>
      )

      provider.offers.forEach((offer: any, offerIndex: number) => {
        const zoneCount = offer.zones.length;

        offer.zones.forEach((zone: any, zoneIndex: number) => {
          const productsWithLineBreaks = offer.products.join('\n');
          tableData.push({
            key: `zone-${providerIndex}-${offerIndex}-${zoneIndex}`,
            prestataires: zoneIndex === 0 && offerIndex === 0 ? (
              <>
                {provider.name}
                <br />
                {provider.Address[0]?.city} ({provider.Address[0]?.postalcode})
              </>
            )  : '',
            produit: zoneIndex === 0 ? productsWithLineBreaks : '',
            tarif: zoneIndex === 0 ? offer.type : '',
            commentaire: zoneIndex === 0 ? (offer.comments || '') : '',
            zone: zone.zone,
            prixPresta: zone.price,
            rowSpan: {
              prestataires: offerIndex === 0 && zoneIndex === 0 ? providerOfferCount : 0,
              produit: zoneIndex === 0 ? zoneCount : 0,
            },
          });
        });
      });
    });

    return { tableData };
  };

  const filteredProvidersData = listPrestataire.filter((provider:any) => {
    // Check if the provider matches the selectedPrestataire
    const matchesPrestataire = selectedPrestataire
      ? provider.name.includes(selectedPrestataire)
      : true;
  
    // Check if the provider matches the selectedProduct in any of its offers
    const matchesProduct = selectedProduct
      ? provider.offers.some((offer:any) =>
          offer.products.some((product:any) => product.includes(selectedProduct))
        )
      : true;
  
    // Include the provider if it matches both conditions
    return matchesPrestataire && matchesProduct;
  });

  // Génération des données du tableau
  const { tableData } = calculateRowSpans(filteredProvidersData);

  const serviceProviderIds = new Set(productsPrice.map((item:any) => item.serviceProviderId));
  const productIds = new Set(productsPrice.map((item:any) => item.productId));

  // Filter serviceProviders and products
  const filteredServiceProviders = serviceProviders?.filter((provider:any) =>
    serviceProviderIds.has(provider.id)
  );

  const filteredProducts = getAllProducts.rows?.filter((product:any) =>
    productIds.has(product.id)
  );

  // Définition des colonnes avec rowSpan pour les fusions
  const columns: ColumnsType<TableRow> = [
    {
      title: 'Prestataires',
      dataIndex: 'prestataires',
      key: 'prestataires',
      width: 200,
      render: (text: string, record: TableRow) => ({
        children: <strong>{text}</strong>,
        props: {
          rowSpan: record.rowSpan.prestataires || 0, // Fusion des lignes pour le prestataire
        },
      }),
    },
    {
      title: 'Produit',
      dataIndex: 'produit',
      key: 'produit',
      width: 300,
      render: (text: string, record: TableRow) => ({
        children: (
          <div style={{ whiteSpace: 'pre-line' }}>
            {text}
          </div>
        ),
        props: {
          rowSpan: record.rowSpan.produit || 0, // Merge rows for products
        },
      }),
    },
    {
      title: 'Tarif',
      dataIndex: 'tarif',
      key: 'tarif',
      render: (text: string, record: TableRow) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan.produit || 0, // Fusion des lignes pour le tarif
        },
      }),
    },
    {
      title: 'Commentaire',
      dataIndex: 'commentaire',
      key: 'commentaire',
      render: (text: string, record: TableRow) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan.produit || 0, // Fusion des lignes pour les commentaires
        },
      }),
    },
    {
      title: 'Zones',
      dataIndex: 'zone',
      key: 'zone',
      width: 100,
      render: (text: string) => ({
        children: text,
        props: {},
      }),
    },
    {
      title: 'Prix Presta',
      dataIndex: 'prixPresta',
      key: 'prixPresta',
      width: 100,
      render: (text: string) => ({
        children: `${text} €`,
        props: {},
      }),
    },
  ];

  useEffect(() => {
    if (!(Object.keys(selectAddressChantier).length > 0) && (Object.keys(inactiveProductList).length > 0)) {
      getAllProductsWithPagination(query);
    }
  }, [query, selectAddressChantier, inactiveProductList]);

  useEffect(() => {
    getInactiveProductHandler();
    getZonesNamesHandler();
    getAllServiceProviders();
  }, []);

  useEffect(() => {
    if ((getAllProducts.rows?.length > 0) && (serviceProviders.length > 0) && (listPrestataire.length > 0)) {
      setLoading(false);
      setTableIsLoading(false);
    }
  }, [getAllProducts, serviceProviders, listPrestataire]);

  useEffect(() => {
    if (productsPrice.length > 0 && getAllProducts.rows?.length > 0 && serviceProviders.length > 0 && zonesNames.length > 0) {
      const processedData = processData(productsPrice, getAllProducts.rows, serviceProviders, zonesNames);
      setListPrestataire(processedData);
    } else {
      setListPrestataire([]);
    }
  }, [productsPrice, getAllProducts, serviceProviders, zonesNames, selectedZoneIds]); 

  return (
    <>
      <PageTitle>RECHERCHE DE PRIX</PageTitle>
        {/* <Spinner loading={loading || loadingProductTypes === 'pending'}> */}
        <Spinner loading={loading}>
          <>
          <MainTitle parent="Recherche de prix" child="PRESTATAIRES" />
            <Divider className="horizontal-bar" />
            <div>
              <div className="product-page__searching">
                <Form layout="vertical">
                  <Row gutter={16}>
                    <Col span={8}>
                      <Form.Item
                          label="Adresse chantier"
                          name="formattedAddressChantier"
                      >
                          <SelectWithGoogleSuggestion
                            autoConvertAddress
                            placeholder="Sélectionner une adresse"
                            options={addressChantier ?? []}
                            defaultValue={selectAddressChantier}
                            onSelect={(value: Address) => {
                              if (value) {
                                handleSelectChantierAddress(value);
                              }
                            }}

                          />

                      </Form.Item>
                    </Col>
                    {/* {(Object.keys(selectAddressChantier).length > 0 && listPrestataire.length > 0) && (
                    <Col span={8}>
                      <Form.Item label="Prestataire" name="vendeur">
                        <Select
                          placeholder="Sélectionner"
                          showSearch
                          allowClear
                          options={filteredServiceProviders?.map((option:any) => ({
                            key: option.id,
                            value: option.name,
                            label: option.name,
                          }))}
                          onSelect={(value) => {
                            handlePrestataireChange(value); // Update selected prestataire
                          }}
                          onClear={() => {
                            setSelectedPrestataire(undefined); // Clear selection when dropdown is cleared
                            triggerLoadingFunc();
                          }}
                        />
                      </Form.Item>
                    </Col>
                    )}
                    {(Object.keys(selectAddressChantier).length > 0 && listPrestataire.length > 0) && (
                    <Col span={8}>
                      <Form.Item label="Produit" name="Produit">
                        <Select
                          placeholder="Sélectionner"
                          showSearch
                          allowClear
                          options={filteredProducts?.map((option:any) => ({
                            key: option.id,
                            value: option.name,
                            label: option.name,
                          }))}
                          onSelect={(value) => {
                            handleProductChange(value); // Update selected prestataire
                          }}
                          onClear={() => {
                            setSelectedProduct(undefined); // Clear selection when dropdown is cleared
                            triggerLoadingFunc();
                          }}
                          filterOption={(input, option) =>
                            ((option?.label ?? '') as string).toLowerCase().includes((input ?? '').toLowerCase())
                          }
                        />
                      </Form.Item>
                    </Col>
                    )} */}
                  </Row>
                </Form>
              </div>
            </div>
            <Divider className="horizontal-bar" />
            <strong className="main-title__child" style={{ fontSize: '20px' }}>
              Tarifs réels prestataires
            </strong>
            <Table
              style={{ marginTop: '20px' }}
              columns={columns}
              dataSource={tableData}
              pagination={false}
              rowKey={(record) => record.key}
              className="region-list__datatable"
              loading={{
                indicator: (
                  <ScaleLoader
                    color='#A6C84D'
                    cssOverride={{
                      display: 'inline-block !important',
                      margin: '0 auto',
                      left: 0,
                      height: '100%',
                      width: '100%',
                    }}
                    aria-label='Loading Spinner'
                    data-testid='loader'
                  />
                ),
                spinning: porvidersWithProductsLoading === 'pending' || tableIsLoading,
              }}
            />
            <div className='pagination__pagination-items'>
            <span className='pagination__number-total'>Total {totalItems} items</span>
            {!(Object.keys(selectAddressChantier).length > 0) && (
            <Pagination
              showSizeChanger
              current={query.page}
              total={totalItems}
              pageSize={query.limit}
              onChange={(page, limit) => onParamChange({ page, limit })}
              className='pagination'
            />
            )}
          </div>
          <Divider className="horizontal-bar" />
          </>
        </Spinner>
    </>
  );
};

export default SearchPriceLogistique;
