import { <PERSON>Title } from 'components/Common';
import { useState } from 'react';
import { Row, Col, Form, Input, Button } from 'antd';
import { Spinner } from 'components/Common';
import useAuthContext from 'store/auth-context';
import Logo from 'assets/images/logo-ecodrop.png';
import { UserOutlined, LockOutlined } from '@ant-design/icons';
import { toast } from 'react-toastify';

const Login = () => {
  const [form] = Form.useForm();
  const [error, setError] = useState<string | null>(null);
  const { onSignIn, loading } = useAuthContext();

  const validateEmail = (email: string) => {
    return String(email)
      .toLowerCase()
      .match(
        /^(([^<>()[\]\\.,;:\s@"]+(\.[^<>()[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/
      );
  };

  const onFinish = async ({
    email,
    password
  }: {
    email: string;
    password: string;
  }) => {
    try {
      if (email && password) {
        email = email.trim();
        password = password.trim();
        const validate = validateEmail(email);
        if (validate && validate.length) {
          await onSignIn({
            email,
            password
          });
        } else {
          throw Error('email invalid');
        }
      } else {
        throw Error('email password null');
      }
    } catch (error: any) {
      toast.error('Erreur');
      setError(
        error?.message === 'email password null'
          ? 'Email ou mot de passe incorrect !'
          : error?.message === 'email invalid'
          ? `L'entrée n'est pas valide`
          : 'Email ou mot de passe incorrect !'
      );
    }
  };
  return (
    <>
      <PageTitle>Login Page</PageTitle>
      <Spinner loading={loading}>
        <div>
          <main className='login-page'>
            <div className='login-page__container'>
              <div className='login-form' id='login-form'>
                <figure className='login-page__logo-ecodrop-box'>
                  <img
                    src={Logo}
                    alt="Espace réservé à l'équipe Ecodrop"
                    width={141}
                    height={77}
                    className='login-page__logo-ecodrop'
                  />
                </figure>
                <h3 className='login-form__title'>
                  Espace réservé à l'équipe Ecodrop
                </h3>
                <Form
                  name='basic'
                  onFinish={onFinish}
                  autoComplete='off'
                  form={form}>
                  <Form.Item
                    name='email'
                    className='login-form__input-email'
                    rules={
                      [
                        // { type: "email", message: `L'entrée n'est pas valide` },
                        // { required: true, message: `Veuillez saisir votre email!` },
                      ]
                    }>
                    <Input
                      placeholder='Email'
                      id={error ? 'ant-input-error-placeholder' : ''}
                      autoComplete='email'
                      prefix={
                        <UserOutlined
                          style={{
                            marginRight: '10.12px',
                            color: '#fff'
                          }}
                        />
                      }
                    />
                  </Form.Item>
                  <Form.Item
                    name='password'
                    className='login-form__input-password'
                    rules={[
                      {
                        // required: true,
                        // message: "Veuillez saisir votre mot de passe!",
                      }
                    ]}
                    style={{ marginBottom: error ? '5px' : '29px' }}>
                    <Input
                      type='password'
                      placeholder='Mot de passe'
                      id={error ? 'ant-input-error-placeholder' : ''}
                      autoComplete='password'
                      prefix={
                        <LockOutlined
                          style={{
                            marginRight: '10.12px',
                            color: '#fff'
                          }}
                        />
                      }
                    />
                  </Form.Item>
                  {error && (
                    <div
                      style={{
                        color: '#FF4D4F',
                        fontSize: '14px',
                        textAlign: 'center',
                        marginBottom: '27px'
                      }}>
                      {error}
                    </div>
                  )}
                  <Form.Item style={{ marginBottom: 0 }}>
                    <Row>
                      <Col span={24} style={{ textAlign: 'center' }}>
                        <Button type='text' className='login-form__forgot'>
                          Mot de passe oublié?
                        </Button>
                      </Col>
                    </Row>
                  </Form.Item>
                  <Button
                    htmlType='submit'
                    className='login-form__login'
                    disabled={loading}
                    block>
                    Se connecter
                  </Button>
                </Form>
              </div>
            </div>
          </main>
        </div>
      </Spinner>
    </>
  );
};

export default Login;
