import { useEffect, useState, useCallback, useRef } from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, Spinner, PageTitle } from 'components/Common';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { Button, Form, Divider, Typography, Modal } from 'antd';
import {
  ArrowLeftOutlined,
  CloseOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleFilled,
  FilePdfOutlined,
  InfoCircleFilled,
  LoadingOutlined,
  UploadOutlined,
} from '@ant-design/icons';
import { useQueryParams, useMergeState } from '../../hooks';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchSales, selectSales } from 'store/slices/sale.slices';
import { selectClientContact, findContactByParams, setClientContact } from 'store/slices/client_contact.slice';
import { setClient<PERSON>ontact<PERSON>erson, setClientContactPersons } from 'store/slices/client_contact_person.slice';
import { findDocumentById } from 'store/slices/document.slice';
import { Client<PERSON>ontact, Client<PERSON>ontact<PERSON>erson, Demander, DocumentFileUpload, Documents, Quotation } from 'models';
import { ClientChantier, DemanderUnPrix, DevisProducts, Logistique } from 'components/Estimate';
import { Address, DevisActionType, FileDetails, PaymentType, UrbanCenterZoneIds } from 'types';
import {
  CREATED_FROM_TYPES,
  DOCUMENT_STATUSES,
  DOCUMENT_TYPES,
  FILE_UPLOAD_TYPES,
  PAYMENT_STATUS,
  STATUSES,
  ZOHO_DOCUMENT_SYNC_STATUS,
} from 'utils/constant';
import { clientContactService, documentService, urbanCenterService } from 'services';
import zohoService from '../../services/zoho.service';
import {
  duplicates,
  frenchCurrencyFormat,
  generateDocumentTitle,
  getBooksTaxId,
  getFileNameWithoutExtension,
} from 'utils';
import { UploadFileCustom } from 'components/Estimate/FileUpload';
import { DevisProductsRef } from 'components/Estimate/DevisProducts';
import { fetchProductTypeUnit } from 'store/slices/product.slices';
import { generateDocumentData } from 'utils/document';
import dayjs from 'dayjs';
import CopyDataTextDocument from 'components/Common/CopyDataText';
// import client_contact_addressesService from "../../services/client_contact_addresses.service";
const { Text } = Typography;

const EstimateDetail = () => {
  const dispatch = useAppDispatch();
  const [query] = useQueryParams<{
    zcrm_contact_id: string;
    referent_id: string;
    lastmodify_by_id: string;
    lastmodify_by_id_books: string;
    create_account: string;
    transform_to_order: boolean;
  }>();
  const [loading, setLoading] = useState<boolean | undefined>(true);
  const [transformLoading, setTransformLoading] = useState<boolean>(true);
  const params = useParams();
  const booksDocumentId = params.documentId as string;
  const navigate = useNavigate();
  const location = useLocation();
  const [form] = Form.useForm();
  const [lastModifyById] = useState<string | undefined>(query?.lastmodify_by_id as string | undefined);
  const referentId = query?.referent_id as string;
  const contact = useAppSelector(selectClientContact);
  const [isSubmitting, setIsSubmitting] = useState<DevisActionType>(null);
  // const [isCopying, setIsCopying] = useState<boolean>(false);
  /* eslint-disable-next-line */
  const [isLogDemandeModalOpen, setIsLogDemandeModalOpen] = useState(false);
  // Determine if the path is "/order" or "/quotation"
  const isOrder = location.pathname.includes('/order');
  const isQuotation = location.pathname.includes('/quotation');
  const documentType = isOrder ? DOCUMENT_TYPES?.ORDER : isQuotation ? DOCUMENT_TYPES?.QUOTATION : '';
  const [documentStatus, setDocumentStatus] = useState('');
  const [isOpenTransactionModal, setIsOpenTransactionModal] = useState<boolean>(false);
  const [quotationData, setQuotationData] = useMergeState<Quotation>({
    status: DOCUMENT_STATUSES.DRAFT.key,
    isGpsAddress: false,
    gpsAddress: '',
    latLongGpsAddress: '',
  });
  const [isDocumentCatalog, setIsDocumentCatalog] = useState<boolean | null>(null);
  const [paymentInfo, setPaymentInfo] = useState<PaymentType | null>(null);
  const [isDraft, setIsDraft] = useState<boolean>(false);
  const [fileTransactionProof, setFileTransactionProof] = useState<FileDetails | null>(null);
  const [isDeleteFileProof, setIsDeleteFileProof] = useState<boolean>(false);
  const sales = useAppSelector(selectSales);
  const [urbanCenterZoneIds, setUrbanCenterZoneIds] = useState<UrbanCenterZoneIds>({
    regionCatalogZoneIds: [],
    serviceProviderZoneIds: [],
    serviceProviderZones: [],
  });
  const [isModalSyncDocumentSuccessOpen, setIsModalSyncDocumentSuccessOpen] = useState<boolean>(false);
  const [objectDocument, setObjectDocument] = useState<Documents | null>(null);
  const [subTotal, setSubTotal] = useState<number>(0);
  const [contactName, setContactName] = useState('');
  const devisProductsRef = useRef<DevisProductsRef>(null);
  const [isLoaded, setIsLoaded] = useState<boolean>(false);
  const hasNavigatedRef = useRef(false);
  const [zohoRedirectLink, setZohoRedirectLink] = useState<string | null>(null);

  useEffect(() => {
    form.resetFields();
    initialData();
  }, []);

  useEffect(() => {
    if (location.state?.navigated) {
      if (!hasNavigatedRef.current) {
        hasNavigatedRef.current = true;
        initialData();
      }
    }
  }, [booksDocumentId, documentType, location.state]);

  useEffect(() => {
    if (quotationData?.document?.createdFrom !== CREATED_FROM_TYPES.QBO && quotationData?.document?.booksDocumentId) {
      window.location.href = getZQORedirectUrl();
    }
  }, [quotationData]);

  useEffect(() => {
    if (
      query.transform_to_order &&
      quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
      quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.SENT.key &&
      form.getFieldValue('lineItems') &&
      isLoaded
    ) {
      setTransformLoading(true);
      onSubmit('marquer-come-accepte', null, false, DOCUMENT_TYPES?.QUOTATION, DOCUMENT_STATUSES.ACCEPTED.key);
    }
  }, [isLoaded, form, quotationData, query]);

  useEffect(() => {
    if (contact) {
      // Code to execute after the contact data has been loaded
      dispatch(setClientContactPersons(contact?.ContactPersons || []));
      if (contact.clientType == 'business') {
        if (contact.name) {
          setContactName(contact.name);
        }
      } else {
        setContactName(contact?.ContactPersons?.[0]?.firstName + ' ' + contact?.ContactPersons?.[0]?.lastName);
      }
    }
  }, [contact]);

  const onChangeAddressChantier = async (address: Address | null, isChangePrice?: boolean) => {
    const urbanCenterZoneIds = await getUrbanCenterZoneIds(address);
    if (isChangePrice) {
      devisProductsRef.current?.updateIsCatalog(urbanCenterZoneIds?.isDocumentCatalog);
    }
    await devisProductsRef.current?.refreshCatalogPrice({
      zoneIds: urbanCenterZoneIds,
      isChangePrice,
      isDocumentCatalog: urbanCenterZoneIds?.isDocumentCatalog,
    });
  };

  const handleCheckAndShowMessageSyncDocument = async () => {
    if (booksDocumentId) {
      const documentRes = await dispatch(
        findDocumentById({
          documentId: booksDocumentId,
          params: {
            include: 'DocumentType',
          },
        }),
      )
        .unwrap()
        .catch(() => {
          toast.error('Erreur');
        });
      if (documentRes && documentRes?.length > 0) {
        const data = documentRes[0];
        setObjectDocument(data);
        if (data.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION) {
          toast.success(
            `Devis ${data.cdeZoho} enregistré dans ZOHO, l'envoi du devis va se faire depuis ZOHO, vous recevrez une copie du devis par email.`,
          );
        } else {
          setIsModalSyncDocumentSuccessOpen(true);
        }

        console.log('handleCheckAndShowMessageSyncDocument: ', documentRes);
      }
    }
  };

  const getZQORedirectUrl = () => {
    return documentType === DOCUMENT_TYPES.QUOTATION
      ? `${process.env.REACT_APP_ZQO_PATH}/${quotationData?.document?.booksDocumentId ?? booksDocumentId}?lastmodify_by_id=${lastModifyById}`
      : `${process.env.REACT_APP_ZQO_PATH}/order/${quotationData?.document?.booksDocumentId ?? booksDocumentId}?lastmodify_by_id=${lastModifyById}`;
  };

  const getCRMRedirectUrl = () => {
    return documentType === DOCUMENT_TYPES.QUOTATION
      ? `${process.env.REACT_APP_CRM_PATH}/${booksDocumentId}?lastmodify_by_id=${lastModifyById}`
      : `${process.env.REACT_APP_CRM_PATH}/order/${booksDocumentId}?lastmodify_by_id=${lastModifyById}`;
  };

  const initialData = async () => {
    setLoading(true);
    try {
      if (location.state?.isCheckSyncDocument) {
        handleCheckAndShowMessageSyncDocument();
      }
      await Promise.all([getSales(), getDocumentStatusAndType(), getListProductTypeUnit()]);
      const document = await getDevisById();
      await getUrbanCenterZoneIds({
        latitude: document?.siteAddressLatitude,
        longitude: document?.siteAddressLongitude,
      });
    } catch (error) {
      console.error('An error occurred during initialization:', error);
    } finally {
      setLoading(false);
      if (!query.transform_to_order) {
        setTransformLoading(false);
      }
    }
  };

  const handleCloseTab = async () => {
    setIsModalSyncDocumentSuccessOpen(false);
    window.close();
  };
  const handleBackToList = async () => {
    window.location.href = await getlinkZoho();
  };
  const getSales = useCallback(async () => {
    await dispatch(fetchSales({}))
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  }, [dispatch]);
  const getClientContact = useCallback(
    async (contactId?: string, contactPersonId?: string) => {
      if (contactId) {
        const contact = await dispatch(findContactByParams({ id: contactId, include: 'ContactPersons' }))
          .unwrap()
          .catch(() => {
            toast.error('Erreur');
          });
        const clientContactData = contact?.rows?.length > 0 ? contact.rows[0] : ({} as ClientContact);
        if (clientContactData?.crmContactId) {
          setQuotationData({
            clientContact: clientContactData,
          });
          dispatch(setClientContactPersons(clientContactData?.ContactPersons || []));
          if (contact?.enCompte === PAYMENT_STATUS.enCompte) {
            setPaymentInfo({
              paiement: PAYMENT_STATUS.aucunPaiement,
              montantPaiement: 0,
            });
          }
          dispatch(setClientContactPerson(null));
        } else {
          const contactPerson = contactPersonId || clientContactData?.ContactPersons?.[0]?.id;
          const contactPersonData = clientContactData?.ContactPersons?.find(
            (item: ClientContactPerson) => item.id?.toString() === `${contactPerson}`,
          );
          const data = {
            ...clientContactData,
            crmContactId: contactPersonData?.crmContactId,
          };
          setQuotationData({
            clientContact: data,
          });
          await Promise.all([
            dispatch(setClientContact(data)),
            dispatch(setClientContactPersons(clientContactData?.ContactPersons || [])),
          ]);
        }
      }
    },
    [dispatch],
  );

  const getDevisById = useCallback(async () => {
    // Only fetch data if booksDocumentId exists
    if (booksDocumentId) {
      try {
        // Call API to fetch full document details by ID
        const documentRes = await dispatch(
          findDocumentById({
            documentId: booksDocumentId,
            params: {
              include:
                'EstimateDocument|DocumentCCLibresContacts|DocumentCCContacts|DocumentProductLines|DocumentProductLines.DocumentProductLinePrices|DocumentProductLines.DocumentProductLineSubOptions|DocumentProductLines.DocumentProductLinePrestations|DocumentProductLines.DocumentProductLinePrestations.DocumentProductLinePrestationSubOptions|DocumentProductLines.DocumentProductLinePrestations.DocumentProductLinePrestationStatus|DocumentType|DocumentStatus|DocumentFileUploads',
            },
          }),
        ).unwrap();
        // If response exists
        if (documentRes) {
          // Redirect to CRM if no document is returned
          if (documentRes?.length === 0) {
            window.location.href = getCRMRedirectUrl();
          }
          // Use the first document from the response, or an empty object as fallback
          const data = documentRes?.length > 0 ? documentRes?.[0] : {};

          // If contactId exists, fetch the related contact information
          if (data?.contactId) {
            await getClientContact(data?.contactId?.toString(), data?.contactPersonId?.toString());
          }
          // Extract only payment proof file uploads (applies only to orders)
          const paymentFileUploads: DocumentFileUpload[] =
            documentType === DOCUMENT_TYPES.ORDER && data?.DocumentFileUploads
              ? data?.DocumentFileUploads?.filter(
                  (file: DocumentFileUpload) => file?.type === FILE_UPLOAD_TYPES.PAYMENT_PROOF,
                )
              : [];
          // If it's an order, get additional payment info
          if (documentType === DOCUMENT_TYPES.ORDER) {
            getOrderPaymentInfo(data);
          }
          // Set the document data to state
          setQuotationData({
            document: data,
          });
          // Prepare the first payment proof file (if any) and normalize the filename
          const documentPaymentProofFile =
            documentType === DOCUMENT_TYPES.ORDER && paymentFileUploads && paymentFileUploads.length > 0
              ? {
                  id: paymentFileUploads[0].id,
                  name: paymentFileUploads[0]?.name ?? '',
                  file_name: paymentFileUploads[0]?.name ?? '',
                  keyFile: paymentFileUploads[0]?.url ?? '',
                }
              : null;
          // Rename the payment proof file to 'preuve_de_virement' if needed
          if (documentPaymentProofFile) {
            const fileNameWithoutExtension = getFileNameWithoutExtension(documentPaymentProofFile.file_name);
            if (fileNameWithoutExtension !== 'preuve_de_virement') {
              const extensionMatch = documentPaymentProofFile.file_name.match(/\.[^/.]+$/);
              const extension = extensionMatch ? extensionMatch[0] : '';
              documentPaymentProofFile.file_name = `preuve_de_virement${extension}`;
            }
          }

          // Set the payment proof file to state
          setFileTransactionProof(documentPaymentProofFile);

          // Set the document status to state
          setDocumentStatus(data?.DocumentStatus?.key);
          // If the document type is not defined, show an error toast and redirect to ZQO
          if (!data?.DocumentType?.key) {
            toast.error('Erreur: Type du document non défini.', {
              toastId: `no-type-${booksDocumentId}`,
            });
            window.location.href = getZQORedirectUrl();
            return null;
          }
          // If type in URL doesn't match actual document type, show error and redirect to correct page
          if (
            (documentType === DOCUMENT_TYPES.QUOTATION && data?.DocumentType?.key !== DOCUMENT_TYPES.QUOTATION) ||
            (documentType === DOCUMENT_TYPES.ORDER && data?.DocumentType?.key !== DOCUMENT_TYPES.ORDER)
          ) {
            toast.error(
              <div>
                Erreur
                <br />
                L&apos;ID du document ne correspond pas au type indiqué dans l&apos;URL. Redirection vers la bonne page.
              </div>,
              {
                toastId: `type-mismatch-${booksDocumentId}`,
                onClose: () => {
                  const correctPath =
                    data?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
                      ? `/quotation/${booksDocumentId}?lastmodify_by_id=${lastModifyById}`
                      : `/order/${booksDocumentId}?lastmodify_by_id=${lastModifyById}`;
                  navigate(correctPath, { state: { navigated: true } });
                },
              },
            );
            return null;
          }
          // Return the processed document data
          return data;
        } else {
          // If no document was found, show an error and redirect to CRM
          toast.error('Erreur: Document non trouvé.', {
            toastId: `not-found-${booksDocumentId}`,
          });
          window.location.href = getCRMRedirectUrl();
          return null;
        }
      } catch (error) {
        // Handle API errors
        console.error('Erreur API findDocumentById:', error);
        toast.error('Erreur: Impossible de récupérer le document.', {
          toastId: `api-error-${booksDocumentId}`,
        });
        return null;
      }
    }
  }, [dispatch, booksDocumentId, documentType, lastModifyById, navigate]);

  const getListProductTypeUnit = useCallback(async () => {
    try {
      await dispatch(fetchProductTypeUnit({})).unwrap();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  }, [dispatch]);
  const getUrbanCenterZoneIds = async (address: Address | null) => {
    if (address?.latitude && address?.longitude) {
      const zones = await urbanCenterService.getZonesByChantierAddress(address?.latitude, address?.longitude);
      const regionCatalogZoneIds = zones?.regionCatalogZoneIds?.map((zone) => zone.zoneId);
      const serviceProviderZoneIds = zones?.serviceProviderZoneIds?.map((zone) => zone.zoneId);
      const serviceProviderZones = zones?.serviceProviderZones;
      const countryRegionId = zones?.countryRegionId;
      const isDocumentCatalog =
        regionCatalogZoneIds?.length > 0 ? true : serviceProviderZoneIds?.length > 0 ? false : null;
      const urbanCenterZoneIds = {
        regionCatalogZoneIds,
        serviceProviderZoneIds,
        serviceProviderZones,
        isDocumentCatalog,
        countryRegionId,
      };
      setIsDocumentCatalog(isDocumentCatalog);
      setUrbanCenterZoneIds(urbanCenterZoneIds);
      return urbanCenterZoneIds;
    }
    setIsDocumentCatalog(null);
    return {
      regionCatalogZoneIds: [],
      serviceProviderZoneIds: [],
      serviceProviderZones: [],
      isDocumentCatalog: null,
    };
  };
  const getOrderPaymentInfo = (document: Documents) => {
    if (document?.paiement) {
      const paiement = document?.paiement;
      const montantPaiement = document?.montantPaiement;
      setPaymentInfo({
        message: paiement !== 'Virement' ? `${paiement} - ${montantPaiement} € HT` : '',
        paiement,
        montantPaiement,
      });
      // if paiement is virement and haven't upload transaction proof
      if (paiement === 'Virement') {
        const paymentFileUploads: DocumentFileUpload[] =
          documentType === DOCUMENT_TYPES.ORDER && document?.DocumentFileUploads
            ? document?.DocumentFileUploads?.filter(
                (file: DocumentFileUpload) => file?.type === FILE_UPLOAD_TYPES.PAYMENT_PROOF,
              )
            : [];
        if (paymentFileUploads.length === 0) {
          setIsOpenTransactionModal(true);
        }
      }
      if (contact?.enCompte === 'En compte') {
        setPaymentInfo({
          paiement: 'Aucun paiement',
          montantPaiement: 0,
        });
      }
    }
  };

  const getDocumentStatusAndType = async () => {
    try {
      const [documentTypes, documentStatus, documentProductLinePrestationStatus] = await Promise.all([
        documentService.getDocumentTypes(),
        documentService.getDocumentStatus(),
        documentService.getDocumentProductLinePrestationStatus(),
      ]);
      setQuotationData({
        documentTypes: documentTypes?.rows,
        documentStatus: documentStatus,
        documentProductLinePrestationStatus: documentProductLinePrestationStatus?.rows,
      });
    } catch (error) {
      console.log(error);
    }
  };

  const handleOpenLogDemandeModal = async () => {
    try {
      setIsDraft(true);
      await form.validateFields();
      const values = form.getFieldsValue();
      const lineItems = Object.keys(values?.lineItems || []).map((key) => ({
        ...values?.lineItems?.[key],
      }));
      const lineItemHasProduct = lineItems?.some((item) => item?.productId);
      if (lineItems && lineItems?.length > 0 && lineItemHasProduct) {
        setIsLogDemandeModalOpen(true);
      } else {
        toast.error(
          'Merci d’ajouter un produit et de remplir les champs obligatoires dans le devis avant de l’envoyer à la LOG',
        );
      }
    } catch (error) {
      console.log(error);
      toast.error(
        'Merci d’ajouter un produit et de remplir les champs obligatoires dans le devis avant de l’envoyer à la LOG',
      );
    }
  };
  const handleQuitter = async () => {
    window.location.href = await getlinkZoho();
  };
  const getlinkZoho = async (crmContactId?: string) => {
    let redirectId = contact?.crmContactId ?? (quotationData?.clientContact?.crmContactId as string | null);
    if (!redirectId) {
      const contactData = await clientContactService.findContactByParams({
        id: contact?.id,
        include: 'ContactPersons',
      });
      const clientContactData: ClientContact = contactData?.rows?.length > 0 ? contactData?.rows[0] : [];
      if (clientContactData?.crmContactId) {
        redirectId = clientContactData?.crmContactId;
      }
    }
    if (crmContactId) {
      redirectId = crmContactId;
    }
    return process.env.REACT_APP_ZOHO_PATH + `/${redirectId}`;
  };

  const onSubmit = async (
    actionType: DevisActionType = 'submit',
    valueDemander: Demander | null = null,
    isCopiedSubmit?: boolean,
    submitType?: string,
    submitStatus?: string,
  ) => {
    try {
      setIsDraft(
        actionType === 'submit' ||
          (actionType === null &&
            quotationData?.document?.DocumentStatus?.key === STATUSES.draft.key &&
            isCopiedSubmit) ||
          actionType === 'demander',
      );
      const vaild = await form.validateFields().catch((error) => {
        console.log(error);
      });
      if (vaild) {
        const booksTaxId = getBooksTaxId(contact?.taxPercentage ?? null);
        if (booksTaxId) {
          const values = form.getFieldsValue();
          if (values.email && values.email?.length > 5) {
            throw new Error('Les cc gratuits ont atteint leur quantité maximale');
          }
          setIsSubmitting(actionType);
          const countryRegionId = urbanCenterZoneIds?.countryRegionId;
          const submitData = await generateDocumentData({
            quotationData,
            contact,
            sales,
            values,
            submitType,
            submitStatus,
            lastModifyById,
            countryRegionId,
            booksTaxId,
            documentType,
            paymentInfo,
            actionType,
            valueDemander,
            isUpdate: true,
          })();

          submitData.syncStatus = ZOHO_DOCUMENT_SYNC_STATUS.IN_PROGRESS;
          submitData.syncMessage = '';

          console.log('submitData', submitData);
          // return;
          const res = await documentService.updateDocument(Number(quotationData?.document?.id), submitData);
          if (res?.id) {
            const linkZohoRedirect = await getlinkZoho(submitData?.contactPerson?.crmContactId);
            setZohoRedirectLink(linkZohoRedirect);
            if (documentType === DOCUMENT_TYPES.QUOTATION && (actionType === 'submit' || actionType === 'demander')) {
              await getDevisById();
              toast.success('Success');
            } else if (actionType === null && isCopiedSubmit) {
              navigate(
                `/${documentType}?lastmodify_by_id=${lastModifyById}&${documentType === DOCUMENT_TYPES?.QUOTATION ? 'estimate_id' : 'order_id'}=${quotationData?.document?.id ?? null}`,
              );
              return;
            } else if (documentType === DOCUMENT_TYPES.QUOTATION && actionType === 'marquer-come-accepte') {
              onTransform();
            } else {
              await getDevisById();
              toast.success('Success');
            }
            await handleCheckAndShowMessageSyncDocument();
          }

          setIsSubmitting(null);
          // setIsSubmitted(false);
          // setIsCopying(false);
        } else {
          toast.error(
            <div>
              Erreur
              <br />
              {
                "L'identifiant fiscal Zoho n'est pas encore synchronisé. Veuillez patienter quelques instants avant de créer ou de mettre à jour le document. Si cela persiste merci de contacter le service IT"
              }
            </div>,
          );
        }
      }
    } catch (error) {
      console.log(error);
      // setIsSubmitted(false);
      setIsSubmitting(null);
      const values = form.getFieldsValue() || {};
      const duplicatedUuids = duplicates(values?.lineItems || {});
      if (duplicatedUuids && duplicatedUuids?.size > 0) {
        toast.error('Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.');
      } else {
        toast.error(
          <div>
            Erreur
            <br />
            {(error as Error)?.message ?? ''}
          </div>,
        );
      }
    }
  };
  const onDuplicatedSubmit = async () => {
    if (
      (quotationData?.document?.DocumentStatus?.key === DOCUMENT_STATUSES.SENT.key ||
        quotationData?.document?.DocumentStatus?.key === DOCUMENT_STATUSES.ACCEPTED.key) &&
      documentType === 'quotation'
    ) {
      navigate(
        `/${documentType}?lastmodify_by_id=${lastModifyById}&estimate_id=${quotationData?.document?.id ?? null}`,
      );
    } else if (
      documentType === 'order' &&
      quotationData?.document?.DocumentStatus?.key === DOCUMENT_STATUSES.CONFIRMED.key
    ) {
      navigate(`/order?lastmodify_by_id=${lastModifyById}&order_id=${quotationData?.document?.id ?? null}`);
    } else {
      await onSubmit(null, null, true, documentType, DOCUMENT_STATUSES.DRAFT.status);
    }
  };
  const getEstimatePdf = async () => {
    setLoading(true);
    try {
      let newBooksDocumentId = quotationData?.document?.booksDocumentId;
      let data = null;
      if (booksDocumentId && !quotationData?.document?.booksDocumentId) {
        const documentRes = await dispatch(
          findDocumentById({
            documentId: booksDocumentId,
          }),
        )
          .unwrap()
          .catch(() => {
            toast.error('Erreur');
          });
        if (documentRes) {
          data = documentRes?.length > 0 ? documentRes?.[0] : {};
          newBooksDocumentId = data?.booksDocumentId;
        }
      }
      if (newBooksDocumentId) {
        console.log(data);
        const response = await zohoService.getDocumentPdf(newBooksDocumentId);
        const binary = atob(response.data);
        const len = binary.length;
        const buffer = new Uint8Array(len);
        for (let i = 0; i < len; i++) {
          buffer[i] = binary.charCodeAt(i);
        }
        const file = new Blob([buffer], { type: 'application/pdf' });
        const fileURL = URL.createObjectURL(file);

        // Create an anchor element to trigger the download
        const anchor = document.createElement('a');
        anchor.href = fileURL;
        anchor.download = data?.cdeZoho ? data?.cdeZoho : quotationData?.document?.cdeZoho + '.pdf';
        document.body.appendChild(anchor);
        anchor.click();

        // Clean up
        document.body.removeChild(anchor);
        URL.revokeObjectURL(fileURL);
      } else {
        toast.error(
          <div>
            Erreur
            <br />
            Certaines données ne sont pas encore synchronisées avec Zoho, Merci de patienter quelques instants.
          </div>,
        );
      }

      setLoading(false);
    } catch (error) {
      setLoading(false);
      setTransformLoading(false);
      console.error('Error fetching data:', error);
    }
  };
  const redirectZoho = async () => {
    const link = zohoRedirectLink || (await getlinkZoho());
    if (link) {
      window.location.href = link;
    } else {
      toast.error('Erreur: Impossible de générer le lien de redirection vers Zoho.');
    }
  };
  const onTransform = () => {
    navigate(`/order?estimate_id=${booksDocumentId}&lastmodify_by_id=${lastModifyById}`);
  };
  const handleUploadProofFile = async (file: UploadFileCustom) => {
    try {
      let fileUpload = file;
      if (booksDocumentId && file) {
        const dataSubmit = {
          uid: file.uid,
          fileUrl: file.fileUrl,
          file_name: file.file_name,
          keyFile: file.keyFile,
          url: file.url,
          name: file.file_name,
          status_file: 'CREATE',
          type: FILE_UPLOAD_TYPES.PAYMENT_PROOF,
        };
        fileUpload = await documentService.uploadFileToSalesorder(dataSubmit, quotationData?.document?.id);
      }
      toast.success('Succès');
      setFileTransactionProof(fileUpload);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleDownloadFile = async (item: FileDetails) => {
    if (item.id) {
      fetch(process.env.REACT_APP_PATH_FILES_S3 + `/${item?.keyFile}`)
        .then((response) => response.blob())
        .then((blob) => {
          const link = window.document.createElement('a');
          link.href = URL.createObjectURL(blob);
          link.download = item.name;
          link.click();
        });
    }
  };

  const handleDeleteFile = async (item: FileDetails) => {
    try {
      console.log(item);
      setIsDeleteFileProof(true);
      if (item?.id) {
        await documentService.deleteFileToSalesorder(item.id);
      }
    } catch (error) {
      console.error(error);
    } finally {
      setFileTransactionProof(null);
      setIsDeleteFileProof(false);
    }
  };

  const title = generateDocumentTitle(documentType, query, quotationData, 'DETAIL', booksDocumentId);
  return (
    <>
      <PageTitle>Devis Detail</PageTitle>
      <Spinner loading={loading || transformLoading}>
        <>
          <MainTitle
            parent={documentType === 'order' ? 'COMMANDES' : 'DEVIS'}
            child={title}
            type={documentType}
            status={quotationData?.document ? quotationData?.document?.DocumentStatus?.status : 'new'}
          />
          <div className='back-to-product-list'>
            <Button type='link' className='back-to-product-list-btn' onClick={handleBackToList}>
              <ArrowLeftOutlined className='left-arrow-icon' />
              Retour fiche client
            </Button>
          </div>
          <DemanderUnPrix
            submit={onSubmit}
            isOpenModal={isLogDemandeModalOpen}
            setIsOpenModal={setIsLogDemandeModalOpen}
            data={quotationData}
            lastModifyById={lastModifyById}
            contact={contact}
            type='updateEstimate'
          />
          <Form
            layout='horizontal'
            form={form}
            className='create-estimate-form'
            disabled={documentStatus !== DOCUMENT_STATUSES.DRAFT.key && documentType === DOCUMENT_TYPES.QUOTATION}
          >
            <ClientChantier
              disabled={
                (documentStatus !== DOCUMENT_STATUSES.DRAFT.key &&
                  documentStatus !== DOCUMENT_STATUSES.SENT.key &&
                  documentType === DOCUMENT_TYPES.QUOTATION) ||
                (documentStatus !== DOCUMENT_STATUSES.DRAFT.key && documentType === DOCUMENT_TYPES.ORDER)
              }
              documentType={documentType}
              form={form}
              lastModifyById={lastModifyById}
              referentId={referentId}
              data={quotationData}
              setData={setQuotationData}
              getClientContact={getClientContact}
              isSaveDraft={isDraft}
              isOpenTransactionModal={isOpenTransactionModal}
              setIsOpenTransactionModal={setIsOpenTransactionModal}
              onDataProofFile={(file: UploadFileCustom) => handleUploadProofFile(file)}
              onChangeAddressChantier={onChangeAddressChantier}
              isCreateForm={false}
            />

            {documentType === DOCUMENT_TYPES.ORDER ? (
              <div className='order-payment-info-wrapper mt-5'>
                <div className='line'></div>
                {paymentInfo?.message && contact?.enCompte !== 'En compte' && (
                  <div className='centered-div'>
                    <ExclamationCircleFilled style={{ color: '#FAAD14', marginRight: 10 }} />
                    {paymentInfo?.message}
                  </div>
                )}
                {!paymentInfo?.message && contact?.enCompte !== 'En compte' && (
                  <div className='centered-div-popup'>
                    {!fileTransactionProof ? (
                      <>
                        <div
                          className='px-4 py-1 d-inline-block'
                          style={{ borderRadius: '5px', boxShadow: '2px 2px 5px rgba(0,0,0,0.5)' }}
                        >
                          <InfoCircleFilled className='mr-2 color_status_draft' style={{ color: 'orange' }} />
                          <Text>Virement, besoin de la preuve de virement</Text>
                        </div>
                        <Button
                          disabled={false}
                          className='ant-btn-white bnt-upload-file ml-2'
                          onClick={() => setIsOpenTransactionModal(!isOpenTransactionModal)}
                          icon={isDeleteFileProof ? <LoadingOutlined /> : <UploadOutlined />}
                        ></Button>
                      </>
                    ) : (
                      <>
                        <div
                          className='px-4 py-1 d-inline-block'
                          style={{ borderRadius: '5px', boxShadow: '2px 2px 5px rgba(0,0,0,0.5)' }}
                        >
                          <a onClick={() => handleDownloadFile(fileTransactionProof)}>
                            <FilePdfOutlined style={{ color: '#95C515' }} />
                            <span className='ml-1 mr-2' style={{ color: '#95C515' }}>
                              {fileTransactionProof?.name ?? fileTransactionProof?.file_name}
                            </span>
                          </a>
                          {isDeleteFileProof ? (
                            <LoadingOutlined />
                          ) : (
                            <DeleteOutlined
                              onClick={() => handleDeleteFile(fileTransactionProof)}
                              style={{ cursor: 'pointer', color: 'red' }}
                            />
                          )}
                        </div>
                        <Button
                          disabled={false}
                          className='ml-2 ant-btn-white-green-border'
                          onClick={() => setIsOpenTransactionModal(!isOpenTransactionModal)}
                          icon={<EditOutlined />}
                        />
                      </>
                    )}
                  </div>
                )}
              </div>
            ) : (
              <Divider className='horizontal-bar' style={{ marginBottom: '44px' }} />
            )}

            <Logistique
              type={documentType}
              form={form}
              contactData={contact}
              data={quotationData}
              disabled={
                (documentStatus !== DOCUMENT_STATUSES.DRAFT.key &&
                  documentStatus !== DOCUMENT_STATUSES.SENT.key &&
                  documentType === DOCUMENT_TYPES.QUOTATION) ||
                (documentStatus !== DOCUMENT_STATUSES.DRAFT.key && documentType === DOCUMENT_TYPES.ORDER)
              }
            />

            <DevisProducts
              form={form}
              data={quotationData}
              disabled={documentStatus !== DOCUMENT_STATUSES.DRAFT.key}
              status={documentStatus}
              isDocumentCatalog={isDocumentCatalog}
              documentType={documentType}
              contact={contact}
              urbanCenterZoneIds={urbanCenterZoneIds}
              loading={loading}
              ref={devisProductsRef}
              isLoaded={isLoaded}
              setIsLoaded={setIsLoaded}
              setSubTotal={setSubTotal}
            />
          </Form>

          <section className='mt-2 section actions-block'>
            <Button onClick={() => handleQuitter()} className='devis-page__btn__quit'>
              Quitter
            </Button>
            {/*show in update confirmed order*/}
            {quotationData.document?.DocumentType?.key === 'order' &&
              quotationData.document?.DocumentStatus?.key === 'confirmed' && (
                <Button
                  className='btn-info'
                  onClick={() => onSubmit('submit', null, false, documentType, DOCUMENT_STATUSES.CONFIRMED.status)}
                  loading={isSubmitting === 'submit'}
                  disabled={!!(isSubmitting && isSubmitting !== 'submit')}
                >
                  Sauvegarder et quitter
                </Button>
              )}
            {/*show in update draft quote*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.DRAFT.key && (
                <Button
                  className='btn-info'
                  onClick={() => onSubmit('submit', null, false, documentType, DOCUMENT_STATUSES.DRAFT.status)}
                  loading={isSubmitting === 'submit'}
                  // disabled={isSubmitting && isSubmitting !== 'submit' ? true : false}
                >
                  Enregistrer en brouillon
                </Button>
              )}
            {/*show in update draft order*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.ORDER &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.DRAFT.key && (
                <Button
                  className='btn-info'
                  onClick={() => onSubmit('submit', null, false, documentType, DOCUMENT_STATUSES.DRAFT.status)}
                  loading={isSubmitting === 'submit'}
                  disabled={isSubmitting && isSubmitting !== 'submit' ? true : false}
                >
                  Renregistrer
                </Button>
              )}
            {/*show in update all status quote*/}
            {quotationData.document?.DocumentType?.key === 'quotation' && (
              <Button
                className='devis-page__btn__send btn-pink'
                onClick={getEstimatePdf}
                // loading={isSubmitting === 'open'}
                // disabled={!!(isSubmitting && isSubmitting !== 'open')}
              >
                Prévisualisation
              </Button>
            )}
            {/*show in update all update form document*/}
            <Button
              className='btn-warning'
              onClick={onDuplicatedSubmit}
              // loading={isSubmitting === 'open'}
              // disabled={!!(isSubmitting && isSubmitting !== 'open')}
            >
              Creer une copie
            </Button>
            {/*show in update draft quote*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.DRAFT.key && (
                <Button onClick={() => handleOpenLogDemandeModal()} className='devis-page__btn__info'>
                  Demander un prix à la log
                </Button>
              )}
            {/*show in update draft quote*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.DRAFT.key && (
                <Button
                  className='btn-success'
                  onClick={() =>
                    onSubmit('sent', null, false, DOCUMENT_TYPES?.QUOTATION, DOCUMENT_STATUSES.SENT.status)
                  }
                  disabled={isSubmitting && isSubmitting !== 'sent' ? true : false}
                  loading={isSubmitting === 'sent'}
                >
                  Valider et envoyer
                </Button>
              )}
            {/*show in update sent quote*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.SENT.key && (
                <Button
                  className='devis-page__btn__send'
                  onClick={() =>
                    onSubmit('re-sent', null, false, DOCUMENT_TYPES?.QUOTATION, DOCUMENT_STATUSES.SENT.status)
                  }
                  loading={isSubmitting === 're-sent'}
                  disabled={isSubmitting && isSubmitting !== 're-sent' ? true : false}
                >
                  Renvoyer le devis
                </Button>
              )}
            {/* {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.SENT.key && (
                <Button
                  className='devis-page__btn__marquer'
                  onClick={() =>
                    onSubmit(
                      'marquer-come-accepte',
                      null,
                      false,
                      DOCUMENT_TYPES?.QUOTATION,
                      DOCUMENT_STATUSES.ACCEPTED.status,
                    )
                  }
                  loading={isSubmitting === 'marquer-come-accepte'}
                  disabled={isSubmitting && isSubmitting !== 'marquer-come-accepte' ? true : false}
                >
                  Marquer comme accepté et créer une commande
                </Button>
              )} */}
            {/*show in update accepted quote*/}
            {quotationData.document?.DocumentType?.key === DOCUMENT_TYPES?.QUOTATION &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.ACCEPTED.key && (
                <Button className='devis-page__btn__send' onClick={onTransform}>
                  Transformer en Commande
                </Button>
              )}
            {/*show in update draft order*/}
            {quotationData.document?.DocumentType?.key === 'order' &&
              quotationData.document?.DocumentStatus?.key === DOCUMENT_STATUSES.DRAFT.key && (
                <Button
                  className='devis-page__btn__send'
                  onClick={() => onSubmit('open', null, false, documentType, DOCUMENT_STATUSES.CONFIRMED.status)}
                  loading={isSubmitting === 'open'}
                  // disabled={isSubmitting && isSubmitting !== 'open' ? true : false}
                >
                  Envoyer à la Logistique
                </Button>
              )}
          </section>
        </>
      </Spinner>
      <Modal
        title='Commande enregistrée dans ZOHO'
        open={isModalSyncDocumentSuccessOpen}
        closeIcon={<CloseOutlined onClick={() => setIsModalSyncDocumentSuccessOpen(false)} />}
        width={800}
        footer={[
          <Button key='fermer' onClick={() => handleCloseTab()} className='ant-modal-content__cancel-btn'>
            Fermer
          </Button>,
          <Button
            key='sur_la_fiche_client'
            type='primary'
            onClick={() => redirectZoho()}
            className='ant-modal-content__add-btn'
          >
            Aller sur la fiche client
          </Button>,
        ]}
      >
        <div style={{ width: '100%' }}>
          {objectDocument?.DocumentType?.key !== DOCUMENT_TYPES?.QUOTATION && (
            <>
              <CopyDataTextDocument
                label='Date de la commande'
                text={dayjs(objectDocument?.createdAt).format('DD/MM/YYYY') || ''}
              />
              <CopyDataTextDocument label='Client' text={contactName} />
              <CopyDataTextDocument
                label='Numéro de commande'
                text={objectDocument?.cdeZoho || ''}
                htmlContent={
                  objectDocument?.cdeZoho
                    ? `<a href="https://bo.ecodrop.net/order/${objectDocument?.booksDocumentId || ''}?lastmodify_by_id=${lastModifyById}">${objectDocument?.cdeZoho}</a>`
                    : ''
                }
              />
              <CopyDataTextDocument
                label='Date de la 1ère prestation'
                text={
                  objectDocument?.latestPrestationDate !== undefined && objectDocument?.latestPrestationDate !== null
                    ? dayjs(objectDocument?.latestPrestationDate).format('DD/MM/YYYY')
                    : ''
                }
              />
              <CopyDataTextDocument
                label='Montant Total HT'
                text={`${frenchCurrencyFormat((Number(subTotal) || 0).toFixed(2))} €`}
                className='copy-data-text__total'
              />
            </>
          )}
        </div>
      </Modal>
    </>
  );
};

export default EstimateDetail;
