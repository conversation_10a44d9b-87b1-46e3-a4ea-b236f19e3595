import { PageTitle, ValidateAndCreateOrderConfirmModal } from 'components/Common';
import { useEffect, useState, useCallback, useRef } from 'react';
import { MainTitle, Spinner } from 'components/Common';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button, Form, Divider, Typography } from 'antd';
import {
  ArrowLeftOutlined,
  DeleteOutlined,
  EditOutlined,
  ExclamationCircleFilled,
  FilePdfOutlined,
  InfoCircleFilled,
  UploadOutlined,
} from '@ant-design/icons';
import { useQueryParams, useMergeState } from '../../hooks';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchSales, selectSales } from 'store/slices/sale.slices';
import { findContactByParams, setClientContact } from 'store/slices/client_contact.slice';
import {
  findContactPersonByParams,
  setClientContactPerson,
  setClientContactPersons,
} from 'store/slices/client_contact_person.slice';
import { selectClientContact } from 'store/slices/client_contact.slice';
import { findDocumentById } from '../../store/slices/document.slice';
import { ClientContact, ClientContactPerson, Demander, Documents, Quotation } from 'models';
import { DevisActionType, FileDetails, PaginationData, PaymentType, Address, UrbanCenterZoneIds } from 'types';
import { ClientChantier, DemanderUnPrix, DevisProducts, Logistique } from 'components/Estimate';
import { clientContactService, documentService, urbanCenterService } from 'services';
import {
  DOCUMENT_FORM,
  DOCUMENT_STATUSES,
  DOCUMENT_TYPES,
  FILE_UPLOAD_TYPES,
  PAYMENT_STATUS,
  ZOHO_DOCUMENT_SYNC_STATUS,
} from 'utils/constant';
import { convertBase64, duplicates, generateDocumentTitle, getBooksTaxId } from 'utils';
import { UploadFileCustom } from 'components/Estimate/FileUpload';
import { DevisProductsRef } from 'components/Estimate/DevisProducts';
import { fetchProductTypeUnit } from 'store/slices/product.slices';
import { generateDocumentData } from 'utils/document';
const { Text } = Typography;

const EstimateCreate = () => {
  const dispatch = useAppDispatch();
  const [query] = useQueryParams<{
    zcrm_contact_id: string;
    referent_id: string;
    lastmodify_by_id: string;
    lastmodify_by_id_books: string;
    create_account: string;
    estimate_id: string;
  }>();
  const contact = useAppSelector(selectClientContact);
  const [loading, setLoading] = useState<boolean | undefined>(true);
  const navigate = useNavigate();
  const location = useLocation();
  // Determine if the path is "/order" or "/quotation"
  const isOrder = location.pathname === '/order';
  const isQuotation = location.pathname === '/quotation';
  const [isOpenModalDuplicateClient, setIsOpenModalDuplicateClient] = useState<number>(-1);
  const documentType = isOrder ? DOCUMENT_TYPES?.ORDER : isQuotation ? DOCUMENT_TYPES?.QUOTATION : '';
  const [form] = Form.useForm();
  const [lastModifyById] = useState<string | undefined>(query?.lastmodify_by_id as string | undefined);
  const referentId = query?.referent_id as string;
  const zcrmContactIdInit = (query?.zcrm_contact_id as string) || null;
  const [zcrmContactId] = useState<string | null>(zcrmContactIdInit);
  const booksDocumentId = (query?.estimate_id as string) || (query?.order_id as string);
  const originDocumentType = query?.estimate_id
    ? DOCUMENT_TYPES?.QUOTATION
    : query?.order_id
      ? DOCUMENT_TYPES?.ORDER
      : undefined;
  const [quotationData, setQuotationData] = useMergeState<Quotation>({});
  const [isDocumentCatalog, setIsDocumentCatalog] = useState<boolean | null>(null);
  const [isSubmitting, setIsSubmitting] = useState<DevisActionType>(null);
  const [isDraft, setIsDraft] = useState<boolean>(false);
  const [isSubmitted, setIsSubmitted] = useState<boolean>(false);
  const [isOpenTransactionModal, setIsOpenTransactionModal] = useState<boolean>(false);
  const [isLogDemandeModalOpen, setIsLogDemandeModalOpen] = useState(false);
  const [paymentInfo, setPaymentInfo] = useState<PaymentType | null>(null);
  const [fileTransactionProof, setFileTransactionProof] = useState<FileDetails | null>(null);
  const [urbanCenterZoneIds, setUrbanCenterZoneIds] = useState<UrbanCenterZoneIds>({
    regionCatalogZoneIds: [],
    serviceProviderZoneIds: [],
    serviceProviderZones: [],
    countryRegionId: null,
  });
  const devisProductsRef = useRef<DevisProductsRef>(null);
  const [createAccount] = useState<string | undefined>(query?.create_account as string | undefined);
  const [isValidateAndCreateOrder, setIsValidateAndCreateOrder] = useState<boolean>(false);

  const sales = useAppSelector(selectSales);
  useEffect(() => {
    form.resetFields();
    initialData();
  }, []);

  useEffect(() => {
    if (documentType === DOCUMENT_TYPES.ORDER && contact) {
      console.log('contact: ', contact);
      if (contact?.enCompte === 'En compte') {
        setPaymentInfo({
          paiement: 'Aucun paiement',
          montantPaiement: 0,
        });
      } else {
        if (!booksDocumentId && contact) {
          // display the transaction proof popup if order has NO estiamte
          setIsOpenTransactionModal(true);
          setNotPaidPaymentInfo();
        }
        if (
          booksDocumentId &&
          quotationData?.document &&
          quotationData?.document?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
        ) {
          // display transaction proof popup if the status is not paid
          if (quotationData.document?.paymentStatus !== 'paid') {
            setIsOpenTransactionModal(true);
            setNotPaidPaymentInfo();
          }
        }
        if (
          booksDocumentId &&
          quotationData?.document &&
          quotationData?.document?.DocumentType?.key === DOCUMENT_TYPES.ORDER
        ) {
          // display transaction proof popup if there is no estmiate or the status is not paid
          if (!quotationData.document?.referentDocumentID || quotationData.document?.paiement === 'Virement') {
            setIsOpenTransactionModal(true);
          }
        }
      }
      if (quotationData?.document) {
        getEstimatePaymentInfo(quotationData.document, contact);
      }
    }
  }, [contact, quotationData.document]);

  const initialData = async () => {
    try {
      await Promise.all([getSales(), getDocumentStatusAndType(), getListProductTypeUnit()]);
      if (booksDocumentId) {
        const document = await getDevisById();
        await getUrbanCenterZoneIds({
          latitude: document?.siteAddressLatitude,
          longitude: document?.siteAddressLongitude,
        });
      } else {
        await getClientContact();
      }
    } catch (error) {
      console.error('An error occurred during initialization:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleBackToList = async () => {
    window.location.href = await getlinkZoho();
  };

  const getSales = useCallback(async () => {
    await dispatch(fetchSales({}))
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  }, [dispatch]);
  const getClientContact = useCallback(
    async (contactId?: string) => {
      let contact = {} as PaginationData<ClientContact>;
      if (contactId) {
        contact = await dispatch(findContactByParams({ id: contactId, include: 'ContactPersons' }))
          .unwrap()
          .catch(() => {
            toast.error('Erreur');
          });
      } else if (zcrmContactId) {
        contact = await dispatch(findContactByParams({ crmContactId: zcrmContactId, include: 'ContactPersons' }))
          .unwrap()
          .catch(() => {
            toast.error('Erreur');
          });
      }
      if (contact?.count > 0) {
        const clientContactData = contact?.rows?.length > 0 ? contact.rows[0] : ({} as ClientContact);
        if (clientContactData?.enCompte === PAYMENT_STATUS.enCompte) {
          setPaymentInfo({
            paiement: PAYMENT_STATUS.aucunPaiement,
            montantPaiement: 0,
          });
        }
        let data = {
          ...clientContactData,
        };
        if (!clientContactData?.crmContactId) {
          data = {
            ...clientContactData,
            crmContactId: clientContactData?.ContactPersons?.[0]?.crmContactId as string,
          };
          dispatch(setClientContact(data));
        }
        setQuotationData({
          clientContact: data,
        });
        dispatch(setClientContactPersons(clientContactData?.ContactPersons || []));
        dispatch(setClientContactPerson(null));
      } else {
        if (zcrmContactId) {
          const contactPersonData = (await dispatch(findContactPersonByParams({ crmContactId: zcrmContactId }))
            .unwrap()
            .catch(() => {
              toast.error('Erreur');
            })) as PaginationData<ClientContactPerson>;
          if (contactPersonData?.count > 0) {
            const contactData = await clientContactService.getContacts({
              id: contactPersonData?.rows?.[0]?.contactId,
              include: 'ContactPersons',
            });
            if (contactData?.count > 0) {
              const clientContactData = contactData?.rows?.length > 0 ? contactData.rows[0] : ({} as ClientContact);
              if (clientContactData?.enCompte === PAYMENT_STATUS.enCompte) {
                setPaymentInfo({
                  paiement: PAYMENT_STATUS.aucunPaiement,
                  montantPaiement: 0,
                });
              }
              const data = {
                ...clientContactData,
                crmContactId: contactPersonData?.rows?.[0]?.crmContactId,
              };
              setQuotationData({
                clientContact: data,
              });
              await Promise.all([
                dispatch(setClientContact(data)),
                dispatch(setClientContactPersons(clientContactData?.ContactPersons || [])),
              ]);
            }
          }
        }
      }
    },
    [dispatch],
  );

  const getDevisById = useCallback(async () => {
    if (booksDocumentId) {
      const documentRes = await dispatch(
        findDocumentById({
          documentId: booksDocumentId,
          params: {
            include:
              'EstimateDocument|DocumentCCLibresContacts|DocumentCCContacts|DocumentProductLines|DocumentProductLines.DocumentProductLinePrices|DocumentProductLines.DocumentProductLineSubOptions|DocumentProductLines.DocumentProductLinePrestations|DocumentProductLines.DocumentProductLinePrestations.DocumentProductLinePrestationSubOptions|DocumentProductLines.DocumentProductLinePrestations.DocumentProductLinePrestationStatus|DocumentType|DocumentStatus|DocumentFileUploads',
          },
        }),
      )
        .unwrap()
        .catch(() => {
          toast.error('Erreur');
        });
      if (documentRes && documentRes?.length > 0) {
        const data = documentRes?.length > 0 ? documentRes?.[0] : {};
        if (data?.contactId) {
          await getClientContact(data?.contactId.toString());
        }
        if (documentType === DOCUMENT_TYPES.ORDER) {
          getOrderPaymentInfo(data);
        }
        setQuotationData({
          document: data,
        });
        return data;
      } else {
        window.location.href = getZQORedirectUrl();
      }
    }
  }, [dispatch]);

  const getZQORedirectUrl = () => {
    return documentType === DOCUMENT_TYPES.QUOTATION
      ? `${process.env.REACT_APP_ZQO_PATH}?lastmodify_by_id=${lastModifyById}&estimate_id=${booksDocumentId}`
      : originDocumentType === DOCUMENT_TYPES.ORDER
        ? `${process.env.REACT_APP_ZQO_PATH}/order?lastmodify_by_id=${lastModifyById}&order_id=${booksDocumentId}`
        : `${process.env.REACT_APP_ZQO_PATH}/order?lastmodify_by_id=${lastModifyById}&estimate_id=${booksDocumentId}`;
  };
  const getListProductTypeUnit = useCallback(async () => {
    try {
      await dispatch(fetchProductTypeUnit({})).unwrap();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  }, [dispatch]);
  const setNotPaidPaymentInfo = () => {
    setPaymentInfo({
      montantPaiement: 0,
      paiement: 'Virement',
    });
  };

  const onChangeAddressChantier = async (address: Address | null, isChangePrice?: boolean) => {
    const urbanCenterZoneIds = await getUrbanCenterZoneIds(address);
    if (isChangePrice) {
      devisProductsRef.current?.updateIsCatalog(urbanCenterZoneIds?.isDocumentCatalog);
    }
    await devisProductsRef.current?.refreshCatalogPrice({
      zoneIds: urbanCenterZoneIds,
      isChangePrice,
      isDocumentCatalog: urbanCenterZoneIds?.isDocumentCatalog,
    });
  };

  const getUrbanCenterZoneIds = async (address: Address | null) => {
    if (address?.latitude && address?.longitude) {
      const zones = await urbanCenterService.getZonesByChantierAddress(address?.latitude, address?.longitude);
      const regionCatalogZoneIds = zones?.regionCatalogZoneIds?.map((zone) => zone.zoneId);
      const serviceProviderZoneIds = zones?.serviceProviderZoneIds?.map((zone) => zone.zoneId);
      const serviceProviderZones = zones?.serviceProviderZones;
      const countryRegionId = zones?.countryRegionId;
      const isDocumentCatalog =
        regionCatalogZoneIds?.length > 0 ? true : serviceProviderZoneIds?.length > 0 ? false : null;
      const urbanCenterZoneIds = {
        regionCatalogZoneIds,
        serviceProviderZoneIds,
        serviceProviderZones,
        isDocumentCatalog,
        countryRegionId,
      };
      setIsDocumentCatalog(isDocumentCatalog);
      setUrbanCenterZoneIds(urbanCenterZoneIds);
      return urbanCenterZoneIds;
    }
    setIsDocumentCatalog(null);
    return {
      regionCatalogZoneIds: [],
      serviceProviderZoneIds: [],
      serviceProviderZones: [],
      isDocumentCatalog: null,
    };
  };

  const getEstimatePaymentInfo = (document: Documents, contact?: ClientContact | null) => {
    if (contact?.enCompte !== 'En compte') {
      if (document?.paymentStatus === 'paid') {
        if (document?.paymentType) {
          const paymentType = document?.paymentType;
          const paymentInfo = {
            montantPaiement: document?.retainerInvoiceSubTotal ?? 0,
          } as PaymentType;
          if (paymentType?.toLocaleLowerCase()?.localeCompare('stripe') === 0) {
            paymentInfo.message = `Paiement CB effectué - ${document?.retainerInvoiceSubTotal ?? 0} € HT`;
            paymentInfo['paiement'] = 'Paiement CB effectué';
          } else {
            paymentInfo.message = `Virement effectué - ${document?.retainerInvoiceSubTotal ?? 0} € HT`;
            paymentInfo['paiement'] = 'Virement effectué';
          }
          setPaymentInfo(paymentInfo);
        }
      }
    } else if (contact?.enCompte === 'En compte') {
      setPaymentInfo({
        paiement: 'Aucun paiement',
        montantPaiement: 0,
      });
    }
  };

  const getOrderPaymentInfo = (document: Documents) => {
    if (document?.paiement) {
      const paiement = document?.paiement;
      const montantPaiement = document?.montantPaiement;
      setPaymentInfo({
        message: paiement !== 'Virement' ? `${paiement} - ${montantPaiement} € HT` : '',
        paiement,
        montantPaiement,
      });
    }
    if (contact?.enCompte === 'En compte') {
      setPaymentInfo({
        paiement: 'Aucun paiement',
        montantPaiement: 0,
      });
    }
  };

  const getDocumentStatusAndType = async () => {
    try {
      const [documentTypes, documentStatus, documentProductLinePrestationStatus] = await Promise.all([
        documentService.getDocumentTypes(),
        documentService.getDocumentStatus(),
        documentService.getDocumentProductLinePrestationStatus(),
      ]);
      setQuotationData({
        documentTypes: documentTypes?.rows,
        documentStatus: documentStatus,
        documentProductLinePrestationStatus: documentProductLinePrestationStatus?.rows,
      });
    } catch (error) {
      console.log(error);
    }
  };
  const handleQuitter = async () => {
    window.location.href = await getlinkZoho();
  };
  const getlinkZoho = async (crmContactId?: string) => {
    let redirectId = contact?.crmContactId ?? quotationData?.clientContact?.crmContactId;
    if (!redirectId) {
      const contactData = await clientContactService.findContactByParams({
        id: contact?.id,
        include: 'ContactPersons',
      });
      const clientContactData: ClientContact = contactData?.rows?.length > 0 ? contactData?.rows[0] : [];
      if (clientContactData?.crmContactId) {
        redirectId = clientContactData?.crmContactId;
      }
    }
    if (crmContactId) {
      redirectId = crmContactId;
    }
    return process.env.REACT_APP_ZOHO_PATH + `/${redirectId}`;
  };
  const handleOpenLogDemandeModal = async () => {
    try {
      setIsDraft(true);
      await form.validateFields();
      const values = form.getFieldsValue();
      const lineItems = Object.keys(values?.lineItems || []).map((key) => ({
        ...values?.lineItems?.[key],
      }));
      const lineItemHasProduct = lineItems?.some((item) => item?.productId);
      if (lineItems && lineItems?.length > 0 && lineItemHasProduct) {
        setIsLogDemandeModalOpen(true);
      } else {
        toast.error(
          'Merci d’ajouter un produit et de remplir les champs obligatoires dans le devis avant de l’envoyer à la LOG',
        );
      }
    } catch (error) {
      console.log(error);
      toast.error(
        'Merci d’ajouter un produit et de remplir les champs obligatoires dans le devis avant de l’envoyer à la LOG',
      );
    }
  };

  const onSubmit = async (
    actionType: DevisActionType = 'submit',
    valueDemander: Demander | null = null,
    isCopiedSubmit?: boolean,
    submitType?: string,
    submitStatus?: string,
  ) => {
    setIsDraft(actionType === 'submit' || actionType === 'demander');
    setIsSubmitted(true);
    try {
      setIsDraft(actionType === 'submit' || actionType === 'demander');
      const vaild = await form.validateFields();
      if (
        query?.estimate_id &&
        isOrder &&
        !(
          quotationData?.document?.DocumentStatus?.key === DOCUMENT_STATUSES.ACCEPTED.key &&
          quotationData?.document?.DocumentType?.key === DOCUMENT_TYPES.QUOTATION
        )
      ) {
        toast.error(
          <div>
            Erreur
            <br />
            Veuillez noter que seul un Devis accepté peut être converti en commande client.
          </div>,
        );
      } else {
        if (vaild) {
          const booksTaxId = getBooksTaxId(contact?.taxPercentage ?? null);
          if (booksTaxId) {
            setIsSubmitting(actionType);
            const values = form.getFieldsValue();
            console.log('values: ', values);
            if (values.email && values.email?.length > 5) {
              throw new Error('Les cc gratuits ont atteint leur quantité maximale');
            }
            const countryRegionId = urbanCenterZoneIds?.countryRegionId;
            const submitData = await generateDocumentData({
              quotationData,
              contact,
              sales,
              values,
              submitType,
              submitStatus,
              lastModifyById,
              countryRegionId,
              booksTaxId,
              documentType,
              paymentInfo,
              actionType,
              valueDemander,
            })();
            if (documentType === DOCUMENT_TYPES.ORDER && quotationData?.document?.EstimateDocument) {
              submitData.referentDocumentID = quotationData?.document?.EstimateDocument?.id;
            }
            const documentStatus = quotationData?.documentStatus?.filter((item) => item.key === submitStatus);
            submitData.syncStatus = ZOHO_DOCUMENT_SYNC_STATUS.IN_PROGRESS;
            submitData.documentStatus = documentStatus && documentStatus[0]?.key;
            submitData.fileUpload = quotationData?.fileUpload;
            if (fileTransactionProof && documentType === DOCUMENT_TYPES?.ORDER) {
              submitData.paymentProof = {
                name: fileTransactionProof.name,
                url: fileTransactionProof.keyFile,
                type: FILE_UPLOAD_TYPES.PAYMENT_PROOF,
              };
            }
            submitData.genererLienDePaiement = null;
            if (documentType === DOCUMENT_TYPES.ORDER && quotationData?.document?.cdeZoho) {
              submitData.estimateNumber = query?.order_id
                ? quotationData?.document?.EstimateDocument?.cdeZoho || ''
                : quotationData?.document?.cdeZoho || '';
            }
            if (documentType === DOCUMENT_TYPES?.ORDER && booksDocumentId) {
              submitData.estimateId = booksDocumentId;
            }

            console.log('submitData', submitData);
            // return;
            let res = null;
            if (actionType === 'validate-and-create-order') {
              res = await documentService.createEstimateAndOrder(submitData);
            } else {
              res = await documentService.createDocument(submitData);
            }
            if (res?.id) {
              const linkZohoRedirect = await getlinkZoho(submitData?.contactPerson?.crmContactId);
              console.log('linkZohoRedirect: ', linkZohoRedirect);
              if (actionType === 'validate-and-create-order') {
                navigate(`/order/${res?.id}?lastmodify_by_id=${lastModifyById}`, {
                  state: {
                    redirectedFrom: DOCUMENT_FORM.CREATE,
                    isCheckSyncDocument: true,
                    linkZohoRedirect: linkZohoRedirect,
                  },
                });
              } else {
                navigate(`/${documentType}/${res?.id}?lastmodify_by_id=${lastModifyById}`, {
                  state: {
                    redirectedFrom: DOCUMENT_FORM.CREATE,
                    isCheckSyncDocument: true,
                    linkZohoRedirect: linkZohoRedirect,
                  },
                });
              }
            }

            setIsSubmitting(null);
          } else {
            toast.error(
              <div>
                Erreur
                <br />
                {
                  "L'identifiant fiscal Zoho n'est pas encore synchronisé. Veuillez patienter quelques instants avant de créer ou de mettre à jour le document. Si cela persiste merci de contacter le service IT"
                }
              </div>,
            );
          }
        }
      }
    } catch (error) {
      console.log(error);
      // setIsSubmitted(false);
      setIsSubmitting(null);
      const values = form.getFieldsValue() || {};
      const duplicatedUuids = duplicates(values?.lineItems || {});
      if (duplicatedUuids && duplicatedUuids?.size > 0) {
        toast.error('Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.');
      } else {
        toast.error(
          <div>
            Erreur
            <br />
            {(error as Error)?.message ?? ''}
          </div>,
        );
      }
    }
  };

  const handleUploadProofFile = async (file: UploadFileCustom) => {
    try {
      const fileUpload = {
        ...file,
        name: file.file_name,
        status_file: 'CREATE',
        type: FILE_UPLOAD_TYPES.PAYMENT_PROOF,
        dataBase64: file.originFileObj ? ((await convertBase64(file.originFileObj)) as string) : null,
      };
      setFileTransactionProof(fileUpload);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const title = generateDocumentTitle(documentType, query, quotationData, 'CREATE');
  return (
    <>
      <PageTitle>Devis Create</PageTitle>
      <Spinner loading={loading}>
        <>
          <MainTitle
            parent={documentType === 'order' ? 'COMMANDES' : 'DEVIS'}
            child={title}
            type={documentType}
            status={'new'}
          />
          <div className='back-to-product-list'>
            <Button type='link' className='back-to-product-list-btn' onClick={handleBackToList}>
              <ArrowLeftOutlined className='left-arrow-icon' />
              Retour fiche client
            </Button>
          </div>
          {documentType === DOCUMENT_TYPES?.QUOTATION && (
            <DemanderUnPrix
              submit={onSubmit}
              isOpenModal={isLogDemandeModalOpen}
              setIsOpenModal={setIsLogDemandeModalOpen}
              data={quotationData}
              lastModifyById={lastModifyById}
              contact={contact}
              type='createEstimate'
            />
          )}
          <Form layout='horizontal' form={form} className='create-estimate-form'>
            <ClientChantier
              isSubmitted={isSubmitted}
              documentType={documentType}
              form={form}
              lastModifyById={query?.lastmodify_by_id}
              referentId={referentId}
              data={quotationData}
              setData={setQuotationData}
              getClientContact={getClientContact}
              isSaveDraft={isDraft}
              isOpenTransactionModal={isOpenTransactionModal}
              setIsOpenTransactionModal={setIsOpenTransactionModal}
              onDataProofFile={(file: UploadFileCustom) => handleUploadProofFile(file)}
              onChangeAddressChantier={onChangeAddressChantier}
              isCopyForm={(isQuotation && query?.estimate_id) || (isOrder && query?.order_id)}
              isCreateForm={true}
              isTransForm={query?.estimate_id ? true : false}
              createAccount={createAccount}
              isOpenModalDuplicateClient={isOpenModalDuplicateClient}
              setIsOpenModalDuplicateClient={setIsOpenModalDuplicateClient}
              zcrm_contact_id={zcrmContactId}
            />

            {documentType === DOCUMENT_TYPES.ORDER ? (
              <div className='order-payment-info-wrapper mt-5'>
                <div className='line'></div>
                {documentType === DOCUMENT_TYPES.ORDER && paymentInfo?.message && contact?.enCompte !== 'En compte' && (
                  <div className='centered-div'>
                    <ExclamationCircleFilled style={{ color: '#FAAD14', marginRight: 10 }} />
                    {paymentInfo?.message}
                  </div>
                )}
                {contact?.enCompte !== 'En compte' &&
                  (!quotationData?.document || quotationData?.document?.paymentStatus !== 'paid') &&
                  (!quotationData?.document ||
                    !quotationData?.document?.referentDocumentID ||
                    quotationData?.document?.paiement === 'Virement') && (
                    <div className='centered-div-popup'>
                      {!fileTransactionProof ? (
                        <>
                          <div
                            className='px-4 py-1 d-inline-block'
                            style={{ borderRadius: '5px', boxShadow: '2px 2px 5px rgba(0,0,0,0.5)' }}
                          >
                            <InfoCircleFilled className='mr-2 color_status_draft' style={{ color: 'orange' }} />
                            <Text>Virement, besoin de la preuve de virement</Text>
                          </div>
                          <Button
                            disabled={false}
                            className='ant-btn-white bnt-upload-file ml-2'
                            onClick={() => setIsOpenTransactionModal(!isOpenTransactionModal)}
                            icon={<UploadOutlined />}
                          ></Button>
                        </>
                      ) : (
                        <>
                          <div
                            className='px-4 py-1 d-inline-block'
                            style={{ borderRadius: '5px', boxShadow: '2px 2px 5px rgba(0,0,0,0.5)' }}
                          >
                            <a>
                              <FilePdfOutlined style={{ color: '#95C515' }} />
                              <span className='ml-1 mr-2' style={{ color: '#95C515' }}>
                                {fileTransactionProof?.file_name}
                              </span>
                            </a>
                            <DeleteOutlined
                              onClick={() => setFileTransactionProof(null)}
                              style={{ cursor: 'pointer', color: 'red' }}
                            />
                          </div>
                          <Button
                            disabled={false}
                            className='ml-2 ant-btn-white-green-border'
                            onClick={() => setIsOpenTransactionModal(!isOpenTransactionModal)}
                            icon={<EditOutlined />}
                          />
                        </>
                      )}
                    </div>
                  )}
              </div>
            ) : (
              <Divider className='horizontal-bar' style={{ marginBottom: '44px' }} />
            )}

            <Logistique type={documentType} form={form} contactData={contact} data={quotationData} />

            <DevisProducts
              form={form}
              data={quotationData}
              documentType={documentType}
              contact={contact}
              isDocumentCatalog={isDocumentCatalog}
              urbanCenterZoneIds={urbanCenterZoneIds}
              ref={devisProductsRef}
              loading={loading}
            />
          </Form>
          <section className='mt-2 section actions-block'>
            {/*always show*/}
            <Button onClick={() => handleQuitter()} className='devis-page__btn__quit'>
              Quitter
            </Button>
            {/*always show in create form*/}
            <Button
              className='devis-page__btn__draft'
              onClick={() => onSubmit('submit', null, false, documentType, DOCUMENT_STATUSES.DRAFT.status)}
              loading={isSubmitting === 'submit'}
              // disabled={isSubmitting && isSubmitting !== 'submit' ? true : false}
            >
              Enregistrer en brouillon
            </Button>
            {/*show in create quote and update draft quote*/}
            {documentType === DOCUMENT_TYPES?.QUOTATION && (
              <Button
                onClick={() => handleOpenLogDemandeModal()}
                // loading={isSubmitting === 'submit'}
                // disabled={!!(isSubmitting && isSubmitting !== 'submit')}
                className='devis-page__btn__info'
              >
                Demander un prix à la log
              </Button>
            )}
            {/*show in create quote and update draft quote*/}
            {documentType === DOCUMENT_TYPES.QUOTATION && (
              <Button
                className='devis-page__btn__send'
                onClick={() => onSubmit('sent', null, false, documentType, DOCUMENT_STATUSES.SENT.status)}
                disabled={false}
                loading={isSubmitting === 'sent'}
              >
                Valider et envoyer
              </Button>
            )}
            {/*show in create quote*/}
            {documentType === DOCUMENT_TYPES.QUOTATION && (
              <>
                <Button
                  className='btn-red'
                  onClick={() => setIsValidateAndCreateOrder(true)}
                  disabled={!!(isSubmitting && isSubmitting !== 'validate-and-create-order')}
                  // Hide this button temporary - QBO-1635
                  hidden
                >
                  Valider et créer la commande
                </Button>
                <ValidateAndCreateOrderConfirmModal
                  isOpenModal={isValidateAndCreateOrder}
                  onCancel={() => setIsValidateAndCreateOrder(false)}
                  onConfirmation={async () => {
                    await onSubmit(
                      'validate-and-create-order',
                      null,
                      false,
                      documentType,
                      DOCUMENT_STATUSES.ACCEPTED.status,
                    );
                    setIsValidateAndCreateOrder(false);
                  }}
                  loading={isSubmitting === 'validate-and-create-order'}
                />
              </>
            )}

            {/*show in create order*/}
            {documentType === DOCUMENT_TYPES.ORDER && (
              <Button
                className='devis-page__btn__send'
                onClick={() => onSubmit('open', null, false, documentType, DOCUMENT_STATUSES.CONFIRMED.status)}
                loading={isSubmitting === 'open'}
                // disabled={isSubmitting && isSubmitting !== 'open' ? true : false}
              >
                Envoyer à la Logistique
              </Button>
            )}
          </section>
        </>
      </Spinner>
    </>
  );
};

export default EstimateCreate;
