import { useState, useEffect, useMemo} from 'react';
import { <PERSON>Title, MainTitle, Spinner } from 'components/Common';
import { Divider, Form, Row, Col, Button, Select, Input, Table, Tag} from 'antd';
import { ScaleLoader } from 'react-spinners';
import { fetchAllServiceProviders } from 'store/slices/service_provider.slices';
import {
  fetchProvidersInvoicesList,
  setFilters,
  selectInvoices,
  selectInvoicesLoading,
  selectFilters,
  selectExpandedComments,
  toggleExpandedComment
} from 'store/slices/provider_invoice.slices';
import { useAppDispatch, useAppSelector } from 'store';
import { toast } from 'react-toastify';
import { useNavigate } from 'react-router-dom';
import { Invoice, Comment } from 'types';

const statusOptions = [
    { id: 1, value: 'validée', label: 'Validée' },
    { id: 2, value: 'brouillon', label: 'Brouillon' },
    { id: 3, value: 'a_revoir', label: 'A revoir' },
];

interface InvoiceData {
  key: string;
  createdAt: string;
  validatedBy: string;
  invoiceNumber: string;
  prestataire: string;
  sellingPrice: number;
  orderPrice: number;
  invoicingPrice: number;
  avoir: number;
  marge: number;
  diffAchat: number;
  commentaire: string;
  status: string;
}

// Transform invoices from Redux store to table data format
const transformInvoicesToTableData = (invoices: Invoice[]) => {
  return invoices.map((invoice: Invoice, index: number) => ({
    key: invoice.id?.toString() || index.toString(),
    createdAt: invoice.createdAt ? new Date(invoice.createdAt).toLocaleDateString('fr-FR') : '-',
    validatedBy: invoice.createdBy || '-', // Default user for now
    invoiceNumber: invoice.prestataireInvoiceNumber || '-',
    prestataire: invoice.prestataire || '-',
    sellingPrice: invoice.totalAmount || 0,
    orderPrice: invoice.totalAmount || 0, // Using same value for now
    invoicingPrice: invoice.invoicedAmount || 0,
    avoir: invoice.totalAvoirs || 0,
    marge: 0, // Will be calculated later
    diffAchat: 0, // Will be calculated later
    commentaire: invoice.commentDetails?.length > 0 ? invoice.commentDetails.map((c: Comment) => c.comment).join(', ') : '-',
    status: invoice.status || 'brouillon',
  }));
};

interface FilterValues {
  prestataire?: string;
  invoiceNumber?: string;
  amount?: number | null;
  status?: string;
}

const ProviderInvoice = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();

  // Redux selectors
  const invoices = useAppSelector(selectInvoices);
  const invoicesLoading = useAppSelector(selectInvoicesLoading);
  const storeFilters = useAppSelector(selectFilters);
  const expandedComments = useAppSelector(selectExpandedComments);

  // Local state
  const [loading, setLoading] = useState<boolean>(false);
  const [serviceProviders, setServiceProviders] = useState<{ key: number; value: string; label: string }[]>([]);
  const [filteredInvoices, setFilteredInvoices] = useState<InvoiceData[]>([]);

  const isSaveButtonDisabled = useMemo(() => {
    return (
      !storeFilters.prestataire ||
      !storeFilters.invoiceNumber ||
      !storeFilters.amount ||
      filteredInvoices.length > 0
    );
  }, [storeFilters, filteredInvoices]);

  const simulateLoading = (timeout: number): void => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, timeout);
  };

  const handleFilterChange = (field: keyof FilterValues, value: string | number | null | undefined) => {
    dispatch(setFilters({ [field]: value }));
    simulateLoading(500);
  };

  const getAllServiceProviders = async () => {
    try {
      const result = await dispatch(fetchAllServiceProviders()).unwrap();
      const formattedProviders = result.rows.map((sp) => ({
        key: sp.id,
        value: sp.name,
        label: sp.name,
      }));
      setServiceProviders(formattedProviders);
    } catch (error) {
      toast.error('Erreur lors du chargement des prestataires');
    }
  };

  // Transform invoices from Redux store to table data and apply filters
  useEffect(() => {
    const tableData = transformInvoicesToTableData(invoices);
    let result = [...tableData];

    // Filtre par prestataire
    if (storeFilters.prestataire) {
      result = result.filter(item =>
        item.prestataire.toLowerCase().includes(storeFilters.prestataire!.toLowerCase())
      );
    }

    // Filtre par numéro de facture
    if (storeFilters.invoiceNumber) {
      result = result.filter(item =>
        item.invoiceNumber.toLowerCase().includes(storeFilters.invoiceNumber!.toLowerCase())
      );
    }

    // Filtre par montant
    if (storeFilters.amount !== null && storeFilters.amount !== undefined) {
      result = result.filter(item =>
        item.sellingPrice === storeFilters.amount
      );
    }

    // Filtre par statut
    if (storeFilters.status) {
      result = result.filter(item =>
        item.status.toLowerCase() === storeFilters.status!.toLowerCase()
      );
    }

    setFilteredInvoices(result);
  }, [invoices, storeFilters]);

  useEffect(() => {
    simulateLoading(2000);
    getAllServiceProviders();
    // Fetch initial invoice data
    dispatch(fetchProvidersInvoicesList({}));
  }, [dispatch]);

  const handleRowClick = (record: InvoiceData) => {
    // Navigate to edit page with ONLY mode, invoice ID, and prestataire
    navigate(`/logistique/factures-fournisseurs/${record.key}`, {
      state: {
        mode: 'edit',
        invoiceId: record.key,
        prestataire: record.prestataire // Pass prestataire for data fetching
      }
    });
  };

  const handleSaveInvoice = () => {
    // Navigate to CREATE mode with only essential data - no full invoice object
    navigate('/logistique/factures-fournisseurs/enregistrer-facture', {
      state: {
        mode: 'create',
        prestataire: storeFilters.prestataire || 'Prestataire inconnu',
        prestataireInvoiceNumber: storeFilters.invoiceNumber || 'N° FACTURE',
        invoicedAmount: storeFilters.amount || 0
      }
    });
  };

  const toggleComment = (key: string) => {
    dispatch(toggleExpandedComment(key));
  };

  return (
    <>
      <PageTitle>FACTURES FOURNISSEURS</PageTitle>
      <Spinner loading={loading}>
        <>
          <MainTitle parent="Validation" child="FACTURES FOURNISSEURS" />
          <Divider className="horizontal-bar" />
          <div className="product-page__searching">
            <Form layout="vertical">
              <Row gutter={24}>
                <Col span={4}>
                  <Form.Item
                    label="Sélection prestataire"
                    name="prestataire"
                    rules={[{ required: true }]}
                  >
                    <Select
                      placeholder="Prestataire"
                      showSearch
                      allowClear
                      options={serviceProviders}
                      onChange={(value) => handleFilterChange('prestataire', value)}
                      value={storeFilters.prestataire}
                    />
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label="N° de facture"
                    name="invoiceNumber"
                    rules={[{ required: true }]}
                  >
                    <Input 
                      placeholder='N° de facture' 
                      value={storeFilters.invoiceNumber}
                      onChange={(e) => handleFilterChange('invoiceNumber', e.target.value)}
                    />                                   
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label="Montant de la facture"
                    name="amount"
                    rules={[{ required: true }]}
                  >
                    <Input 
                      placeholder='Montant' 
                      type="number"
                      value={storeFilters.amount || ''}
                      onChange={(e) => handleFilterChange('amount', e.target.value ? Number(e.target.value) : null)}
                    />   
                  </Form.Item>
                </Col>
                <Col span={4}>
                  <Form.Item
                    label="Statut de commande"
                    name="status"
                  >
                    <Select
                      placeholder="Statut"
                      showSearch
                      allowClear
                      options={statusOptions.map(option => ({
                        value: option.value,
                        label: option.label,
                      }))}
                      onChange={(value) => handleFilterChange('status', value)}
                      value={storeFilters.status}
                    />
                  </Form.Item>
                </Col>
                <Col span={6}>
                  <Form.Item label=" " name="action">
                    <Button 
                      className={isSaveButtonDisabled ? 'btn-grey' : 'btn-success'}
                      onClick={handleSaveInvoice}
                      disabled={isSaveButtonDisabled}
                      style={{
                        fontWeight: 500,
                        cursor: isSaveButtonDisabled ? 'not-allowed' : 'pointer'
                      }}
                    >
                      Enregister la facture
                    </Button>
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
          <Divider className="horizontal-bar" />
          <Table
            columns={[
              { title: 'Date', dataIndex: 'createdAt', key: 'createdAt',
                render: (_, record: InvoiceData) => (
                  <>
                    <div>{record.createdAt}</div>
                    <div style={{ fontSize: 12, color: '#888' }}>{record.validatedBy}</div>
                  </>
                )
              },
              { title: 'N° Facture', dataIndex: 'invoiceNumber', key: 'invoiceNumber' },
              { title: 'Prestataire', dataIndex: 'prestataire', key: 'prestataire' },
              { title: 'Prix de vente', dataIndex: 'sellingPrice', key: 'sellingPrice' },
              { title: 'Prix Com.', dataIndex: 'orderPrice', key: 'orderPrice' },
              { title: 'Prix facturé', dataIndex: 'invoicingPrice', key: 'invoicingPrice' },
              { title: 'Avoir', dataIndex: 'avoir', key: 'avoir' },
              { title: 'Marge', dataIndex: 'marge', key: 'marge' },
              { title: 'Diff Achat', dataIndex: 'diffAchat', key: 'diffAchat' },
              { title: 'Commentaire', dataIndex: 'commentaire', key: 'commentaire',  width: 250, 
                render: (text: string, record) => {
                  const isExpanded = expandedComments.includes(record.key);
                  if (!text) return null;

                  return (
                    <>
                      {isExpanded ? text : text.slice(0, 100)}
                      {text.length > 100 && (
                        <Button 
                          type="link" 
                          onClick={(e) => {
                            e.stopPropagation(); // <- stoppe le clic sur la ligne
                            toggleComment(record.key);
                          }}
                        >
                          {isExpanded ? 'Moins' : 'Plus'}
                        </Button>
                      )}
                    </>
                  );
                }
              },
              { title: 'Statut', dataIndex: 'status', key: 'status',
                render: (status: string) => {
                  if (status === 'validée') {
                    return <Tag color="green">Validée</Tag>;
                  } else if (status === 'brouillon') {
                    return <Tag color="orange">Brouillon</Tag>;
                  } else if (status === 'a_revoir') {
                    return <Tag color="red">À revoir</Tag>;
                  }
                }
              }
            ]}
            dataSource={filteredInvoices}
            pagination={false}
            className='product-page__datatable'
            rowKey="key"
            loading={{
              indicator: <ScaleLoader color='#A6C84D' />,
              spinning: invoicesLoading === 'pending',
            }}
            onRow={(record) => ({
              onClick: () => handleRowClick(record),
              style: { cursor: 'pointer' },
            })}
          />
        </>
      </Spinner>
    </>
  );
};

export default ProviderInvoice;