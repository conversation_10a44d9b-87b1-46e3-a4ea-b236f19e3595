import { PageTitle } from 'components/Common';
import { <PERSON>ert, Button, Space } from 'antd';
import { MainTitle, Spinner } from 'components/Common';
import { useState, useEffect, useCallback } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useAppDispatch, useAppSelector } from 'store';
import { useParams } from 'react-router-dom';
import {
  findProductTypeById,
  selectProductType,
  selectProductTypeLoading,
  fetchProducts,
  fetchProductTypeUnit,
  fetchProductTypeInterventions,
  fetchFacturationType,
  selectProducts,
} from 'store/slices/product.slices';
import { Product, ProductType } from 'models';
import { productService } from 'services';
import { toast } from 'react-toastify';
import { selectOptionTypesLoading, fetchOptionTypes } from 'store/slices/option.slices';
import {
  ProductInformation,
  ProductOption,
  ProductVariation,
  ProductRegul,
  ProductDescription,
  ProductExclusions,
  ProductTarifs,
} from 'components/Product';
import { fetchPriceTypes } from 'store/slices/price.slices';
import dayjs from 'dayjs';
import { PRODUCT_SYNC_STATUS } from 'utils/constant';

const ProductDetail = () => {
  const dispatch = useAppDispatch();
  const params = useParams();
  const productTypeId = parseInt(params.productTypeId as string);
  const [productTypeName, setProductTypeName] = useState<string | undefined>('');
  const productType: ProductType | null = useAppSelector(selectProductType);
  const optionTypesLoading = useAppSelector(selectOptionTypesLoading);
  const [loading, setLoading] = useState<boolean>(false);
  const [isExport, setIsExport] = useState<boolean>(false);
  const [isGeneratingProduct, setIsGeneratingProduct] = useState<boolean>(false);
  const [messageSyncZohoProduct, setMessageSyncZohoProduct] = useState<string>('');
  const productTypeLoading = useAppSelector(selectProductTypeLoading);
  const navigate = useNavigate();
  const location = useLocation();
  const originProducts = useAppSelector(selectProducts);

  const handleBackToProductPage = () => {
    const search = location?.state?.search ?? '';
    navigate('/products' + search);
  };
  const initData = async () => {
    try {
      await Promise.all([
        getProductType(productTypeId),
        getOptionTypes(),
        getProducts(),
        getListPriceTypes(),
        getListProductTypeUnit(),
        getListProductTypeIntervention(),
        getFacturationType(),
      ]);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  useEffect(() => {
    initData();
  }, []);

  useEffect(() => {
    setMessageSyncZohoProduct('');
    if (originProducts && originProducts.length > 0) {
      checkAndShowMessageProductSync();
    }
  }, [originProducts]);

  const checkAndShowMessageProductSync = () => {
    const isProductEligible = (product: Product) => product.isActive && product.isVisible;
    const isCurrentTimeBetween = (currentHour: number) =>
      // Current time is in between 5h → 23h
      currentHour >= 5 && currentHour < 23;
    const currentDateTime = dayjs();
    const yesterday = currentDateTime.subtract(1, 'day');
    const currentDate = currentDateTime.format('DD-MM-YYYY');
    const currentHour = Number(currentDateTime.format('HH'));
    console.log('currentDate: ', currentDate);
    console.log('currentHour: ', currentHour);
    let isErrorSyncZohoProduct = false;
    // Sync process finishes & successes
    let messageSyncZohoProduct = '';
    // Current time is in between 5h → 23h
    if (isCurrentTimeBetween(currentHour)) {
      const itemSync = originProducts.find(function (product) {
        const updatedAt = dayjs(product.updatedAt);
        const lastSyncZohoDate = dayjs(product.lastSyncZohoDate);
        console.log('updatedAt: ', updatedAt);
        console.log('lastSyncZohoDate: ', lastSyncZohoDate);
        console.log('updatedAt.isAfter(lastSyncZohoDate): ', updatedAt.isAfter(lastSyncZohoDate));
        return (
          isProductEligible(product) &&
          (updatedAt.isAfter(lastSyncZohoDate) ||
            (product.lastSyncZohoDate === null && product.exclude === false) ||
            ((product.syncStatus === PRODUCT_SYNC_STATUS.IN_PROGRESS ||
              product.syncStatus === PRODUCT_SYNC_STATUS.FAILED) &&
              lastSyncZohoDate.format('DD-MM-YYYY') == yesterday.format('DD-MM-YYYY')))
        );
      });
      if (itemSync) {
        // User generate/ exclude products
        // display message #1
        messageSyncZohoProduct = `Synchronisation en attente, date de synchronisation ${currentDate} 23:00`;
        isErrorSyncZohoProduct = true;
      }
    }
    if (isErrorSyncZohoProduct === false) {
      const itemInProgress = originProducts.find(function (product) {
        return isProductEligible(product) && product.syncStatus === PRODUCT_SYNC_STATUS.IN_PROGRESS;
      });
      if (itemInProgress) {
        // Sync status process
        // display message #2
        messageSyncZohoProduct = `Synchronisation en cours, ne pas mettre à jour les produits`;
        isErrorSyncZohoProduct = true;
      }
    }
    if (isErrorSyncZohoProduct === false) {
      const itemFailed = originProducts.find(function (product) {
        return isProductEligible(product) && product.syncStatus === PRODUCT_SYNC_STATUS.FAILED;
      });
      if (itemFailed) {
        // Sync status error
        // display message #4
        messageSyncZohoProduct = `Échec de la synchronisation des données avec Zoho. Veuillez contacter le support technique.`;
        isErrorSyncZohoProduct = true;
      }
    }
    if (isErrorSyncZohoProduct === false) {
      const itemNotSuccess = originProducts.find(
        (product) =>
          isProductEligible(product) &&
          product.syncStatus !== PRODUCT_SYNC_STATUS.SUCCESS &&
          !(product.booksProductId == null && product.exclude === true),
      );
      if (!itemNotSuccess) {
        // Sync process finishes & successes
        // display message #3
        const maxDate = Math.max(
          ...originProducts
            .filter((product) => !(product.booksProductId == null && product.exclude === true))
            .map((product) => dayjs(product.lastSyncZohoDate).unix()),
        );
        let lastSyncZohoDateMax = '';
        if (dayjs(maxDate * 1000).isValid()) {
          lastSyncZohoDateMax = dayjs(maxDate * 1000).format('DD/MM/YYYY');
        }
        console.log('maxDate: ', maxDate);
        messageSyncZohoProduct = `Synchronisation effectuée le ${lastSyncZohoDateMax}`;
      }
    }
    setMessageSyncZohoProduct(messageSyncZohoProduct);
  };

  const getOptionTypes = async () => {
    await dispatch(fetchOptionTypes())
      .unwrap()
      .catch((error) => {
        console.log(error);
        toast.error('Erreur');
      });
  };

  const getProducts = async () => {
    await dispatch(fetchProducts({ productTypeId, limit: 'unlimited', orderBy: 'name' }))
      .unwrap()
      .catch((error) => {
        console.log(error);
        toast.error('Erreur');
      });
  };

  const getProductType = async (productTypeId: number) => {
    await dispatch(findProductTypeById(productTypeId))
      .unwrap()
      .then(() => {
        setLoading(false);
      })
      .catch((error) => {
        console.log(error);
        setLoading(false);
        toast.error('Erreur');
      });
  };

  const getListPriceTypes = async () => {
    await dispatch(fetchPriceTypes({})).unwrap();
  };

  const getListProductTypeUnit = useCallback(async () => {
    try {
      await dispatch(fetchProductTypeUnit({})).unwrap();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  }, [dispatch]);

  const getListProductTypeIntervention = useCallback(async () => {
    try {
      await dispatch(fetchProductTypeInterventions({})).unwrap();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  }, [dispatch]);
  const getFacturationType = useCallback(async () => {
    try {
      await dispatch(fetchFacturationType({})).unwrap();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  }, [dispatch]);

  const handleUpdateProductType = async (
    productTypeId: number,
    value: {
      titleOfProduct?: string;
      purchaseAccountingCode?: string;
      salesAccountingCode?: string;
      productTypeUnitId?: number | string;
      interventionId?: number | string;
      facturationTypeId?: number | string;
    },
  ) => {
    try {
      const result: ProductType = await productService.updateProductType(productTypeId, {
        ...(value?.titleOfProduct ? { name: value.titleOfProduct } : {}),
        ...(value?.purchaseAccountingCode ? { purchaseAccountingCode: value.purchaseAccountingCode } : {}),
        ...(value?.salesAccountingCode ? { salesAccountingCode: value.salesAccountingCode } : {}),
        ...(value?.productTypeUnitId ? { productTypeUnitId: value.productTypeUnitId } : {}),
        ...(value?.interventionId ? { interventionId: value.interventionId } : {}),
        ...(value?.facturationTypeId ? { facturationTypeId: value.facturationTypeId } : {}),
      });
      setProductTypeName(result?.name);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleGenerateProducts = async () => {
    try {
      setIsGeneratingProduct(true);
      await productService.generateProduct(productTypeId);
      getProducts();
      toast.success('Succès');
      setIsGeneratingProduct(false);
    } catch (error) {
      console.log(error);
      toast.error(
        'La liste des produits est en cours de synchronisation avec ZOHO, veuillez attendre la fin de la synchronisation avant de continuer à exclure ou à régénérer.',
      );
      setIsGeneratingProduct(false);
    }
  };

  const handleExportProduct = async () => {
    try {
      setIsExport(true);
      const res = await productService.exportProducts(productTypeId);
      setIsExport(false);
      const mediaType = 'data:application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;base64,';
      const downloadLink = document.createElement('a');
      const fileName = `${productType?.name}.xlsx`;
      downloadLink.href = mediaType + res;
      downloadLink.download = fileName;
      downloadLink.click();
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
      setIsExport(false);
    }
  };

  if (productTypeLoading === 'pending' || optionTypesLoading === 'pending') {
    return <Spinner />;
  }
  return (
    <>
      <PageTitle>TYPE DE PRODUIT</PageTitle>
      <Spinner loading={loading}>
        <>
          <MainTitle
            parent='TYPE DE PRODUIT'
            child={`${productTypeName || productType?.name}`}
            otherStyles={{ marginBottom: '7px' }}
          />
          <div className='back-to-product-list'>
            <Button type='link' className='back-to-product-list-btn' onClick={handleBackToProductPage}>
              <ArrowLeftOutlined className='left-arrow-icon' />
              Retour liste type de produit
            </Button>
          </div>
          <ProductInformation
            productType={productType}
            productTypeId={productTypeId}
            handleUpdateProductType={handleUpdateProductType}
          />
          <ProductOption productTypeId={productTypeId} productType={productType as ProductType} />
          <ProductVariation productTypeId={productTypeId} />
          <ProductRegul />
          <ProductDescription productTypeId={productTypeId} productType={productType} />
          {messageSyncZohoProduct.length > 0 && (
            <div className='product-form__button-block my-3'>
              <Alert message={messageSyncZohoProduct} type='warning' showIcon />
            </div>
          )}
          <div className='product-form__button-block'>
            <Space className='product-form__create-product-space'>
              <Button
                size='large'
                type='primary'
                htmlType='button'
                loading={isGeneratingProduct}
                className='product-form__create-product-button'
                onClick={handleGenerateProducts}
              >
                Générer les produits
              </Button>
            </Space>
            <Space className='product-form__create-product-space'>
              <Button
                onClick={handleExportProduct}
                size='large'
                type='primary'
                loading={isExport}
                htmlType='button'
                className='product-form__export-product-button'
              >
                Exporter les produits
              </Button>
            </Space>
          </div>
          <ProductExclusions productTypeId={productTypeId} getProducts={getProducts} />
          <ProductTarifs productTypeId={productTypeId} productType={productType} />
        </>
      </Spinner>
    </>
  );
};

export default ProductDetail;
