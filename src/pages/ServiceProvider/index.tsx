import { Space, Input } from 'antd';
import { AxiosError } from 'axios';
import { ButtonAdd, MainTitle, PageTitle } from 'components/Common';
import { CreateProviderModal, ServiceProviderList } from 'components/ServiceProvider';
import { useQueryParams } from 'hooks';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { toast } from 'react-toastify';
import { serviceProviderService } from 'services';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchServiceTypes, selectServiceTypes } from 'store/slices/service_provider.slices';
import { QueryParams } from 'types';
import { SERVICE_TYPES } from 'utils/constant';
import { Loading } from 'types';
import { ServiceProvider as ServiceProviderModel } from 'models';

const { Search } = Input;

export type ServiceProviderParams = QueryParams & {
  productType?: number | null | undefined;
  tableType?: number | null;
  urbanCenter?: number | null | undefined;
  zone?: number | null | undefined;
  keyword?: string;
};

const ServiceProvider = () => {
  const navigate = useNavigate();
  const dispatch = useAppDispatch();
  const location = useLocation();

  const serviceTypeKey = location.pathname.split('/').pop() as string;
  if (
    serviceTypeKey &&
    ![SERVICE_TYPES.benneur, SERVICE_TYPES.camionneur, SERVICE_TYPES.pup].includes(serviceTypeKey)
  ) {
    navigate('/');
  }
  const [query, onParamChange] = useQueryParams<ServiceProviderParams>() as [
    ServiceProviderParams,
    (query: ServiceProviderParams) => void,
  ];
  const [loading, setLoading] = useState<boolean>(true);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const serviceTypes = useAppSelector(selectServiceTypes);
  const serviceType = serviceTypes?.find((item) => item.key === serviceTypeKey);
  const [name, setName] = useState('');
  const [serviceProviders, setServiceProviders] = useState<ServiceProviderModel[]>([]);
  const [serviceProvidersLoading, setServiceProvidersLoading] = useState<Loading>('idle');
  const [serviceProvidersTotal, setServiceProvidersTotal] = useState(0);
  const [pageLoad, setPageLoad] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const NUMBER_ITEM_LOAD = 50;

  useEffect(() => {
    getServiceTypes();
  }, []);

  const getServiceTypes = async () => {
    await dispatch(fetchServiceTypes())
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
    setLoading(false);
  };

  const loadMoreData = () => {
    console.log('loadMoreData');
    setPageLoad(pageLoad + 1);
  };

  useEffect(() => {
    getServiceProviders(true, serviceTypeKey, { ...query });
  }, [pageLoad]);

  useEffect(() => {
    if (serviceProviders.length < serviceProvidersTotal) {
      setHasMore(true);
    } else {
      setHasMore(false);
    }
  }, [serviceProviders]);

  const getServiceProviders = async (
    isLoadMore: boolean,
    serviceTypeKey: string,
    query: QueryParams & {
      keyword?: string;
    },
  ) => {
    try {
      setServiceProvidersLoading('pending');
      const response = await serviceProviderService.getServiceProviders({
        serviceType: serviceTypeKey,
        include: 'ServiceTypes|WasteCenters',
        ...{ page: isLoadMore ? pageLoad : 1 },
        ...{ limit: NUMBER_ITEM_LOAD },
        ...(query.keyword ? { name: '*' + query.keyword + '*' } : {}),
        orderBy: 'createdAt,desc',
      });

      console.log(response);
      setServiceProvidersLoading('succeeded');
      if (response.rows && response.rows.length === 0) {
        setHasMore(false);
      }

      if (isLoadMore) {
        setServiceProviders((prev) => [...prev, ...response.rows]);
      } else {
        setServiceProviders(response.rows);
      }
      setServiceProvidersTotal(response.count);
    } catch (error) {
      setServiceProvidersLoading('failed');
      console.log(error);
    }
  };

  useEffect(() => {
    setPageLoad(1);
    setServiceProviders([]);
    setServiceProvidersTotal(0);
    setHasMore(true);
    if (serviceTypeKey === 'benneur') getServiceProviders(false, serviceTypeKey, query);
    if (serviceTypeKey === 'camionneur') getServiceProviders(false, serviceTypeKey, query);
    if (serviceTypeKey === 'pup') getServiceProviders(false, serviceTypeKey, query);
    // Reset scroll position
    window.scrollTo(0, 0);
  }, [query, serviceTypeKey]);

  const handleCancel = () => {
    setName('');
    setIsModalOpen(false);
  };

  const handleSubmit = async () => {
    if (!name) return;
    try {
      setLoading(true);
      const data = {
        serviceTypeId: serviceType?.id as number,
        name,
      };
      const result = await serviceProviderService.createServiceProvider(data);
      navigate(`/prestataires/${serviceTypeKey}/${result.id}`, {
        state: location,
      });
      setLoading(false);
      setIsModalOpen(false);
    } catch (error: unknown) {
      console.error(error);
      const axiosError = error as AxiosError;
      if (axiosError.response?.status === 409) {
        toast.error('Le nom du fournisseur de services existe déjà');
      } else {
        toast.error('Erreur');
      }
      setLoading(false);
    }
  };

  return (
    <>
      <PageTitle>{serviceType?.name ?? serviceTypeKey}</PageTitle>
      <>
        <MainTitle parent={serviceType?.name === 'PUP' ? serviceType?.name : `${serviceType?.name}s`} child='LISTE' />
        <div className='service-provider__creation-and-searching'>
          <ButtonAdd handleClick={() => setIsModalOpen(true)} otherStyles={{ height: '42.1px' }}>
            {`Ajouter un ${serviceType?.name}`}
          </ButtonAdd>
          <div className='service-provider__searching'>
            <Space direction='vertical'>
              <Search
                placeholder='Rechercher'
                allowClear={true}
                defaultValue={query.keyword}
                size='large'
                onSearch={(value) => onParamChange({ keyword: value })}
                style={{
                  width: 304,
                }}
                className='service-provider__search-bar'
              />
            </Space>
          </div>
        </div>
        {serviceTypeKey === SERVICE_TYPES.benneur && (
          <ServiceProviderList
            type={SERVICE_TYPES.benneur}
            dataSource={serviceProviders}
            dataSourceLoading={serviceProvidersLoading}
            query={query}
            total={serviceProvidersTotal}
            fetchServiceProviderList={getServiceProviders}
            loadMoreData={loadMoreData}
            hasMore={hasMore}
          />
        )}
        {serviceTypeKey === SERVICE_TYPES.camionneur && (
          <ServiceProviderList
            type={SERVICE_TYPES.camionneur}
            dataSource={serviceProviders}
            dataSourceLoading={serviceProvidersLoading}
            query={query}
            total={serviceProvidersTotal}
            fetchServiceProviderList={getServiceProviders}
            loadMoreData={loadMoreData}
            hasMore={hasMore}
          />
        )}
        {serviceTypeKey === SERVICE_TYPES.pup && (
          <ServiceProviderList
            type={SERVICE_TYPES.pup}
            dataSource={serviceProviders}
            dataSourceLoading={serviceProvidersLoading}
            query={query}
            total={serviceProvidersTotal}
            fetchServiceProviderList={getServiceProviders}
            loadMoreData={loadMoreData}
            hasMore={hasMore}
          />
        )}
      </>
      <CreateProviderModal
        loading={loading}
        name={name}
        serviceType={serviceType}
        setName={setName}
        handleCancel={handleCancel}
        handleOk={handleSubmit}
        visible={isModalOpen}
      />
    </>
  );
};
export default ServiceProvider;
