import { useState, useEffect, useMemo, useCallback } from 'react';
import { PageTitle, MainTitle, Spinner } from 'components/Common';
import {
  Button,
  Table,
  Form,
  Row,
  Col,
  Input,
  DatePicker,
  InputNumber,
  Typography,
  Switch,
  Select,
  Divider,
  Badge,
} from 'antd';
import { CheckOutlined, CloseOutlined, MailOutlined, UploadOutlined, TruckOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { ScaleLoader } from 'react-spinners';
import { InvoiceSummary, InvoiceComment, FileUploadModal, AddWasteTypeModal, AddRegulModal } from 'components/ProviderInvoice';
import {
  useProductsQuery,
  useProducts,
} from 'hooks';
import { useProviderInvoice, useProviderInvoiceTable } from 'hooks/useProviderInvoiceOperations';
import { useAppDispatch } from 'store';
import useAuthContext from 'store/auth-context';

import {
  fetchInvoiceDetailsByProviderIdEdit,
  fetchInvoiceLinesByProviderIdCreate,
} from 'store/slices/provider_invoice.slices';

import { Invoice, InvoiceLine, InvoiceDetail, Avoir, Comment } from 'types';
import { ReactComponent as AddDechetIcon } from 'assets/icons/add-dechet-icon.svg';

const { Text } = Typography;

// Interfaces for component-specific types
interface RegulEntry {
  regulName: string;
  unitPrice: number;
  quantity: number;
  montant?: number;
  prixClient?: number;
  margin?: number;
}

interface WasteTypeEntry {
  wasteTypeName: string;
  unitPrice: number;
  quantity: number;
  date?: string;
  unit?: string;
  montant?: number;
  prixClient?: number;
  margin?: number;
  total?: number;
  wasteType?: string;
}

interface PrestationDetail {
  type: string;
  date: string;
  time: string;
}

interface ExtendedInvoiceLine {
  key?: string;
  productName?: string;
  productDetails?: string;
  productId?: number;
  quantity?: number;
  unity?: string;
  orderId?: string;
  zohoId?: string;
  commandId?: string;
  orderLink?: string;
  purchasePrice?: number;
  invoicedPrice?: number;
  clientPrice?: number;
  margin?: number;
  diffPurchase?: number;
  status?: string;
  isSelected?: boolean;
  isInvoiced?: boolean;
  invoiceNumber?: string | null;
  isWaitingForPrestReply?: boolean;
  comment?: Comment[];
  rowSpan?: number;
  isFirstLineOfDetail?: boolean;
  totalLinesInDetail?: number;
  detailId?: string;
  prestationDetails?: PrestationDetail[];
  wasteType?: string;
  wasteTypeLabel?: string;
  detailProductName?: string;
  commandNumber?: string;
  siteAddress?: string;
  sitePostalCode?: string;
  siteCity?: string;
  contact?: string;
}


const ProviderInvoiceCreateRefactored = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const dispatch = useAppDispatch();


  const {
    mode: locationMode,
    invoiceId,
    prestataire: passedPrestataire,
    prestataireInvoiceNumber: passedInvoiceNumber,
    invoicedAmount: passedInvoicedAmount
  } = (location.state || {}) as {
    mode?: 'create' | 'edit';
    invoiceId?: string | number;
    prestataire?: string;
    prestataireInvoiceNumber?: string;
    invoicedAmount?: number;
  };

  const urlInvoiceId = location.pathname.split('/').pop();
  const currentInvoiceId = invoiceId || urlInvoiceId;
  const mode = locationMode || 'create';
  const {
    currentInvoice,
    finalTableData,
    displayFilters,
    selectedLines,
    isFiltering,
    setInvoice,
    applyFormFilters,
    toggleDisplayFilter,
    toggleLineSelection,
    addCommentToLine,
    updateInvoiceTotals,
    clearInvoiceState,
  } = useProviderInvoiceTable();


  // Provider invoice hook - Only for save loading state
  const {
    loading: saveLoading
  } = useProviderInvoice();


  const [productsQuery] = useProductsQuery({
    orderBy: 'createdAt,desc|name',
    isVisible: 1,
  });
  const [getAllProducts] = useProducts(productsQuery);


  const [loading, setLoading] = useState<boolean>(false);
  const [form] = Form.useForm();


  const storeAvoirs = currentInvoice?.avoirDetails || [];
  const storeComments = currentInvoice?.commentDetails || [];
  const [localAvoirs, setLocalAvoirs] = useState<Avoir[]>([]);
  const [localComments, setLocalComments] = useState<Comment[]>([]);
  const [deletedAvoirIds, setDeletedAvoirIds] = useState<number[]>([]);

  // Combine store data (minus deleted) with local additions
  const allAvoirs = [
    ...storeAvoirs.filter(avoir => !deletedAvoirIds.includes(avoir.id)),
    ...localAvoirs
  ];
  const allComments = [...storeComments, ...localComments];

  // Initialize loading simulation
  useEffect(() => {
    setLoading(true);
    setTimeout(() => setLoading(false), 2000);
  }, []);

  // Modal states
  const [fileUploadModalVisible, setFileUploadModalVisible] = useState(false);
  const [selectedLineForFiles, setSelectedLineForFiles] = useState<InvoiceLine | null>(null);
  const [wasteTypeModalVisible, setWasteTypeModalVisible] = useState(false);
  const [selectedLineForWasteTypes, setSelectedLineForWasteTypes] = useState<InvoiceLine | null>(null);
  const [RegulModalVisible, setRegulModalVisible] = useState(false);
  const [selectedLineForRegul, setSelectedLineForRegul] = useState<InvoiceLine | null>(null);

  // ============================================================================
  // COMPUTED VALUES
  // ============================================================================
  
  const currentInvoiceData = useMemo(() => {
    // Create minimal invoice data - will be populated from fetched data
    return {
      id: currentInvoiceId || Date.now(),
      createdAt: new Date().toISOString(),
      prestataire: passedPrestataire || 'Loading...', // Will be updated from fetched data
      prestataireInvoiceNumber: 'Loading...', // Will be updated from fetched data
      status: 'brouillon',
      totalSelected: 0,
      totalAvoirs: 0,
      totalAmount: 0, // Will be updated from fetched data
      invoicedAmount: 0, // Will be updated from fetched data
      avoirDetails: [],
      commentDetails: [],
      invoiceDetails: []
    };
  }, [currentInvoiceId, passedPrestataire]);

  // Calculate total avoirs from combined store + local data
  const totalAvoirs = useMemo(() => allAvoirs.reduce((sum: number, avoir: Avoir) => sum + avoir.amount, 0), [allAvoirs]);

  // Calculate selected amount directly from selectedLines (unit price × quantity)
  const totalSelectedAmount = useMemo(() => {
    return selectedLines.reduce((sum, line) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
  }, [selectedLines]);

  const { profile } = useAuthContext();
  const currentUser = profile ? `${profile.firstName || ''} ${profile.lastName || ''}`.trim() || 'Utilisateur inconnu' : 'Utilisateur inconnu';


  const displayPrestataire = currentInvoice?.prestataire || passedPrestataire || 'Prestataire inconnu';
  const displayInvoiceNumber = currentInvoice?.prestataireInvoiceNumber || currentInvoiceData.prestataireInvoiceNumber || 'N° FACTURE';
  const mainTitleChild = `${displayPrestataire} - ${displayInvoiceNumber.toUpperCase()}`;

  // Use processed invoice totals from store
  const displayTotalSelected = currentInvoice?.totalSelected || totalSelectedAmount;
  const displayTotalAvoirs = currentInvoice?.totalAvoirs || totalAvoirs;
  const displayInvoicedAmount = currentInvoice?.invoicedAmount || currentInvoiceData.invoicedAmount;

  // Calculate save button state using store data
  const isSaveButtonDisabled = useMemo(() => {
    return displayInvoicedAmount === (displayTotalAvoirs + displayTotalSelected);
  }, [displayTotalAvoirs, displayTotalSelected, displayInvoicedAmount]);

  // ============================================================================
  // DATA FETCHING AND PROCESSING
  // ============================================================================
  
  useEffect(() => {
    const fetchData = async () => {
      try {
        // Clear old data first
        clearInvoiceState();

        if (mode === 'edit') {
          // For edit mode, fetch specific invoice data using invoice ID
          await dispatch(fetchInvoiceDetailsByProviderIdEdit({
            invoiceId: currentInvoiceId,
            prestataire: passedPrestataire
          }));
        } else {
          // For create mode, fetch available lines using prestataire
          if (!passedPrestataire) {
            return;
          }

          // Pass metadata to store for CREATE mode processing
          const createMetadata = {
            prestataire: passedPrestataire,
            prestataireInvoiceNumber: passedInvoiceNumber,
            invoicedAmount: passedInvoicedAmount
          };

          await dispatch(fetchInvoiceLinesByProviderIdCreate({
            prestataire: passedPrestataire,
            metadata: createMetadata
          }));
        }
      } catch (error) {
        console.error(`❌ ${mode.toUpperCase()}: Error fetching data:`, error);
      }
    };

    fetchData();
  }, [dispatch, mode, currentInvoiceId, passedPrestataire]); // Removed clearInvoiceState to avoid dependency loop

  // ============================================================================
  // CLEAN DATA FLOW - Store handles everything automatically
  // ============================================================================


  // All data processing, row span calculation, and key attribution
  // is now handled automatically in the store when API data is fetched
  // No more duplicate logic or manual transformations needed!

  // ============================================================================
  // EVENT HANDLERS
  // ============================================================================
  
  const handleFilterChange = () => {
    const filters = form.getFieldsValue();
    applyFormFilters(filters);
  };

  const handleToggleSelection = (lineKey: string) => {

    toggleLineSelection(lineKey);

    // Debug after selection toggle
    setTimeout(() => { }, 100);
  };

  const handleAddRegulEntries = (selectedLine: InvoiceLine, regulEntries: unknown[]) => {
    if (!currentInvoice || regulEntries.length === 0) {
      return;
    }

    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let targetDetail: InvoiceDetail | null = null;
    let targetDetailIndex = -1;

    updatedInvoice.invoiceDetails.forEach((detail: unknown, detailIndex: number) => {
      const typedDetail = detail as InvoiceDetail;
      typedDetail.invoiceLines.forEach((line: InvoiceLine) => {
        if (line.key === selectedLine.key) {
          targetDetail = typedDetail;
          targetDetailIndex = detailIndex;
        }
      });
    });

    if (!targetDetail) {
      return;
    }

    const newInvoiceLines = regulEntries.map((regul, index) => {
      const newLineKey = `${updatedInvoice.id}-detail${targetDetailIndex}-regul${Date.now()}-${index}`;
      const typedRegul = regul as RegulEntry;

      const newLine: Partial<InvoiceLine> & { key: string } = {
        key: newLineKey,
        productName: typedRegul.regulName || 'Régularisation',
        productDetails: `Régul ajoutée le ${new Date().toLocaleDateString()}`,
        productId: Date.now() + index,
        quantity: typedRegul.quantity || 1,
        unity: 'Unité',
        orderId: selectedLine.orderId,
        zohoId: selectedLine.zohoId,
        commandId: selectedLine.commandId,
        orderLink: selectedLine.orderLink,
        purchasePrice: 0, // Reguls don't have purchase price
        invoicedPrice: typedRegul.montant || 0,
        clientPrice: typedRegul.prixClient || 0,
        margin: typedRegul.margin || 0,
        diffPurchase: typedRegul.montant || 0,
        status: 'regul',
        isSelected: true, // Auto-select regul lines
        isInvoiced: false,
        isWaitingForPrestReply: false,
        comment: [],

        rowSpan: 0,
        isFirstLineOfDetail: false,
        totalLinesInDetail: 0,
        detailId: selectedLine.detailId,
      };

      return newLine;
    });

    if (targetDetail) {
      const typedTargetDetail = targetDetail as InvoiceDetail;
      typedTargetDetail.invoiceLines.push(...(newInvoiceLines as InvoiceLine[]));

      typedTargetDetail.invoiceLines.forEach((line: InvoiceLine, lineIndex: number) => {
        line.rowSpan = lineIndex === 0 ? typedTargetDetail.invoiceLines.length : 0;
        line.isFirstLineOfDetail = lineIndex === 0;
        line.totalLinesInDetail = typedTargetDetail.invoiceLines.length;
      });
    }

    setInvoice(updatedInvoice);

    setTimeout(() => {
      const newSelectedAmount = updatedInvoice.invoiceDetails
        .flatMap((detail: InvoiceDetail) => detail.invoiceLines)
        .filter((line: InvoiceLine) => line.isSelected)
        .reduce((sum: number, line: InvoiceLine) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
      updateInvoiceTotals(newSelectedAmount, displayTotalAvoirs);
    }, 100);
  };

  const handleAddWasteTypeEntries = (selectedLine: InvoiceLine, wasteTypeEntries: unknown[]) => {
    if (!currentInvoice || wasteTypeEntries.length === 0) {
      return;
    }

    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let targetDetail: InvoiceDetail | null = null;
    let targetDetailIndex = -1;

    updatedInvoice.invoiceDetails.forEach((detail: InvoiceDetail, detailIndex: number) => {
      detail.invoiceLines.forEach((line: InvoiceLine) => {
        if (line.key === selectedLine.key) {
          targetDetail = detail;
          targetDetailIndex = detailIndex;
        }
      });
    });

    if (!targetDetail) {
      return;
    }

    const newInvoiceLines = wasteTypeEntries.map((waste, index) => {
      const newLineKey = `${updatedInvoice.id}-detail${targetDetailIndex}-waste${Date.now()}-${index}`;
      const typedWaste = waste as WasteTypeEntry;

      const newLine: Partial<ExtendedInvoiceLine> & { key: string } = {
        key: newLineKey,
        productName: `Traitement de déchet : ${typedWaste.wasteTypeName}`,
        productDetails: `Traitement ajouté le ${new Date(typedWaste.date || Date.now()).toLocaleDateString()}`,
        productId: Date.now() + index,
        quantity: typedWaste.quantity || 1,
        unity: typedWaste.unit || 'kg',
        orderId: selectedLine.orderId,
        zohoId: selectedLine.zohoId,
        commandId: selectedLine.commandId,
        orderLink: selectedLine.orderLink,
        purchasePrice: 0, // Waste types don't have purchase price
        invoicedPrice: typedWaste.montant || 0,
        clientPrice: typedWaste.prixClient || 0,
        margin: typedWaste.margin || 0,
        diffPurchase: typedWaste.total || 0,
        status: 'waste',
        isSelected: true,
        isInvoiced: false,
        invoiceNumber: null,
        isWaitingForPrestReply: false,
        comment: [],

        rowSpan: 0,
        isFirstLineOfDetail: false,
        totalLinesInDetail: 0,
        detailId: selectedLine.detailId,

        wasteType: typedWaste.wasteType,
        wasteTypeLabel: typedWaste.wasteTypeName,
      };

      return newLine;
    });

    if (targetDetail) {
      const typedTargetDetail = targetDetail as InvoiceDetail;
      typedTargetDetail.invoiceLines.push(...(newInvoiceLines as InvoiceLine[]));

      typedTargetDetail.invoiceLines.forEach((line: InvoiceLine, lineIndex: number) => {
        line.rowSpan = lineIndex === 0 ? typedTargetDetail.invoiceLines.length : 0;
        line.isFirstLineOfDetail = lineIndex === 0;
        line.totalLinesInDetail = typedTargetDetail.invoiceLines.length;
      });
    }

    setInvoice(updatedInvoice);

    setTimeout(() => {
      const newSelectedAmount = updatedInvoice.invoiceDetails
        .flatMap((detail: InvoiceDetail) => detail.invoiceLines)
        .filter((line: InvoiceLine) => line.isSelected)
        .reduce((sum: number, line: InvoiceLine) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
      updateInvoiceTotals(newSelectedAmount, displayTotalAvoirs);
    }, 100);
  };

  const handleDeleteSpecialLine = (lineKey: string) => {
    if (!currentInvoice) {
      return;
    }

    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let lineDeleted = false;
    let wasLineSelected = false;

    updatedInvoice.invoiceDetails.forEach((detail: InvoiceDetail) => {
      const originalLength = detail.invoiceLines.length;
      detail.invoiceLines = detail.invoiceLines.filter((line: InvoiceLine) => {
        if (line.key === lineKey && (line.status === 'regul' || line.status === 'waste')) {
          wasLineSelected = line.isSelected;
          lineDeleted = true;
          return false;
        }
        return true;
      });

      if (detail.invoiceLines.length !== originalLength) {
        detail.invoiceLines.forEach((line: InvoiceLine, lineIndex: number) => {
          line.rowSpan = lineIndex === 0 ? detail.invoiceLines.length : 0;
          line.isFirstLineOfDetail = lineIndex === 0;
          line.totalLinesInDetail = detail.invoiceLines.length;
        });
      }
    });

    if (lineDeleted) {
      setInvoice(updatedInvoice);

      if (wasLineSelected) {
        setTimeout(() => {
          const newSelectedAmount = updatedInvoice.invoiceDetails
            .flatMap((detail: InvoiceDetail) => detail.invoiceLines)
            .filter((line: InvoiceLine) => line.isSelected)
            .reduce((sum: number, line: InvoiceLine) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
          updateInvoiceTotals(newSelectedAmount, displayTotalAvoirs);
        }, 100);
      }
    }
  };

  const handleTogglePrestReply = (lineKey: string) => {
    if (!currentInvoice) {
      return;
    }

    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let lineFound = false;

    updatedInvoice.invoiceDetails.forEach((detail: InvoiceDetail) => {
      detail.invoiceLines.forEach((line: InvoiceLine) => {
        if (line.key === lineKey) {
          line.isWaitingForPrestReply = !line.isWaitingForPrestReply;
          lineFound = true;
        }
      });
    });

    if (lineFound) {
      setInvoice(updatedInvoice);
    }
  };

  // Dynamic price calculation handlers
  const handleUpdatePrice = (lineKey: string, newUnitPrice: number) => {

    if (!currentInvoice) return;

    // Create deep copy of the invoice structure to avoid read-only errors
    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let lineFound = false;
    let isLineSelected = false;

    updatedInvoice.invoiceDetails.forEach((detail: InvoiceDetail) => {
      detail.invoiceLines.forEach((line: InvoiceLine) => {
        if (line.key === lineKey) {
          line.invoicedPrice = newUnitPrice;
          lineFound = true;
          isLineSelected = line.isSelected;
        }
      });
    });

    if (lineFound) {
      // Update the invoice in store
      setInvoice(updatedInvoice);

      // If line is selected, recalculate totals using updated invoice
      if (isLineSelected) {
        setTimeout(() => {
          const newSelectedAmount = updatedInvoice.invoiceDetails
            .flatMap((detail: InvoiceDetail) => detail.invoiceLines)
            .filter((line: InvoiceLine) => line.isSelected)
            .reduce((sum: number, line: InvoiceLine) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
          updateInvoiceTotals(newSelectedAmount, displayTotalAvoirs);
        }, 100);
      }
    }
  };

  const handleUpdateQuantity = (lineKey: string, newQuantity: number) => {
    if (!currentInvoice) return;

    // Create deep copy of the invoice structure to avoid read-only errors
    const updatedInvoice = JSON.parse(JSON.stringify(currentInvoice));
    let lineFound = false;
    let isLineSelected = false;

    updatedInvoice.invoiceDetails.forEach((detail: InvoiceDetail) => {
      detail.invoiceLines.forEach((line: InvoiceLine) => {
        if (line.key === lineKey) {
          line.quantity = newQuantity;
          lineFound = true;
          isLineSelected = line.isSelected;

        }
      });
    });

    if (lineFound) {
      // Update the invoice in store
      setInvoice(updatedInvoice);

      if (isLineSelected) {
        setTimeout(() => {
          const newSelectedAmount = updatedInvoice.invoiceDetails
            .flatMap((detail: InvoiceDetail) => detail.invoiceLines)
            .filter((line: InvoiceLine) => line.isSelected)
            .reduce((sum: number, line: InvoiceLine) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);
          updateInvoiceTotals(newSelectedAmount, displayTotalAvoirs);
        }, 100);
      }
    }
  };

  const handleAddComment = (lineKey: string, newComment: Comment) => {

    // Extract text from the comment object since the hook expects just text
    addCommentToLine(lineKey, newComment.comment, newComment.createdBy);

    // Debug after comment addition
    setTimeout(() => {}, 100);
  };

  const cleanInvoiceForAPI = (invoice: Invoice): unknown => {

    const cleanedInvoice = {
      id: invoice.id,
      createdAt: invoice.createdAt,
      prestataire: invoice.prestataire,
      prestataireInvoiceNumber: invoice.prestataireInvoiceNumber,
      status: invoice.status,
      totalSelected: invoice.totalSelected,
      totalAvoirs: invoice.totalAvoirs,
      totalAmount: invoice.totalAmount,
      invoicedAmount: invoice.invoicedAmount,
      avoirDetails: invoice.avoirDetails?.map((avoir: Avoir) => ({
        id: avoir.id,
        avoirNumber: avoir.avoirNumber,
        createdAt: avoir.createdAt,
        amount: avoir.amount,
        createdBy: avoir.createdBy
      })) || [],
      commentDetails: invoice.commentDetails?.map((comment: Comment) => ({
        id: comment.id,
        createdBy: comment.createdBy,
        createdAt: comment.createdAt,
        comment: comment.comment
      })) || [],
      invoiceDetails: invoice.invoiceDetails?.map((detail: unknown) => {
        const typedDetail = detail as InvoiceDetail & { id?: number };
        return {
          id: typedDetail.id,
          productName: typedDetail.productName,
          siteAddress: typedDetail.siteAddress,
          sitePostalCode: typedDetail.sitePostalCode,
          siteCity: typedDetail.siteCity,
          prestationDetails: typedDetail.prestationDetails,
          commandNumber: typedDetail.commandNumber,
          contact: typedDetail.contact,
          invoiceNumber: typedDetail.invoiceNumber,
          invoicedPrice: typedDetail.invoicedPrice,
          clientPrice: typedDetail.clientPrice,
          totalMargin: typedDetail.totalMargin,
          totalDiffPurchase: typedDetail.totalDiffPurchase,
          invoiceLines: typedDetail.invoiceLines?.map((line: InvoiceLine) => ({
            key: line.key,
            productName: line.productName,
            productDetails: line.productDetails,
            productId: line.productId,
            quantity: line.quantity,
            unity: line.unity,
            orderId: line.orderId,
            zohoId: line.zohoId,
            commandId: line.commandId,
            orderLink: line.orderLink,
            purchasePrice: line.purchasePrice,
            invoicedPrice: line.invoicedPrice,
            clientPrice: line.clientPrice,
            margin: line.margin,
            diffPurchase: line.diffPurchase,
            status: line.status,
            isSelected: line.isSelected,
            isInvoiced: line.isInvoiced,
            isWaitingForPrestReply: line.isWaitingForPrestReply,
            comment: line.comment || []
          })) || []
        };
      }) || []
    };

    return cleanedInvoice;
  };

  // ============================================================================
  // SAVE FUNCTIONALITY WITH STATUS
  // ============================================================================

  const handleSave = useCallback((status: 'brouillon' | 'a_revoir' | 'validée') => {

    if (!currentInvoice) {
      alert('Error: No invoice data available');
      return;
    }

    // Calculate the correct total amount from current selected lines
    const currentSelectedAmount = currentInvoice.invoiceDetails
      .flatMap(detail => detail.invoiceLines)
      .filter(line => line.isSelected)
      .reduce((sum, line) => sum + ((line.invoicedPrice || 0) * (line.quantity || 1)), 0);

    const currentTotalAvoirs = allAvoirs.reduce((sum, avoir) => sum + avoir.amount, 0);
    const correctTotalAmount = currentSelectedAmount + currentTotalAvoirs;

    // Create the complete Invoice object for API
    const invoiceToSave: Invoice = {
      ...currentInvoice,
      // Update metadata
      status: status,
      // Update totals with correct calculated values
      totalSelected: currentSelectedAmount,
      totalAvoirs: currentTotalAvoirs,
      totalAmount: correctTotalAmount,

      // Add invoice-level avoirs to avoirDetails (store avoirs minus deleted + local additions)
      avoirDetails: [
        ...currentInvoice.avoirDetails.filter(avoir => !deletedAvoirIds.includes(avoir.id)),
        ...localAvoirs.map(avoir => ({
          id: avoir.id,
          avoirNumber: avoir.avoirNumber,
          createdAt: avoir.createdAt,
          amount: avoir.amount,
          createdBy: avoir.createdBy
        }))
      ],

      // Add invoice-level comments to commentDetails (store + local additions)
      commentDetails: [
        ...currentInvoice.commentDetails,
        ...localComments.map(comment => ({
          id: comment.id,
          createdBy: comment.createdBy,
          createdAt: comment.createdAt,
          comment: comment.comment
        }))
      ],

      // The invoiceDetails already contain:
      // - Lines with their comments (line.comment[])
      // - Selection state (line.isSelected)
      // - All line data (prices, quantities, etc.)
      // The totals are automatically updated via Redux action
      // No need to transform - they're already in the correct Invoice structure!
    };

    // Clean the invoice data to match API structure (remove UI-specific properties)
    const cleanedInvoiceForAPI = cleanInvoiceForAPI(invoiceToSave);

    console.log('💾 Component: Complete Invoice object to save:', cleanedInvoiceForAPI);

    // TODO: Replace with actual API call
    console.log('🚀 Would POST cleaned Invoice to API:', JSON.stringify(cleanedInvoiceForAPI, null, 2));

    // Show success message
    alert(`Invoice saved with status: ${status}\n\nCheck console for complete Invoice structure!`);
  }, [
    currentInvoice,
    totalSelectedAmount,
    totalAvoirs,
    localAvoirs,
    localComments,
    deletedAvoirIds,
  ]);

  // ============================================================================
  // CLEANUP
  // ============================================================================
  
  // Update Invoice totals whenever they change
  useEffect(() => {
    if (currentInvoice) {
      updateInvoiceTotals(totalSelectedAmount, totalAvoirs);
    }
  }, [totalSelectedAmount, totalAvoirs, updateInvoiceTotals, currentInvoice]);

  useEffect(() => {
    return () => {
      // Clear invoice state when component unmounts
      clearInvoiceState();
    };
  }, [clearInvoiceState]);


  // ============================================================================
  // RENDER
  // ============================================================================
  
  return (
    <div>
      <PageTitle>FACTURES FOURNISSEURS</PageTitle>
      <Spinner loading={loading}>
        <>
          {/* Header */}
          <div className="product-page__searching">
            <Row justify="end" gutter={24} align="top" style={{ marginBottom: 16 }}>
              <Col span={12}>
                <MainTitle parent="Validation" child={mainTitleChild} />
              </Col>
              <Col span={12}>
                <Row justify="end" gutter={24}>
                  <Col>
                    <Button
                      className='devis-page__btn__quit'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      onClick={() => navigate('/logistique/factures-fournisseurs')}
                    >
                      Annuler
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className='devis-page__btn__info'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      onClick={() => handleSave('a_revoir')}
                    >
                      A revoir
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className='devis-page__btn__draft'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      onClick={() => handleSave('brouillon')}
                    >
                      Brouillon
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className={!isSaveButtonDisabled ? 'btn-grey-inactive' : 'devis-page__btn__send'}
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      disabled={!isSaveButtonDisabled}
                      onClick={() => handleSave('validée')}
                    >
                      Valider
                    </Button>
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>

          {/* Summary and Comments */}
          <Row justify="end" gutter={24} align="top" style={{ marginBottom: 16 }}>
            <Col span={16}>
              <InvoiceComment
                comments={allComments}
                currentUser={currentUser}
                onAddComment={(newComment) => setLocalComments([...localComments, newComment])}
              />
            </Col>
            <Col span={8} style={{ justifyContent: 'right', display: 'flex' }}>
              <InvoiceSummary
                totalSelected={displayTotalSelected}
                totalAvoirs={displayTotalAvoirs}
                avoirsCount={allAvoirs.length}
                invoiceAmount={displayInvoicedAmount}
                avoirDetails={allAvoirs}
                onAddAvoir={(data) => {
                  const newAvoir = {
                    id: Date.now(),
                    avoirNumber: data.avoirNumber,
                    createdAt: new Date().toISOString(),
                    amount: data.amount,
                    createdBy: currentUser,
                  };
                  setLocalAvoirs([...localAvoirs, newAvoir]);
                }}
                onRemoveAvoir={(id) => {
                  // Check if it's a store avoir or local avoir
                  const isStoreAvoir = storeAvoirs.some(avoir => avoir.id === id);
                  if (isStoreAvoir) {
                    // Mark store avoir as deleted
                    setDeletedAvoirIds([...deletedAvoirIds, id]);
                  } else {
                    // Remove from local avoirs
                    setLocalAvoirs(localAvoirs.filter(a => a.id !== id));
                  }
                }}
              />
            </Col>
          </Row>

          {/* Statistics and Display toggles */}
          <Row gutter={24} justify="start" style={{ marginBottom: 6 }}>
            <Col span={5}>
              <Text strong style={{ fontSize: '16px' }}>
                Prestations sélectionnées : {selectedLines.length} / {finalTableData.length}
              </Text>
            </Col>

            <Col span={8}>
              <Row>
                <Col>
                  <Switch
                    checked={displayFilters.showInvoicedRows}
                    onChange={checked => toggleDisplayFilter('showInvoicedRows', checked)}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    className='product-tarifs__switch'
                  />
                </Col>
                <Col style={{ marginLeft: 8 }}>
                  <Text strong>
                    {displayFilters.showInvoicedRows
                      ? 'Afficher toutes les prestations (y compris facturées)'
                      : 'Masquer les prestations facturées'}
                  </Text>
                </Col>
              </Row>
            </Col>
            <Col span={8}>
              <Row>
                <Col>
                  <Switch
                    checked={displayFilters.showSelectedRows}
                    onChange={checked => toggleDisplayFilter('showSelectedRows', checked)}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    className='product-tarifs__switch'
                  />
                </Col>
                <Col style={{ marginLeft: 8 }}>
                  <Text strong>
                    {displayFilters.showSelectedRows
                      ? 'Afficher toutes les lignes (y compris sélectionnées)'
                      : 'Masquer les lignes sélectionnées'}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>

          {/* Search and filter section */}
          <div className="product-page__searching">
            <Form layout="vertical" form={form} onValuesChange={handleFilterChange}>
              <Row gutter={24} style={{ marginTop: 16 }}>
                <Col span={5}>
                  <Form.Item name="date">
                    <DatePicker placeholder='Sélectionner une date' style={{ width: '100%' }} />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name="commandeNumber">
                    <Input placeholder='N° de commande' />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item name="product">
                    <Select
                      placeholder="Produits"
                      showSearch
                      allowClear
                      options={(getAllProducts?.rows || []).map(product => ({
                        label: product.name,
                        value: product.id,
                      }))}
                      filterOption={(input, option) => {
                        const normalize = (str: string) =>
                          (str || '')
                            .normalize('NFD')
                            .replace(/[\u0300-\u036f]/g, '')
                            .replace(/[^\w\s]/gi, '')
                            .toLowerCase();
                        const normalizedInput = normalize(input);
                        const normalizedLabel = normalize(option?.label || '');
                        return normalizedInput.split(/\s+/).every(word =>
                          word && normalizedLabel.includes(word)
                        );
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={7}>
                  <Form.Item name="siteAddress">
                    <Input placeholder='Adresse de chantier' />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>


          {/* Table */}
          <Table
            dataSource={finalTableData}
            loading={{
              indicator: <ScaleLoader color="#1890ff" />,
              spinning: isFiltering,
            }}
            pagination={false}
            rowKey="key"
            bordered
            columns={[
              // Column 1: Commande / Adresse / Date (with row spans)
              {
                title: 'Commande / Adresse / Date',
                key: 'commandeAdresseDate',
                width: 180,
                onCell: (record: unknown) => {
                  const typedRecord = record as InvoiceLine;
                  if (typedRecord.isFirstLineOfDetail) {
                    return {
                      rowSpan: typedRecord.totalLinesInDetail,
                      style: { verticalAlign: 'middle' },
                    };
                  }
                  return { rowSpan: 0 };
                },
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  if (!typedRecord.isFirstLineOfDetail) return null;

                  return (
                    <div style={{ display: 'flex', flexDirection: 'column', textAlign: 'left' }}>
                      <div style={{ fontWeight: 'bold', marginBottom: 5 }}>
                        {typedRecord.detailProductName || 'Transport/Service'}
                      </div>
                      <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
                        <strong>{typedRecord.invoiceNumber || 'N/A'}</strong>
                      </div>
                      <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
                        <strong>{typedRecord.commandNumber || 'N/A'}</strong>
                      </div>
                      <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
                        {typedRecord.siteAddress}, {typedRecord.sitePostalCode} {typedRecord.siteCity}
                      </div>
                      <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
                        {typedRecord.prestationDetails?.map((detail: PrestationDetail, index: number) => (
                          <div key={index} style={{ marginBottom: 2 }}>
                            {detail.type} : {new Date(detail.date).toLocaleDateString('fr-FR')} - {detail.time}
                          </div>
                        )) || 'Aucun détail de prestation'}
                      </div>
                      <div style={{ fontSize: '0.85em' }}>
                        {(() => {
                          // Get the parent detail data from the invoice structure
                          const parentDetail = currentInvoice?.invoiceDetails?.find(detail =>
                            detail.invoiceLines?.some(line => line.key === typedRecord.key)
                          );

                          return (
                            <>
                              <div><strong>Prix facturé:</strong> {(parentDetail?.invoicedPrice || 0).toFixed(2)} €</div>
                              <div><strong>Prix client:</strong> {(parentDetail?.clientPrice || 0).toFixed(2)} €</div>
                              <div style={{ color: '#2ba748' }}><strong>Marge:</strong> {parentDetail?.totalMargin || 0}%</div>
                              <div style={{ color: '#2ba748' }}><strong>Diff. Achat:</strong> {(parentDetail?.totalDiffPurchase || 0).toFixed(2)} €</div>
                            </>
                          );
                        })()}
                      </div>
                      <div style={{ fontSize: '0.85em', marginTop: 8, padding: 8 }}>
                        {/* <Button
                          type={selectedLines.includes(record.key) ? 'default' : 'primary'}
                          size="small"
                          onClick={() => handleToggleSelection(record.key)}
                          style={selectedLines.includes(record.key) ? {
                            backgroundColor: '#1890ff',
                            borderColor: '#1890ff',
                            color: '#fff',
                            fontWeight: 'bold',
                          } : {}}
                        >
                          {selectedLines.includes(record.key) ? 'Désélectionner' : 'Sélectionner'}
                        </Button> */}
                      </div>
                    </div>
                  );
                },
              },

              // Column 2: Prestation / Commande client
              {
                title: 'Prestation / Commande client',
                key: 'prestationCommande',
                width: 300,
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  const hasRibbon = selectedLines.some(l => l.key === typedRecord.key) || typedRecord.isInvoiced;
                  const content = (
                    <div style={{ paddingTop: hasRibbon ? 30 : 0 }}>
                      <div style={{ fontWeight: 'bold', marginBottom: 5, display: 'flex', alignItems: 'center', gap: 8 }}>
                        {typedRecord.productName}
                        {typedRecord.isWaitingForPrestReply && (
                          <Badge
                            count="En attente presta."
                            style={{
                              backgroundColor: '#ff4d4f',
                              color: '#ffffff',
                              fontSize: '10px',
                              height: '18px',
                              lineHeight: '18px',
                              borderRadius: '9px',
                              padding: '0 6px',
                              whiteSpace: 'nowrap'
                            }}
                          />
                        )}
                      </div>
                      <a
                        href={typedRecord.orderLink}
                        target="_blank"
                        rel="noopener noreferrer"
                        style={{ fontSize: '0.85em', textDecoration: 'underline' }}
                      >
                        {typedRecord.orderId}
                      </a>
                      <Divider style={{ margin: '8px 0' }} />
                      <InvoiceComment
                        comments={typedRecord.comment || []}
                        currentUser={currentUser}
                        onAddComment={(newComment) => {
                          if (typedRecord.key) {
                            handleAddComment(typedRecord.key, newComment);
                          }
                        }}
                        isCommentLine={true}
                      />
                    </div>
                  );

                  let wrappedContent = content;
                  if (typedRecord.isInvoiced) {
                    const badgeText = typedRecord.invoiceNumber ? `Facturé - ${typedRecord.invoiceNumber}` : 'Facturé';
                    wrappedContent = <Badge.Ribbon text={badgeText} color="blue" placement="start">{wrappedContent}</Badge.Ribbon>;
                  }
                  if (selectedLines.some(l => l.key === typedRecord.key)) {
                    wrappedContent = <Badge.Ribbon text="Sélectionné" placement="start">{wrappedContent}</Badge.Ribbon>;
                  }

                  return wrappedContent;
                },
              },

              // Column 3: Prix unitaire
              {
                title: 'Prix unitaire',
                key: 'prixUnitaire',
                width: 180,
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  return (
                  <div>
                    <div style={{ marginBottom: 5 }}>
                      <span>Prix com:</span> {(typedRecord.purchasePrice || 0).toFixed(2)} €
                    </div>
                    <div style={{ marginBottom: 5 }}>
                      <span>Prix facturé:</span>
                      <InputNumber
                        min={0}
                        step={0.01}
                        value={typedRecord.invoicedPrice}
                        onChange={(value) => {
                          if (typedRecord.key) {
                            handleUpdatePrice(typedRecord.key, value as number);
                          }
                        }}
                        style={{ width: '90px', marginLeft: '5px' }}
                      />
                    </div>
                    <div style={{ color: '#2ba748' }}>
                      <span>Diff achat:</span> {((typedRecord.invoicedPrice || 0) - (typedRecord.purchasePrice || 0)).toFixed(2)} €
                    </div>
                  </div>
                  );
                },
              },

              // Column 4: Prix commande
              {
                title: 'Prix commande',
                key: 'prixCommande',
                width: 160,
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  return (
                  <>
                    {/* Show waste type selector for waste type lines */}
                    {typedRecord.status === 'waste' && (
                      <div style={{ textAlign: 'center', marginBottom: 8 }}>
                        <span style={{ fontSize: '12px', color: '#666' }}>Type de déchet:</span>
                        <Select
                          value={typedRecord.wasteType}
                          disabled
                          style={{ width: '100%', marginTop: 2 }}
                          size="small"
                        >
                          <Select.Option value={typedRecord.wasteType}>
                            {typedRecord.wasteTypeLabel || typedRecord.wasteType}
                          </Select.Option>
                        </Select>
                      </div>
                    )}
                    <div style={{
                      textAlign: 'center',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '5px'
                    }}>
                      <InputNumber
                        min={0}
                        step={0.01}
                        value={typedRecord.quantity}
                        onChange={(value) => {
                          if (typedRecord.key) {
                            handleUpdateQuantity(typedRecord.key, value as number);
                          }
                        }}
                        style={{ width: '90px' }}
                      />
                      <span style={{ fontWeight: 'bold' }}>{typedRecord.unity}</span>
                    </div>
                    <div style={{
                      textAlign: 'center',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      gap: '5px'
                    }}>
                      <span>Prix facturé:</span>
                      <InputNumber
                        step={0.01}
                        value={((typedRecord.invoicedPrice || 0) * (typedRecord.quantity || 1)).toFixed(2)}
                        style={{ width: '90px' }}
                        disabled
                      />
                    </div>
                    <div style={{ textAlign: 'center' }}>
                      <div style={{ fontSize: '0.85em', marginTop: 5 }}>
                        Prix client: {((typedRecord.clientPrice || 0) * (typedRecord.quantity || 1)).toFixed(2)} €
                      </div>
                      <div style={{ fontSize: '0.85em', marginTop: 5, color: '#2ba748' }}>
                        Marge total: {(typedRecord.margin || 0).toFixed(2)} %
                      </div>
                    </div>
                  </>
                  );
                },
              },

              // Column 5: Actions
              {
                title: 'Actions',
                key: 'actions',
                width: 150,
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  return (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 5, alignItems: 'center' }}>
                    <MailOutlined
                      style={{ cursor: 'pointer', color: '#807f84', fontSize: '22px' }}
                      onClick={() => {
                        // Use global invoice data instead of line-specific data
                        const globalInvoiceNumber = displayInvoiceNumber || 'N/A';
                        const globalInvoicedAmount = displayInvoicedAmount || 0;

                        // Format prestations types (map and join with "et")
                        const prestationTypes = typedRecord.prestationDetails?.map((detail: PrestationDetail) => detail.type) || [];
                        const formattedPrestationTypes = prestationTypes.length > 1
                          ? prestationTypes.slice(0, -1).join(', ') + ' et ' + prestationTypes[prestationTypes.length - 1]
                          : prestationTypes[0] || '';

                        // Get first date from prestationDetails and format to French format
                        const firstDateRaw = typedRecord.prestationDetails?.[0]?.date;
                        const firstDate = firstDateRaw ? new Date(firstDateRaw).toLocaleDateString('fr-FR') : 'N/A';

                        // Format address
                        const fullAddress = `${typedRecord.siteAddress}, ${typedRecord.sitePostalCode} ${typedRecord.siteCity}`;

                        const subject = encodeURIComponent(`ECODROP - ${globalInvoiceNumber}`);
                        const body = encodeURIComponent(
                          `Bonjour,\n\n` +
                          `Je vous contacte concernant la facture ${globalInvoiceNumber} d'un montant de ${globalInvoicedAmount} €.\n\n` +
                          `La prestation ${formattedPrestationTypes} ${typedRecord.productName} à l'adresse de chantier ${fullAddress} au ${firstDate}.\n\n` +
                          `Bien cordialement,\n`
                        );
                        window.location.href = `mailto:${typedRecord.contact}?subject=${subject}&body=${body}`;
                      }}
                    />
                    {!typedRecord.isInvoiced && (
                      <>
                        {(typedRecord.status === 'regul' || typedRecord.status === 'waste') ? (
                          // Show delete button for regul and waste type lines
                          <Button
                            type="default"
                            size="small"
                            onClick={() => {
                              if (typedRecord.key) {
                                handleDeleteSpecialLine(typedRecord.key);
                              }
                            }}
                            style={{
                              color: '#ffffff',
                              borderColor: '#ffffff',
                              backgroundColor: '#ff7875',
                            }}
                            title="Supprimer cette régularisation"
                          >
                             Supprimer
                          </Button>
                        ) : (
                          // Show normal presta button for regular lines
                          <Button
                            type={typedRecord.isWaitingForPrestReply ? 'primary' : 'default'}
                            size="small"
                            onClick={() => {
                              if (typedRecord.key) {
                                handleTogglePrestReply(typedRecord.key);
                              }
                            }}
                            style={{
                              color: '#ffffff',
                              backgroundColor: selectedLines.some(l => l.key === typedRecord.key) ? '#d9d9d9' : '#ffa940'
                            }}
                            disabled={selectedLines.some(l => l.key === typedRecord.key)}
                          >
                            {!typedRecord.isWaitingForPrestReply ? 'En attente presta.' : 'Réponse presta.'}
                          </Button>
                        )}
                        <Button
                          type={selectedLines.some(l => l.key === typedRecord.key) ? 'default' : 'primary'}
                          size="small"
                          onClick={() => {
                            if (typedRecord.key) {
                              handleToggleSelection(typedRecord.key);
                            }
                          }}
                          style={selectedLines.some(l => l.key === typedRecord.key) ? {
                            backgroundColor: '#1890ff',
                            borderColor: '#1890ff',
                            color: '#fff',
                            fontWeight: 'bold',
                          } : {}}
                        >
                          {selectedLines.some(l => l.key === typedRecord.key) ? 'Désélectionner' : 'Sélectionner'}
                        </Button>
                      </>
                    )}
                  </div>
                  );
                },
              },

              // Column 6: Additional Actions (with row spans)
              {
                title: '',
                key: 'plusActions',
                width: 50,
                onCell: (record: ExtendedInvoiceLine) => {
                  if (record.isFirstLineOfDetail) {
                    return {
                      rowSpan: record.totalLinesInDetail,
                      style: { verticalAlign: 'middle' },
                    };
                  }
                  return { rowSpan: 0 };
                },
                render: (_: unknown, record: unknown) => {
                  const typedRecord = record as ExtendedInvoiceLine;
                  return (
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 5, alignItems: 'center' }}>
                    <TruckOutlined
                      style={{ cursor: 'pointer', color: '#807f84', fontSize: '22px' }}
                      onClick={() => {
                        setSelectedLineForRegul(typedRecord as InvoiceLine);
                        setRegulModalVisible(true);
                      }}
                      title="Ajouter des reguls"
                    />
                    <AddDechetIcon
                      style={{ cursor: 'pointer', color: '#807f84' }}
                      onClick={() => {
                        setSelectedLineForWasteTypes(typedRecord as InvoiceLine);
                        setWasteTypeModalVisible(true);
                      }}
                      title="Ajouter un type de déchets"
                    />
                    <UploadOutlined
                      style={{ cursor: 'pointer', color: '#807f84', fontSize: '22px' }}
                      onClick={() => {
                        setSelectedLineForFiles(typedRecord as InvoiceLine);
                        setFileUploadModalVisible(true);
                      }}
                      title="Gérer les fichiers"
                    />
                  </div>
                  );
                },
              },
            ]}
          />
        </>
      </Spinner>

      {/* Modals */}
      <FileUploadModal
        visible={fileUploadModalVisible}
        onClose={() => {
          setFileUploadModalVisible(false);
          setSelectedLineForFiles(null);
        }}
        onSave={() => {
          setFileUploadModalVisible(false);
          setSelectedLineForFiles(null);
        }}
        initialFiles={[]}
        title="Gestion des fichiers"
        invoiceNumber={selectedLineForFiles?.invoiceNumber || undefined}
        prestataire={currentInvoice?.prestataire || passedPrestataire}
      />

      <AddWasteTypeModal
        visible={wasteTypeModalVisible}
        onClose={() => {
          setWasteTypeModalVisible(false);
          setSelectedLineForWasteTypes(null);
        }}
        onSave={(wasteTypes) => {
          if (selectedLineForWasteTypes && wasteTypes.length > 0) {
            // Add waste type entries as new invoice lines
            handleAddWasteTypeEntries(selectedLineForWasteTypes, wasteTypes);
          }

          setWasteTypeModalVisible(false);
          setSelectedLineForWasteTypes(null);
        }}
        initialWasteTypes={[]}
        invoiceNumber={selectedLineForWasteTypes?.invoiceNumber || undefined}
        prestataire={currentInvoice?.prestataire || passedPrestataire}
      />

      <AddRegulModal
        visible={RegulModalVisible}
        onClose={() => {
          setRegulModalVisible(false);
          setSelectedLineForRegul(null);
        }}
        onSave={(reguls) => {

          if (selectedLineForRegul && reguls.length > 0) {
            // Add regul entries as new invoice lines
            handleAddRegulEntries(selectedLineForRegul, reguls);
          }

          setRegulModalVisible(false);
          setSelectedLineForRegul(null);
        }}
        initialReguls={[]}
        invoiceNumber={selectedLineForRegul?.invoiceNumber || undefined}
        prestataire={currentInvoice?.prestataire || passedPrestataire}
      />
    </div>
  );
};

export default ProviderInvoiceCreateRefactored;
