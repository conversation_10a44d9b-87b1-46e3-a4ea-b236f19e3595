import { useState, useEffect, useMemo, useRef } from 'react';
import { PageTitle, MainTitle, Spinner } from 'components/Common';
import {
  Button,
  Table,
  Form,
  Row,
  Col,
  Input,
  DatePicker,
  InputNumber,
  Typography,
  Switch,
  Select,
  Divider,
  Badge,
} from 'antd';
import { CheckOutlined, CloseOutlined, MailOutlined, CommentOutlined, UploadOutlined, TruckOutlined, PlusCircleOutlined } from '@ant-design/icons';
import { useLocation, useNavigate } from 'react-router-dom';
import { ScaleLoader } from 'react-spinners';
import { InvoiceSummary, InvoiceComment, FileUploadModal, AddWasteTypeModal, AddRegulModal } from 'components/ProviderInvoice';
import {
  useProductsQuery,
  useProducts,
} from 'hooks';
import { useProviderInvoice } from 'hooks/provider-invoice';
import { useAppDispatch, useAppSelector } from 'store';

import {
  fetchInvoiceDetailsByProviderIdEdit,
  fetchInvoiceLinesByProviderIdCreate,
} from 'store/slices/provider_invoice.slices';

import { Invoice, InvoiceLine, Avoir, Comment } from 'types';
import { ReactComponent as AddRegulIcon } from 'assets/icons/add-regul-icon.svg';

const { Text } = Typography;

/**
 * ProviderInvoiceCreate Component
 *
 * OVERVIEW:
 * This component handles the creation and editing of provider invoices with a complex
 * hierarchical table structure that requires careful filtering and row span management.
 *
 * KEY FEATURES:
 * 1. Hierarchical Data Display: invoiceDetails → invoiceLines
 * 2. Multi-level Filtering: Form filters + Display filters
 * 3. Dynamic Row Spans: Automatically calculated based on data grouping
 * 4. Real-time Updates: Prices, quantities, and selections update dynamically
 *
 * CRITICAL COMPONENTS:
 * - serviceLines: Current filtered data displayed in table
 * - originalServiceLines: Unfiltered source data for filtering operations
 * - filteredServiceLines: Final data after all filters applied
 * - recalculateGroupingFlags(): Ensures row spans are correct after filtering
 *
 * FILTERING ARCHITECTURE:
 * 1. Form Filters (date, command, address, product) → handleFilterChange()
 * 2. Display Filters (showSelectedRows) → filteredServiceLines useMemo
 * 3. Row spans recalculated at each filtering step
 *
 * DEBUGGING TIPS:
 * - Check console logs with 🔍, 📊, 🎯 emojis for filtering operations
 * - Verify isFirstLineOfDetail and totalLinesInDetail flags
 * - Ensure originalServiceLines is never modified by filters
 */
const ProviderInvoiceCreate = () => {
  const location = useLocation();
  const navigate = useNavigate();

  // ============================================================================
  // 1. EXTRACT DATA FROM LOCATION STATE (PASSED FROM PREVIOUS PAGE)
  // ============================================================================
  const { initialValues, invoiceData, mode: locationMode, invoiceId } = (location.state || {}) as {
    initialValues?: Invoice;
    invoiceData?: any; // Data passed from provider invoice list page
    mode?: 'create' | 'edit';
    invoiceId?: string;
  };

  // Default to 'create' mode if not specified
  const mode = locationMode || 'create';

  // ============================================================================
  // 2. DETERMINE INVOICE DATA SOURCE BASED ON MODE
  // ============================================================================

  // For EDIT mode: Use data from location.state (passed from previous page)
  // For CREATE mode: Use filtered data from previous page or default structure

  const defaultInvoice: Invoice = {
    id: Date.now(),
    createdAt: new Date().toISOString(),
    prestataire: 'Prestataire inconnu',
    prestataireInvoiceNumber: 'N° FACTURE',
    status: 'brouillon',
    totalSelected: 0,
    totalAvoirs: 0,
    totalAmount: 0,
    invoicedAmount: 0,
    avoirDetails: [],
    commentDetails: [],
    invoiceDetails: []
  };

  // EDIT MODE: Use data passed from provider invoice list page
  // CREATE MODE: Use filtered data from previous page or default
  const baseInvoiceData = useMemo(() => {
    if (mode === 'edit' && invoiceData) {
      // Convert invoiceData from list page to Invoice format
      return {
        id: invoiceData.key || Date.now(),
        createdAt: invoiceData.createdAt || new Date().toISOString(),
        prestataire: invoiceData.prestataire || 'Prestataire inconnu',
        prestataireInvoiceNumber: invoiceData.invoiceNumber || 'N° FACTURE',
        status: invoiceData.status || 'brouillon',
        totalSelected: 0,
        totalAvoirs: 0,
        totalAmount: invoiceData.sellingPrice || 0,
        invoicedAmount: invoiceData.invoicingPrice || 0,
        avoirDetails: [],
        commentDetails: [],
        invoiceDetails: []
      };
    }
    return initialValues || defaultInvoice;
  }, [mode, invoiceData, initialValues]);

  // ============================================================================
  // 3. REDUX AND HOOKS SETUP
  // ============================================================================
  const dispatch = useAppDispatch();
  const {
    invoiceDetails,
    invoiceLines,
    invoiceDetailsLoading,
    invoiceLinesLoading
  } = useAppSelector((state) => state.providerInvoice);

  const {
    data: availableInvoiceDetails,
    fetchAvailableDetailsCreate,
    loading: saveLoading
  } = useProviderInvoice();

  // ============================================================================
  // 4. DETERMINE CURRENT INVOICE DATA BASED ON MODE
  // ============================================================================

  // For EDIT mode: Get data from Redux store (fetched from API)
  // For CREATE mode: Use data from previous page (location.state)

  const currentInvoiceData = useMemo(() => {
    if (mode === 'edit') {
      // In edit mode, use fetched data from Redux store
      const fetchedData = invoiceDetails?.[0]; // Assuming first item is the current invoice
      if (fetchedData) {
        return {
          ...baseInvoiceData,
          prestataire: fetchedData.prestataire || baseInvoiceData.prestataire,
          prestataireInvoiceNumber: fetchedData.prestataireInvoiceNumber || baseInvoiceData.prestataireInvoiceNumber,
          totalAmount: fetchedData.totalAmount || baseInvoiceData.totalAmount,
          // Add other fields as needed
        };
      }
    }
    // In create mode or if no fetched data, use base data
    return baseInvoiceData;
  }, [mode, invoiceDetails, baseInvoiceData]);

  // ============================================================================
  // 5. EXTRACT STABLE VALUES FOR DEPENDENCIES
  // ============================================================================
  const prestataire = currentInvoiceData?.prestataire;
  const prestataireInvoiceNumber = currentInvoiceData?.prestataireInvoiceNumber;
  const invoiceAmount = currentInvoiceData?.totalAmount || 0;

  // ============================================================================
  // 6. COMPONENT STATE INITIALIZATION
  // ============================================================================

  // Products data
  const [productsQuery] = useProductsQuery({
    orderBy: 'createdAt,desc|name',
    isVisible: 1,
  });
  const [getAllProducts] = useProducts(productsQuery);

  // Loading states
  const [loading, setLoading] = useState<boolean>(false);
  const [isInitialLoad, setIsInitialLoad] = useState(true);

  // ============================================================================
  // SERVICE LINES STATE - CRITICAL FOR FILTERING AND ROW SPAN LOGIC
  // ============================================================================

  /**
   * serviceLines: Current displayed lines (may be filtered)
   * - This is what gets displayed in the table
   * - Gets updated when filters are applied
   * - Contains calculated row span flags (isFirstLineOfDetail, totalLinesInDetail)
   */
  const [serviceLines, setServiceLines] = useState<InvoiceLine[]>([]);

  /**
   * originalServiceLines: Unfiltered source data
   * - Always contains the complete dataset from API
   * - Used as the base for all filtering operations
   * - Never gets modified by filters, only by data fetching
   */
  const [originalServiceLines, setOriginalServiceLines] = useState<any[]>([]);

  // Comments and avoirs (initialize from current invoice data)
  const [comments, setComments] = useState<Comment[]>(currentInvoiceData?.commentDetails || []);
  const [avoirs, setAvoirs] = useState<Avoir[]>(currentInvoiceData?.avoirDetails || []);

  // Selection and display states
  const [selectedLines, setSelectedLines] = useState<InvoiceLine[]>([]);
  const [showSelectedRows, setShowSelectedRows] = useState(false);
  const [showInvoicedRows, setShowInvoicedRows] = useState(false);

  // Form
  const [form] = Form.useForm();

  // Modal states
  const [fileUploadModalVisible, setFileUploadModalVisible] = useState(false);
  const [selectedLineForFiles, setSelectedLineForFiles] = useState<InvoiceLine | null>(null);
  const [lineFiles, setLineFiles] = useState<Record<string, any[]>>({});

  const [wasteTypeModalVisible, setWasteTypeModalVisible] = useState(false);
  const [selectedLineForWasteTypes, setSelectedLineForWasteTypes] = useState<InvoiceLine | null>(null);
  const [lineWasteTypes, setLineWasteTypes] = useState<Record<string, any[]>>({});

  const [RegulModalVisible, setRegulModalVisible] = useState(false);
  const [selectedLineForRegul, setSelectedLineForRegul] = useState<InvoiceLine | null>(null);
  const [lineRegul, setLineRegul] = useState<Record<string, any[]>>({});

  // ============================================================================
  // 7. COMPUTED VALUES
  // ============================================================================
  const totalAvoirs = useMemo(() =>
    avoirs.reduce((sum, avoir) => sum + avoir.amount, 0),
    [avoirs]
  );
  const avoirsCount = useMemo(() => avoirs.length, [avoirs]);

  const currentUser = 'Jean Dupont';

  const mainTitleChild = `${prestataire || 'Prestataire inconnu'} - ${prestataireInvoiceNumber?.toUpperCase() || 'N° FACTURE'}`;

  // ============================================================================
  // 8. DATA FETCHING LOGIC - CLEAN AND ORGANIZED
  // ============================================================================

  // Track what data has been fetched to prevent multiple calls
  const fetchedDataRef = useRef<Set<string>>(new Set());

  // SINGLE useEffect for data fetching - handles both edit and create modes
  useEffect(() => {
    const fetchData = async () => {
      // Skip if no prestataire
      if (!prestataire || prestataire === 'Prestataire inconnu') {
        console.log('❌ No valid prestataire found, skipping data fetch');
        return;
      }

      // Create unique key to prevent duplicate fetches
      const fetchKey = `${mode}-${prestataire}`;
      if (fetchedDataRef.current.has(fetchKey)) {
        console.log('📋 Data already fetched for this combination, skipping...');
        return;
      }

      try {
        console.log(`🔄 Fetching data for mode: ${mode}, prestataire: ${prestataire}`);
        fetchedDataRef.current.add(fetchKey);

        if (mode === 'edit') {
          // EDIT MODE: Fetch invoice details for this specific invoice
          console.log('📥 Fetching invoice details for edit mode...');
          await dispatch(fetchInvoiceDetailsByProviderIdEdit({ prestataire }));
        } else {
          // CREATE MODE: Fetch available invoice lines for this prestataire
          console.log('📥 Fetching invoice lines for create mode...');
          await dispatch(fetchInvoiceLinesByProviderIdCreate({ prestataire }));
        }
      } catch (error) {
        console.error('❌ Error fetching data:', error);
        fetchedDataRef.current.delete(fetchKey); // Allow retry on error
      }
    };

    fetchData();
  }, [dispatch, prestataire, mode]);

  // SINGLE useEffect for processing fetched data into service lines
  useEffect(() => {
    const dataSource = mode === 'edit' ? invoiceDetails : invoiceLines;

    console.log(`🔍 DEBUG - Mode: ${mode}`);
    console.log(`🔍 DEBUG - invoiceDetails:`, invoiceDetails);
    console.log(`🔍 DEBUG - invoiceLines:`, invoiceLines);
    console.log(`🔍 DEBUG - dataSource:`, dataSource);

    if (!dataSource || dataSource.length === 0) {
      console.log(`📭 No ${mode === 'edit' ? 'invoice details' : 'invoice lines'} data available`);
      return;
    }

    console.log(`🔄 Processing ${mode === 'edit' ? 'invoice details' : 'invoice lines'} data into service lines...`);
    const allServiceLines: any[] = [];

    const processInvoiceData = (invoice: any) => {
      try {
        if (!invoice.invoiceDetails || !Array.isArray(invoice.invoiceDetails)) {
          return;
        }

        invoice.invoiceDetails.forEach((detail: any, detailIndex: number) => {

          if (!detail.invoiceLines || !Array.isArray(detail.invoiceLines)) {
            return;
          }

          detail.invoiceLines.forEach((line: any, lineIndex: number) => {
            const serviceLineData = {
              ...line,
              // Add unique identifiers for grouping
              detailId: `${invoice.id || 'current'}-${detailIndex}`,
              lineId: `${invoice.id || 'current'}-${detailIndex}-${lineIndex}`,
              // Add unique key for React rendering to prevent duplicate key warnings
              key: `${invoice.id || 'current'}-${detailIndex}-${lineIndex}`,
              isFirstLineOfDetail: lineIndex === 0, // Flag to show detail info only on first line
              totalLinesInDetail: detail.invoiceLines.length, // For rowspan calculation

              // Parent detail context (for first column)
              parentDetail: detail,
              detailProductName: detail.productName, // Main product name from detail
              siteAddress: detail.siteAddress,
              sitePostalCode: detail.sitePostalCode,
              siteCity: detail.siteCity,
              prestationDetails: detail.prestationDetails,
              commandNumber: detail.commandNumber,
              contact: detail.contact,
              invoiceNumber: detail.invoiceNumber,

              // Detail-level prices (for summary in first column)
              detailInvoicedPrice: detail.invoicedPrice,
              detailClientPrice: detail.clientPrice,
              detailTotalMargin: detail.totalMargin,
              detailTotalDiffPurchase: detail.totalDiffPurchase,

              // Invoice context
              invoiceId: invoice.id,
              prestataire: invoice.prestataire,
              prestataireInvoiceNumber: invoice.prestataireInvoiceNumber
            };
            allServiceLines.push(serviceLineData);
          });
        });
      } catch (error) {
        console.error(`❌ Error processing invoice ${invoice.prestataire}:`, error);
      }
    };

    // Process all data
    dataSource.forEach((invoice: any, index: number) => {
      console.log(`📝 Processing ${mode === 'edit' ? 'invoice details' : 'invoice lines'} option ${index + 1}: ${invoice.prestataire}`);
      processInvoiceData(invoice);
    });

    console.log('🎯 FINAL RESULTS:');
    console.log(`   - Total service lines: ${allServiceLines.length}`);
    console.log('   - Service lines breakdown:');
    allServiceLines.forEach((line, index) => {
      console.log(`     ${index + 1}. ${line.prestataire} - ${line.productName} (Detail: ${line.detailId}, Line: ${line.lineId}, Key: ${line.key})`);
    });

    setServiceLines(allServiceLines);
    setOriginalServiceLines(allServiceLines); // Store original data for filtering

    // Update comments and avoirs from fetched data (only if not already set)
    const fetchedComments = dataSource.flatMap((invoice: any) => invoice.commentDetails || []);
    const fetchedAvoirs = dataSource.flatMap((invoice: any) => invoice.avoirDetails || []);

    // For edit mode, use fetched data; for create mode, keep existing data
    if (mode === 'edit' && fetchedComments.length > 0) {
      setComments(fetchedComments);
    }
    if (mode === 'edit' && fetchedAvoirs.length > 0) {
      setAvoirs(fetchedAvoirs);
    }

    console.log('✅ Service lines processed successfully:', {
      serviceLineCount: allServiceLines.length,
      mode: mode,
      dataSourceLength: dataSource.length
    });
  }, [invoiceDetails, invoiceLines, mode]);

  // Initialize selected lines when serviceLines change
  useEffect(() => {
    const initialSelectedLines = serviceLines.filter(line => line.isSelected);
    setSelectedLines(initialSelectedLines);
  }, [serviceLines]);

  const totalSelectedAmount = useMemo(
    () =>
      selectedLines
        .reduce((sum, line) => sum + line.invoicedPrice, 0),
    [selectedLines]
  );

  const isSaveButtonDisabled = useMemo(() => {
    return (
      invoiceAmount === (totalAvoirs + totalSelectedAmount)
    );
  }, [totalAvoirs, totalSelectedAmount, invoiceAmount]);
    

  const handleAddAvoir = (data: { amount: number; avoirNumber: string }) => {
    const newAvoir: Avoir = {
      id: Date.now(), // Temporary unique ID
      avoirNumber: data.avoirNumber,
      createdAt: new Date().toISOString(),
      amount: data.amount,
      createdBy: currentUser,
    };
    
    setAvoirs([...avoirs, newAvoir]);
    console.log('Ajout d\'avoir:', newAvoir);
  };

  // Handle removing an avoir
  const handleRemoveAvoir = (id: number) => {
    setAvoirs(avoirs.filter(a => a.id !== id));
  };
  
  const simulateLoading = (timeout: number): void => {
    setLoading(true);
    setTimeout(() => {
      setLoading(false);
    }, timeout);
  };

  // Toggle selection of a service line
  const toggleLineSelection = (key: string) => {
    const updatedLines = serviceLines.map(line =>
      line.key === key ? { ...line, isSelected: !line.isSelected } : line
    );
    const newSelectedLines = updatedLines.filter(line => line.isSelected);
    setServiceLines(updatedLines);
    setSelectedLines(newSelectedLines);
  };

  const updateInvoicedPrice = (key: string, unitPrice: number) => {
    const updatedLines = serviceLines.map(line => {
      if (line.key === key) {
        const updatedLine = {
          ...line,
          invoicedPrice: unitPrice,
          totalInvoicedPrice: unitPrice * (line.quantity || 1), // Recalculate total
        };
        return updatedLine;
      }
      return line;
    });

    setServiceLines(updatedLines);

    if (selectedLines.some(line => line.key === key)) {
      const newSelectedLines = selectedLines.map(line => {
        if (line.key === key) {
          return {
            ...line,
            invoicedPrice: unitPrice,
            totalInvoicedPrice: unitPrice * (line.quantity || 1),
          };
        }
        return line;
      });
      setSelectedLines(newSelectedLines);
    }
  };
  const updateQuantity = (key: string, quantity: number) => {
    const updatedLines = serviceLines.map(line => {
      if (line.key === key) {
        const updatedLine = {
          ...line,
          quantity,
          totalInvoicedPrice: (line.invoicedPrice || 0) * quantity, // Recalculate
        };
        return updatedLine;
      }
      return line;
    });

    setServiceLines(updatedLines);

    if (selectedLines.some(line => line.key === key)) {
      const newSelectedLines = selectedLines.map(line => {
        if (line.key === key) {
          return {
            ...line,
            quantity,
            totalInvoicedPrice: (line.invoicedPrice || 0) * quantity,
          };
        }
        return line;
      });
      setSelectedLines(newSelectedLines);
    }
  };

  const updateTotalInvoicedPrice = (key: string, total: string | null) => {
    const totalNumber = total ? parseFloat(total) : 0;
    const updatedLines = serviceLines.map(line => {
      if (line.key === key) {
        const newUnitPrice = line.quantity > 0 ? totalNumber / line.quantity : 0;
        return {
          ...line,
          invoicedPrice: newUnitPrice,
          totalInvoicedPrice: totalNumber,
        };
      }
      return line;
    });

    setServiceLines(updatedLines);

    if (selectedLines.some(line => line.key === key)) {
      const newSelectedLines = selectedLines.map(line => {
        if (line.key === key) {
          const newUnitPrice = line.quantity > 0 ? totalNumber / line.quantity : 0;
          return {
            ...line,
            invoicedPrice: newUnitPrice,
            totalInvoicedPrice: totalNumber,
          };
        }
        return line;
      });
      setSelectedLines(newSelectedLines);
    }
  };

  const handleSendEmail = (record: any, facture: string, montant: string, date: string, address: string) => {
    const subject = encodeURIComponent('ECODROP - ${facture}');
    const body = encodeURIComponent(
      `Bonjour,\n\nJe vous contacte concernant la facture ${facture} d'un montant de ${montant}.\n` +
      `La prestation ${record.detailProductName || ''} à l'adresse de chantier ${address} au ${date}.\n\n` +
      `Bien cordialement,\n`
    );
    const mailtoLink = `mailto:${record.contact}?subject=${subject}&body=${body}`;
    window.location.href = mailtoLink;
  };

  // Handle saving with status using the new API service
  const handleSaveWithStatus = async (status: string) => {
    try {
      console.log('🔄 Saving invoice with status:', status);

      // Prepare the data for the API call
      const saveData = {
        currentInvoice: currentInvoiceData as Invoice, // Ensure proper typing
        selectedLines: selectedLines,
        comments: comments,
        avoirs: avoirs,
        status: status,
        totalSelectedAmount: totalSelectedAmount,
        // Additional metadata
        savedAt: new Date().toISOString(),
        savedBy: currentUser,
        // Include form data if needed
        formData: form.getFieldsValue()
      };

      console.log('📤 Sending save request with data:', saveData);

      // Call the API service
      // const response = await saveInvoice(saveData);

      // console.log('✅ Invoice saved successfully:', response);

      // Show success message (you can replace with toast notification)
      alert(`Facture sauvegardée avec le statut: ${status}`);

      // Optionally navigate back or refresh data
      // navigate('/logistique/factures-fournisseurs');

    } catch (error) {
      console.error('❌ Failed to save invoice:', error);

      // Show error message (you can replace with toast notification)
      alert(`Erreur lors de la sauvegarde: ${error instanceof Error ? error.message : 'Erreur inconnue'}`);
    }
  };

  // Handle adding comment
  const handleAddComment = (newComment:any) => {
    setComments([...comments, newComment]);
    console.log('Adding comment:', newComment.comment);
    console.log('Adding comment:', newComment);

    // Logique pour ajouter un commentaire
  };

  // Simulate loading for 2 seconds
  useEffect(() => {
    simulateLoading(2000);
  }, []);

  const handleAnnuler = () => {
    navigate('/logistique/factures-fournisseurs');
  };

  // File upload modal handlers
  const handleOpenFileUploadModal = (line: InvoiceLine) => {
    setSelectedLineForFiles(line);
    setFileUploadModalVisible(true);
  };

  const handleCloseFileUploadModal = () => {
    setFileUploadModalVisible(false);
    setSelectedLineForFiles(null);
  };

  const handleSaveFiles = (files: any[]) => {
    if (selectedLineForFiles) {
      setLineFiles(prev => ({
        ...prev,
        [selectedLineForFiles.key]: files
      }));
      console.log('Files saved for line:', selectedLineForFiles.key, files);
    }
  };

  // Waste type modal handlers
  const handleOpenWasteTypeModal = (line: InvoiceLine) => {
    setSelectedLineForWasteTypes(line);
    setWasteTypeModalVisible(true);
  };

  const handleCloseWasteTypeModal = () => {
    setWasteTypeModalVisible(false);
    setSelectedLineForWasteTypes(null);
  };

  const handleSaveWasteTypes = (wasteTypes: any[]) => {
    if (selectedLineForWasteTypes) {
      setLineWasteTypes(prev => ({
        ...prev,
        [selectedLineForWasteTypes.key]: wasteTypes
      }));
      console.log('Waste types saved for line:', selectedLineForWasteTypes.key, wasteTypes);
    }
  };

  const handleOpenRegulModal = (line: InvoiceLine) => {
    setSelectedLineForRegul(line);
    setRegulModalVisible(true);
  };

  const handleCloseRegulModal = () => {
    setRegulModalVisible(false);
    setSelectedLineForRegul(null);
  };

  const handleSaveRegul = (wasteTypes: any[]) => {
    // if (selectedLineForWasteTypes) {
    //   setLineWasteTypes(prev => ({
    //     ...prev,
    //     [selectedLineForWasteTypes.key]: wasteTypes
    //   }));
    //   console.log('Waste types saved for line:', selectedLineForWasteTypes.key, wasteTypes);
    // }
  };

  // ============================================================================
  // IMPROVED ROW SPAN CALCULATION WITH DETAILED LOGGING AND ERROR HANDLING
  // ============================================================================

  /**
   * Recalculates grouping flags for filtered data to ensure proper row spans
   * This function is critical for maintaining correct table display when data is filtered
   *
   * @param lines - Array of invoice lines to recalculate grouping for
   * @returns Array of lines with updated grouping flags
   */
  const recalculateGroupingFlags = (lines: any[]) => {
    console.log('🔄 Recalculating grouping flags for', lines.length, 'lines');

    if (!lines || lines.length === 0) {
      console.log('📭 No lines to recalculate, returning empty array');
      return [];
    }

    // Group lines by detailId to recalculate grouping flags
    const groupedByDetail: { [key: string]: any[] } = {};

    lines.forEach((line, index) => {
      const detailId = line.detailId;
      if (!detailId) {
        console.warn(`⚠️ Line at index ${index} missing detailId:`, line.key);
        return;
      }

      if (!groupedByDetail[detailId]) {
        groupedByDetail[detailId] = [];
      }
      groupedByDetail[detailId].push(line);
    });

    console.log('📊 Grouped lines by detail:', Object.keys(groupedByDetail).map(detailId => ({
      detailId,
      lineCount: groupedByDetail[detailId].length
    })));

    // Recalculate flags for each group
    const recalculatedLines: any[] = [];
    Object.entries(groupedByDetail).forEach(([detailId, detailLines]) => {
      console.log(`🔧 Processing detail ${detailId} with ${detailLines.length} lines`);

      detailLines.forEach((line, index) => {
        const updatedLine = {
          ...line,
          isFirstLineOfDetail: index === 0,
          totalLinesInDetail: detailLines.length
        };

        recalculatedLines.push(updatedLine);

        // Log first line of each detail for debugging
        if (index === 0) {
          console.log(`📌 First line of detail ${detailId}:`, {
            key: line.key,
            totalLinesInDetail: detailLines.length,
            productName: line.detailProductName || line.productName
          });
        }
      });
    });

    console.log('✅ Recalculation complete:', {
      originalLines: lines.length,
      recalculatedLines: recalculatedLines.length,
      detailGroups: Object.keys(groupedByDetail).length
    });

    return recalculatedLines;
  };

  // ============================================================================
  // IMPROVED FILTERING ALGORITHM WITH BETTER ERROR HANDLING AND LOGIC
  // ============================================================================

  /**
   * FILTERING PROCESS OVERVIEW:
   *
   * 1. Form filters (date, commandeNumber, siteAddress, product) are applied via handleFilterChange()
   * 2. This updates serviceLines state with filtered + recalculated data
   * 3. Display filters (showSelectedRows) are applied via filteredServiceLines useMemo
   * 4. Final result is passed to Table component
   *
   * DATA FLOW:
   * originalServiceLines (source)
   *   → [form filters] → serviceLines (state)
   *   → [display filters] → filteredServiceLines (final)
   *   → Table component
   *
   * ROW SPAN RECALCULATION:
   * - Happens after form filtering (in handleFilterChange)
   * - Happens after display filtering (in filteredServiceLines useMemo)
   * - Ensures table displays correctly regardless of filtering combination
   */
  const handleFilterChange = () => {
    // Skip filtering on initial load
    if (isInitialLoad) {
      console.log('🔄 Initial load detected, skipping filter');
      setIsInitialLoad(false);
      return;
    }

    const { date, commandeNumber, siteAddress, product } = form.getFieldsValue();

    console.log('🔍 DEBUGGING - Filter values:', { date, commandeNumber, siteAddress, product });
    console.log('🔍 DEBUGGING - Original service lines count:', originalServiceLines.length);
    console.log('🔍 DEBUGGING - Sample line structure:', originalServiceLines[0]);

    // If no filters are applied, reset to original serviceLines
    if (!date && !commandeNumber && !siteAddress && !product) {
      console.log('📭 No filters applied, resetting to original data');
      setServiceLines([...originalServiceLines]); // Create new array to trigger re-render
      return;
    }

    // Apply filters to original serviceLines (not current filtered ones)
    let filtered = originalServiceLines.filter((line, index) => {
      try {
        console.log(`🔍 DEBUGGING - Processing line ${index}:`, {
          key: line.key,
          commandNumber: line.commandNumber,
          invoiceNumber: line.invoiceNumber,
          productName: line.productName,
          detailProductName: line.detailProductName,
          siteAddress: line.siteAddress,
          prestationDetails: line.prestationDetails
        });

        // ========================================================================
        // DATE FILTER - Simplified and more robust
        // ========================================================================
        let dateMatch = true;
        if (date) {
          dateMatch = false; // Start with false, set to true if match found

          if (line.prestationDetails && Array.isArray(line.prestationDetails)) {
            const filterDateStr = new Date(date).toISOString().split('T')[0]; // YYYY-MM-DD format

            dateMatch = line.prestationDetails.some((detail: any) => {
              if (!detail?.date) return false;

              // Try different date formats
              const detailDateStr = new Date(detail.date).toISOString().split('T')[0];
              const match = detailDateStr === filterDateStr;

              console.log(`   📅 Date comparison: ${detailDateStr} === ${filterDateStr} = ${match}`);
              return match;
            });
          }

          console.log(`   📅 Final date match: ${dateMatch}`);
        }

        // ========================================================================
        // COMMAND NUMBER FILTER - Simplified
        // ========================================================================
        let commandeMatch = true;
        if (commandeNumber && commandeNumber.trim()) {
          const searchTerm = commandeNumber.toLowerCase().trim();

          const fieldsToCheck = [
            line.commandNumber,
            line.invoiceNumber,
            line.orderId,
            line.zohoId,
            line.commandId
          ];

          commandeMatch = fieldsToCheck.some(field => {
            if (!field) return false;
            const fieldStr = String(field).toLowerCase();
            const match = fieldStr.includes(searchTerm);
            console.log(`   🔢 Command check: "${fieldStr}" includes "${searchTerm}" = ${match}`);
            return match;
          });

          console.log(`   🔢 Final command match: ${commandeMatch}`);
        }

        // ========================================================================
        // SITE ADDRESS FILTER - Simplified
        // ========================================================================
        let addressMatch = true;
        if (siteAddress && siteAddress.trim()) {
          const searchTerm = siteAddress.toLowerCase().trim();

          const addressFields = [
            line.siteAddress,
            line.sitePostalCode,
            line.siteCity
          ];

          addressMatch = addressFields.some(field => {
            if (!field) return false;
            const fieldStr = String(field).toLowerCase();
            const match = fieldStr.includes(searchTerm);
            console.log(`   🏠 Address check: "${fieldStr}" includes "${searchTerm}" = ${match}`);
            return match;
          });

          console.log(`   🏠 Final address match: ${addressMatch}`);
        }

        // ========================================================================
        // PRODUCT FILTER - Simplified
        // ========================================================================
        let productMatch = true;
        if (product) {
          productMatch = false; // Start with false

          if (typeof product === 'string' && product.trim()) {
            const searchTerm = product.toLowerCase().trim();

            const productFields = [
              line.detailProductName,
              line.productName
            ];

            productMatch = productFields.some(field => {
              if (!field) return false;
              const fieldStr = String(field).toLowerCase();
              const match = fieldStr.includes(searchTerm);
              console.log(`   📦 Product check: "${fieldStr}" includes "${searchTerm}" = ${match}`);
              return match;
            });
          } else {
            // Product ID match
            productMatch = line.productId === product ||
                          line.detailProductId === product ||
                          String(line.productId) === String(product);
            console.log(`   📦 Product ID check: ${line.productId} === ${product} = ${productMatch}`);
          }

          console.log(`   📦 Final product match: ${productMatch}`);
        }

        const finalResult = dateMatch && commandeMatch && addressMatch && productMatch;

        console.log(`   ✅ Line ${index} final result: ${finalResult} (date:${dateMatch}, cmd:${commandeMatch}, addr:${addressMatch}, prod:${productMatch})`);

        return finalResult;

      } catch (error) {
        console.error('❌ Error filtering line:', error, line);
        return false; // Exclude lines that cause errors
      }
    });

    console.log(`🎯 Filtering results: ${filtered.length} / ${originalServiceLines.length} lines match`);

    // Recalculate grouping flags for filtered data
    if (filtered.length > 0) {
      filtered = recalculateGroupingFlags(filtered);
    }

    console.log('✅ Setting filtered lines:', filtered);
    setServiceLines(filtered);
  };


  // ============================================================================
  // TABLE COLUMN DEFINITIONS WITH ROW SPAN LOGIC
  // ============================================================================

  /**
   * IMPORTANT: Understanding the Table Structure and Row Spans
   *
   * This table displays a hierarchical structure:
   * - Each invoiceDetail can contain multiple invoiceLines
   * - Some columns (like "Commande/Adresse/Date") show detail-level info and span multiple rows
   * - Other columns show line-level info and appear on every row
   *
   * Row Span Logic:
   * - isFirstLineOfDetail: true only for the first line of each detail group
   * - totalLinesInDetail: number of lines in the current detail group
   * - Columns that show detail info use rowSpan = totalLinesInDetail on first line, 0 on others
   * - Columns that show line info always use rowSpan = 1 (default)
   *
   * Filtering Impact:
   * - When lines are filtered, detail groups may be broken
   * - Row spans must be recalculated to maintain proper display
   * - This is handled by recalculateGroupingFlags() function
   */
  const columns = [
    // Column 1: Commande / Adresse / Date - Shows invoiceDetail info (grouped by detail)
    // This shows the parent invoiceDetail information for each invoiceLine
    {
      title: 'Commande / Adresse / Date',
      key: 'commandeAdresseDate',
      width: 180,
      onCell: (record: any) => {
        // Only show content on the first line of each detail, span multiple rows
        if (record.isFirstLineOfDetail) {
          return {
            rowSpan: record.totalLinesInDetail,
            style: {
              verticalAlign: 'middle', // Critical for vertical centering
              paddingTop: 0,
              paddingBottom: 0,
            },
          };
        }
        return {
          rowSpan: 0, // Hide cell for subsequent lines of the same detail
        };
      },
      render: (_: any, record: any) => {
        // Only render content for the first line of each detail
        if (!record.isFirstLineOfDetail) {
          return null;
        }

        return (
          <div style={{ display: 'flex', alignItems: 'center', height: '100%' }}>
          <div style={{ display: 'flex', flexDirection: 'column', textAlign: 'left', height: '100%', justifyContent: 'center' }}>

            <div style={{ fontWeight: 'bold', marginBottom: 5, }}>
              {record.detailProductName || 'Transport/Service'}
            </div>
            <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
              <strong>{record.invoiceNumber || 'N/A'}</strong> 
            </div>
            <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
              <strong>{record.commandNumber || 'N/A'}</strong> 
            </div>
            <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
              {record.siteAddress},<br />
              {record.sitePostalCode} {record.siteCity}
            </div>
            <div style={{ fontSize: '0.85em', marginBottom: 5 }}>
              {record.prestationDetails?.map((detail: any, index: number) => (
                <div key={index} style={{ marginBottom: 2 }}>
                  {detail.type} : {detail.date} - {detail.time}
                </div>
              )) || 'Aucun détail de prestation'}
            </div>
            <div style={{ fontSize: '0.85em' }}>
              <div><strong>Prix facturé:</strong> {(record.detailInvoicedPrice || 0).toFixed(2)} €</div>
              <div><strong>Prix client:</strong> {(record.detailClientPrice || 0).toFixed(2)} €</div>
              <div style={{ color: '#2ba748' }}><strong>Marge:</strong> {record.detailTotalMargin || 0}%</div>
              <div style={{ color: '#2ba748' }}><strong>Diff. Achat:</strong> {(record.detailTotalDiffPurchase || 0).toFixed(2)} €</div>
            </div>
            <div style={{ fontSize: '0.85em', marginTop: 8, padding: 8, }}>
              <Button
                type={record.isSelected ?  'default' : 'primary'}
                size="small"
                onClick={() => toggleLineSelection(record.key)}
                style={
                  record.isSelected
                    ? {
                        backgroundColor: '#1890ff',
                        borderColor: '#1890ff',
                        color: '#fff',
                        fontWeight: 'bold',
                      }
                  : {}
                }
              >
                {record.isSelected ? 'Désélectionner' : 'Sélectionner'}
              </Button>
            </div>
          </div>
          </div>
        );
      },
    },
    // Column 2: Prestation / Commande client - Shows invoiceLine info (one row per line)
    // This shows individual invoiceLine details
    {
      title: 'Prestation / Commande client',
      key: 'prestationCommande',
      width: 300,
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
            paddingTop: 0,
            paddingBottom: 0,
          },
        };
      },
      render: (_: any, record: any) => {
        const hasRibbon = record.isSelected || record.isInvoiced;
        const content = (
          <div  style={{ paddingTop: hasRibbon ? 30 : 0 }}>
            <div style={{ fontWeight: 'bold', marginBottom: 5 }}>
              {record.productName}
            </div>
            <a
              href={record.orderLink}
              target="_blank"
              rel="noopener noreferrer"
              className="btn-add__creation-icon"
              style={{ fontSize: '0.85em', textDecoration: 'underline' }}
            >
              {record.orderId}
            </a>
            <Divider style={{ margin: '8px 0' }} />
            <InvoiceComment
              comments={record.comment}
              currentUser={currentUser}
              onAddComment={handleAddComment} // needs to be aupdated
              isCommentLine={true}
            />
          </div>
        );

        let wrappedContent = content;
        if (record.isInvoiced) {
          wrappedContent = <Badge.Ribbon text="Facturé" color="blue" placement="start" >{wrappedContent}</Badge.Ribbon>;
        }
        if (record.isSelected) {
          wrappedContent = <Badge.Ribbon text="Sélectionné"  placement="start">{wrappedContent}</Badge.Ribbon>;
        }

        return wrappedContent;
      },
    },

    // Column 3: Prix unitaire - Shows invoiceLine pricing (one row per line)
    // This shows individual invoiceLine pricing details
    {
      title: 'Prix unitaire',
      key: 'prixUnitaire',
      width: 180,
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
            paddingTop: 0,
            paddingBottom: 0,
          },
        };
      },
      render: (_: any, record: any) => (
        <div>
          <div style={{ marginBottom: 5 }}>
            <span >Prix com:</span> {(record.purchasePrice || 0).toFixed(2)} €
          </div>
          <div style={{ marginBottom: 5 }}>
            <span >Prix facturé:</span>
            <InputNumber
              min={0}
              step={0.01}
              value={record.invoicedPrice}
              onChange={(value) => updateInvoicedPrice(record.key, value as number)}
              style={{ width: '90px', marginLeft: '5px' }}
            />
          </div>
          <div style={{ color: '#2ba748' }}>
            <span >Diff achat:</span> {((record.invoicedPrice || 0) - (record.purchasePrice || 0)).toFixed(2)} €
          </div>
        </div>
      ),
    },
    // Column 4: Prix commande - Shows invoiceLine order info (one row per line)
    // This shows individual invoiceLine order details
    {
      title: 'Prix commande',
      key: 'prixCommande',
      width: 160,
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
            paddingTop: 0,
            paddingBottom: 0,
          },
        };
      },
      render: (_: any, record: any) => (
        <>
          <div style={{ textAlign: 'center' }}>
            <InputNumber
              min={0}
              step={0.01}
              value={record.quantity}
              onChange={(value) => updateQuantity(record.key, value as number)}
              style={{ width: '90px', marginLeft: '5px' }}
            />
            <span style={{ fontWeight: 'bold' }}>{(record.unity)}</span>
          </div>
          <div style={{ textAlign: 'center' }}>
            <span >Prix facturé :</span>
            <InputNumber
              // min={0}
              step={0.01}
              value= {((record.invoicedPrice || 0) * (record.quantity || 1)).toFixed(2)} 
              onChange={(value) => updateTotalInvoicedPrice(record.key, value)}
              style={{ width: '90px', marginLeft: '5px' }}
            />
          </div>
          <div style={{ textAlign: 'center' }}>
            <div style={{ fontSize: '0.85em', marginTop: 5 }}>
              Prix client : {((record.clientPrice || 0) * (record.quantity || 1)).toFixed(2)} €
            </div>
            <div style={{ fontSize: '0.85em', marginTop: 5, color: '#2ba748' }}>
              Marge total: {(record.margin).toFixed(2)} %
            </div>
          </div>
        </>
      ),
    },
    // Column 5: Actions - Shows actions for each invoiceLine
    {
      title: 'Actions',
      key: 'actions',
      width: 150,
      onCell: () => {
        return {
          style: {
            verticalAlign: 'middle',
            paddingTop: 0,
            paddingBottom: 0,
          },
        };
      },
      render: (_: any, record: any) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 5, alignItems: 'center' }}>
          <MailOutlined
            style={{
              cursor: 'pointer',
              color: '#807f84',
              fontSize: '22px',
              padding: '4px'
            }}
            onClick={() =>
              handleSendEmail(
                record,
                record.invoiceNumber || 'N/A',
                (record.totalInvoicedPrice || 0).toFixed(2) + ' €',
                record.date || 'N/A',
                `${record.siteAddress}, ${record.sitePostalCode} ${record.siteCity}`
              )
            }
          />

          {!record.isInvoiced && (
            <>
              <Button
                type={record.isWaitingForPrestReply ? 'primary' : 'default'}
                size="small"
                onClick={() => {
                  /* Handle presta reply */
                }}
                style={{ color: '#ffffff', backgroundColor: '#ffa940' }}
                disabled={record.isSelected}
              >
                {!record.isWaitingForPrestReply ? 'En attente presta.' : 'Répondu'}
              </Button>
              <Button
                type={record.isSelected ? 'default' : 'primary'}
                size="small"
                onClick={() => toggleLineSelection(record.key)}
                style={
                  record.isSelected
                    ? {
                        backgroundColor: '#1890ff',
                        borderColor: '#1890ff',
                        color: '#fff',
                        fontWeight: 'bold',
                      }
                    : {}
                }
              >
                {record.isSelected ? 'Désélectionner' : 'Sélectionner'}
              </Button>
            </>
          )}
        </div>
      )

    },
    // Column 6: Actions - Shows actions for each invoiceLine
    {
      title: '',
      key: 'plusActions',
      width: 50,
      // onCell: () => {
      //   return {
      //     style: {
      //       verticalAlign: 'middle',
      //       paddingTop: 0,
      //       paddingBottom: 0,
      //     },
      //   };
      // },
      onCell: (record: any) => {
        // Only show content on the first line of each detail, span multiple rows
        if (record.isFirstLineOfDetail) {
          return {
            rowSpan: record.totalLinesInDetail,
            style: {
              verticalAlign: 'middle', // Critical for vertical centering
              paddingTop: 0,
              paddingBottom: 0,
            },
          };
        }
        return {
          rowSpan: 0, // Hide cell for subsequent lines of the same detail
        };
      },
      render: (_: any, record: any) => (
        <div style={{ display: 'flex', flexDirection: 'column', gap: 5, alignItems: 'center' }}>
          <TruckOutlined
            style={{
              cursor: 'pointer',
              color: '#807f84',
              fontSize: '22px',
              padding: '4px'
            }}
            onClick={() => handleOpenWasteTypeModal(record)}
            title="Ajouter un type de déchets"
          />
          <AddRegulIcon 
            style={{
              cursor: 'pointer',
              color: '#807f84',
            }}
            onClick={() => handleOpenRegulModal(record)}
            title="Ajouter des reguls"
          />
          {/* <PlusCircleOutlined
            style={{
              cursor: 'pointer',
              color: '#807f84',
              fontSize: '22px',
              padding: '4px'
            }}
          /> */}
          <UploadOutlined
            style={{
              cursor: 'pointer',
              color: '#807f84',
              fontSize: '22px',
              padding: '4px'
            }}
            onClick={() => handleOpenFileUploadModal(record)}
            title="Gérer les fichiers"
          />
          
        </div>
      ),
    },
  ];

  // ============================================================================
  // FINAL TABLE DATA WITH PROPER ROW SPAN RECALCULATION
  // ============================================================================

  /**
   * Calculate the final filtered service lines for table display
   * This ensures row spans are recalculated whenever the display filter changes
   */
  const filteredServiceLines = useMemo(() => {
    console.log('🎯 Calculating final filtered service lines...');
    console.log('   - showSelectedRows:', showSelectedRows);
    console.log('   - serviceLines count:', serviceLines.length);
    console.log('   - selected lines count:', serviceLines.filter(line => line.isSelected).length);

    let finalLines = serviceLines;

    // Apply the "show selected rows only" filter if enabled
    if (showSelectedRows) {
      console.log('🔍 Filtering to show only selected rows...');
      finalLines = serviceLines.filter(line => line.isSelected);
      console.log('   - Filtered to', finalLines.length, 'selected lines');

      // CRITICAL: Recalculate row spans for the filtered selected lines
      // This is necessary because filtering can break detail groupings
      finalLines = recalculateGroupingFlags(finalLines);
      console.log('   - Row spans recalculated for selected lines');
    }

    console.log('✅ Final table data ready:', {
      totalLines: finalLines.length,
      showingSelectedOnly: showSelectedRows,
      detailGroups: [...new Set(finalLines.map((line: any) => line.detailId))].length
    });

    return finalLines;
  }, [serviceLines, showSelectedRows]); // Dependencies: recalculate when serviceLines or showSelectedRows change

  return (
    <div>
      <PageTitle>FACTURES FOURNISSEURS</PageTitle>
      <Spinner loading={loading}>
        <>
          <div className="product-page__searching">
            <Row justify="end" gutter={24} align="top" style={{ marginBottom: 16 }}>
              <Col span={12}>
                <MainTitle parent="Validation" child={mainTitleChild} />
              </Col>
              <Col span={12}>
                <Row justify="end" gutter={24}>
                  <Col>
                    <Button
                      type="dashed"
                      size="large"
                      onClick={() => {
                        console.log('🔍 DEBUG DATA:');
                        console.log('originalServiceLines:', originalServiceLines);
                        console.log('serviceLines:', serviceLines);
                        console.log('Form values:', form.getFieldsValue());
                        alert('Check console for debug data');
                      }}
                    >
                      Debug Data
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className='devis-page__btn__quit'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      onClick={() => handleAnnuler()}
                    >
                      Annuler
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className='devis-page__btn__info'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      onClick={() => handleSaveWithStatus('a_revoir')}
                    >
                      A revoir
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      className='devis-page__btn__draft'
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      onClick={() => handleSaveWithStatus('brouillon')}
                    >
                      Brouillon
                    </Button>
                  </Col>
                  <Col>
                    <Button
                      // className='devis-page__btn__send'
                      className={!isSaveButtonDisabled ? 'btn-grey-inactive' : 'devis-page__btn__send'}
                      style={{ fontWeight: 'bold' }}
                      size="large"
                      loading={saveLoading === 'pending'}
                      disabled={!isSaveButtonDisabled}
                      onClick={() => handleSaveWithStatus('validée')}
                    >
                      Valider
                    </Button>
                  </Col>
                </Row>
              </Col>
            </Row>
          </div>
          {/* Search and filter section */}
          <div className="product-page__searching">
            <Form layout="vertical" form={form} onValuesChange={handleFilterChange}>
              <Row justify="end" gutter={24} align="top">
                <Col span={16}>
                  <InvoiceComment
                    comments={comments}
                    currentUser={currentUser}
                    onAddComment={handleAddComment}
                  />
                </Col>
                <Col span={8} style={{ justifyContent: 'right', display: 'flex' }}>
                  <InvoiceSummary
                    totalSelected={totalSelectedAmount}
                    totalAvoirs={totalAvoirs}
                    avoirsCount={avoirsCount}
                    invoiceAmount={invoiceAmount}
                    avoirDetails={avoirs}
                    onAddAvoir={handleAddAvoir}
                    onRemoveAvoir={handleRemoveAvoir}
                  />
                </Col>
              </Row>
              <Row gutter={24} style={{ marginTop: 16 }}>
                <Col span={5}>
                  <Form.Item
                    name="date"
                  >
                    <DatePicker placeholder='Sélectionner une date' style={{ width: '100%' }}  />
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item
                    name="commandeNumber"
                  >
                    <Input 
                      placeholder='N° de commande' 
                    />                                   
                  </Form.Item>
                </Col>
                <Col span={5}>
                  <Form.Item
                    name="product"
                  >
                    <Select
                      placeholder="Produits"
                      showSearch
                      allowClear
                      options={(getAllProducts?.rows || []).map(product => ({
                        label: product.name,
                        value: product.id,
                      }))}
                      filterOption={(input, option) => {
                        const normalize = (str:string) => 
                          (str || '')
                            .normalize('NFD')
                            .replace(/[\u0300-\u036f]/g, '')
                            .replace(/[^\w\s]/gi, '') 
                            .toLowerCase();
                        const normalizedInput = normalize(input);
                        const normalizedLabel = normalize(option?.label || '');
                        return normalizedInput.split(/\s+/).every(word => 
                          word && normalizedLabel.includes(word)
                        );
                      }}
                    />
                  </Form.Item>
                </Col>
                <Col span={7}>
                  <Form.Item
                    name="siteAddress"
                  >
                    <Input
                      placeholder='Adresse de chantier'
                    />
                  </Form.Item>
                </Col>
              </Row>
            </Form>
          </div>
          <Row gutter={24} justify="start" style={{ marginBottom: 6 }} >
            <Col span={5}>
              <Text strong style={{ fontSize: '16px' }}>
                Prestations sélectionnées : {selectedLines.length} / {serviceLines.length}
              </Text>
            </Col>
            
            <Col span={6}>
              <Row>
                <Col>
                  <Switch
                    checked={showInvoicedRows}
                    onChange={checked => setShowInvoicedRows(checked)}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    className='product-tarifs__switch'
                  />
                </Col>
                <Col style={{ marginLeft: 8 }}>
                  <Text strong>
                    {!showInvoicedRows 
                      ? 'Afficher les prestations facturées' 
                      : 'Masquer les prestations facturées'}
                  </Text>
                </Col>
              </Row>
            </Col>
            <Col span={6}>
              <Row>
                <Col>
                  <Switch
                    checked={showSelectedRows}
                    onChange={checked => setShowSelectedRows(checked)}
                    checkedChildren={<CheckOutlined />}
                    unCheckedChildren={<CloseOutlined />}
                    className='product-tarifs__switch'
                  />
                </Col>
                <Col style={{ marginLeft: 8 }}>
                  <Text strong>
                    {!showSelectedRows 
                      ? 'Afficher les lignes sélectionnées' 
                      : 'Masquer les lignes sélectionnées'}
                  </Text>
                </Col>
              </Row>
            </Col>
          </Row>
          <Table
            columns={columns}
            dataSource={filteredServiceLines}
            pagination={false}
            rowKey="key"
            loading={{
              indicator: <ScaleLoader color="#1890ff" />,
              spinning: loading,
            }}
            // scroll={{ y: 1500 }}
            bordered
          />
        </>
      </Spinner>

      {/* File Upload Modal */}
      <FileUploadModal
        visible={fileUploadModalVisible}
        onClose={handleCloseFileUploadModal}
        onSave={handleSaveFiles}
        initialFiles={selectedLineForFiles ? lineFiles[selectedLineForFiles.key] || [] : []}
        title="Gestion des fichiers"
        invoiceNumber={(selectedLineForFiles as any)?.invoiceNumber}
        prestataire={(selectedLineForFiles as any)?.prestataire}
      />

      {/* Waste Type Modal */}
      <AddWasteTypeModal
        visible={wasteTypeModalVisible}
        onClose={handleCloseWasteTypeModal}
        onSave={handleSaveWasteTypes}
        initialWasteTypes={selectedLineForWasteTypes ? lineWasteTypes[selectedLineForWasteTypes.key] || [] : []}
        invoiceNumber={(selectedLineForWasteTypes as any)?.invoiceNumber}
        prestataire={(selectedLineForWasteTypes as any)?.prestataire}
      />

      {/* Regul Modal */}
      <AddRegulModal
        visible={RegulModalVisible}
        onClose={handleCloseRegulModal}
        onSave={handleSaveRegul}
        initialReguls={selectedLineForRegul ? lineRegul[selectedLineForRegul.key] || [] : []}
        invoiceNumber={(selectedLineForRegul as any)?.invoiceNumber}
        prestataire={(selectedLineForRegul as any)?.prestataire}
      />

    </div>
  );
};

export default ProviderInvoiceCreate;