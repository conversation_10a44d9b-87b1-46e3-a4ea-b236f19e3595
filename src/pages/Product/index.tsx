import { PageTitle } from 'components/Common';
import { useEffect, useState } from 'react';
import { MainTitle } from 'components/Common';
import { Button, Input, Space, Table, Modal, Typography, Divider, Spin } from 'antd';
import { EditOutlined, DeleteOutlined } from '@ant-design/icons';
import { ProductType } from 'models';
import { useLocation, useNavigate } from 'react-router-dom';
import ButtonAdd from 'components/Common/ButtonAdd';
import { productService } from 'services';
import { ScaleLoader } from 'react-spinners';
import { toast } from 'react-toastify';
import { useQueryParams } from 'hooks';
import InfiniteScroll from 'react-infinite-scroll-component';
import { LoadingOutlined } from '@ant-design/icons';
import { Loading, QueryParams } from '../../types';

const { Search } = Input;
const { Text } = Typography;

const Product = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const [loading, setLoading] = useState<boolean | undefined>(undefined);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [titleOfProduct, setTitleOfProduct] = useState<string | undefined>('');
  const [query, onParamChange] = useQueryParams();
  const [unVisibleItem, setUnVisibleItem] = useState<number | null>(null);

  const [productTypes, setProductTypes] = useState<ProductType[]>([]);
  const [productTypesLoading, setProductTypesLoading] = useState<Loading>('idle');
  const [productTypesTotal, setProductTypesTotal] = useState(0);
  const [pageLoad, setPageLoad] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const NUMBER_ITEM_LOAD = 50;

  const loadMoreData = () => {
    console.log('loadMoreData');
    setPageLoad(pageLoad + 1);
  };

  useEffect(() => {
    if (pageLoad > 1) {
      getProductTypes(true, { ...query });
    }
  }, [pageLoad]);

  useEffect(() => {
    if (productTypes.length < productTypesTotal) {
      setHasMore(true);
    } else {
      setHasMore(false);
    }
  }, [productTypes, productTypesTotal]);

  const getProductTypes = async (isLoadMore: boolean, query: QueryParams) => {
    try {
      setProductTypesLoading('pending');
      const response = await productService.getProductTypes({
        name: query.keyword ? `*${query.keyword}*` : undefined,
        page: isLoadMore ? pageLoad : 1,
        limit: NUMBER_ITEM_LOAD,
        orderBy: 'createdAt,desc',
        isVisible: 1,
      });

      console.log(response);
      setProductTypesLoading('succeeded');
      if (response.rows && response.rows.length === 0) {
        setHasMore(false);
      }
      if (isLoadMore) {
        setProductTypes([...productTypes, ...response.rows]);
      } else {
        setProductTypes(response.rows);
      }
      setProductTypesTotal(response.count);
    } catch (error) {
      setProductTypesLoading('failed');
      console.log(error);
    }
  };

  useEffect(() => {
    setPageLoad(1);
    getProductTypes(false, query);
    // Reset scroll position
    window.scrollTo(0, 0);
  }, [query]);

  const handleOk = async () => {
    if (!titleOfProduct) return;
    setLoading(true);
    try {
      const result: ProductType = await productService.createProductType({
        name: titleOfProduct,
      });
      handleGotoDetail(result);
      setLoading(false);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      setLoading(false);
      toast.error('Erreur');
    }
  };

  const handleGotoDetail = (item: ProductType) => {
    navigate(`/products/${item.id}`, { state: location });
  };
  const handleUnVisible = async (item: ProductType) => {
    setUnVisibleItem(item?.id);
    setLoading(true);
    try {
      await productService.unVisibleProductType(item.id).then((response) => {
        if (response.data) {
          toast.success('Succès');
          getProductTypes(false, query);
        }
      });
      setLoading(false);
      setUnVisibleItem(null);
    } catch (error) {
      console.log(error);
      setLoading(false);
      setUnVisibleItem(null);
      toast.error('Erreur');
    }
  };
  const handleCancel = () => {
    setTitleOfProduct('');
    setIsModalOpen(false);
  };

  const handleOpenModal = () => {
    setIsModalOpen(true);
  };

  console.log('dataSource.length total', productTypes, 'total: ', productTypesTotal);
  const LoaderRow = () => (
    <div className='datatable-loader'>
      <Spin indicator={<LoadingOutlined style={{ fontSize: 40 }} spin />} />
    </div>
  );

  return (
    <>
      <PageTitle>PRODUITS TYPE</PageTitle>
      <>
        <MainTitle parent='PRODUITS TYPE' child='LISTE' />
        <div className='product-page__creation-and-searching'>
          <ButtonAdd handleClick={handleOpenModal} otherStyles={{ height: '42.1px' }}>
            Type de Produit
          </ButtonAdd>
          <div className='product-page__searching'>
            <Space direction='vertical'>
              <Search
                placeholder='Rechercher'
                allowClear
                onSearch={(value) => onParamChange({ keyword: value })}
                size='large'
                defaultValue={query.keyword}
                style={{
                  width: 304,
                }}
                className='product-page__search-bar'
              />
            </Space>
          </div>
        </div>
        <InfiniteScroll
          dataLength={productTypes.length}
          next={loadMoreData}
          hasMore={hasMore}
          loader={<LoaderRow />}
          endMessage={<Divider plain>Vous avez atteint la fin du flux</Divider>}
        >
          <Table
            columns={[
              {
                title: 'Nom Type de Produit',
                dataIndex: 'name',
                key: 'name',
                width: 400,
                render: (text) => <p className='datatable__item'>{text}</p>,
              },
              {
                title: 'Action',
                key: 'action',
                width: 20,
                render: (_, record) => (
                  <Space size='middle'>
                    <Button
                      type='link'
                      icon={<EditOutlined />}
                      className='datatable__action-edit-button'
                      onClick={() => handleGotoDetail(record)}
                    />
                    <Button
                      loading={loading && unVisibleItem === record.id}
                      type='link'
                      icon={<DeleteOutlined />}
                      className='datatable__action-destroy-button'
                      onClick={() => handleUnVisible(record)}
                    />
                  </Space>
                ),
              },
            ]}
            dataSource={productTypes}
            pagination={false}
            className='product-page__datatable'
            rowKey={(productType) => productType.id}
            loading={{
              indicator: (
                <ScaleLoader
                  color='#A6C84D'
                  cssOverride={{
                    display: 'inline-block !important',
                    margin: '0 auto',
                    left: 0,
                    height: '100%',
                    width: '100%',
                  }}
                  aria-label='Loading Spinner'
                  data-testid='loader'
                />
              ),
              spinning: productTypesLoading === 'pending',
            }}
          />
        </InfiniteScroll>
        <Modal
          title='AJOUTER UN TYPE DE PRODUIT'
          open={isModalOpen}
          onOk={handleOk}
          onCancel={handleCancel}
          centered
          footer={[
            <Button key='annuler' onClick={handleCancel} className='ant-modal-content__cancel-btn'>
              Annuler
            </Button>,
            <Button
              key='ajouter'
              type='primary'
              onClick={handleOk}
              loading={loading}
              className='ant-modal-content__add-btn'
            >
              Ajouter
            </Button>,
          ]}
        >
          <Space direction='vertical'>
            <Text className='ant-modal-content__title-of-product-label'>Titre du Produit</Text>
            <Input
              placeholder='Input'
              value={titleOfProduct}
              className='ant-modal-content__title-of-product-input'
              onChange={(e) => setTitleOfProduct(e.target.value)}
            />
          </Space>
        </Modal>
      </>
    </>
  );
};

export default Product;
