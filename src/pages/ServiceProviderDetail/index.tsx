import { ButtonSync, MainTitle, PageTitle, Spinner } from 'components/Common';
import { useEffect, useState } from 'react';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store';
import {
  fetchServiceProviderById,
  fetchServiceTypes,
  selectServiceProvider,
  selectServiceProviderLoading,
  selectServiceTypes,
} from 'store/slices/service_provider.slices';
import {
  ServiceProviderInfo,
  ServiceProviderContact,
  ServiceProviderTarification,
  ServiceProviderWasteCenter,
  ServiceProviderBankInfo,
} from 'components/ServiceProvider';
import { SERVICE_TYPES } from 'utils/constant';
import { Button } from 'antd';
import { ArrowLeftOutlined } from '@ant-design/icons';

const ServiceProviderDetail = () => {
  const navigate = useNavigate();
  const params = useParams();
  const location = useLocation();
  const pathParts = location.pathname.split('/');
  const serviceTypeKey = pathParts[2];
  const dispatch = useAppDispatch();
  const [isFirstLoading, setIsFirstLoading] = useState(true);
  const serviceProviderId = parseInt(params.serviceProviderId as string);
  const serviceTypes = useAppSelector(selectServiceTypes);
  const serviceProvider = useAppSelector(selectServiceProvider);
  const serviceProviderLoading = useAppSelector(selectServiceProviderLoading);
  const serviceType = serviceTypes?.find((item) => item.key === serviceTypeKey);

  const getServiceTypes = async () => {
    await dispatch(fetchServiceTypes())
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  };

  const getServiceProvider = async () => {
    await dispatch(
      fetchServiceProviderById({
        id: serviceProviderId,
        serviceType: serviceTypeKey,
        include: 'ServiceTypes|WasteCenters.WasteTypes|Contacts',
      }),
    )
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
    setIsFirstLoading(false);
  };

  const handleBackToProductPage = () => {
    navigate('/prestataires/' + serviceTypeKey);
  };

  useEffect(() => {
    getServiceTypes();
    getServiceProvider();
  }, []);

  if (serviceProviderLoading === 'pending' && isFirstLoading) {
    return <Spinner />;
  }

  const getFormattedTitle = (titlePage: string | undefined) => {
    if (titlePage === 'PUP') return titlePage;
    return `${titlePage}s`;
  };

  return (
    <>
      <PageTitle>{'Detail'}</PageTitle>
      <MainTitle
        parent={getFormattedTitle(serviceType?.name)}
        child={serviceProvider?.name}
        otherStyles={{ marginBottom: '7px' }}
      />
      <div className='service-provider'>
        <div className='back-to-product-list'>
          <Button type='link' className='back-to-product-list-btn' onClick={handleBackToProductPage}>
            <ArrowLeftOutlined className='left-arrow-icon' />
            Retour liste {getFormattedTitle(serviceType?.name)}
          </Button>
        </div>
        <div className=''>
          <div className='service-provider-detail__content'>
            {(serviceType?.name.toLowerCase() == SERVICE_TYPES.benneur ||
              serviceType?.name.toLowerCase() == SERVICE_TYPES.camionneur) &&
              (serviceProvider && serviceProvider.enableSync ? (
                serviceProvider.isSynced ? (
                  <div className='service-provider-list__sync_bo'>
                    <Button type='link' className='service-provider-list__sync_bo__button' disabled={true}>
                      BO synchronisé
                    </Button>
                  </div>
                ) : (
                  <ButtonSync serviceProvider={serviceProvider} getServiceProvider={getServiceProvider} />
                )
              ) : (
                <div className=''></div>
              ))}
          </div>
        </div>
      </div>

      <ServiceProviderInfo
        typeKey={serviceTypeKey}
        getServiceProvider={getServiceProvider}
        serviceProvider={serviceProvider}
      />
      <ServiceProviderContact getServiceProvider={getServiceProvider} />
      <ServiceProviderBankInfo serviceProviderId={serviceProviderId} serviceProvider={serviceProvider} />
      {serviceTypeKey === SERVICE_TYPES.benneur && (
        <ServiceProviderWasteCenter
          serviceProviderId={serviceProviderId}
          serviceProvider={serviceProvider}
          getServiceProvider={getServiceProvider}
        />
      )}
      <ServiceProviderTarification typeKey={serviceTypeKey} serviceProvider={serviceProvider} />
    </>
  );
};
export default ServiceProviderDetail;
