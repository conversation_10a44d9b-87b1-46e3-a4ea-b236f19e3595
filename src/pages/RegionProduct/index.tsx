import { useEffect, useState } from 'react';
import { Select, Button, Switch, Form, Spin } from 'antd';
import { ButtonAdd, MainTitle, PageTitle, UrbanCenterModal } from 'components/Common';
import { useAppDispatch, useAppSelector } from 'store';
import { useNavigate, useParams, useSearchParams } from 'react-router-dom';
import { Region, UrbanCenter, Year } from 'models';
import { toast } from 'react-toastify';
import { findRegionById, selectRegion } from 'store/slices/region.slices';
import { useUrbanCentersQuery, useUrbanCenters, useYearQuery, useYears } from 'hooks';
import { ArrowLeftOutlined, CopyOutlined } from '@ant-design/icons';
import { CatalogPriceTab } from 'components/Common';
import { catalogPriceLineService, urbanCenterService, yearService } from 'services';
import YearPriceCopyModal from 'components/Common/YearPriceCopyModal';
import CopyConfirmationModal from 'components/Common/YearPriceCopyModal/CopyConfirmationModal';
const { Option } = Select;

const RegionProduct = () => {
  const [form] = Form.useForm();
  const dispatch = useAppDispatch();
  const params = useParams();
  const navigate = useNavigate();
  const [searchParams, setSearchParams] = useSearchParams();
  const regionId = parseInt(params.regionId as string);
  const currentYear = new Date().getFullYear();
  const [isOpenModal, setIsOpenModal] = useState(false);
  const region: Region | null = useAppSelector(selectRegion);
  const [selectedYearId, setSelectedYearId] = useState(parseInt(searchParams.get('yearId') as string));
  const urbanCenterId = parseInt(searchParams.get('urbanCenterId') as string);
  const [yearsQuery] = useYearQuery({
    isActive: 1,
  });
  const [years] = useYears(yearsQuery);
  const currentYearData = years.find((year) => parseInt(year.year as unknown as string) === currentYear);
  const [urbanCentersQuery] = useUrbanCentersQuery({
    regionId,
    isActive: 1,
  });
  const [urbanCenters, refreshUrbanCenters, loadingUrbanCenters] = useUrbanCenters(urbanCentersQuery);
  const [isYearPriceActive, setIsYearPriceActive] = useState<boolean>(false);
  const [isShowCopyPriceModal, setIsShowCopyPriceModal] = useState<boolean>(false);
  const [selectedYear, setSelectedYear] = useState<Year | null>(null);
  const [isOpenCopyConfirmation, setIsOpenCopyConfirmation] = useState<boolean>(false);
  const [isDuplicatingYearPrice, setIsDuplicatingYearPrice] = useState<boolean>(false);
  const [isShowPrixActive, setIsShowPrixActive] = useState<boolean>(true);
  const isLoading = loadingUrbanCenters === 'pending';
  const getRegion = async (regionId: number) => {
    await dispatch(findRegionById(regionId))
      .unwrap()
      .catch((error) => {
        console.log(error);
        toast.error('Erreur');
      });
  };

  useEffect(() => {
    getRegion(regionId);
  }, []);

  useEffect(() => {
    if (currentYearData?.id) {
      form.setFieldsValue({
        year: currentYearData.id,
      });
      setSearchParams({ yearId: `${currentYearData?.id}` });
      if (urbanCenterId) {
        setSearchParams({ yearId: `${currentYearData?.id}`, urbanCenterId: `${urbanCenterId}` });
      }
      setSelectedYearId(currentYearData.id);
    }
  }, [years, currentYearData]);

  useEffect(() => {
    handleShowPriceActive();
    handleShowYearPriceActivate();
  }, [selectedYearId, urbanCenters]);

  const handleShowYearPriceActivate = async () => {
    try {
      const catalogPriceLineNumbers = await fetchCatalogPriceLineNumbers();
      if (catalogPriceLineNumbers) {
        form.setFieldsValue({
          isVisible: catalogPriceLineNumbers > 0,
        });
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleShowPriceActive = async () => {
    try {
      const urbanCenterIds = urbanCenters.map((urbanCenter) => urbanCenter.id) as number[];
      const catalogPriceLineNumbers = await catalogPriceLineService.countCatalogPriceLines({
        yearId: selectedYearId,
        'urbanCenterIds[]': `[${urbanCenterIds.join(',')}]`,
      });
      setIsShowPrixActive(catalogPriceLineNumbers > 0);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const fetchCatalogPriceLineNumbers = async () => {
    try {
      const urbanCenterIds = urbanCenters.map((urbanCenter) => urbanCenter.id) as number[];
      const catalogPriceLineNumbers = await catalogPriceLineService.countCatalogPriceLines({
        yearId: selectedYearId,
        'urbanCenterIds[]': `[${urbanCenterIds.join(',')}]`,
        isVisible: 1,
      });
      setIsYearPriceActive(catalogPriceLineNumbers > 0);
      return catalogPriceLineNumbers;
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };
  const handleBackToProductPage = () => {
    navigate('/regions');
  };

  const handleShowModal = (value: boolean) => {
    setIsOpenModal(value);
  };

  const handleSubmit = async (data: UrbanCenter) => {
    try {
      const submitData = {
        ...data,
        regionId: regionId,
        yearId: selectedYearId,
      };
      await urbanCenterService.createUrbanCenter(regionId, submitData);
      await Promise.all([refreshUrbanCenters()]);
      toast.success('Succès');
      setIsOpenModal(false);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleActiveYearPrice = async () => {
    if (!selectedYearId) return;
    try {
      const urbanCenterIds = urbanCenters.map((urbanCenter) => urbanCenter.id);
      setIsYearPriceActive(!isYearPriceActive);
      const submitData = {
        urbanCenterIds,
        isVisible: !isYearPriceActive,
      };
      await yearService.activateYearPrice(selectedYearId, submitData);
      Promise.all([refreshUrbanCenters()]);
      toast.success('Succès');
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleChangeYear = async (yearId: number) => {
    const year = years.find((year) => year.id === yearId);
    if (year) {
      setSearchParams({ yearId: `${year.id}` });
      if (urbanCenterId) {
        setSearchParams({ yearId: `${year.id}`, urbanCenterId: `${urbanCenterId}` });
      }
      setSelectedYearId(yearId);
    }
  };

  const handleShowCopyPriceModal = (isOpen: boolean) => {
    setIsShowCopyPriceModal(isOpen);
  };

  const handleShowCopyConfirmation = (isOpen: boolean) => {
    setIsOpenCopyConfirmation(isOpen);
  };

  const handleSelectYear = (year: Year) => {
    setSelectedYear(year);
  };

  const handleSubmitYearPriceDuplicates = async () => {
    if (!selectedYear) return;
    try {
      setIsDuplicatingYearPrice(true);
      const dataSubmit = {
        duplicatedYearId: selectedYearId,
        urbanCenterIds: urbanCenters.map((urbanCenter) => urbanCenter.id),
      };
      await yearService.duplicateYearPrice(selectedYear.id, dataSubmit);
      setIsDuplicatingYearPrice(false);
      Promise.all([refreshUrbanCenters()]);
      toast.success('Succès');
      handleShowCopyConfirmation(false);
    } catch (error) {
      console.log(error);
      setIsDuplicatingYearPrice(false);
      toast.error('Erreur');
    }
  };

  return (
    <Form name='catalog-price' form={form} autoComplete='off'>
      <PageTitle>CATALOGUE RÉGIONS</PageTitle>
      <MainTitle parent='CATALOGUE RÉGIONS' child={region?.name || ''} />
      <div
        className='back-to-product-list'
        style={{
          marginBottom: '60px',
        }}
      >
        <Button type='link' className='back-to-product-list-btn' onClick={handleBackToProductPage}>
          <ArrowLeftOutlined className='left-arrow-icon' />
          Retour liste catalogue regions
        </Button>
      </div>
      <div
        className='region-product-list__selections'
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
        }}
      >
        <div style={{ display: 'flex' }}>
          <Form.Item
            name='year'
            initialValue={selectedYearId}
            rules={[{ required: true, message: '' }]}
            style={{ marginBottom: 20, width: '100%' }}
          >
            <Select placeholder='Année' className='region-product-list__zone-selection' onChange={handleChangeYear}>
              {years.map((year) => (
                <Option key={year.id} value={year.id}>
                  {year.year}
                </Option>
              ))}
            </Select>
          </Form.Item>
          {urbanCenters.length > 0 && isShowPrixActive && (
            <Form.Item name='isVisible' label='Activer le prix de cette année' initialValue={isYearPriceActive}>
              <Switch
                checked={isYearPriceActive}
                size='small'
                className='option__option-attributes-switch-default'
                onChange={handleActiveYearPrice}
              />
            </Form.Item>
          )}
        </div>
        <Button
          type='link'
          disabled={isYearPriceActive && isShowPrixActive}
          icon={
            <CopyOutlined style={{ fontSize: 24, color: '#95C515' }} onClick={() => handleShowCopyPriceModal(true)} />
          }
        />
        {isShowCopyPriceModal && (
          <YearPriceCopyModal
            yearList={years.filter((year) => year.id !== selectedYearId)}
            isOpenModal={isShowCopyPriceModal}
            onCancel={() => handleShowCopyPriceModal(false)}
            onShowCopyConfirmation={handleShowCopyConfirmation}
            onSelectYear={handleSelectYear}
          />
        )}
        {isOpenCopyConfirmation && (
          <CopyConfirmationModal
            isOpenModal={isOpenCopyConfirmation}
            form={form}
            selectedYear={years?.find((year) => year.id === selectedYearId)}
            onCancel={() => handleShowCopyConfirmation(false)}
            onSubmit={handleSubmitYearPriceDuplicates}
            isLoading={isDuplicatingYearPrice}
          />
        )}
      </div>
      {isLoading ? (
        <Spin
          spinning={true}
          style={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            marginTop: '100px',
          }}
        >
          <div style={{ height: 200 }}></div>
        </Spin>
      ) : (
        <>
          {urbanCenters.length > 0 && (
            <CatalogPriceTab
              showDuplicateProduct={false}
              urbanCenters={urbanCenters}
              refreshUrbanCenters={refreshUrbanCenters}
              selectedYearId={selectedYearId}
              fetchCatalogPriceLinesByUrbanCenterIds={handleShowPriceActive}
              fetchCatalogPriceLineNumbers={fetchCatalogPriceLineNumbers}
            />
          )}
        </>
      )}
      {!urbanCenters?.length ? (
        <div className='service-provider__contact mt-5'>
          <ButtonAdd
            otherStyles={{
              height: '32px',
              borderRadius: '6px',
            }}
            handleClick={() => handleShowModal(true)}
          >
            Ajouter un centre urbain
          </ButtonAdd>
        </div>
      ) : null}
      <UrbanCenterModal
        action='create'
        isOpenModal={isOpenModal}
        onCancel={() => setIsOpenModal(false)}
        onSubmit={handleSubmit}
      />
    </Form>
  );
};
export default RegionProduct;
