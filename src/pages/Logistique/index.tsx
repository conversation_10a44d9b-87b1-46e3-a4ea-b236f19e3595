import { Space, Input, Badge } from 'antd';
import { MainTitle, PageTitle } from 'components/Common';
import { useQueryParams } from 'hooks';
import { useEffect, useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { useAppSelector } from 'store';
import { QueryParams } from 'types';
import { LOGISTIQUE_ORDER_TYPES } from 'utils/constant';
import LogistiqueList from '../../components/Logistique/LogistiqueList';
import ButtonFilter from '../../components/Common/ButtonFilter';
import FilterLogistiqueModal from '../../components/Logistique/FilterLogistiqueModal';
import { selectProductTypeInterventions } from 'store/slices/product.slices';
import { documentService } from 'services';
import Document from 'models/document_documents';
import { Loading } from 'types';
import { Documents } from 'models';
const { Search } = Input;

const Logistique = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [isModalFilterOpen, setIsModalFilterOpen] = useState(false);

  const logistiqueOrderTypeKey = location.pathname.split('/').pop() as string;
  if (logistiqueOrderTypeKey && !LOGISTIQUE_ORDER_TYPES.nouvelles_commandes.includes(logistiqueOrderTypeKey)) {
    navigate('/');
  }
  const [query, onParamChange] = useQueryParams();
  const logistiqueOrderType = 'NOUVELLES COMMANDE';
  const [documents, setDocuments] = useState<Document[]>([]);
  const [documentsLoading, setDocumentsLoading] = useState<Loading>('idle');
  const [documentsTotal, setDocumentsTotal] = useState(0);
  const productTypeInterventions = useAppSelector(selectProductTypeInterventions);
  const [pageLoad, setPageLoad] = useState({ page: 1, isLoadMore: false });
  const [hasMore, setHasMore] = useState(true);
  const [selectedDocument, setSelectedDocument] = useState<Documents | null>(null);
  const NUMBER_ITEM_LOAD = 50;
  const filterKeys = ['countryRegionId', 'interventionId', 'documentStatusId'];
  let activeFilterCount = filterKeys.reduce((count, key) => {
    return query[key] ? count + 1 : count;
  }, 0);
  if (query['createdAt[gte]'] && query['createdAt[lte]']) {
    activeFilterCount += 1;
  }

  const handleCancel = () => {
    setIsModalFilterOpen(false);
  };

  useEffect(() => {
    if (logistiqueOrderTypeKey === 'nouvelles-commandes' && productTypeInterventions.length > 0)
      getLogistiqueOrders(logistiqueOrderTypeKey, query);
    handleReloadPageList();
  }, [query, logistiqueOrderTypeKey, productTypeInterventions]);

  const handleReloadPageList = () => {
    if (logistiqueOrderTypeKey === 'nouvelles-commandes' && productTypeInterventions.length > 0)
      getLogistiqueOrders(logistiqueOrderTypeKey, query);
  };
  const loadMoreData = () => {
    console.log('loadMoreData');
    setPageLoad({ page: pageLoad.page + 1, isLoadMore: true });
  };

  useEffect(() => {
    if (logistiqueOrderTypeKey === 'nouvelles-commandes' && productTypeInterventions.length > 0) {
      getLogistiqueOrders(logistiqueOrderTypeKey, { ...query });
    }
  }, [pageLoad]);

  useEffect(() => {
    if (documents.length < documentsTotal) {
      setHasMore(true);
    } else {
      setHasMore(false);
    }
    setSelectedDocument(documents.find((doc) => doc.id === selectedDocument?.id) || null);
  }, [documents]);

  /**
   * getLogistiqueOrders
   * @param logistiqueOrderTypeKey
   * @param query
   */
  const getLogistiqueOrders = async (logistiqueOrderTypeKey: string, query: QueryParams) => {
    try {
      setDocumentsLoading('pending');
      const response = await documentService.getDocuments({
        ...{ page: pageLoad.page },
        ...{ limit: NUMBER_ITEM_LOAD },
        ...(query.keyword ? { fullSearch: query.keyword.trim() } : {}),
        ...(query.countryRegionId ? { siteCountryRegionId: query.countryRegionId } : {}),
        ...(query.interventionId ? { interventionId: query.interventionId } : {}),
        ...(query.documentStatusId ? { documentStatusId: query.documentStatusId } : {}),
        ...(query['createdAt[gte]'] && query['createdAt[lte]']
          ? {
              'createdAt[gte]': query['createdAt[gte]'] ? query['createdAt[gte]'] : undefined,
              'createdAt[lte]': query['createdAt[lte]'] ? query['createdAt[lte]'] : undefined,
            }
          : {}),
        orderBy: 'createdAt,desc',
      });
      console.log(response);
      setDocumentsLoading('succeeded');
      if (response.rows && response.rows.length === 0) {
        setHasMore(false);
      }
      if (pageLoad.isLoadMore) {
        setDocuments([...documents, ...response.rows]);
      } else {
        setDocuments(response.rows);
      }
      setDocumentsTotal(response.count);
    } catch (error) {
      setDocumentsLoading('failed');
      console.log(error);
    }
  };
  /**
   * handleReloadDocuments
   * @param ids
   */
  const handleReloadDocumentSelected = async () => {
    try {
      // Check if a document not select
      if (!selectedDocument) return;
      const response = await documentService.getDocuments({
        ...{ 'ids[]': JSON.stringify([selectedDocument.id]) },
      });
      const documentsUpdate = response.rows;
      // Find and update the documents in the state
      const updatedDocuments = documents.map((document) => {
        // Find the updated document in the updatedDocuments array
        const updatedDocument = documentsUpdate.find((doc) => doc.id === document.id);
        // If found, update the document
        return updatedDocument ? updatedDocument : document;
      });
      // Set the updated documents in the state
      setDocuments(updatedDocuments);
    } catch (error) {
      console.log(error);
    }
  };

  const handleReloadDocuments = async () => {
    setPageLoad({ page: 1, isLoadMore: false });
    // Reset scroll position when query or list type changes
    window.scrollTo(0, 0);
  };

  useEffect(() => {
    handleReloadDocuments();
  }, [query, logistiqueOrderTypeKey, productTypeInterventions]);

  return (
    <>
      <PageTitle>Nouvelles commandes</PageTitle>
      <>
        <MainTitle parent={`${logistiqueOrderType}S`} child='LISTE' />
        <div className='service-provider__creation-and-searching'>
          <Badge count={activeFilterCount}>
            <ButtonFilter
              isActive={activeFilterCount > 0}
              otherStyles={{ height: '42.1px' }}
              handleClick={() => setIsModalFilterOpen(true)}
            >
              Filtrer
            </ButtonFilter>
          </Badge>
          <div className='service-provider__searching'>
            <Space direction='vertical'>
              <Search
                placeholder='Rechercher'
                allowClear={true}
                defaultValue={query.keyword}
                size='large'
                onSearch={(value) => onParamChange({ keyword: value })}
                style={{
                  width: 304,
                }}
                className='service-provider__search-bar'
              />
            </Space>
          </div>
        </div>
        {logistiqueOrderTypeKey === LOGISTIQUE_ORDER_TYPES.nouvelles_commandes &&
          productTypeInterventions.length > 0 && (
            <LogistiqueList
              type={LOGISTIQUE_ORDER_TYPES.nouvelles_commandes}
              dataSource={documents}
              dataSourceLoading={documentsLoading}
              loadMoreData={loadMoreData}
              hasMore={hasMore}
              query={query}
              total={documentsTotal}
              productTypeInterventions={productTypeInterventions}
              handleReloadDocumentSelected={handleReloadDocumentSelected}
              selectedDocument={selectedDocument}
              setSelectedDocument={setSelectedDocument}
              handleReloadDocuments={handleReloadDocuments}
            />
          )}
      </>
      <FilterLogistiqueModal
        handleCancel={handleCancel}
        visible={isModalFilterOpen}
        query={query}
        onQueryChange={onParamChange}
      />
    </>
  );
};
export default Logistique;
