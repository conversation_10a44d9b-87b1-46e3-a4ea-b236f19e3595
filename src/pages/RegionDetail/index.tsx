import { PageTitle } from 'components/Common';
import { Button } from 'antd';
import { MainTitle, Spinner } from 'components/Common';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useLocation, useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import RegionInformation from 'components/Region/RegionInformation';
import CenterUrban from 'components/Region/CenterUrban';
import { toast } from 'react-toastify';
import { useAppDispatch, useAppSelector } from 'store';
import { fetchPlatforms } from 'store/slices/platform.slices';
import {
  findRegionById,
  selectRegion,
  selectRegionLoading
} from 'store/slices/region.slices';

const RegionDetail = () => {
  const dispatch = useAppDispatch();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const regionId = parseInt(params.regionId as string);
  const loading = useAppSelector(selectRegionLoading);
  const selectedRegion = useAppSelector(selectRegion);
  const [isFirstLoading, setIsFirstLoading] = useState(true);
  const [regionName, setRegionName] = useState<string | undefined>('');

  const getRegion = async () => {
    await dispatch(findRegionById(regionId))
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
    setIsFirstLoading(false);
  };

  const getPlatforms = async () => {
    await dispatch(fetchPlatforms())
      .unwrap()
      .catch(() => {
        toast.error('Erreur');
      });
  };

  useEffect(() => {
    getRegion();
    getPlatforms();
  }, []);

  const handleBackToProductPage = () => {
    const params = location?.state?.search ?? '';
    navigate('/regions' + params);
  };

  if (loading === 'pending' && isFirstLoading) {
    return <Spinner />;
  }

  return (
    <>
      <PageTitle>CATALOGUE RÉGIONS</PageTitle>
      <MainTitle
        parent='CATALOGUE RÉGIONS'
        child={regionName ? regionName : selectedRegion?.name ?? ''}
        otherStyles={{ marginBottom: '7px' }}
      />
      <div className='back-to-product-list'>
        <Button
          type='link'
          className='back-to-product-list-btn'
          onClick={handleBackToProductPage}>
          <ArrowLeftOutlined className='left-arrow-icon' />
          Retour liste catalogue regions
        </Button>
      </div>
      <RegionInformation
        regionId={regionId}
        setRegionName={setRegionName}
        onRefreshRegion={getRegion}
      />
      <CenterUrban regionId={regionId} />
    </>
  );
};
export default RegionDetail;
