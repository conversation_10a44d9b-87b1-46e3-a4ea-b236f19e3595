import { useEffect, useState } from 'react';
import { Form, Divider, Row, Col, Table, Button } from 'antd';
import { PageTitle, MainTitle, Spinner } from 'components/Common';
import { DemanderUnPrix } from 'components/Estimate';
import SelectWithGoogleSuggestion from 'components/Common/SelectWithGoogleSuggestion';
import { Address } from '../../types/index';
import { TableRow, RawDataSales, DevisActionType } from 'types';
import type { ColumnsType } from 'antd/es/table';
import { ScaleLoader } from 'react-spinners';
import { urbanCenterService, productService, serviceProviderService, zohoService } from 'services';
import { useProductsQuery, useProducts } from 'hooks';
import { useAppDispatch, useAppSelector } from 'store';
import { toast } from 'react-toastify';
import {
  resetState,
  fetchProductWithZonesAndPricesSales,
  selectSearchProductsSales,
  selectSearchProductsLoadingSales,
  selectSearchProductsTotalSales,
  triggerSalesLoading,
} from 'store/slices/search_price.slices';
import { Demander, Quotation } from 'models';
import { selectSale } from 'store/slices/sale.slices';
import { DOCUMENT_STATUSES } from 'utils/constant';
import dayjs from 'dayjs';

type Product = {
  id?: number;
  name?: string;
};

type ServiceProvider = {
  id?: number;
  name?: string;
};

type FinalData = {
  name: string;
  offers: {
    type: string;
    zones: { zone: string; price: number }[];
    products: string[];
    comments: string | null;
  }[];
};

type ServiceProviderWithOffers = ServiceProvider & {
  offers: FinalData['offers'];
};

type ProviderInfo = {
  distanceByCar: string;
  zoneId: number;
  zoneName: string;
};

type RawProviderData = {
  zoneId: number;
  zoneName: string;
  distanceByCar?: string;
  [key: string]: unknown;
};

const SearchPriceCommercial = () => {
  const dispatch = useAppDispatch();
  const [loading, setLoading] = useState<boolean>(true);
  const [tableIsloading, setTableIsLoading] = useState<boolean>(true);
  const [addressChantier, setAddressChantier] = useState<Address[]>([]);
  const [selectAddressChantier, setSelectAddressChantier] = useState<Address>({});
  // eslint-disable-next-line
  const [selectedProduct, setSelectedProduct] = useState<string | undefined>(undefined);
  const [listPrestataire, setListPrestataire] = useState<object[]>([]);
  const [productsQuery] = useProductsQuery({
    orderBy: 'createdAt,desc|name',
  });
  const [getAllProducts] = useProducts(productsQuery);
  const productsPrice: RawDataSales[] = useAppSelector(selectSearchProductsSales);
  const porvidersWithProductsLoading = useAppSelector(selectSearchProductsLoadingSales);
  const porvidersWithProductsTotal = useAppSelector(selectSearchProductsTotalSales);
  // eslint-disable-next-line
  const [quotationData, setQuotationData] = useState<Quotation>({});
  const sale = useAppSelector(selectSale);
  const [isLogDemandeModalOpen, setIsLogDemandeModalOpen] = useState(false);

  const getInactiveProductHandler = async () => {
    try {
      const result = await productService?.getInactiveProduct();
      const idString = result.map((item: Product) => item.id).join(',');
      const resultsss = idString.split(',').map((s: string) => s.trim());
      return resultsss;
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const getInactiveSrerviceProviderHandler = async () => {
    try {
      const result = await serviceProviderService?.getInactiveServiceProvider();
      const idString = result.map((item: ServiceProvider) => item.id).join(',');
      return idString;
    } catch (error) {
      console.error(error);
      toast.error('Erreur');
    }
  };

  const getProviderIds = async (address: Address | null): Promise<ProviderInfo[]> => {
    const inactiveServiceProviderList = await getInactiveSrerviceProviderHandler();

    if (address?.latitude && address?.longitude && inactiveServiceProviderList.length > 0) {
      const serviceProviders = await urbanCenterService?.getProvidersZonesByChantierAddress(
        address.latitude,
        address.longitude,
        false,
        inactiveServiceProviderList,
      );

      const isServiceProvidersEmptyObject = (providers: Record<string, unknown> | null): boolean => {
        if (!providers) return true;
        return Object.values(providers).every((value) => Array.isArray(value) && value.length === 0);
      };

      if (
        !serviceProviders ||
        (typeof serviceProviders === 'object' &&
          !Array.isArray(serviceProviders) &&
          isServiceProvidersEmptyObject(serviceProviders))
      ) {
        setListPrestataire([]);
        dispatch(resetState());
        return [];
      }

      if (Array.isArray(serviceProviders)) {
        return serviceProviders.map((provider: RawProviderData) => ({
          distanceByCar: provider.distanceByCar ?? '',
          zoneId: provider.zoneId,
          zoneName: provider.zoneName,
        }));
      }
    }

    return [];
  };

  const handleSelectChantierAddress = async (address: Address) => {
    setSelectAddressChantier(address);
    dispatch(triggerSalesLoading());
    const existingAddress = addressChantier.find((a) => a.address_id === address.address_id);
    if (!existingAddress) {
      setAddressChantier((prevAddress) => [...prevAddress, address]);
    }
    try {
      const providerInfos = (await getProviderIds(address)) as ProviderInfo[];
      const providerData = providerInfos;
      const productInactive = await getInactiveProductHandler();
      await dispatch(
        fetchProductWithZonesAndPricesSales({
          query: {
            page: 1,
            limit: 10000,
            orderBy: 'createdAt,desc',
          },
          providerData,
          productInactive,
        }),
      )
        .unwrap()
        .catch(() => {
          toast.error('Erreur');
        });
    } catch (error) {
      console.error('Error fetching zones with providers:', error);
    }
  };

  const processData = (rawData: RawDataSales[], products: Product[]): FinalData[] => {
    const productMap = new Map(products.map((p) => [p.id, p.name]));
    const groupedData: Record<string, FinalData> = {};
    rawData.forEach((item: RawDataSales) => {
      const productName = productMap.get(item.productId) || item.Produit || 'Unknown Product';
      const refKey = item.ref || 'UNKNOWN_REF';
      if (!groupedData[refKey]) {
        groupedData[refKey] = {
          name: refKey,
          offers: [],
        };
      }

      const serviceProviderGroup = groupedData[refKey];

      let existingOffer = serviceProviderGroup.offers.find((offer: FinalData['offers'][number]) =>
        offer.products.includes(productName),
      );

      if (!existingOffer) {
        existingOffer = {
          type:
            (item.Tarif ? item.Tarif : '') +
            (item.Tarif1 && item.Tarif != 'Semi-Forfait (déchets inclus)' ? ', ' + item.Tarif1 : ''),
          // (item.Tarif === "Semi-Forfait (déchets inclus)"  && item.Produit === "Prix tonne supp" ? " - Traitement des déchets, tonne supplémentaire" : "") +
          // (item.Tarif === "Semi-Forfait (déchets inclus)"  && item.Produit != "Prix tonne supp" ? " - Traitement des déchets inclus dans la limite de tonnage du prestataire" : ""),
          zones: [],
          products: [productName],
          comments: item.comment || null,
        };
        serviceProviderGroup.offers.push(existingOffer);
      }

      if (
        existingOffer &&
        !existingOffer.zones.some((zone: { zone: string; price: number }) => zone.zone === item.zonenumber)
      ) {
        existingOffer.zones.push({
          zone: item.zonenumber,
          price: item.priceValue,
        });
      }
    });

    return Object.values(groupedData);
  };

  const filteredProvidersData = (listPrestataire as ServiceProviderWithOffers[]).filter((provider) => {
    const matchesProduct = selectedProduct
      ? provider.offers.some((offer) => offer.products.some((product) => product.includes(selectedProduct)))
      : true;

    return matchesProduct;
  });

  const calculateRowSpans = (providers: FinalData[]) => {
    const tableData: TableRow[] = [];

    providers.forEach((provider, providerIndex) => {
      const providerOfferCount = provider.offers.reduce((acc: number, offer) => acc + offer.zones.length, 0);

      <>{provider.name}</>;
      provider.offers.forEach((offer, offerIndex) => {
        const zoneCount = offer.zones.length;
        offer.zones.forEach((zone, zoneIndex) => {
          const productsWithLineBreaks = offer.products.join('\n');
          tableData.push({
            key: `zone-${providerIndex}-${offerIndex}-${zoneIndex}`,
            prestataires:
              zoneIndex === 0 && offerIndex === 0 ? (
                <>
                  {provider.name}
                  <br />
                </>
              ) : (
                ''
              ),
            produit: zoneIndex === 0 ? productsWithLineBreaks : '',
            tarif: zoneIndex === 0 ? offer.type : '',
            commentaire: zoneIndex === 0 ? offer.comments || '' : '',
            zone: zone.zone,
            prixPresta: zone.price,
            rowSpan: {
              prestataires: offerIndex === 0 && zoneIndex === 0 ? providerOfferCount : 0,
              produit: zoneIndex === 0 ? zoneCount : 0,
            },
          });
        });
      });
    });

    tableData.sort((a, b) => {
      if (a.produit < b.produit) return -1;
      if (a.produit > b.produit) return 1;
      return 0;
    });

    return { tableData };
  };

  const { tableData } = calculateRowSpans(filteredProvidersData as FinalData[]);

  const columns: ColumnsType<TableRow> = [
    {
      title: 'Produit',
      dataIndex: 'produit',
      key: 'produit',
      render: (text: string, record: TableRow) => ({
        children: <div style={{ whiteSpace: 'pre-line' }}>{text}</div>,
        props: {
          rowSpan: record.rowSpan.produit || 0,
        },
      }),
    },
    {
      title: 'Tarif',
      dataIndex: 'tarif',
      key: 'tarif',
      render: (text: string, record: TableRow) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan.produit || 0,
        },
      }),
    },
    {
      title: 'Commentaire',
      dataIndex: 'commentaire',
      key: 'commentaire',
      render: (text: string, record: TableRow) => ({
        children: text,
        props: {
          rowSpan: record.rowSpan.produit || 0,
        },
      }),
    },
    {
      title: 'Prix de vente',
      dataIndex: 'prixPresta',
      key: 'prixPresta',
      render: (text: string) => ({
        children: `${text} € (15%)`,
        props: {},
      }),
    },
  ];

  useEffect(() => {
    if (getAllProducts.rows?.length > 0) {
      if (productsPrice?.length > 0) {
        const processedData = processData(productsPrice, getAllProducts.rows);
        setListPrestataire(processedData);
      } else {
        setListPrestataire([]);
        dispatch(resetState());
      }
      setLoading(false);
      setTableIsLoading(false);
    }
  }, [getAllProducts, productsPrice]);

  useEffect(() => {
    return () => {
      dispatch(resetState());
    };
  }, [dispatch]);

  const handleOpenLogDemandeModal = async () => {
    try {
      setIsLogDemandeModalOpen(true);
    } catch (error) {
      console.log(error);
      toast.error(
        'Merci d&apos;ajouter un produit et de remplir les champs obligatoires dans le devis avant de l&apos;envoyer à la LOG',
      );
    }
  };
  const handleEstimateSubmit = async (
    actionType: DevisActionType = 'demander',
    valueDemander: Demander | null = null,
    isCopiedSubmit?: boolean,
    submitType?: string,
    submitStatus?: string,
  ) => {
    try {
      if (!valueDemander) return;
      const submitData = {
        ...valueDemander,
        documentStatus: submitStatus,
        owner: sale?.crmUserId || '',
        date_du_besoin: dayjs(valueDemander.date_du_besoin).format('YYYY-MM-DD'),
      };

      if (actionType === 'demander') {
        submitData.documentStatus = DOCUMENT_STATUSES.DRAFT.key;
      }

      const res = await zohoService.createOrUpdateEstimate({
        actionType,
        data: submitData,
      });

      if (res?.data?.[0]?.details) {
        const createdByName = res.data[0].details.Created_By?.name || 'Nom inconnu';
        const logId = res.data[0].details.id || 'ID non trouvé';
        toast.success(
          <div>
            Succès ! Cr&#233;&#233; par {createdByName}
            <br />
            ID de l&#39;échange logistique : {logId}
          </div>,
        );
      }
    } catch (error) {
      console.error('Submission error:', error);
      toast.error(
        <div>
          Erreur lors de la soumission
          <br />
          {(error as Error)?.message || 'Une erreur inconnue est survenue'}
        </div>,
      );
    }
  };

  return (
    <>
      <PageTitle>RECHERCHE DE PRIX</PageTitle>
      <Spinner loading={loading}>
        <>
          <MainTitle parent='Recherche de prix' child='COMMERCE' />
          <Divider className='horizontal-bar' />
          <DemanderUnPrix
            submit={handleEstimateSubmit}
            isOpenModal={isLogDemandeModalOpen}
            setIsOpenModal={setIsLogDemandeModalOpen}
            data={quotationData}
            contact={null}
            type='createEstimate'
            showClientInput={true}
          />
          <div>
            <div className='product-page__searching'>
              <Form layout='vertical'>
                <Row gutter={16}>
                  <Col span={8}>
                    <Form.Item label='Adresse chantier' name='formattedAddressChantier'>
                      <SelectWithGoogleSuggestion
                        autoConvertAddress
                        placeholder='Sélectionner une adresse'
                        options={addressChantier ?? []}
                        defaultValue={selectAddressChantier}
                        onSelect={(value: Address) => {
                          if (value) {
                            handleSelectChantierAddress(value);
                          }
                        }}
                      />
                    </Form.Item>
                  </Col>
                  <Col span={8}>
                    <Form.Item label=' ' name='requestPriceLog'>
                      <Button onClick={() => handleOpenLogDemandeModal()} className='devis-page__btn__send'>
                        Demander un prix à la log
                      </Button>
                    </Form.Item>
                  </Col>
                </Row>
              </Form>
            </div>
          </div>
          <Divider className='horizontal-bar' />
          <strong className='main-title__child' style={{ fontSize: '20px' }}>
            Prix de vente commerce
          </strong>
          <Table
            style={{ marginTop: '20px' }}
            columns={columns}
            dataSource={tableData}
            pagination={false}
            rowKey={(record) => record.key}
            className='region-list__datatable'
            loading={{
              indicator: (
                <ScaleLoader
                  color='#A6C84D'
                  cssOverride={{
                    display: 'inline-block !important',
                    margin: '0 auto',
                    left: 0,
                    height: '100%',
                    width: '100%',
                  }}
                  aria-label='Loading Spinner'
                  data-testid='loader'
                />
              ),
              spinning: porvidersWithProductsLoading === 'pending' || tableIsloading,
            }}
          />
          <div className='pagination__pagination-items'>
            <span className='pagination__number-total'>Total {porvidersWithProductsTotal} items</span>
          </div>
          <Divider className='horizontal-bar' />
        </>
      </Spinner>
    </>
  );
};

export default SearchPriceCommercial;
