/* eslint-disable @typescript-eslint/no-explicit-any */
import { DocumentProductLine, Quotation, Zone } from 'models';
import { Address, DocumentQueryTypes } from 'types';
import { v4 as uuidv4 } from 'uuid';
import { DOCUMENT_TYPES } from './constant';
import { FormInstance } from 'antd';
import { toast } from 'react-toastify';
export const getExistProp = (data: any) => {
  const result = Object.keys(data).reduce((nextData, key) => {
    if (data[key]) {
      return {
        ...nextData,
        [key]: data[key],
      };
    }

    return nextData;
  }, {});

  return result;
};

export const pick = <T extends object, K extends keyof T>(whitelisted: K[], target: T, defaultValue?: any) =>
  Object.fromEntries(whitelisted.map((key) => [key, key in target ? target[key] : defaultValue]));

export const convertAddress = (place: any): null | Address => {
  if (!place?.address_components || !place?.formatted_address || !place?.place_id) {
    return null;
  }
  const result: {
    city?: string;
    zip?: string;
    postalcode?: string;
    country?: string;
    address?: string;
    latitude?: string;
    longitude?: string;
    fullAddress?: string;
    isCompleted?: boolean;
    formattedAddress?: string;
    countryRegion?: string;
  } = {};
  const { address_components } = place;

  for (const component of address_components) {
    if (component.types.includes('postal_town')) {
      result.city = component.long_name;
    } else if (component.types.includes('locality')) {
      result.city = component.long_name;
    } else if (component.types.includes('administrative_area') && !result.city) {
      result.city = component.long_name;
    } else if (component.types.includes('postal_code')) {
      result.zip = component.long_name;
      result.postalcode = component.long_name;
    } else if (component.types.includes('country')) {
      result.country = component.long_name;
    } else if (component.types.includes('route')) {
      result.address = (result.address ? result.address + ' ' : '') + component.long_name;
    } else if (component.types.includes('street_number')) {
      result.address = component.long_name + (result.address ? ' ' + result.address : '');
    } else if (component.types.includes('administrative_area_level_1')) {
      result.countryRegion = component.long_name;
    }
  }

  const { lat, lng } = place.geometry.location;

  result.latitude = lat ? (typeof lat === 'number' ? lat : lat()) : '';
  result.longitude = lng ? (typeof lng === 'number' ? lng : lng()) : '';
  result.isCompleted = result.city && result.zip && result.address ? true : false;
  // result.fullAddress = formatted_address;
  result.formattedAddress = getFullAddress(result);
  result.fullAddress = getFullAddress(result);
  return result;
};

export const validateZones = (zones: Zone[] | undefined) => {
  if (zones?.length) {
    for (let index = 0; index < zones.length; index++) {
      const zone = zones[index];
      if (!zone.maxDistance) {
        return false;
      }
      const prevZone = zones[index - 1];
      if (index > 0 && prevZone.maxDistance && zone.maxDistance <= prevZone.maxDistance) {
        return false;
      }
    }
  }
  return true;
};

export const numberWithCommas = (value: string | number | null) => {
  return (
    parseFloat(value?.toString() ?? '0')
      ?.toString()
      .replace(/\B(?=(\d{3})+(?!\d))/g, ',') ?? 0
  );
};

export const frenchCurrencyFormat = (amount: number | string) => {
  return amount
    .toString()
    .replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1 ')
    .replace('.', ',');
};

export const getFullAddress = (addressObj?: Address) => {
  let result = '';
  if (addressObj) {
    result = [addressObj.address, ((addressObj?.postalcode ?? '') + ' ' + (addressObj?.city ?? '')).trim()]
      .filter((i) => i)
      .join(', ');
  }
  return result;
};
export const isObjectEmpty = (objectName: any) => {
  return !objectName || Object.keys(objectName).length === 0;
};

export const mapAddressArray = (addresses: any, addressType: string) => {
  return addresses
    ?.filter((item: any) => item?.addressType === addressType)
    .map((item: any) => ({
      ...item.Address,
      contactAddressId: item.id,
      address_id: uuidv4(),
      postalcode: item.Address?.postalCode,
      crmAddressId: item?.crmAddressId,
      bookAddressId: item?.bookAddressId,
      addressType: item?.addressType,
    }));
};

export const anglicizedFrench = (value: string) => {
  return value.normalize('NFD').replace(/[\u0300-\u036f]/g, '');
};

export const formatDate = (isoDateString: string): string => {
  const dateParts = isoDateString.split('T')[0].split('-');
  const formattedDate = `${dateParts[2]}/${dateParts[1]}/${dateParts[0]}`;
  return formattedDate;
};

export const getFileNameWithoutExtension = (fileName: string) => {
  return fileName.replace(/\.[^/.]+$/, '');
};

export const getRappelDevis = (data: any) => {
  const { relance_devis_automatique, tache_de_rappel } = data;
  return relance_devis_automatique
    ? tache_de_rappel
      ? 'Relance devis + Rappel'
      : 'Relance devis'
    : tache_de_rappel
      ? 'Rappel'
      : '';
};

export const convertBase64 = (file: File) => {
  return new Promise((resolve, reject) => {
    const fileReader = new FileReader();
    fileReader.readAsDataURL(file);
    fileReader.onload = () => {
      resolve(fileReader.result);
    };
    fileReader.onerror = (error) => {
      reject(error);
    };
  });
};

export const getBooksTaxId = (taxPercentage: number | null) => {
  if (taxPercentage == 0) {
    return process.env.REACT_APP_BOOK_TAX_ID_1;
  } else if (taxPercentage == null || taxPercentage == 20) {
    return process.env.REACT_APP_BOOK_TAX_ID_2;
  } else {
    return null;
  }
};

// Normalize the text and remove characters like colon, hyphen, space, etc.
export const normalizeString = (text: string): string => {
  return anglicizedFrench(text)
    .replace(/[:\\-\s]+/g, ' ')
    .toLowerCase()
    .replace(/\s+/g, ' ')
    .trim();
};

export const productFilter = (input: string, option?: { label?: string }): boolean => {
  if (!option || !option.label) return false;

  const normalize = (text: string) => normalizeString(text);
  const inputTokens = normalize(input).split(' '); // Split input into tokens
  const optionLabel = normalize(option.label);

  // Ensure all tokens are found in the label
  return inputTokens.every((token) => optionLabel.includes(token));
};

export function generateDocumentTitle(
  documentType: string,
  query: DocumentQueryTypes,
  quotationData: Quotation,
  type: 'CREATE' | 'DETAIL',
  booksDocumentId?: string,
) {
  const defaultTitle = 'DEVIS DEV-2024';
  const originalQuotationId = quotationData?.document?.cdeZoho;
  const originalEstimateId = quotationData?.document?.EstimateDocument?.cdeZoho;
  if (type === 'CREATE') {
    if (documentType === DOCUMENT_TYPES.QUOTATION) {
      if (query?.estimate_id) {
        return `NOUVEAU DEVIS ${originalQuotationId ? `- (Devis original: ${originalQuotationId})` : ''}`;
      }
      return defaultTitle;
    } else if (documentType === DOCUMENT_TYPES.ORDER) {
      if (query?.estimate_id) {
        return `NOUVELLE COMMANDE ${originalQuotationId ? `- (Devis original: ${originalQuotationId})` : ''}`;
      } else if (query?.order_id) {
        return `NOUVELLE COMMANDE ${originalQuotationId ? `- (Commande originale: ${originalQuotationId})` : ''}`;
      }
      return 'NOUVELLE COMMANDE ECODROP';
    }
  } else if (type === 'DETAIL') {
    if (documentType === DOCUMENT_TYPES.ORDER && originalEstimateId) {
      return `${originalQuotationId ?? booksDocumentId} - Devis original: ${originalEstimateId}`;
    } else {
      return `${originalQuotationId ?? booksDocumentId}`;
    }
  }
  return defaultTitle;
}
export const duplicates = (values: Record<string, DocumentProductLine>, value?: string) => {
  try {
    if (!values) return;
    const nameCount: Record<string, Set<string>> = {}; // Use Set to store unique UUIDs

    Object.entries(values)?.forEach(([uuid, item]) => {
      const lineItem = item as DocumentProductLine;
      if (!(lineItem?.creationType === 'header' || lineItem?.creationType === 'header-multi-product')) return;
      const headerName = lineItem?.headerName?.trim();
      const productNameForClient = lineItem?.productNameForClient?.trim();
      const itemName = productNameForClient || headerName;
      if (value && value.localeCompare(itemName || '') !== 0) return;

      if (itemName) {
        if (!nameCount[itemName]) {
          nameCount[itemName] = new Set(); // Ensure it's initialized as a Set
        }
        nameCount[itemName]!.add(uuid);
      }
    });
    // Get a set of UUIDs that appear more than once
    const duplicatedUuids = new Set(
      Object.values(nameCount)
        .filter((uuids) => uuids.size > 1)
        .flatMap((uuids) => Array.from(uuids)), // Convert Set to an array before flattening
    );

    return duplicatedUuids;
  } catch (error) {
    console.log(error);
    return new Set();
  }
};
export const formSetValue = (form: FormInstance, value?: string) => {
  try {
    const values = form.getFieldsValue() || {};
    if (!values?.lineItems) return;
    const duplicatedUuids = duplicates(values?.lineItems, value);
    const updatedFields = Object.keys(values?.lineItems)?.flatMap((uuid) => {
      const hasError = duplicatedUuids?.has(uuid);
      if (values?.lineItems[uuid]?.creationType === 'header') {
        return [
          {
            name: ['lineItems', uuid, 'headerName'],
            errors: hasError
              ? ['Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.']
              : [],
            validated: !hasError,
          },
        ];
      } else if (values?.lineItems[uuid]?.creationType === 'header-multi-product') {
        return [
          {
            name: ['lineItems', uuid, 'productNameForClient'],
            errors: hasError
              ? ['Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.']
              : [],
            validated: !hasError,
          },
        ];
      }
      return [];
    });
    form.setFields(updatedFields);
    return duplicatedUuids;
  } catch (error) {
    console.log('formSetValue: ', error);
  }
};
export const checkHeaderNameDuplicates = (form: FormInstance, value?: string) => {
  try {
    const duplicatedUuids = formSetValue(form, value);
    if (duplicatedUuids && duplicatedUuids?.size > 0) {
      toast.error('Impossible de créer un en-tête avec le même contenu. Veuillez modifier vos en-têtes.');
    }
  } catch (error) {
    console.log('checkHeaderNameDuplicates: ', error);
  }
};

export const isValidUrl = (url: string) => {
  const regex = /^(ftp|http|https):\/\/[^ "]+$/;
  return regex.test(url);
};
export const debounce = <T extends (...args: any[]) => void>(fn: T, delay: number) => {
  let timeoutId: NodeJS.Timeout;
  return (...args: Parameters<T>) => {
    clearTimeout(timeoutId);
    timeoutId = setTimeout(() => fn(...args), delay);
  };
};
