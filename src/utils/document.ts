import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Demander, DocumentProductLinePrestation, Quotation, Sale } from 'models';
import {
  ADDRESS_TYPE,
  DOCUMENT_TYPES,
  LIST_CRM_ID_VALIDATE_AND_CREATE_ORDER,
  PRESTAION_STATUS,
  SUB_STATUSES,
} from './constant';
import { DevisActionType, PaymentType } from 'types';
import { getRappelDevis } from 'utils';

export const checkShowButtonValidateAndCreateOrder = (lastModifyById?: string) => {
  const item = LIST_CRM_ID_VALIDATE_AND_CREATE_ORDER.find((obj) => {
    return obj.CRM_ID === lastModifyById;
  });
  return item !== undefined;
};

export const generateDocumentData =
  ({
    quotationData,
    contact,
    sales,
    values,
    submitType,
    submitStatus,
    lastModifyById,
    countryRegionId,
    booksTaxId,
    documentType,
    paymentInfo,
    actionType,
    valueDemander,
    isUpdate = false,
  }: {
    quotationData: Quotation;
    sales: Sale[];
    contact: ClientContact | null;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    values: any;
    submitType?: string;
    submitStatus?: string;
    lastModifyById?: string;
    countryRegionId?: number | null;
    booksTaxId: string;
    documentType: string;
    paymentInfo: PaymentType | null;
    actionType?: DevisActionType;
    valueDemander?: Demander | null;
    isUpdate?: boolean;
  }) =>
  async () => {
    const { city, postalCode, formattedAddress, postalcode, latitude, longitude } =
      quotationData?.chantierAddress || {};
    const typeDocument = quotationData?.documentTypes?.find((item) => item.key === submitType);
    const documentStatus = quotationData?.documentStatus?.find((item) => item.status === submitStatus);
    const selectedLastmodify = sales?.find((i) => i.crmUserId === lastModifyById);
    const lastModifySale = sales?.find((i) => i.id === values?.vendeur);
    const referentSale = sales?.find((i) => i.id === values?.referent);
    const bookContactPerson = contact?.ContactPersons?.find(
      (cp) => cp.id === (values?.contactPersons ? values?.contactPersons : contact?.ContactPersons?.[0]?.id),
    );
    const chantierAddress = !Array.isArray(quotationData?.chantierAddress) ? quotationData?.chantierAddress : null;
    const billingAddress = !Array.isArray(quotationData?.billingAddress) ? quotationData?.billingAddress : null;
    const submitData = {
      ...values,
      ...(valueDemander ? { valueDemander } : {}),
      contactId: contact?.id,
      contactPersonId: bookContactPerson?.id,
      booksTaxId: booksTaxId,
      crmContactPersonId: bookContactPerson?.booksContactPersonsId,
      documentTypeId: typeDocument?.id,
      documentType: typeDocument?.key,
      documentStatusId: documentStatus?.id,
      billingAddress: billingAddress
        ? {
            ...billingAddress,
            attention: [bookContactPerson?.firstName, bookContactPerson?.lastName].join(' '),
          }
        : null,
      billingAddressId: billingAddress?.id,
      siteAddress: chantierAddress,
      siteAddressId: chantierAddress?.id,
      siteCountryRegionId: countryRegionId ?? chantierAddress?.countryRegionId,
      siteAddressPostalCode: postalCode ?? postalcode,
      siteAddressCity: city,
      siteAddressFull: formattedAddress,
      siteAddressIsGps: quotationData?.isGpsAddress,
      siteAddressLatitude: latitude,
      siteAddressLongitude: longitude,
      isAddressChantierTemporary: quotationData?.isAddressChantierTemporary,
      email: values.email?.join(','),
      crmContactId: bookContactPerson?.crmContactId,
      lastModifyById: selectedLastmodify?.booksUserId,
      paiement: paymentInfo?.paiement,
      montantPaiement: paymentInfo?.montantPaiement,
      salespersonId: referentSale?.booksSalespersonId,
      customerId: contact?.booksContactId,
      lastModifyName: selectedLastmodify?.name,
      lastModifyPhone: selectedLastmodify?.phone,
      lastModifyEmail: selectedLastmodify?.email,
      zohoApiClientId: selectedLastmodify?.zohoApiClientId,
      zohoApiConnection: selectedLastmodify?.zohoApiConnection,
      vendeurCrm: lastModifySale?.booksUserId,
      referentCrm: referentSale?.booksSalespersonId,
      genererLienDePaiement: null,
      ...(documentType === DOCUMENT_TYPES.QUOTATION
        ? {
            rappelDevis: getRappelDevis(values),
          }
        : {}),
      salespersonPhone: referentSale?.phone,
      submitType: submitType,
    };
    if (bookContactPerson) {
      submitData.qboContactPersons = [bookContactPerson?.id];
      const contactPerson = values?.ccContactPersons?.filter((cp: number | string) => cp !== values?.contactPersons);
      if (contactPerson) {
        submitData.qboContactPersons = [...submitData.qboContactPersons, ...contactPerson];
      }
    }
    const selectedReferent = sales.find((i) => i.booksSalespersonId === submitData.salespersonId);
    if (billingAddress?.isCreate) {
      const submitContactAddress = {
        ...billingAddress,
        attention: [bookContactPerson?.firstName, bookContactPerson?.lastName].join(' '),
        address: billingAddress?.address ?? billingAddress?.formattedAddress,
        postalCode: billingAddress?.postalcode,
        contactId: contact?.id,
        addressType: ADDRESS_TYPE.BILLING,
        lastUsedAt: documentType === DOCUMENT_TYPES?.ORDER ? new Date() : null,
      };
      submitData.billingAddress = submitContactAddress;
    }
    const bookContact = contact?.ContactPersons?.filter((item) => submitData.qboContactPersons?.includes(item?.id)).map(
      (item) => item.booksContactPersonsId,
    );
    submitData.contactPersons = bookContact ?? [];
    if (chantierAddress?.isCreate) {
      const submitContactAddress = {
        ...chantierAddress,
        address: chantierAddress?.address ?? chantierAddress?.formattedAddress,
        postalCode: chantierAddress?.postalcode,
        addressType: ADDRESS_TYPE.SITE,
        owner: selectedReferent?.crmUserId ? { id: selectedReferent?.crmUserId } : '',
        contactSurPlace: submitData.contactSurPlace,
      };
      submitData.siteAddress = submitContactAddress;
    }
    const lineItems = Object.keys(submitData.lineItems).map((key) => {
      const item = { ...submitData.lineItems[key] };
      if (item.id && !isUpdate) {
        delete item.id;
      }
      if (isUpdate) {
        item.uuid = key;
      }
      return item;
    });
    if (
      actionType === 'open' ||
      (!isUpdate && checkShowButtonValidateAndCreateOrder(lastModifyById) && actionType === 'validate-and-create-order')
    ) {
      submitData.booksCurrentSubStatusId = SUB_STATUSES.a_traiter.id;
      submitData.booksCurrentSubStatus = SUB_STATUSES.a_traiter.label;
    }
    let itemOrder = 0;
    // const listProductLineId: (string | number)[] = [];
    let allPrestationDateLines: object[] = [];
    submitData.lineItems = lineItems
      .sort((a, b) => a.lineOrderNumber - b.lineOrderNumber)
      .flatMap((item, index) => {
        if (item.headerName) {
          return [];
        }
        itemOrder++;
        const { prestation = {}, quantity = 0 } = item;
        const { prixUnitaire = 0, priceFamilyId } = prestation.tarifItems || {};
        const totalBeforeDiscount = Number(prixUnitaire) * Number(quantity);
        const prestationDateLine = Object.keys(item?.prestation?.prestationDateLine || [])?.map((key) => ({
          ...item?.prestation?.prestationDateLine[key],
          prestationDate: item?.prestation?.prestationDateLine[key]?.prestationDate?.format('YYYY-MM-DD'),
          ...(isUpdate
            ? {
                id: item?.prestation?.prestationDateLine[key]?.isNewLine ? null : key,
                documentProductLinePrestationStatusId:
                  item?.prestation?.prestationDateLine[key]?.documentProductLinePrestationStatusId ??
                  quotationData?.documentProductLinePrestationStatus?.find(
                    (i) => PRESTAION_STATUS?.A_TRAITER === i?.name,
                  )?.id,
              }
            : {
                uuid: key,
                documentProductLinePrestationStatusId: quotationData?.documentProductLinePrestationStatus?.find(
                  (item) => PRESTAION_STATUS?.A_TRAITER === item?.name,
                )?.id,
              }),
        }));
        allPrestationDateLines = [...allPrestationDateLines, prestationDateLine];
        const productLineSubOptions = Object.keys(item?.prestation?.productLineSubOption || [])?.map((key) => ({
          ...item?.prestation?.productLineSubOption[key],
          optionId: key,
          ...(isUpdate ? { id: null } : {}),
        }));
        const productLinePrices = Object.keys(item?.prestation?.tarifItems?.productLinePrices || [])
          ?.filter((key) => key !== 'priceOption')
          .map((key) => ({ ...item?.prestation?.tarifItems?.productLinePrices[key] }));
        const productLinePriceOption = Object.keys(
          item?.prestation?.tarifItems?.productLinePrices?.priceOption || [],
        )?.map((key) => ({
          ...item?.prestation?.tarifItems?.productLinePrices?.priceOption[key],
          priceOptionId: key,
        }));
        const newProductLinePrice = productLinePrices?.flatMap((item) => {
          return productLinePriceOption
            ?.filter((i) => i?.priceId === item?.priceId)
            .reduce(
              (result, init) => {
                result.push(init);
                return result;
              },
              [item],
            );
        });
        const result = {
          ...item,
          unitPrice: item?.prestation?.tarifItems?.prixUnitaire,
          lineOrderNumber: itemOrder,
          priceFamilyId,
          totalBeforeDiscount,
          tarifItems: prestation?.tarifItems || [],
          prestationDateLine,
          productLineSubOptions,
          buyingPrice: null,
          priceMargin: null,
          validSP: null,
          productLinePrices: newProductLinePrice,
          priceFamilyValue: priceFamilyId
            ? item?.prestation?.tarifItems?.[priceFamilyId]?.priceFamilyValue
            : item?.prestation?.tarifItems?.priceFamilyValue,
          ...(isUpdate
            ? {
                linePriceFamilyId: priceFamilyId
                  ? item?.prestation?.tarifItems?.[priceFamilyId]?.linePriceFamilyId
                  : item?.prestation?.tarifItems?.linePriceFamilyId,
              }
            : {}),
          quantity: item?.quantity || 0,
        };
        result.priceFamilyPriceMargin = priceFamilyId
          ? item?.prestation?.tarifItems?.[priceFamilyId]?.priceFamilyPriceMargin
          : item?.prestation?.tarifItems?.priceFamilyPriceMargin;
        result.priceFamilyBuyingPrice = priceFamilyId
          ? item?.prestation?.tarifItems?.[priceFamilyId]?.priceFamilyBuyingPrice
          : item?.prestation?.tarifItems?.priceFamilyBuyingPrice;
        result.priceFamilyValidSP = priceFamilyId
          ? item?.prestation?.tarifItems?.[priceFamilyId]?.priceFamilyValidSP
          : item?.prestation?.tarifItems?.priceFamilyValidSP;
        if (result.priceFamilyBuyingPrice) {
          result.buyingPrice = item?.prestation?.tarifItems?.buyingPrice;
          result.priceMargin = parseFloat(
            ((parseFloat(result.unitPrice) - result.buyingPrice) / parseFloat(result.unitPrice)).toFixed(4),
          );
        }
        // adding headerName to product if previous row is header
        if (index > 0 && lineItems[index - 1].headerName) {
          if (lineItems[index - 1]?.creationType === 'header-multi-product') {
            if (
              result?.mainProductId &&
              String(result?.mainProductId) === String(lineItems[index - 1]?.productId || '')
            ) {
              result.headerName = lineItems[index - 1].headerName;
            }
          } else {
            result.headerName = lineItems[index - 1].headerName;
          }
          if (isUpdate) {
            result.booksProductLineHeaderId = lineItems[index - 1].booksProductLineHeaderId;
          }
        }
        // if (isUpdate && result?.id) {
        //   listProductLineId.push(result?.id);
        // }
        return [result];
      });
    // most recent prestation date
    if (!isUpdate) {
      const earliestPrestationDateLineItem = (allPrestationDateLines as DocumentProductLinePrestation[])
        .flatMap((item) => item)
        .reduce((earliest, current) => {
          if (!current.prestationDate) return earliest;
          if (!earliest.prestationDate) return current;
          return current.prestationDate < earliest.prestationDate ? current : earliest;
        }, {} as DocumentProductLinePrestation);
      submitData.latestPrestationDate = earliestPrestationDateLineItem?.prestationDate ?? null;
    }
    if (isUpdate && documentType === DOCUMENT_TYPES.ORDER) {
      // most recent prestation date
      const earliestPrestationDateLineItem = (allPrestationDateLines as DocumentProductLinePrestation[])
        .flatMap((item) => item)
        .reduce((earliest, current) => {
          if (!current.prestationDate) return earliest;
          if (!earliest.prestationDate) return current;
          return current.prestationDate < earliest.prestationDate ? current : earliest;
        }, {} as DocumentProductLinePrestation);
      submitData.latestPrestationDate = earliestPrestationDateLineItem?.prestationDate ?? null;
    }
    return submitData;
  };
