import React from 'react';
import { Tag } from 'antd';
import { TrashButton } from 'components/Common';
import { useState, useEffect } from 'react';
import { priceOptionService, priceService, productService, productTypeRegulService } from 'services';
import { toast } from 'react-toastify';
import { Price, PriceOption } from 'models';
import { useParams } from 'react-router-dom';
interface ConvertFromStringToJSXType {
  str: string;
  menuList: { id?: number; title: string; label: string; key: string }[];
  children?: React.ReactElement;
  handleUpdateWordState: (wordState: string) => void;
}

export const ConvertFromStringToJSX: React.FC<ConvertFromStringToJSXType> = ({
  str,
  menuList,
  handleUpdateWordState,
}: {
  str: string;
  menuList: { id?: number; title: string; label: string; key: string }[];
  handleUpdateWordState: (wordState: string) => void;
}) => {
  let words = str.split('(suggestion)');
  const params = useParams();
  const productTypeId = parseInt(params.productTypeId as string);
  words = words.filter((element: string) => element !== ' ' && element !== '');
  const [wordState, setWordState] = useState<string[]>([]);
  const [deletingIds, setDeletingIds] = useState<(number | undefined)[]>([]);

  useEffect(() => {
    setWordState(words);
  }, []);

  const handleUpdateProductTypeDescription = async (productTypeId: number, description: string) => {
    try {
      await productService.updateProductType(productTypeId, { description: description });
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleDelete = async (index: number, word: string) => {
    try {
      setDeletingIds((prevState) => [...prevState, index as number]);
      let newWords = wordState.map((w, i) => {
        if (i === index) {
          return w.replace(`[${word}`, '');
        }
        return w;
      });
      newWords = newWords.map((w) => {
        if (w.endsWith(']')) {
          return w + '(suggestion)';
        }
        return w;
      });
      await handleUpdateProductTypeDescription(productTypeId, newWords.join(' '));
      handleUpdateWordState(newWords.join(' '));
      setWordState(
        newWords
          .filter((element: string) => element !== '' && element !== ' ')
          .join(' ')
          .split('(suggestion)'),
      );
      setDeletingIds(deletingIds.filter((id) => id !== index));
    } catch (error) {
      console.log(error);
      setDeletingIds(deletingIds.filter((id) => id !== index));
    }
  };

  return (
    <React.Fragment>
      {wordState.map((word: string, index: number) => {
        let newWords = word.split('[');
        newWords = newWords.filter((element: string) => element !== '' && element !== ' ');
        const newContents = newWords.map((w, i) => {
          if (w.includes('\n')) {
            const lines = w.split('\n');
            return (
              <React.Fragment key={`${index}-${i}`}>
                {lines.map((line, lineIndex) => {
                  if (line.trim() === '') {
                    return lineIndex !== 0 ? <br key={`${index}-${i}-${lineIndex}`} /> : null;
                  }
                  if (line.trim().includes(']')) {
                    return '[' + line.trim();
                  }
                  return (
                    <React.Fragment key={`${index}-${i}-${lineIndex}`}>
                      {lineIndex !== 0 && <br />}
                      {line}
                    </React.Fragment>
                  );
                })}
              </React.Fragment>
            );
          }

          if (w.trim().includes(']') && newWords.length - 1 !== i) {
            return '[' + w.trim();
          }

          if (w.endsWith(']') && newWords.length - 1 === i) {
            const nws = w.replace(']', '').split('-');
            const capitalizedWords = nws.map((word) => word.charAt(0).toUpperCase() + word.slice(1));

            const suggestionId = capitalizedWords[1];
            const suggestion = menuList?.find((m) => m.id === Number(suggestionId));

            const optionNameArr = suggestion?.title.split(' ');

            const optionName = optionNameArr
              ?.filter((option: string, k: number) => k !== 0 && k !== optionNameArr.length - 1)
              .join(' ');

            const tagContent = nws[0] + ' ' + optionName + ' ' + nws[nws.length - 1];

            const key = index++;

            return (
              <React.Fragment key={key}>
                <Tag key={key} className='product-description__sub-tag'>
                  {tagContent}
                  <TrashButton
                    className='product-description__trash-icon trash-button'
                    onClick={() => handleDelete(index - 1, w)}
                    loading={deletingIds.includes(index - 1)}
                  />
                </Tag>
              </React.Fragment>
            );
          }

          return w;
        });
        return newContents;
      })}
    </React.Fragment>
  );
};

export const AddTagsPriceRegul = ({
  str,
  handleUpdateWordState,
  selectedRegulId,
  onChangeDescription,
}: {
  str: string;
  handleUpdateWordState: (wordState: string) => void;
  selectedRegulId?: number;
  onChangeDescription?: (wordState: string) => void;
}) => {
  let words = str.split('(Regul)');
  words = words.filter((element: string) => element !== ' ' && element !== '');
  const [wordState, setWordState] = useState<string[]>([]);
  const [deletingIds, setDeletingIds] = useState<(number | undefined)[]>([]);

  useEffect(() => {
    setWordState(words);
  }, []);

  const handleUpdateOption = async (description: string | undefined) => {
    try {
      await productTypeRegulService.updateProductTypeRegul(selectedRegulId as number, {
        description: description,
      });
      if (onChangeDescription) {
        onChangeDescription(description as string);
      }
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
    }
  };

  const handleDelete = async (index: number, word: string) => {
    try {
      setDeletingIds((prevState) => [...prevState, index as number]);
      let newWords = wordState.map((w, i) => {
        if (i === index) {
          return w.replace(`[${word}`, '');
        }
        return w;
      });
      newWords = newWords.map((w) => {
        if (w.endsWith(']')) {
          return w + '(Regul)';
        }
        return w;
      });
      await handleUpdateOption(newWords.join(' '));
      handleUpdateWordState(newWords.join(' '));
      setWordState(
        newWords
          .filter((element: string) => element !== '' && element !== ' ')
          .join(' ')
          .split('(Regul)'),
      );
      setDeletingIds(deletingIds.filter((id) => id !== index));
    } catch (error) {
      console.log(error);
      setDeletingIds(deletingIds.filter((id) => id !== index));
    }
  };

  return (
    <React.Fragment>
      {wordState.map((word: string, index: number) => {
        let newWords = word.split('[');
        newWords = newWords.filter((element: string) => element !== '' && element !== ' ');
        const newContents = newWords.map((w, i) => {
          if (w.includes('\n')) {
            const lines = w.split('\n');
            return (
              <React.Fragment key={`${index}-${i}`}>
                {lines.map((line, lineIndex) => {
                  if (line.trim() === '') {
                    return lineIndex !== 0 ? <br key={`${index}-${i}-${lineIndex}`} /> : null;
                  }
                  if (line.trim().includes(']')) {
                    return '[' + line.trim();
                  }
                  return (
                    <React.Fragment key={`${index}-${i}-${lineIndex}`}>
                      {lineIndex !== 0 && <br />}
                      {line}
                    </React.Fragment>
                  );
                })}
              </React.Fragment>
            );
          }

          if (w.trim().includes(']') && newWords.length - 1 !== i) {
            return '[' + w.trim();
          }

          if (w.trim().endsWith(']') && newWords.length - 1 === i) {
            const nws = w.trim().replace(']', '');
            return (
              <React.Fragment key={`${index}-${i}`}>
                <Tag className='product-description__sub-tag'>
                  {nws}
                  <TrashButton
                    className='product-description__trash-icon trash-button'
                    onClick={() => handleDelete(index, w)}
                    loading={deletingIds.includes(index)}
                  />
                </Tag>
              </React.Fragment>
            );
          }

          return w;
        });
        return newContents;
      })}
    </React.Fragment>
  );
};

export const AddTagsPriceOption = ({
  str,
  handleUpdateWordState,
  selectedPrice,
  onChangeDescription,
}: {
  str: string;
  handleUpdateWordState: (wordState: string) => void;
  selectedPrice?: Price;
  onChangeDescription?: (wordState: string) => void;
}) => {
  let words = str.split('(Tarif)');
  words = words.filter((element: string) => element !== ' ' && element !== '');
  const [wordState, setWordState] = useState<string[]>(words ?? []);
  const [deletingIds, setDeletingIds] = useState<(number | undefined)[]>([]);
  const [loading, setLoading] = useState<boolean>(false);

  const handleUpdateOption = async (newWords: string[]) => {
    const description = newWords.join(' ');
    if (!selectedPrice?.priceFamilyId) return;
    try {
      let catalogPriceStatus = false;
      let serviceProviderPriceStatus = false;
      selectedPrice?.CatalogPrices?.forEach((item) => {
        if (item.isActive && item.isCatalog) {
          catalogPriceStatus = true;
        } else if (item.isActive && !item.isCatalog) {
          serviceProviderPriceStatus = true;
        }
      });
      const priceOptions = await priceOptionService.getPriceOptions(selectedPrice?.id, {
        include: 'PriceType|Price|PriceSubOptions|CatalogPrices',
      });
      const newPriceOptions = priceOptions.map((priceOption) => {
        let newPriceOption: PriceOption & { catalogPriceStatus?: boolean; serviceProviderPriceStatus?: boolean } =
          priceOption;
        priceOption?.CatalogPrices?.forEach((item) => {
          if (item.isCatalog) {
            newPriceOption = {
              ...newPriceOption,
              catalogPriceStatus: item.isActive,
            };
          } else {
            newPriceOption = {
              ...newPriceOption,
              serviceProviderPriceStatus: item.isActive,
            };
          }
        });
        return newPriceOption;
      });
      await priceService.updatePrice(selectedPrice?.id, {
        priceFamilyId: selectedPrice?.priceFamilyId,
        name: selectedPrice?.name ?? '',
        description: description,
        priceTypeLogicId: selectedPrice.priceTypeLogicId,
        PriceOptions: newPriceOptions,
        catalogPriceStatus: catalogPriceStatus,
        serviceProviderPriceStatus: serviceProviderPriceStatus,
      });
      if (onChangeDescription) {
        onChangeDescription(description as string);
      }
      handleUpdateWordState(newWords.join(' '));
      setWordState(
        newWords
          .filter((element: string) => element !== '' && element !== ' ')
          .join(' ')
          .split('(Tarif)'),
      );
      setDeletingIds([]);
      toast.success('Succès');
      setLoading(false);
      return true;
    } catch (error) {
      console.log(error);
      setLoading(false);
      setDeletingIds([]);
      toast.error('Erreur');
    }
  };

  const handleDelete = async (index: number, word: string) => {
    try {
      setLoading(true);
      setDeletingIds([index]);
      let newWords = wordState.map((w, i) => {
        if (i === index) {
          return w.replace(`[${word}`, '');
        }
        return w;
      });
      newWords = newWords.map((w) => {
        if (w.endsWith(']')) {
          return w + '(Tarif)';
        }
        return w;
      });
      console.log(newWords);
      await handleUpdateOption(newWords);
    } catch (error) {
      console.log(error);
      toast.error('Erreur');
      setLoading(false);
      setDeletingIds([]);
    }
  };

  return (
    <React.Fragment>
      {wordState.map((word: string, index: number) => {
        let newWords = word.split('[');
        newWords = newWords.filter((element: string) => element !== '' && element !== ' ');
        const newContents = newWords.map((w, i) => {
          const originalWord = w;
          w = w.replaceAll(`Prix-${selectedPrice?.id}-Prix`, 'Prix');
          selectedPrice?.PriceOptions?.forEach((priceOption) => {
            w = w.replaceAll(`Prix-Option-${priceOption.id}-Input`, `${priceOption.name} Input`);
          });
          if (w.includes('\n')) {
            const lines = w.split('\n');
            return (
              <React.Fragment key={`${index}-${i}`}>
                {lines.map((line, lineIndex) => {
                  if (line.trim() === '') {
                    return lineIndex !== 0 ? <br key={`${index}-${i}-${lineIndex}`} /> : null;
                  }
                  if (line.trim().includes(']')) {
                    return '[' + line.trim();
                  }
                  return (
                    <React.Fragment key={`${index}-${i}-${lineIndex}`}>
                      {lineIndex !== 0 && <br />}
                      {line}
                    </React.Fragment>
                  );
                })}
              </React.Fragment>
            );
          }

          if (w.trim().includes(']') && newWords.length - 1 !== i) {
            return '[' + w.trim();
          }

          if (w.trim().endsWith(']') && newWords.length - 1 === i) {
            const nws = w.trim().replace(']', '');
            return (
              <React.Fragment key={`${index}-${i}`}>
                <Tag className='product-description__sub-tag'>
                  {nws}
                  <TrashButton
                    className='product-description__trash-icon trash-button'
                    onClick={() => handleDelete(index, originalWord)}
                    loading={loading && deletingIds.includes(index)}
                  />
                </Tag>
              </React.Fragment>
            );
          }

          return w;
        });
        return newContents;
      })}
    </React.Fragment>
  );
};
