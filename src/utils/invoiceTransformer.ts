import { Invoice, InvoiceDetail, Comment, PrestationDetail } from 'types';

// Interface for raw service line data from API
interface RawServiceLine {
  key?: string;
  productName?: string;
  productDetails?: string;
  productId?: number;
  quantity?: number;
  unity?: string;
  orderId?: string;
  zohoId?: string;
  commandId?: string;
  orderLink?: string;
  purchasePrice?: number;
  invoicedPrice?: number;
  clientPrice?: number;
  margin?: number;
  diffPurchase?: number;
  totalMargin?: number;
  totalDiffPurchase?: number;
  status?: string;
  isSelected?: boolean;
  isInvoiced?: boolean;
  invoiceNumber?: string | null;
  isWaitingForPrestReply?: boolean;
  comment?: Comment[];
  comments?: Comment[];

  // Detail-related properties
  detailId?: string;
  parentDetail?: RawParentDetail;
  detailProductName?: string;
  siteAddress?: string;
  sitePostalCode?: string;
  siteCity?: string;
  prestationDetails?: PrestationDetail[];
  commandNumber?: string;
  contact?: string;
  invoiceId?: number;
  prestataire?: string;
  prestataireInvoiceNumber?: string;

  // Row span properties
  isFirstLineOfDetail?: boolean;
  totalLinesInDetail?: number;
}

// Interface for parent detail data
interface RawParentDetail {
  productName?: string;
  productId?: number;
  invoiceNumber?: string;
  commandNumber?: string;
  siteAddress?: string;
  sitePostalCode?: string;
  siteCity?: string;
  prestationDetails?: PrestationDetail[];
  invoicedPrice?: number;
  clientPrice?: number;
  totalMargin?: number;
  totalDiffPurchase?: number;
  isSelected?: boolean;
  contact?: string;
}

// Interface for extracted service line (for backward compatibility)
export interface ExtractedServiceLine extends RawServiceLine {
  detailId: string;
  parentDetail: InvoiceDetail;
  invoiceId: number;
  prestataire: string;
  prestataireInvoiceNumber: string;
  isFirstLineOfDetail: boolean;
  totalLinesInDetail: number;
  comments: Comment[];
  rowSpan?: number; // For table display grouping
}

/**
 * Transform current service lines format to Invoice structure
 * This utility converts the existing data structure to the new Invoice-based format
 */
export const transformServiceLinesToInvoice = (data: {
  serviceLines: RawServiceLine[];
  prestataire: string;
  prestataireInvoiceNumber?: string;
  currentUser: string;
}): Invoice => {
  const { serviceLines, prestataire, prestataireInvoiceNumber } = data;

  // Group service lines by detail
  const groupedByDetail: { [key: string]: RawServiceLine[] } = {};
  
  serviceLines.forEach(line => {
    // Create a unique detail key based on product and invoice info
    const detailKey = line.detailId || `${line.invoiceId}-${line.productId}-${line.commandNumber || 'no-command'}`;
    if (!groupedByDetail[detailKey]) {
      groupedByDetail[detailKey] = [];
    }
    groupedByDetail[detailKey].push(line);
  });

  // Create invoice details
  const invoiceDetails: InvoiceDetail[] = [];
  
  Object.entries(groupedByDetail).forEach(([, lines]) => {
    const firstLine = lines[0];
    const parentDetail = firstLine.parentDetail || {};

    const detail: InvoiceDetail = {
      productName: parentDetail.productName || firstLine.detailProductName || '',
      productId: parentDetail.productId || firstLine.productId || 0,
      invoiceNumber: parentDetail.invoiceNumber || firstLine.invoiceNumber || '',
      commandNumber: parentDetail.commandNumber || firstLine.commandNumber || '',
      siteAddress: parentDetail.siteAddress || firstLine.siteAddress || '',
      sitePostalCode: parentDetail.sitePostalCode || firstLine.sitePostalCode || '',
      siteCity: parentDetail.siteCity || firstLine.siteCity || '',
      prestationDetails: (parentDetail.prestationDetails || firstLine.prestationDetails || []) as PrestationDetail[],
      invoicedPrice: parentDetail.invoicedPrice || firstLine.invoicedPrice || 0,
      clientPrice: parentDetail.clientPrice || firstLine.clientPrice || 0,
      totalMargin: parentDetail.totalMargin || firstLine.totalMargin || 0,
      totalDiffPurchase: parentDetail.totalDiffPurchase || firstLine.totalDiffPurchase || 0,
      isSelected: parentDetail.isSelected || firstLine.isSelected || false,
      contact: parentDetail.contact || firstLine.contact || '',
      invoiceLines: lines.map((line, index) => ({
        key: line.key || `line-${index}-${Date.now()}`,
        productName: line.productName || '',
        productDetails: line.productDetails || '',
        productId: line.productId || 0,
        quantity: line.quantity || 0,
        unity: line.unity || '',
        orderId: line.orderId || '',
        zohoId: line.zohoId || '',
        commandId: line.commandId || '',
        orderLink: line.orderLink || '',
        purchasePrice: line.purchasePrice || 0,
        invoicedPrice: line.invoicedPrice || 0,
        clientPrice: line.clientPrice || 0,
        margin: line.margin || 0,
        diffPurchase: line.diffPurchase || 0,
        status: line.status || '',
        isSelected: line.isSelected || false,
        isInvoiced: line.isInvoiced || false,
        invoiceNumber: line.invoiceNumber || null,
        isWaitingForPrestReply: line.isWaitingForPrestReply || false,
        comment: line.comments || line.comment || [] // Handle both formats
      }))
    };

    invoiceDetails.push(detail);
  });

  // Calculate totals
  const totalSelected = serviceLines
    .filter(line => line.isSelected)
    .reduce((sum, line) => sum + (line.invoicedPrice || 0), 0);

  const totalAmount = serviceLines
    .reduce((sum, line) => sum + (line.invoicedPrice || 0), 0);

  // Create the complete Invoice object
  const transformedInvoice: Invoice = {
    id: serviceLines[0]?.invoiceId || Date.now(),
    createdAt: new Date().toISOString(),
    prestataire: prestataire,
    prestataireInvoiceNumber: prestataireInvoiceNumber || '',
    status: 'brouillon',
    totalSelected: totalSelected,
    totalAvoirs: 0,
    totalAmount: totalAmount,
    invoicedAmount: totalAmount,
    avoirDetails: [],
    commentDetails: [],
    invoiceDetails: invoiceDetails
  };

  return transformedInvoice;
};

/**
 * Extract flat service lines from Invoice structure (for backward compatibility)
 */
export const extractServiceLines = (invoice: Invoice): ExtractedServiceLine[] => {
  const serviceLines: ExtractedServiceLine[] = [];
  
  invoice.invoiceDetails.forEach((detail, detailIndex) => {
    detail.invoiceLines.forEach((line, lineIndex) => {
      const extractedLine: ExtractedServiceLine = {
        ...line,
        // Add detail context for compatibility
        detailId: `${invoice.id}-${detailIndex}`, // Create a unique detail ID
        parentDetail: detail,
        detailProductName: detail.productName,
        siteAddress: detail.siteAddress,
        sitePostalCode: detail.sitePostalCode,
        siteCity: detail.siteCity,
        prestationDetails: detail.prestationDetails,
        commandNumber: detail.commandNumber,
        contact: detail.contact,
        invoiceNumber: detail.invoiceNumber,
        invoiceId: invoice.id,
        prestataire: invoice.prestataire,
        prestataireInvoiceNumber: invoice.prestataireInvoiceNumber,

        // Row span calculation
        isFirstLineOfDetail: lineIndex === 0,
        totalLinesInDetail: detail.invoiceLines.length,

        // Use comment from Invoice structure
        comments: line.comment || []
      };
      serviceLines.push(extractedLine);
    });
  });
  
  return serviceLines;
};
