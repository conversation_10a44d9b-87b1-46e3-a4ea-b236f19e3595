import { LogisticAction, PriseEnCharge } from '../models/bo';

export const OPTION_TYPES = {
  timeSlot: 'time-slot',
  option: 'option',
  variation: 'variation',
  regul: 'regul',
};

export const DEFAULT_TAX = 20;

export const PRICE_TYPES = {
  textInput: 'text-input',
  numberInput: 'number-input',
  selection: 'selection',
};

export const PRICE_TYPE_OPTIONS = [
  {
    id: 1,
    name: 'Text Input',
    key: 'text-input',
  },
  {
    id: 2,
    name: 'Number Input',
    key: 'number-input',
  },
  {
    id: 3,
    name: 'Selection',
    key: 'selection',
  },
];

export const PLATFORMS = {
  ecodrop: 'ecodrop',
  pointp: 'pointp',
  dalkia: 'dalkia',
};

export const SERVICE_TYPES = {
  benneur: 'benneur',
  camionneur: 'camionneur',
  pup: 'pup',
};

export const LOGISTIQUE_ORDER_TYPES = {
  nouvelles_commandes: 'nouvelles-commandes',
};

export const LOGISTIQUE_ORDER_STATUSES = {
  a_traiter: {
    key: 'confirmed',
    name: 'A traiter',
    status: 'toProcess',
    color: 'yellow',
  },
  prise_en_compte: {
    key: 'confirmed',
    name: 'Prise en compte',
    status: 'considered',
    color: 'blue',
  },
  traite: {
    key: 'confirmed',
    name: 'Traité',
    status: 'processed',
    color: 'green',
  },
  cancel: {
    key: 'confirmed',
    name: 'Annulée',
    status: 'void',
    color: 'red',
  },
};

export const BO_LOGISTIC_ACTION = {
  A_TRAITER: {
    key: 'A_TRAITER',
    name: 'A traiter',
  },
  PLANIFIEE: {
    key: 'PLANIFIEE',
    name: 'Planifiee',
  },
  ATTENTE_RETOUR_PRESTA: {
    key: 'ATTENTE_RETOUR_PRESTA',
    name: 'Attente retour presta',
  },
  ATTENTE_RETOUR_COMMERCIAL: {
    key: 'ATTENTE_RETOUR_COMMERCIAL',
    name: 'Attente retour commercial',
  },
  EN_DECHETERIE: {
    key: 'EN_DECHETERIE',
    name: 'en decheterie',
  },
  ANNULEE: {
    key: 'ANNULEE',
    name: 'Annulee',
  },
  KO: {
    key: 'KO',
    name: 'KO',
  },
  SUR_CHANTIER: {
    key: 'SUR_CHANTIER',
    name: 'Sur chantier',
  },
};

export const MP_COUNTRIES = [
  { key: 1, name: 'France', value: 'France' },
  { key: 2, name: 'Allemagne', value: 'Allemagne' },
  { key: 3, name: 'Espagne', value: 'Espagne' },
];

export const MP_PAYOUTS = [
  { key: 1, name: 'Pay-out automatique', value: true },
  { key: 2, name: 'Pay-out manuel', value: false },
];

export const PRICE_TYPE_LOGICS = {
  product: 'product',
  calculate: 'calculate',
  indicative_purpose: 'indicative-purpose',
};

export const CONTACT_TYPES = {
  BUSINESS: 'business',
  INDIVIDUEL: 'individual',
  PARTICULIER: 'Particulier',
  PROFESSIONNEL: 'professionnel',
};

export const DOCUMENT_TYPES = {
  QUOTATION: 'quotation',
  ORDER: 'order',
  INVOICE: 'invoice',
};

export const DOCUMENT_FORM = {
  CREATE: 'create',
  UPDATE: 'update',
};

export const CREATE_ACCOUNT = {
  CREATE_A_CONTACT: 'create-a-contact',
  CREATE_CONTACT_ESTIMATE: 'create-contact-estimate',
};

export const LIST_SERVICE = [
  { value: null, name: '-None-' },
  { value: 'Comptabilité', name: 'Comptabilité' },
  { value: 'Commerce', name: 'Commerce' },
  { value: 'Direction', name: 'Direction' },
  { value: 'Conducteur de travaux', name: 'Conducteur de travaux' },
  { value: 'Achat', name: 'Achat' },
  { value: 'Qualité', name: 'Qualité' },
  { value: 'Service Généraux', name: 'Service Généraux' },
  { value: 'service après vente', name: 'service après vente' },
];

export const CUSTOM_ORDER_FIELDS = {
  REFERENCE_CLIENT: 'cf_crm_contact_id',
  REFERENCE_BDC_CLIENT: 'cf_bdc_client',
  BOOKS_CONTACT_ID: 'cf_books_contact_id',
  ADDRESS_CHANTIER: 'cf_adresse_du_chantier',
  ADDRESS_CHANTIER_COMPL_TE: 'cf_adresse_compl_te_chantier',
  ADDRESS_CHANTIER_ZIP: 'cf_code_postal_du_chantier',
  ADDRESS_CHANTIER_CITY: 'cf_ville_du_chantier',
  CONTACT_CHANTIER: 'cf_contact_chantier',
  OBJECT_DEVIS: 'cf_objet_devis',
  DEMANDE_COMMERCIALE: 'cf_demande_commerciale',
  RESPONSE_LOGISTIQUE: 'cf_r_ponse_logistique',
  LAST_MODIFY_BY_ID: 'cf_modifi_par',
  VENDEUR: 'cf_vendeur',
  EMAILCC01: 'cf_emailcc01',
  EMAILCC02: 'cf_emailcc02',
  EMAILCC03: 'cf_emailcc03',
  EMAILCC04: 'cf_emailcc04',
  EMAILCC05: 'cf_emailcc05',
  EMAILCC: 'cf_emailcc',
  GENERER_LIEN_DE_PAIEMENT: 'cf_generer_lien_de_paiement',
  CURRENT_SUB_STATUS_ID: 'current_sub_status_id',
  CURRENT_SUB_STATUS: 'current_sub_status',
  ADDRESS_CHANTIER_LONG: 'cf_chantier_longitude',
  ADDRESS_CHANTIER_LAT: 'cf_chantier_latitude',
  GPS: 'cf_gps',
  GPS_LAT_ADDRESS: 'cf_chantier_latitude',
  GPS_LONG_ADDRESS: 'cf_chantier_longitude',
  PAIEMENT: 'cf_paiement',
  MONTANT_PAIEMENT: 'cf_montant_paiement',
  RECENT_PRESTATION_DATE: 'cf_ere_date_prestation',
};

export const ADDRESS_TYPE = {
  BILLING: 'billing',
  INVOICE: 'invoice',
  SITE: 'site',
};

export const CURRENCIES = [
  { value: '%', label: '%' },
  { value: '€', label: '€' },
];
export type StatusType = {
  key: string;
  title: string;
  color: string;
};
export const ORDER_STATUSES: {
  [key in
    | 'new'
    | 'draft'
    | 'open'
    | 'toProcess'
    | 'processed'
    | 'considered'
    | 'modificationInProgress'
    | 'void']: StatusType;
} = {
  new: {
    key: 'new',
    title: 'New',
    color: 'color_status_new',
  },
  draft: {
    key: 'draft',
    title: 'Brouillon',
    color: 'color_status_draft',
  },
  open: {
    key: 'open',
    title: 'Confirmée',
    color: 'color_status_sent',
  },
  toProcess: {
    key: 'confirmed',
    title: 'À traiter',
    color: 'color_status_todo',
  },
  processed: {
    key: 'confirmed',
    title: 'Traitée',
    color: 'color_status_accepted',
  },

  considered: {
    key: 'confirmed',
    title: 'Prise en compte',
    color: 'color_status_prise',
  },
  modificationInProgress: {
    key: 'confirmed',
    title: 'Modification en cours',
    color: 'color_status_modify',
  },
  void: {
    key: 'confirmed',
    title: 'Annulée',
    color: 'color_status_expire',
  },
};
export const STATUSES: {
  [key in
    | 'new'
    | 'draft'
    | 'sent'
    | 'accepted'
    | 'invoiced'
    | 'partiallyInvoiced'
    | 'declined'
    | 'expired']: StatusType;
} = {
  new: {
    key: 'new',
    title: 'New',
    color: 'color_status_new',
  },
  draft: {
    key: 'draft',
    title: 'Brouillon',
    color: 'color_status_draft',
  },
  sent: {
    key: 'sent',
    title: 'Envoyé',
    color: 'color_status_sent',
  },
  accepted: {
    key: 'accepted',
    title: 'Accepté',
    color: 'color_status_accepted',
  },
  invoiced: {
    key: 'accepted',
    title: 'Facturé',
    color: 'color_status_facture',
  },
  partiallyInvoiced: {
    key: 'confirmed',
    title: 'Partiellement facturée',
    color: 'color_status_facture',
  },
  declined: {
    key: 'accepted',
    title: 'Refusé',
    color: 'color_status_refuse',
  },
  expired: {
    key: 'accepted',
    title: 'Expiré',
    color: 'color_status_expire',
  },
};

export const ACCESS_PL = [
  { value: 'Oui', name: 'Oui' },
  { value: 'Non', name: 'Non' },
];

export const LIST_CHARGEMENT_PAR_LE_CLIENT = [
  { value: 'Oui', name: 'Oui' },
  { value: 'Non', name: 'Non' },
];

export const LIST_TYPE_DE_DEMANDE = [
  { value: 'Demande d’infos LOG', name: 'Demande d’infos LOG' },
  { value: 'Demande d’infos DD', name: 'Demande d’infos DD' },
];

export const DEFAULT_TYPE_DE_DEMANDE = 'Demande d’infos LOG';

export const LIST_TYPE_DE_BESOIN_DD = [
  { value: 'Benne', name: 'Benne' },
  { value: 'Collecte Palette', name: 'Collecte Palette' },
  { value: 'Vrac', name: 'Vrac' },
];

export const LIST_TYPE_DE_BESOIN = [
  { value: 'Benne', name: 'Benne' },
  { value: 'Camion', name: 'Camion' },
  { value: 'Semi / 8x4', name: 'Semi / 8x4' },
  { value: 'Camion Grappin', name: 'Camion Grappin' },
];

export const LIST_TYPE_DE_DECHET = [
  { value: 'Bois A', name: 'Bois A' },
  { value: 'Bois B', name: 'Bois B' },
  { value: 'Carton', name: 'Carton' },
  { value: 'Déchets Vert', name: 'Déchets Vert' },
  { value: 'DIB', name: 'DIB' },
  { value: 'Ferraille', name: 'Ferraille' },
  { value: 'Gravats', name: 'Gravats' },
  { value: 'Metal', name: 'Metal' },
  { value: 'Plastiques', name: 'Plastiques' },
  { value: 'Platre', name: 'Platre' },
];
export const LIST_TYPE_DE_DECHET_DD = [
  { value: 'Amiante lié', name: 'Amiante lié' },
  { value: 'Amiante Libre', name: 'Amiante Libre' },
  { value: 'Plombs', name: 'Plombs' },
  { value: 'Aérosol', name: 'Aérosol' },
  { value: 'Peinture', name: 'Peinture' },
  { value: 'Pneu', name: 'Pneu' },
  { value: 'Autres (préciser dans commentaire)', name: 'Autres (préciser dans commentaire)' },
];

export const LIST_VOLUME = [
  { value: '3 m3', name: '3 m3' },
  { value: '6 m3', name: '6 m3' },
  { value: '8 m3', name: '8 m3' },
  { value: '10 m3', name: '10 m3' },
  { value: '12 m3', name: '12 m3' },
  { value: '15 m3', name: '15 m3' },
  { value: '20 m3', name: '20 m3' },
  { value: '30 m3', name: '30 m3' },
];

export const LIST_QUESTIONS = [
  { value: 'PME', name: 'PME' },
  { value: 'Particulier', name: 'Particulier' },
  { value: 'Grand compte', name: 'Grand compte' },
];

export const PRESTAION_STATUS = {
  A_TRAITER: 'À Traiter',
  SUR_CHANTIER: 'Sur Chantier',
  PLANIFIEE: 'Planifiée',
  CANCEL: 'Annulée',
  EN_DECHETERIE: 'En Déchèterie',
};

export const DOCUMENT_ACTION = {
  CREATE_QUOTATION: 'create_quotation',
  UPDATE_QUOTATION: 'update_quotation',
  CREATE_ORDER: 'create_order',
  UPDATE_ORDER: 'update_order',
  CREATE_INVOICE: 'create_invoice',
  UPDATE_INVOICE: 'update_invoice',
};

export const DOCUMENT_STATUSES = {
  DRAFT: {
    key: 'draft',
    status: 'draft',
    name: 'Brouillon',
  },
  SENT: {
    key: 'sent',
    status: 'sent',
    name: 'Envoyé',
  },
  ACCEPTED: {
    key: 'accepted',
    status: 'accepted',
    name: 'Accepté',
  },
  CONFIRMED: {
    key: 'confirmed',
    status: 'toProcess',
    name: 'Confirmé',
  },
  A_TRAITER: {
    name: 'A traiter',
    key: 'confirmed',
    status: 'toProcess',
    color: 'yellow',
  },
  PRISE_EN_COMPTE: {
    name: 'Prise en compte',
    key: 'confirmed',
    status: 'considered',
    color: 'blue',
  },
  TRAITEE: {
    name: 'Traitée',
    key: 'confirmed',
    status: 'processed',
    color: 'green',
  },
  CANCEL: {
    key: 'confirmed',
    name: 'Annulée',
    status: 'void',
    color: 'red',
  },
  INVOICED: {
    key: 'accepted',
    name: 'Facturé',
    status: 'invoiced',
    color: 'color_status_facture',
  },
  EXPIRED: {
    key: 'accepted',
    name: 'Expiré',
    status: 'expired',
    color: 'red',
  },
  TO_MODIFY: {
    key: 'confirmed',
    name: 'Modification en cours',
    status: 'modificationInProgress',
    color: 'color_status_modify',
  },
  PAID_CLOSE: {
    key: 'confirmed',
    name: 'Facturée / Fermée',
    status: 'invoicedOrder',
    color: 'color_status_facture',
  },
  PARTIALLY_INVOICED: {
    key: 'confirmed',
    name: 'Partiellement facturée',
    status: 'partiallyInvoiced',
    color: 'color_status_facture',
  },
  REFUSED: {
    key: 'accepted',
    name: 'Refusé',
    status: 'declined',
    color: 'red',
  },
};

export const PAYMENT_STATUS = {
  enCompte: 'En compte',
  aucunPaiement: 'Aucun paiement',
  paiementRequis: 'Paiement requis',
};
export const DOCUMENT_FILE_UPLOADS_TYPE = {
  BDC_CLIENT: 'BDC_CLIENT',
  PAYMENT: 'PAYMENT',
};

export const LIST_CRM_ID_VALIDATE_AND_CREATE_ORDER = [
  { CRM_ID: '465671000000388001', name: 'Arnaud Lebrun' },
  { CRM_ID: '465671000000620028', name: 'Rémi Calmel' },
  { CRM_ID: '465671000000638001', name: 'Eliot Cayla' },
  { CRM_ID: '465671000002962001', name: 'Natsuho Ito' },
];
export const TRANSACTION_PROOF = 'preuve_de_virement';

export const CLIENT_SEARCHING_MESSAGE = {
  SEARCHING: 'Recherche du client',
  NO_EXISTING: 'Pas de comptes existants',
  ENTER_REQUIREMENT: 'Veuillez entrer le nom de la Société pour effectuer une recherche Pappers',
  CUSTOMER_SEARCHING: 'Recherche du client sur Papper',
  CLIENT_NOT_FOUND: 'Client introuvable sur Pappers, création d’un nouveau compte',
};

export type SubStatusType = {
  key: string;
  id: string;
  label: string;
};

export const SUB_STATUSES: {
  [key in 'a_traiter']: SubStatusType;
} = {
  a_traiter: {
    key: 'a_traiter',
    id: '337859000059783589',
    label: 'cs_atraite',
  },
};

export const FILE_UPLOAD_TYPES = {
  BDC_CLIENT: 'BDC_CLIENT',
  PAYMENT_PROOF: 'PAYMENT',
};

export const CREATED_FROM_TYPES = {
  ZOHO: 'Zoho',
  QBO: 'QBO',
};

export const TARIF_TYPE = {
  FORFAIT: 'FORFAIT',
  SEMIFORFAIT: 'SEMIFORFAIT',
};

export const PRODUCT_SYNC_STATUS = {
  FAILED: 'failed',
  IN_PROGRESS: 'in_progress',
  SUCCESS: 'success',
};
export const PRICE_MARGIN = 0.15;

export const ZOHO_DOCUMENT_SYNC_STATUS = {
  IN_PROGRESS: 'IN_PROGRESS',
  FAILED: 'FAILED',
  SUCCESS: 'SUCCESS',
};

export const ZOHO_SYNC_STATUS = {
  IN_PROGRESS: 'inprogress',
  FAILED: 'failed',
  SUCCESS: 'success',
};

export const LIST_ACTION_BO_ORDER: LogisticAction[] = [
  { disable: false, key: 'A_TRAITER', name: 'À Traiter' },
  { disable: false, key: 'REALISEE', name: 'Planifiée' },
  { disable: false, key: 'KO', name: 'KO' },
  { disable: false, key: 'ANNULEE', name: 'Annulée' },
  { disable: false, key: 'EN_DECHETERIE', name: 'En déchèterie' },
];
export const LIST_ACTION_BO_BENNE_ORDER = [
  { key: 'A_TRAITER', name: 'À Traiter' },
  { key: 'PLANIFIEE', name: 'Planifiée' },
  { key: 'ATTENTE_RETOUR_PRESTA', name: 'Attente retour presta' },
  { key: 'ATTENTE_RETOUR_COMMERCIAL', name: 'Attente retour commercial' },
  // {key: 'SUR_CHANTIER', name: 'Sur chantier'},
  // {key: 'EN_DECHETERIE', name: 'En déchèterie'},
  // {key: 'KO', name: 'KO'},
  // {key: 'ANNULEE', name: 'Annulée'},
];

export const LIST_PRISE_EN_CHARGE: PriseEnCharge[] = [
  { name: 'Camion', value: 'Camion' },
  { name: 'Autre', value: 'Autre' },
  { name: "A l'Etage", value: "A l'Etage" },
];
export const LIST_FLOORS = [
  { name: '1', value: 1, key_equal: '"1 ou 2"' },
  { name: '2', value: 2, key_equal: '"1 ou 2"' },
  { name: '3', value: 3, key_equal: '"3 ou 4"' },
  { name: '4', value: 4, key_equal: '"3 ou 4"' },
  { name: '5 et plus', value: 5, key_equal: '"5 ou +"' },
];
export const LIST_LIFTS = [
  { name: 'Non', value: '"Non"' },
  { name: 'Oui', value: '"Oui"' },
];
export const BIGBAG_ACTIONS = [
  { key: 'A_TRAITER', name: 'À Traiter' },
  { key: 'PLANIFIEE', name: 'Planifiée' },
  { key: 'KO', name: 'KO' },
  { key: 'ANNULEE', name: 'Annulée' },
  { key: 'EN_DECHETERIE', name: 'En déchèterie' },
  { key: 'ATTENTE_RETOUR_PRESTA', name: 'Attente retour presta' },
  { key: 'ATTENTE_RETOUR_COMMERCIAL', name: 'Attente retour commercial' },
];
export const BO_ORDER_TYPES = {
  BENNE: 'BENNE',
  BIG_BAG: 'BIGBAG',
  CAMION: 'CAMION',
};
export const BO_TIME_TYPES = {
  HOUR: 'HOUR',
  PERIOD: 'PERIOD',
};
export const LIST_TIME_FROM_00_TO_23_30 = [
  { key: '00_00', name: '00:00', hour: 0, minute: 0, disable: false },
  { key: '00_30', name: '00:30', hour: 0, minute: 30, disable: false },
  { key: '01_00', name: '01:00', hour: 1, minute: 0, disable: false },
  { key: '01_30', name: '01:30', hour: 1, minute: 30, disable: false },
  { key: '02_00', name: '02:00', hour: 2, minute: 0, disable: false },
  { key: '02_30', name: '02:30', hour: 2, minute: 30, disable: false },
  { key: '03_00', name: '03:00', hour: 3, minute: 0, disable: false },
  { key: '03_30', name: '03:30', hour: 3, minute: 30, disable: false },
  { key: '04_00', name: '04:00', hour: 4, minute: 0, disable: false },
  { key: '04_30', name: '04:30', hour: 4, minute: 30, disable: false },
  { key: '05_00', name: '05:00', hour: 5, minute: 0, disable: false },
  { key: '05_30', name: '05:30', hour: 5, minute: 30, disable: false },
  { key: '06_00', name: '06:00', hour: 6, minute: 0, disable: false },
  { key: '06_30', name: '06:30', hour: 6, minute: 30, disable: false },
  { key: '07_00', name: '07:00', hour: 7, minute: 0, disable: false },
  { key: '07_30', name: '07:30', hour: 7, minute: 30, disable: false },
  { key: '08_00', name: '08:00', hour: 8, minute: 0, disable: false },
  { key: '08_30', name: '08:30', hour: 8, minute: 30, disable: false },
  { key: '09_00', name: '09:00', hour: 9, minute: 0, disable: false },
  { key: '09_30', name: '09:30', hour: 9, minute: 30, disable: false },
  { key: '10_00', name: '10:00', hour: 10, minute: 0, disable: false },
  { key: '10_30', name: '10:30', hour: 10, minute: 30, disable: false },
  { key: '11_00', name: '11:00', hour: 11, minute: 0, disable: false },
  { key: '11_30', name: '11:30', hour: 11, minute: 30, disable: false },
  { key: '12_00', name: '12:00', hour: 12, minute: 0, disable: false },
  { key: '12_30', name: '12:30', hour: 12, minute: 30, disable: false },
  { key: '13_00', name: '13:00', hour: 13, minute: 0, disable: false },
  { key: '13_30', name: '13:30', hour: 13, minute: 30, disable: false },
  { key: '14_00', name: '14:00', hour: 14, minute: 0, disable: false },
  { key: '14_30', name: '14:30', hour: 14, minute: 30, disable: false },
  { key: '15_00', name: '15:00', hour: 15, minute: 0, disable: false },
  { key: '15_30', name: '15:30', hour: 15, minute: 30, disable: false },
  { key: '16_00', name: '16:00', hour: 16, minute: 0, disable: false },
  { key: '16_30', name: '16:30', hour: 16, minute: 30, disable: false },
  { key: '17_00', name: '17:00', hour: 17, minute: 0, disable: false },
  { key: '17_30', name: '17:30', hour: 17, minute: 30, disable: false },
  { key: '18_00', name: '18:00', hour: 18, minute: 0, disable: false },
  { key: '18_30', name: '18:30', hour: 18, minute: 30, disable: false },
  { key: '19_00', name: '19:00', hour: 19, minute: 0, disable: false },
  { key: '19_30', name: '19:30', hour: 19, minute: 30, disable: false },
  { key: '20_00', name: '20:00', hour: 20, minute: 0, disable: false },
  { key: '20_30', name: '20:30', hour: 20, minute: 30, disable: false },
  { key: '21_00', name: '21:00', hour: 21, minute: 0, disable: false },
  { key: '21_30', name: '21:30', hour: 21, minute: 30, disable: false },
  { key: '22_00', name: '22:00', hour: 22, minute: 0, disable: false },
  { key: '22_30', name: '22:30', hour: 22, minute: 30, disable: false },
  { key: '23_00', name: '23:00', hour: 23, minute: 0, disable: false },
  { key: '23_30', name: '23:30', hour: 23, minute: 30, disable: false },
];
