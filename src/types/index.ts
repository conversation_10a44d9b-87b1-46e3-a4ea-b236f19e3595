import { CatalogPrice, Zone } from 'models';
import { LazyExoticComponent } from 'react';
import { Permissions } from 'services/permission.service';

export type Loading = 'idle' | 'pending' | 'succeeded' | 'failed';

export interface DynamicObject<T> {
  [key: string]: T;
}

export type PaginationData<M> = { count: number; rows: M[] };

export type SearchData = {
  search?: string | undefined;
  page: number | undefined;
  limit: number | undefined | 'unlimited';
} & { [key: string]: string | number | undefined };

export interface AuthenticationResult {
  AccessToken: string;
  IdToken: string;
  RefreshToken: string;
  LoginDate?: string;
}

export interface AppRouteType {
  name: string;
  path: string;
  auth?: boolean;
  component: LazyExoticComponent<() => JSX.Element | null>;
  roles?: number;
  isPublic?: boolean;
  permissions?: Permissions[];
}

export interface BaseSelectRef {
  focus: () => void;
  blur: () => void;
  // eslint-disable-next-line
  scrollTo: any;
}

export interface RegionProductTableType {
  value: number | null | undefined;
  label: string;
}

export interface Address {
  city?: string;
  postalcode?: string;
  zip?: string;
  country?: string;
  address?: string;
  latitude?: string;
  longitude?: string;
  formattedAddress?: string;
  countryRegionId?: number;
  fullAddress?: string;
  // eslint-disable-next-line
  address_id?: any;
  isCompleted?: boolean;
  isCreate?: boolean;
  // eslint-disable-next-line
  [key: string]: any;
  contactAddressId?: number;
}

export type QueryParams = {
  search?: string;
  page?: string | number;
  limit?: string | number | 'unlimited';
  include?: string;
  orderBy?: string;
  subQuery?: boolean;
  name?: string;
  // eslint-disable-next-line
} & { [key: string]: any };

export type DevisActionType =
  | 'submit'
  | 'sent'
  | 're-sent'
  | 'validate-and-create-order'
  | 'demander'
  | 'open'
  | 'marquer-come-accepte'
  | null;
export const dateFormat = 'YYYY-MM-DD';
export type PaymentType = {
  message?: string;
  paiement?: string;
  montantPaiement?: string | number;
};

export type Base64String = string;

export interface FileDetails {
  id?: string | number;
  name: string;
  file_name: string;
  fileType?: string;
  keyFile?: string;
}

export type ConvertBase64 = (file: Blob) => Promise<Base64String | undefined>;
export type DocumentTypes = 'quotation' | 'order' | 'invoice' | string;

export type CatalogPriceZone = CatalogPrice & {
  priceValue: string;
  zoneId: number;
  serviceProviderId?: number;
  productId?: number;
  comment?: string;
  isMandatory?: boolean;
  DestCatalogPrice?: CatalogPrice;
};

export interface ServiceProvider {
  id: number;
  name: string;
  address: string;
  city?: string;
  postalcode: string;
  country: string;
  latitude: string;
  longitude: string;
  formattedAddress: string;
}

export type Offer = {
  type: string | null;
  zones: { zone: string; price: string }[];
  products: string[];
  comments: string | null;
};

export interface Product {
  id: number;
  name: string;
  description?: string;
}

export type FinalData = {
  name: string;
  Address: Address[] | [];
  offers: Offer[];
};

export interface TableRow {
  key: string;
  prestataires: string | JSX.Element;
  produit: string;
  tarif: string;
  commentaire: string;
  zone: string;
  prixPresta: string | number;
  isProvider?: boolean;
  isOffer?: boolean;
  isZone?: boolean;
  isPrice?: boolean;
  rowSpan: {
    prestataires: number;
    produit: number;
  };
}

export interface RawDataLog {
  serviceProviderId: number;
  urbanCenterId: number;
  id: number;
  productId: number;
  productTypeId: number;
  Tarif?: string | null;
  Tarif1?: string | null;
  Produit?: string | null;
  name?: string | null;
  comment?: string;
  zoneId: number;
  priceValue: string;
  year: number;
}

export interface RawDataSales {
  ref?: string;
  productTypeId?: number;
  productId: number;
  priceFamilyId: number;
  priceId: number | null;
  priceOptionId: number | null;
  zonenumber: string;
  Tarif?: string;
  Tarif1?: string;
  Produit?: string | null;
  name?: string | null;
  comment?: string | null;
  distanceByCar: string;
  priceValue: number;
  year: number;
}

export type DocumentQueryTypes = {
  zcrm_contact_id: string;
  referent_id: string;
  lastmodify_by_id: string;
  lastmodify_by_id_books: string;
  create_account: string;
  estimate_id?: string;
  order_id?: string;
};

export type UrbanCenterWithZone = {
  id: number;
  zoneId: number;
  latitude: string;
  longitude: string;
  name: string;
  formattedAddress: string;
  zoneName: string;
  maxDistance: number;
  minDistance: number;
  platformName: string;
};

export type UrbanCenterWithZoneResponse = {
  regionCatalogZoneIds: UrbanCenterWithZone[];
  serviceProviderZoneIds: UrbanCenterWithZone[];
  serviceProviderZones: Zone[];
  countryRegionId?: number | null;
};

export type UrbanCenterZoneIds = {
  regionCatalogZoneIds: number[];
  serviceProviderZoneIds: number[];
  serviceProviderZones: Zone[];
  isCatalog?: boolean | null;
  countryRegionId?: number | null;
};

export type TarifType = 'FORFAIT' | 'SEMIFORFAIT';

export type CatalogPriceZoneResponse = {
  tarifType: TarifType;
  forfait?: CatalogPriceZone[];
  semiforfait?: CatalogPriceZone[];
  countServiceProvider?: number;
  countServiceProviderForfait?: number;
};

export type CatalogPriceZoneServiceProviderRequest = {
  serviceProviderZoneIds: number[];
  products: {
    productId?: number;
    priceFamilyId?: number;
  }[];
};

export interface Comment {
  id: number;
  createdBy: string;
  createdAt: string;
  comment: string;
}

export interface Avoir {
  id: number;
  avoirNumber: string;
  createdAt: string;
  amount: number;
  createdBy: string;
}

export interface PrestationDetail {
  date: string;
  time: string;
  type: string;
}

export interface InvoiceLine {
  key: string;
  productName: string;
  productDetails: string;
  productId: number;
  quantity: number;
  unity: string;
  orderId: string;
  zohoId: string;
  commandId: string;
  orderLink: string;
  purchasePrice: number;
  invoicedPrice: number;
  clientPrice: number;
  margin: number;
  diffPurchase: number;
  status: string;
  isSelected: boolean;
  isInvoiced: boolean;
  invoiceNumber: string | null;
  isWaitingForPrestReply: boolean;
  comment: Comment[];
  rowSpan?: number;
  isFirstLineOfDetail?: boolean;
  totalLinesInDetail?: number;
  detailId?: string;
}

export interface InvoiceDetail {
  productName: string;
  productId: number;
  invoiceNumber: string;
  commandNumber: string;
  siteAddress: string;
  sitePostalCode: string;
  siteCity: string;
  prestationDetails: PrestationDetail[];
  invoicedPrice: number;
  clientPrice: number;
  totalMargin: number;
  totalDiffPurchase: number;
  isSelected: boolean;
  contact: string;
  invoiceLines: InvoiceLine[];
}

export interface Invoice {
  id: number;
  createdAt: string;
  prestataire: string;
  prestataireInvoiceNumber: string;
  status: string;
  totalSelected: number;
  totalAvoirs: number;
  totalAmount: number;
  invoicedAmount: number;
  avoirDetails: Avoir[];
  commentDetails: Comment[];
  invoiceDetails: InvoiceDetail[];
}
