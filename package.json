{"name": "ecodrop-qbofrontend", "version": "1.02.04", "private": true, "dependencies": {"@ant-design/icons": "^5.1.4", "@dnd-kit/core": "^6.1.0", "@dnd-kit/modifiers": "^7.0.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "@reduxjs/toolkit": "^1.9.3", "@testing-library/jest-dom": "^5.16.5", "@testing-library/react": "^13.4.0", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.5.2", "@types/node": "^16.18.23", "@types/react": "^18.2.17", "@types/react-dom": "^18.2.7", "antd": "5.10.0", "axios": "^1.3.4", "buffer": "^6.0.3", "dayjs": "^1.11.13", "js-cookie": "^3.0.1", "moment": "^2.29.4", "node-sass": "^8.0.0", "react": "^18.2.0", "react-calendar": "^4.1.0", "react-dom": "^18.2.0", "react-google-autocomplete": "^2.7.3", "react-helmet-async": "^1.3.0", "react-highlight-words": "^0.20.0", "react-infinite-scroll-component": "^6.1.0", "react-redux": "^8.0.5", "react-router-dom": "^6.10.0", "react-scripts": "5.0.1", "react-spinners": "^0.13.8", "react-toastify": "^9.1.3", "typescript": "^4.9.5", "uuid": "^9.0.0", "web-vitals": "^2.1.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject", "prepare": "husky"}, "lint-staged": {"src/**/*.{js,jsx,ts,tsx}": "eslint --cache --fix", "src/**/*.{js,jsx,ts,tsx,md}": "prettier --write --ignore-unknown"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/js": "^9.10.0", "@types/js-cookie": "^3.0.3", "@types/node-sass": "^4.11.3", "@types/react-calendar": "^3.9.0", "@types/react-highlight-words": "^0.16.4", "@types/uuid": "^9.0.1", "eslint": "^8.57.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-react": "^7.36.1", "globals": "^15.9.0", "husky": "^9.1.6", "lint-staged": "^15.2.10", "prettier": "^3.3.3", "typescript-eslint": "^8.5.0"}}