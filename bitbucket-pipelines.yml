definitions:
  caches:
    node: node_modules

pipelines:
  custom:
    deploy-dev-zoho-prod:
      - stage:
          name: Build and Deploy to Dev zoho PROD
          deployment: development-zoho-prod
          steps:
            - step:
                name: Building the app
                image: node:20.14.0
                script:
                  - npm install
                  - >
                    CI=false
                    REACT_APP_GOOGLE_MAPS_API_KEY=${REACT_APP_GOOGLE_MAPS_API_KEY}
                    REACT_APP_BASE_USER_API=${REACT_APP_BASE_USER_API}
                    REACT_APP_BASE_PRODUCT_API=${REACT_APP_BASE_PRODUCT_API}
                    REACT_APP_BASE_SERVICE_PROVIDER_API=${REACT_APP_BASE_SERVICE_PROVIDER_API}
                    REACT_APP_BASE_REGION_API=${REACT_APP_BASE_REGION_API}
                    REACT_APP_BASE_PRICE_SERVICE_API=${REACT_APP_BASE_PRICE_SERVICE_API}
                    REACT_APP_BASE_DOCUMENT_SERVICE_API=${REACT_APP_BASE_DOCUMENT_SERVICE_API}
                    REACT_APP_BASE_CLIENT_SERVICE_API=${REACT_APP_BASE_CLIENT_SERVICE_API}
                    REACT_APP_ZQO_PATH=${REACT_APP_ZQO_PATH}
                    REACT_APP_CRM_PATH=${REACT_APP_CRM_PATH}
                    REACT_APP_BASE_PATH=""
                    REACT_APP_HIDDEN_FEATURES=${REACT_APP_HIDDEN_FEATURES}
                    REACT_APP_PATH_FILES_S3=${REACT_APP_PATH_FILES_S3}
                    REACT_APP_ZOHO_PATH=${REACT_APP_ZOHO_PATH}
                    REACT_APP_BASE_ZOHO_SERVICE_API=${REACT_APP_BASE_ZOHO_SERVICE_API}
                    REACT_APP_BOOK_TAX_ID_1=${REACT_APP_BOOK_TAX_ID_1}
                    REACT_APP_BOOK_TAX_ID_2=${REACT_APP_BOOK_TAX_ID_2}
                    npm run build
                artifacts:
                  - build/**
            - step:
                name: Deploying the app to S3
                script:
                  - pipe: atlassian/aws-s3-deploy:1.2.0
                    variables:
                      AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                      AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                      AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                      S3_BUCKET: $S3_BUCKET
                      ACL: "public-read"
                      LOCAL_PATH: "build"                      
    deploy-uat:
      - stage:
          name: Build and Deploy to UAT Zoho Sandbox
          deployment: uat
          steps:
            - step:
                name: Building the app
                image: node:20.14.0
                script:
                  - npm install
                  - >
                    CI=false
                    REACT_APP_GOOGLE_MAPS_API_KEY=${REACT_APP_GOOGLE_MAPS_API_KEY}
                    REACT_APP_BASE_USER_API=${REACT_APP_BASE_USER_API}
                    REACT_APP_BASE_PRODUCT_API=${REACT_APP_BASE_PRODUCT_API}
                    REACT_APP_BASE_SERVICE_PROVIDER_API=${REACT_APP_BASE_SERVICE_PROVIDER_API}
                    REACT_APP_BASE_REGION_API=${REACT_APP_BASE_REGION_API}
                    REACT_APP_BASE_PRICE_SERVICE_API=${REACT_APP_BASE_PRICE_SERVICE_API}
                    REACT_APP_BASE_DOCUMENT_SERVICE_API=${REACT_APP_BASE_DOCUMENT_SERVICE_API}
                    REACT_APP_BASE_CLIENT_SERVICE_API=${REACT_APP_BASE_CLIENT_SERVICE_API}
                    REACT_APP_ZQO_PATH=${REACT_APP_ZQO_PATH}
                    REACT_APP_CRM_PATH=${REACT_APP_CRM_PATH}
                    REACT_APP_BASE_PATH=""
                    REACT_APP_HIDDEN_FEATURES=${REACT_APP_HIDDEN_FEATURES}
                    REACT_APP_PATH_FILES_S3=${REACT_APP_PATH_FILES_S3}
                    REACT_APP_BOOK_TAX_ID_1=${REACT_APP_BOOK_TAX_ID_1}
                    REACT_APP_BOOK_TAX_ID_2=${REACT_APP_BOOK_TAX_ID_2}
                    npm run build
                artifacts:
                  - build/**
            - step:
                name: Deploying the app to S3
                script:
                  - pipe: atlassian/aws-s3-deploy:1.2.0
                    variables:
                      AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                      AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                      AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                      S3_BUCKET: $S3_BUCKET
                      ACL: "public-read"
                      LOCAL_PATH: "build"
    deploy-uat-zoho-prod:
      - stage:
          name: Build and Deploy to UAT zoho PROD
          deployment: uat-zoho-prod
          steps:
            - step:
                name: Building the app
                image: node:20.14.0
                script:
                  - npm install
                  - >
                    CI=false
                    REACT_APP_GOOGLE_MAPS_API_KEY=${REACT_APP_GOOGLE_MAPS_API_KEY}
                    REACT_APP_BASE_USER_API=${REACT_APP_BASE_USER_API}
                    REACT_APP_BASE_PRODUCT_API=${REACT_APP_BASE_PRODUCT_API}
                    REACT_APP_BASE_SERVICE_PROVIDER_API=${REACT_APP_BASE_SERVICE_PROVIDER_API}
                    REACT_APP_BASE_REGION_API=${REACT_APP_BASE_REGION_API}
                    REACT_APP_BASE_PRICE_SERVICE_API=${REACT_APP_BASE_PRICE_SERVICE_API}
                    REACT_APP_BASE_DOCUMENT_SERVICE_API=${REACT_APP_BASE_DOCUMENT_SERVICE_API}
                    REACT_APP_BASE_CLIENT_SERVICE_API=${REACT_APP_BASE_CLIENT_SERVICE_API}
                    REACT_APP_ZQO_PATH=${REACT_APP_ZQO_PATH}
                    REACT_APP_CRM_PATH=${REACT_APP_CRM_PATH}
                    REACT_APP_BASE_PATH=""
                    REACT_APP_HIDDEN_FEATURES=${REACT_APP_HIDDEN_FEATURES}
                    REACT_APP_PATH_FILES_S3=${REACT_APP_PATH_FILES_S3}
                    REACT_APP_BOOK_TAX_ID_1=${REACT_APP_BOOK_TAX_ID_1}
                    REACT_APP_BOOK_TAX_ID_2=${REACT_APP_BOOK_TAX_ID_2}
                    npm run build
                artifacts:
                  - build/**
            - step:
                name: Deploying the app to S3
                script:
                  - pipe: atlassian/aws-s3-deploy:1.2.0
                    variables:
                      AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                      AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                      AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                      S3_BUCKET: $S3_BUCKET
                      ACL: "public-read"
                      LOCAL_PATH: "build"
    DEPLOY-PROD:
      - stage:
          name: Build and Deploy to Production
          deployment: production
          steps:
            - step:
                name: Building the app
                image: node:20.14.0
                script:
                  - npm install
                  - >
                    CI=false
                    REACT_APP_GOOGLE_MAPS_API_KEY=${REACT_APP_GOOGLE_MAPS_API_KEY}
                    REACT_APP_BASE_USER_API=${REACT_APP_BASE_USER_API}
                    REACT_APP_BASE_PRODUCT_API=${REACT_APP_BASE_PRODUCT_API}
                    REACT_APP_BASE_SERVICE_PROVIDER_API=${REACT_APP_BASE_SERVICE_PROVIDER_API}
                    REACT_APP_BASE_REGION_API=${REACT_APP_BASE_REGION_API}
                    REACT_APP_BASE_PRICE_SERVICE_API=${REACT_APP_BASE_PRICE_SERVICE_API}
                    REACT_APP_BASE_DOCUMENT_SERVICE_API=${REACT_APP_BASE_DOCUMENT_SERVICE_API}
                    REACT_APP_BASE_CLIENT_SERVICE_API=${REACT_APP_BASE_CLIENT_SERVICE_API}
                    REACT_APP_ZQO_PATH=${REACT_APP_ZQO_PATH}
                    REACT_APP_CRM_PATH=${REACT_APP_CRM_PATH}
                    REACT_APP_BASE_PATH=""
                    REACT_APP_HIDDEN_FEATURES=${REACT_APP_HIDDEN_FEATURES}
                    REACT_APP_PATH_FILES_S3=${REACT_APP_PATH_FILES_S3}
                    REACT_APP_BOOK_TAX_ID_1=${REACT_APP_BOOK_TAX_ID_1}
                    REACT_APP_BOOK_TAX_ID_2=${REACT_APP_BOOK_TAX_ID_2}
                    npm run build
                  - export VERSION="v$(node -p "require('./package.json').version")"
                  - git tag -a ${VERSION} -m "Production deployment v${VERSION}"
                  - git push origin ${VERSION}
                artifacts:
                  - build/**
            - step:
                name: Deploying the app to S3
                script:
                  - pipe: atlassian/aws-s3-deploy:1.2.0
                    variables:
                      AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                      AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                      AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                      S3_BUCKET: $S3_BUCKET
                      ACL: "public-read"
                      LOCAL_PATH: "build"
  branches:
    Feature-Test-Dev:
      - stage:
          name: Build and Deploy to Dev Zoho Sandbox
          deployment: development
          steps:
            - step:
                name: Building the app
                image: node:20.14.0
                script:
                  - npm install
                  - >
                    CI=false
                    REACT_APP_GOOGLE_MAPS_API_KEY=${REACT_APP_GOOGLE_MAPS_API_KEY}
                    REACT_APP_BASE_USER_API=${REACT_APP_BASE_USER_API}
                    REACT_APP_BASE_PRODUCT_API=${REACT_APP_BASE_PRODUCT_API}
                    REACT_APP_BASE_SERVICE_PROVIDER_API=${REACT_APP_BASE_SERVICE_PROVIDER_API}
                    REACT_APP_BASE_REGION_API=${REACT_APP_BASE_REGION_API}
                    REACT_APP_BASE_PRICE_SERVICE_API=${REACT_APP_BASE_PRICE_SERVICE_API}
                    REACT_APP_BASE_DOCUMENT_SERVICE_API=${REACT_APP_BASE_DOCUMENT_SERVICE_API}
                    REACT_APP_BASE_CLIENT_SERVICE_API=${REACT_APP_BASE_CLIENT_SERVICE_API}
                    REACT_APP_ZQO_PATH=${REACT_APP_ZQO_PATH}
                    REACT_APP_CRM_PATH=${REACT_APP_CRM_PATH}
                    REACT_APP_BASE_PATH=""
                    REACT_APP_HIDDEN_FEATURES=${REACT_APP_HIDDEN_FEATURES}
                    REACT_APP_PATH_FILES_S3=${REACT_APP_PATH_FILES_S3}
                    REACT_APP_ZOHO_PATH=${REACT_APP_ZOHO_PATH}
                    REACT_APP_BASE_ZOHO_SERVICE_API=${REACT_APP_BASE_ZOHO_SERVICE_API}
                    REACT_APP_BOOK_TAX_ID_1=${REACT_APP_BOOK_TAX_ID_1}
                    REACT_APP_BOOK_TAX_ID_2=${REACT_APP_BOOK_TAX_ID_2}
                    npm run build
                artifacts:
                  - build/**
            - step:
                name: Deploying the app to S3
                script:
                  - pipe: atlassian/aws-s3-deploy:1.2.0
                    variables:
                      AWS_ACCESS_KEY_ID: $AWS_ACCESS_KEY_ID
                      AWS_SECRET_ACCESS_KEY: $AWS_SECRET_ACCESS_KEY
                      AWS_DEFAULT_REGION: $AWS_DEFAULT_REGION
                      S3_BUCKET: $S3_BUCKET
                      ACL: "public-read"
                      LOCAL_PATH: "build"